import { html, render } from 'lit'
import { $, expect } from '@wdio/globals'

import './Component.js'
import './Component.css'

describe('Lit component testing', () => {
    it('should increment value on click', async () => {
        render(
            html`<my-element id="root">WebdriverIO Component Testing</my-element>`,
            document.body
        )

        const button = await $('my-element').$('button')
        await expect(button).toHaveText('count is 0')

        await button.click()
        await button.click()

        await expect(button).toHaveText('count is 2')<%-
        answers.includeVisualTesting ? `
        await expect(button).toMatchElementSnapshot('counterButton')` : '' %>
    })
})
