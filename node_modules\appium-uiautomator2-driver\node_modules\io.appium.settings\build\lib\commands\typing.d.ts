/**
 * Performs the given editor action on the focused input field.
 * This method requires <PERSON><PERSON><PERSON> Settings helper to be installed on the device.
 * No exception is thrown if there was a failure while performing the action.
 * You must investigate the logcat output if something did not work as expected.
 *
 * @this {import('../client').SettingsApp}
 * @param {string|number} action - Either action code or name. The following action
 *                                 names are supported: `normal, unspecified, none,
 *                                 go, search, send, next, done, previous`
 */
export function performEditorAction(this: import("../client").SettingsApp, action: string | number): Promise<void>;
/**
 * Types the given Unicode string.
 * It is expected that the focus is already put
 * to the destination input field before this method is called.
 *
 * @this {import('../client').SettingsApp}
 * @param {string} text The string to type
 * @returns {Promise<boolean>} `true` if the input text has been successfully sent to adb
 */
export function typeUnicode(this: import("../client").SettingsApp, text: string): Promise<boolean>;
//# sourceMappingURL=typing.d.ts.map