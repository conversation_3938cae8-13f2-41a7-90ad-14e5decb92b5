{"name": "package-changed", "version": "3.0.0", "description": "Tells you whether or not dependencies in package.json have been changed.", "main": "lib/index.js", "types": "types/index.d.ts", "typings": "types/index.d.ts", "bin": {"package-changed": "bin/package-changed.js"}, "files": ["lib", "esm", "types"], "scripts": {"build:cjs": "tsc --declarationdir types", "build:es": "tsc -m esNext --outDir esm --declaration false", "build": "npm run build:cjs && npm run build:es", "lint": "eslint --ext .js,.ts", "format": "prettier --write \"src/**/*.ts\"", "clean": "rimraf lib esm types", "preversion": "npm run build && npm run lint", "version": "npm run format && git add -A", "postversion": "git push && git push --tags && npm publish"}, "repository": {"type": "git", "url": "https://github.com/thdk/package-changed.git"}, "author": "<PERSON> <<EMAIL>>(http://github.com/thdk)", "license": "ISC", "keywords": ["npm", "install", "modified", "changed", "changes", "package", "dependencies", "devDepencies", "package", "hash"], "dependencies": {"commander": "^6.2.0"}, "devDependencies": {"@babel/core": "^7.12.3", "@babel/preset-env": "^7.12.1", "@babel/preset-typescript": "^7.12.1", "@types/jest": "^26.0.15", "@types/node": "^14.14.6", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "babel-jest": "^26.6.3", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "prettier": "^2.1.2", "rimraf": "^3.0.2", "typescript": "^4.0.5"}}