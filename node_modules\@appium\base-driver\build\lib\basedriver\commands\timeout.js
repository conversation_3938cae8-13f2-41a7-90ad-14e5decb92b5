"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const asyncbox_1 = require("asyncbox");
const lodash_1 = __importDefault(require("lodash"));
const support_1 = require("@appium/support");
const protocol_1 = require("../../protocol");
const mixin_1 = require("./mixin");
const MIN_TIMEOUT = 0;
const TimeoutCommands = {
    async timeouts(type, ms, script, pageLoad, implicit) {
        if (support_1.util.hasValue(type) && support_1.util.hasValue(ms)) {
            this.log.debug(`MJSONWP timeout arguments: ${JSON.stringify({ type, ms })}}`);
            switch (type) {
                case 'command':
                    await this.newCommandTimeout(ms);
                    return;
                case 'implicit':
                    await this.implicitWaitMJSONWP(ms);
                    return;
                case 'page load':
                    await this.pageLoadTimeoutMJSONWP(ms);
                    return;
                case 'script':
                    await this.scriptTimeoutMJSONWP(ms);
                    return;
                default:
                    throw new Error(`'${type}' type is not supported for MJSONWP timeout`);
            }
        }
        // Otherwise assume it is W3C protocol
        this.log.debug(`W3C timeout argument: ${JSON.stringify({
            script,
            pageLoad,
            implicit,
        })}}`);
        if (support_1.util.hasValue(script)) {
            await this.scriptTimeoutW3C(script);
        }
        if (support_1.util.hasValue(pageLoad)) {
            await this.pageLoadTimeoutW3C(pageLoad);
        }
        if (support_1.util.hasValue(implicit)) {
            await this.implicitWaitW3C(implicit);
        }
    },
    async getTimeouts() {
        return {
            command: this.newCommandTimeoutMs,
            implicit: this.implicitWaitMs,
        };
    },
    // implicit
    async implicitWaitW3C(ms) {
        await this.implicitWait(ms);
    },
    async implicitWaitMJSONWP(ms) {
        await this.implicitWait(ms);
    },
    async implicitWait(ms) {
        this.setImplicitWait(this.parseTimeoutArgument(ms));
    },
    // pageLoad
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async pageLoadTimeoutW3C(ms) {
        throw new protocol_1.errors.NotImplementedError('Not implemented yet for pageLoad.');
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async pageLoadTimeoutMJSONWP(ms) {
        throw new protocol_1.errors.NotImplementedError('Not implemented yet for pageLoad.');
    },
    // script
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async scriptTimeoutW3C(ms) {
        throw new protocol_1.errors.NotImplementedError('Not implemented yet for script.');
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async scriptTimeoutMJSONWP(ms) {
        throw new protocol_1.errors.NotImplementedError('Not implemented yet for script.');
    },
    // command
    async newCommandTimeout(ms) {
        this.setNewCommandTimeout(this.parseTimeoutArgument(ms));
    },
    setImplicitWait(ms) {
        this.implicitWaitMs = ms;
        this.log.debug(`Set implicit wait to ${ms}ms`);
        if (this.managedDrivers?.length) {
            this.log.debug('Setting implicit wait on managed drivers');
            for (const driver of this.managedDrivers) {
                if (lodash_1.default.isFunction(driver.setImplicitWait)) {
                    driver.setImplicitWait(ms);
                }
            }
        }
    },
    setNewCommandTimeout(ms) {
        this.newCommandTimeoutMs = ms;
        this.log.debug(`Set new command timeout to ${ms}ms`);
        if (this.managedDrivers?.length) {
            this.log.debug('Setting new command timeout on managed drivers');
            for (const driver of this.managedDrivers) {
                if (lodash_1.default.isFunction(driver.setNewCommandTimeout)) {
                    driver.setNewCommandTimeout(ms);
                }
            }
        }
    },
    async implicitWaitForCondition(condFn) {
        this.log.debug(`Waiting up to ${this.implicitWaitMs} ms for condition`);
        const wrappedCondFn = async (...args) => {
            // reset command timeout
            await this.clearNewCommandTimeout();
            return await condFn(...args);
        };
        return await (0, asyncbox_1.waitForCondition)(wrappedCondFn, {
            waitMs: this.implicitWaitMs,
            intervalMs: 500,
            logger: this.log,
        });
    },
    parseTimeoutArgument(ms) {
        const duration = parseInt(String(ms), 10);
        if (lodash_1.default.isNaN(duration) || duration < MIN_TIMEOUT) {
            throw new protocol_1.errors.UnknownError(`Invalid timeout value '${ms}'`);
        }
        return duration;
    },
};
(0, mixin_1.mixin)(TimeoutCommands);
//# sourceMappingURL=timeout.js.map