/**
 * @see https://github.com/appium/appium-uiautomator2-driver/blob/master/docs/scheduled-actions.md#mobile-scheduleaction
 * @this {AndroidUiautomator2Driver}
 * @param {string} name
 * @param {import('@appium/types').StringRecord[]} steps
 * @param {number} [maxPass]
 * @param {number} [maxFail]
 * @param {number} [times]
 * @param {number} [intervalMs]
 * @param {number} [maxHistoryItems]
 * @returns {Promise<any>}
 */
export function mobileScheduleAction(this: import("../driver").AndroidUiautomator2Driver, name: string, steps: import("@appium/types").StringRecord[], maxPass?: number, maxFail?: number, times?: number, intervalMs?: number, maxHistoryItems?: number): Promise<any>;
/**
 * @see https://github.com/appium/appium-uiautomator2-driver/blob/master/docs/scheduled-actions.md#mobile-getactionhistory
 * @this {AndroidUiautomator2Driver}
 * @param {string} name
 * @returns {Promise<import('./types').ActionResult>}
 */
export function mobileGetActionHistory(this: import("../driver").AndroidUiautomator2Driver, name: string): Promise<import("./types").ActionResult>;
/**
 * @this {AndroidUiautomator2Driver}
 * @see https://github.com/appium/appium-uiautomator2-driver/blob/master/docs/scheduled-actions.md#mobile-unscheduleaction
 * @param {string} name
 * @returns {Promise<any>}
 */
export function mobileUnscheduleAction(this: import("../driver").AndroidUiautomator2Driver, name: string): Promise<any>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {import('@appium/types').StringRecord[]} actions
 * @returns {Promise<void>}
 */
export function performActions(this: import("../driver").AndroidUiautomator2Driver, actions: import("@appium/types").StringRecord[]): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<void>}
 */
export function releaseActions(this: import("../driver").AndroidUiautomator2Driver): Promise<void>;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
//# sourceMappingURL=actions.d.ts.map