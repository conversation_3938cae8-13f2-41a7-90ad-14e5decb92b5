{"version": 3, "file": "proxy-signals.js", "sourceRoot": "", "sources": ["../../src/proxy-signals.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAA;AAE7C;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,KAAmB,EAAE,EAAE;IAClD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;IAE3B,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,8CAA8C;YAC9C,IAAI,CAAC;gBACH,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACf,qBAAqB;YACvB,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC,CAAA,CAAC;YACd,oBAAoB;QACtB,CAAC,CAAA;QACD,IAAI,CAAC;YACH,0DAA0D;YAC1D,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;YACzB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;YAC5B,qBAAqB;QACvB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC,CAAA,CAAC;QACd,oBAAoB;IACtB,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,EAAE;QACnB,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,SAAS,EAAE,CAAC;YACxC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;QACvC,CAAC;IACH,CAAC,CAAA;IACD,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACzB,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA", "sourcesContent": ["import { type ChildProcess } from 'child_process'\nimport { allSignals } from './all-signals.js'\n\n/**\n * Starts forwarding signals to `child` through `parent`.\n */\nexport const proxySignals = (child: ChildProcess) => {\n  const listeners = new Map()\n\n  for (const sig of allSignals) {\n    const listener = () => {\n      // some signals can only be received, not sent\n      try {\n        child.kill(sig)\n        /* c8 ignore start */\n      } catch (_) {}\n      /* c8 ignore stop */\n    }\n    try {\n      // if it's a signal this system doesn't recognize, skip it\n      process.on(sig, listener)\n      listeners.set(sig, listener)\n      /* c8 ignore start */\n    } catch (_) {}\n    /* c8 ignore stop */\n  }\n\n  const unproxy = () => {\n    for (const [sig, listener] of listeners) {\n      process.removeListener(sig, listener)\n    }\n  }\n  child.on('exit', unproxy)\n  return unproxy\n}\n"]}