{"version": 3, "file": "driver.d.ts", "sourceRoot": "", "sources": ["../../../lib/basedriver/driver.ts"], "names": [], "mappings": "AACA,OAAO,EAEL,KAAK,YAAY,EACjB,KAAK,wBAAwB,EAC7B,KAAK,YAAY,EACjB,KAAK,WAAW,EAChB,KAAK,0BAA0B,EAC/B,KAAK,MAAM,EACX,KAAK,UAAU,EACf,KAAK,UAAU,EACf,KAAK,gBAAgB,EACrB,KAAK,UAAU,EACf,KAAK,YAAY,EACjB,KAAK,aAAa,EAClB,KAAK,WAAW,EAChB,KAAK,0BAA0B,EAC/B,KAAK,mBAAmB,EACxB,KAAK,mBAAmB,EACzB,MAAM,eAAe,CAAC;AAOvB,OAAO,EAAC,UAAU,EAAC,MAAM,QAAQ,CAAC;AASlC,qBAAa,UAAU,CACnB,KAAK,CAAC,CAAC,SAAS,WAAW,EAC3B,KAAK,SAAS,YAAY,GAAG,YAAY,EACzC,QAAQ,SAAS,YAAY,GAAG,YAAY,EAC5C,YAAY,GAAG,0BAA0B,CAAC,CAAC,CAAC,EAC5C,YAAY,GAAG,0BAA0B,EACzC,WAAW,SAAS,YAAY,GAAG,YAAY,CAEjD,SAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,CAC9B,YAAW,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC;IAE9E,OAAO,EAAE,KAAK,GAAG,UAAU,CAAC;IAC5B,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;IACpB,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;IAC/B,qBAAqB,EAAE,CAAC,CAAC;IACzB,MAAM,CAAC,EAAE,YAAY,CAAC;IACtB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;gBAER,IAAI,EAAE,WAAW,EAAE,kBAAkB,UAAO;IAOxD;;;;;;OAMG;IACH,SAAS,KAAK,sBAAsB,IAAI,QAAQ,CAAC,wBAAwB,GAAG,CAAC,CAAC,CAE7E;IAED;;;;;OAKG;IACG,cAAc,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;IA+FpE,uBAAuB,CAC3B,GAAG,GAAE,KAA8E;IAa/E,sBAAsB;IAqB5B,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAWrE,KAAK;IA8BX;;;;;;OAMG;IACG,aAAa,CACjB,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC,EAClC,gBAAgB,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EACnC,eAAe,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EAElC,UAAU,CAAC,EAAE,UAAU,EAAE,GACxB,OAAO,CAAC,YAAY,CAAC;IAsFxB;;;OAGG;IACG,WAAW;IAajB;;;;OAIG;IACG,UAAU;IAMhB;;OAEG;IACG,4BAA4B,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAK/D,aAAa,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI;IAa7C,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;IAUlC,mBAAmB,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC;IAqB/C,cAAc,CAAC,WAAW,EAAE,QAAQ;IAOpC,WAAW;CAMlB;AAED,cAAc,YAAY,CAAC;AAE3B,eAAe,UAAU,CAAC"}