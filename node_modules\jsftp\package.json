{"name": "jsftp", "id": "jsftp", "version": "2.1.3", "description": "A sane FTP client implementation for NodeJS", "keywords": ["ftp", "protocol", "files", "server", "client", "async"], "author": "<PERSON><PERSON> <<EMAIL>> (https://sergimansilla.com)", "homepage": "https://github.com/sergi/jsftp", "repository": {"type": "git", "url": "https://github.com/sergi/jsftp.git"}, "bugs": {"url": "https://github.com/sergi/jsftp/issues"}, "dependencies": {"debug": "^3.1.0", "ftp-response-parser": "^1.0.1", "once": "^1.4.0", "parse-listing": "^1.1.3", "stream-combiner": "^0.2.2", "unorm": "^1.4.1"}, "devDependencies": {"concat-stream": "^1.6.0", "mocha": "^4.0.1", "nyc": "^11.4.0", "rimraf": "^2.6.1", "sinon": "^4.1.2"}, "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"test": "make -f test/Makefile test-pureftpd", "run-mocha": "nyc mocha -R spec -t 5000", "clean": "rm -rf reports", "check-format": "prettier --list-different ./lib/**"}, "license": "MIT"}