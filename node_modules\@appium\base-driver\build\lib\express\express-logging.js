"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startLogFormatter = exports.endLogFormatter = void 0;
const lodash_1 = __importDefault(require("lodash"));
require("@colors/colors");
const morgan_1 = __importDefault(require("morgan"));
const logger_1 = __importDefault(require("./logger"));
const constants_1 = require("../constants");
// Copied the morgan compile function over so that cooler formats
// may be configured
function compile(fmt) {
    // escape quotes
    fmt = fmt.replace(/"/g, '\\"');
    fmt = fmt.replace(/:([-\w]{2,})(?:\[([^\]]+)\])?/g, function replace(_, name, arg) {
        return `"\n    + (tokens["${name}"](req, res, "${arg}") || "-") + "`;
    });
    let js = `  return "${fmt}";`;
    return new Function('tokens, req, res', js);
}
function requestEndLoggingFormat(tokens, req, res) {
    let status = res.statusCode;
    let statusStr = ':status';
    if (status >= 500) {
        statusStr = statusStr.red;
    }
    else if (status >= 400) {
        statusStr = statusStr.yellow;
    }
    else if (status >= 300) {
        statusStr = statusStr.cyan;
    }
    else {
        statusStr = statusStr.green;
    }
    let fn = compile(`${'<-- :method :url '.white}${statusStr} ${':response-time ms - :res[content-length]'.grey}`);
    return fn(tokens, req, res);
}
const endLogFormatter = (0, morgan_1.default)((tokens, req, res) => {
    logger_1.default.info(requestEndLoggingFormat(tokens, req, res), (res.jsonResp || '').grey);
});
exports.endLogFormatter = endLogFormatter;
const requestStartLoggingFormat = compile(`${'-->'.white} ${':method'.white} ${':url'.white}`);
const startLogFormatter = (0, morgan_1.default)((tokens, req, res) => {
    // morgan output is redirected straight to winston
    let reqBody = '';
    if (req.body) {
        try {
            reqBody = lodash_1.default.truncate(lodash_1.default.isString(req.body) ? req.body : JSON.stringify(req.body), {
                length: constants_1.MAX_LOG_BODY_LENGTH,
            });
        }
        catch { }
    }
    logger_1.default.info(requestStartLoggingFormat(tokens, req, res), reqBody.grey);
}, { immediate: true });
exports.startLogFormatter = startLogFormatter;
//# sourceMappingURL=express-logging.js.map