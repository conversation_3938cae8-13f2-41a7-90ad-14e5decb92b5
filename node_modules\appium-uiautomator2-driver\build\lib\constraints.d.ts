declare const UIAUTOMATOR2_CONSTRAINTS: {
    readonly platformName: {
        readonly isString: true;
        readonly inclusionCaseInsensitive: readonly ["Android"];
        readonly presence: true;
    };
    readonly deviceName: {
        readonly isString: true;
    };
    readonly appActivity: {
        readonly isString: true;
    };
    readonly appPackage: {
        readonly isString: true;
    };
    readonly appWaitActivity: {
        readonly isString: true;
    };
    readonly appWaitPackage: {
        readonly isString: true;
    };
    readonly appWaitDuration: {
        readonly isNumber: true;
    };
    readonly deviceReadyTimeout: {
        readonly isNumber: true;
    };
    readonly androidDeviceReadyTimeout: {
        readonly isNumber: true;
    };
    readonly androidDeviceSocket: {
        readonly isString: true;
    };
    readonly androidInstallTimeout: {
        readonly isNumber: true;
    };
    readonly adbPort: {
        readonly isNumber: true;
    };
    readonly remoteAdbHost: {
        readonly isString: true;
    };
    readonly adbExecTimeout: {
        readonly isNumber: true;
    };
    readonly avd: {
        readonly isString: true;
    };
    readonly avdLaunchTimeout: {
        readonly isNumber: true;
    };
    readonly avdReadyTimeout: {
        readonly isNumber: true;
    };
    readonly avdArgs: {};
    readonly avdEnv: {
        readonly isObject: true;
    };
    readonly useKeystore: {
        readonly isBoolean: true;
    };
    readonly keystorePath: {
        readonly isString: true;
    };
    readonly keystorePassword: {
        readonly isString: true;
    };
    readonly keyAlias: {
        readonly isString: true;
    };
    readonly keyPassword: {
        readonly isString: true;
    };
    readonly webviewDevtoolsPort: {
        readonly isNumber: true;
    };
    readonly ensureWebviewsHavePages: {
        readonly isBoolean: true;
    };
    readonly enableWebviewDetailsCollection: {
        readonly isBoolean: true;
    };
    readonly chromedriverPort: {
        readonly isNumber: true;
    };
    readonly chromedriverPorts: {
        readonly isArray: true;
    };
    readonly chromedriverArgs: {
        readonly isObject: true;
    };
    readonly chromedriverExecutable: {
        readonly isString: true;
    };
    readonly chromedriverExecutableDir: {
        readonly isString: true;
    };
    readonly chromedriverChromeMappingFile: {
        readonly isString: true;
    };
    readonly chromedriverUseSystemExecutable: {
        readonly isBoolean: true;
    };
    readonly chromedriverDisableBuildCheck: {
        readonly isBoolean: true;
    };
    readonly chromeLoggingPrefs: {
        readonly isObject: true;
    };
    readonly autoWebviewTimeout: {
        readonly isNumber: true;
    };
    readonly autoWebviewName: {
        readonly isString: true;
    };
    readonly intentAction: {
        readonly isString: true;
    };
    readonly intentCategory: {
        readonly isString: true;
    };
    readonly intentFlags: {
        readonly isString: true;
    };
    readonly optionalIntentArguments: {
        readonly isString: true;
    };
    readonly dontStopAppOnReset: {
        readonly isBoolean: true;
    };
    readonly unicodeKeyboard: {
        readonly isBoolean: true;
    };
    readonly hideKeyboard: {
        readonly isBoolean: true;
    };
    readonly noSign: {
        readonly isBoolean: true;
    };
    readonly recreateChromeDriverSessions: {
        readonly isBoolean: false;
    };
    readonly autoLaunch: {
        readonly isBoolean: true;
    };
    readonly nativeWebScreenshot: {
        readonly isBoolean: true;
    };
    readonly clearSystemFiles: {
        readonly isBoolean: true;
    };
    readonly extractChromeAndroidPackageFromContextName: {
        readonly isBoolean: true;
    };
    readonly autoGrantPermissions: {
        readonly isBoolean: true;
    };
    readonly networkSpeed: {
        readonly isString: true;
    };
    readonly gpsEnabled: {
        readonly isBoolean: true;
    };
    readonly isHeadless: {
        readonly isBoolean: true;
    };
    readonly showChromedriverLog: {
        readonly isBoolean: true;
    };
    readonly skipUnlock: {
        readonly isBoolean: true;
    };
    readonly clearDeviceLogsOnStart: {
        readonly isBoolean: true;
    };
    readonly unlockType: {
        readonly isString: true;
    };
    readonly unlockKey: {
        readonly isString: true;
    };
    readonly unlockStrategy: {
        readonly isString: true;
        readonly inclusionCaseInsensitive: readonly ["locksettings", "uiautomator"];
    };
    readonly otherApps: {
        readonly isString: true;
    };
    readonly uninstallOtherPackages: {
        readonly isString: true;
    };
    readonly allowTestPackages: {
        readonly isBoolean: true;
    };
    readonly pageLoadStrategy: {
        readonly isString: true;
    };
    readonly localeScript: {
        readonly isString: true;
    };
    readonly skipDeviceInitialization: {
        readonly isBoolean: true;
    };
    readonly remoteAppsCacheLimit: {
        readonly isNumber: true;
    };
    readonly buildToolsVersion: {
        readonly isString: true;
    };
    readonly skipLogcatCapture: {
        readonly isBoolean: true;
    };
    readonly chromeOptions: {
        readonly isObject: true;
    };
    readonly enablePerformanceLogging: {
        readonly isBoolean: true;
    };
    readonly userProfile: {
        readonly isNumber: true;
    };
    readonly browserName: {
        readonly isString: true;
    };
    readonly enforceAppInstall: {
        readonly isBoolean: true;
    };
    readonly suppressKillServer: {
        readonly isBoolean: true;
    };
    readonly allowOfflineDevices: {
        readonly isBoolean: true;
    };
    readonly ignoreHiddenApiPolicyError: {
        readonly isBoolean: true;
    };
    readonly unlockSuccessTimeout: {
        readonly isNumber: true;
    };
    readonly mockLocationApp: {
        readonly isString: true;
    };
    readonly logcatFormat: {
        readonly isString: true;
    };
    readonly logcatFilterSpecs: {
        readonly isArray: true;
    };
    readonly allowDelayAdb: {
        readonly isBoolean: true;
    };
    readonly ignoreUnimportantViews: {
        readonly isBoolean: true;
    };
    readonly disableWindowAnimation: {
        readonly isBoolean: true;
    };
    readonly appWaitForLaunch: {
        readonly isBoolean: true;
    };
    readonly timeZone: {
        readonly isString: true;
    };
    readonly injectedImageProperties: {
        readonly isObject: true;
    };
    readonly launchTimeout: {
        readonly isNumber: true;
    };
    readonly uiautomator2ServerLaunchTimeout: {
        readonly isNumber: true;
    };
    readonly uiautomator2ServerInstallTimeout: {
        readonly isNumber: true;
    };
    readonly uiautomator2ServerReadTimeout: {
        readonly isNumber: true;
    };
    readonly systemPort: {
        readonly isNumber: true;
    };
    readonly mjpegServerPort: {
        readonly isNumber: true;
    };
    readonly mjpegScreenshotUrl: {
        readonly isString: true;
    };
    readonly skipServerInstallation: {
        readonly isBoolean: true;
    };
    readonly disableSuppressAccessibilityService: {
        readonly isBoolean: true;
    };
    readonly forceAppLaunch: {
        readonly isBoolean: true;
    };
    readonly shouldTerminateApp: {
        readonly isBoolean: true;
    };
};
export default UIAUTOMATOR2_CONSTRAINTS;
export type Uiautomator2Constraints = typeof UIAUTOMATOR2_CONSTRAINTS;
//# sourceMappingURL=constraints.d.ts.map