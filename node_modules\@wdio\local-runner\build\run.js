// src/run.ts
import { asyncExitHook, gracefulExit } from "exit-hook";
import logger from "@wdio/logger";
import Runner from "@wdio/runner";

// src/constants.ts
var SHUTDOWN_TIMEOUT = 5e3;
var BUFFER_OPTIONS = {
  initialSize: 1e3 * 1024,
  // start at 100 kilobytes.
  incrementAmount: 100 * 1024
  // grow by 10 kilobytes each time buffer overflows.
};

// src/run.ts
var log = logger("@wdio/local-runner");
if (typeof process.send === "function") {
  process.send({
    name: "ready",
    origin: "worker"
  });
}
var runner = new Runner();
runner.on("exit", (code = 0) => gracefulExit(code));
runner.on("error", ({ name, message, stack }) => process.send({
  origin: "worker",
  name: "error",
  content: { name, message, stack }
}));
process.on("message", (m) => {
  if (!m || !m.command || !runner[m.command] || typeof runner[m.command] !== "function") {
    return;
  }
  log.info(`Run worker command: ${m.command}`);
  runner[m.command](m).then(
    (result) => process.send({
      origin: "worker",
      name: "finishedCommand",
      content: {
        command: m.command,
        result
      }
    }),
    (e) => {
      log.error(`Failed launching test session: ${e.stack}`);
      setTimeout(() => process.exit(1), 10);
    }
  );
});
process.once("SIGINT", () => {
  runner.sigintWasCalled = true;
  gracefulExit(130);
});
asyncExitHook(
  async () => {
    if (runner.sigintWasCalled) {
      log.info(`Received SIGINT, giving process ${SHUTDOWN_TIMEOUT}ms to shutdown gracefully`);
      return await new Promise((res) => setTimeout(res, SHUTDOWN_TIMEOUT));
    }
  },
  {
    wait: SHUTDOWN_TIMEOUT
  }
);
export {
  runner
};
