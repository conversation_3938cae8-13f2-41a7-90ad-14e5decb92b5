# lodash.isfinite v3.3.2

The [lodash](https://lodash.com/) method `_.isFinite` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.isfinite
```

In Node.js:
```js
var isFinite = require('lodash.isfinite');
```

See the [documentation](https://lodash.com/docs#isFinite) or [package source](https://github.com/lodash/lodash/blob/3.3.2-npm-packages/lodash.isfinite) for more details.
