{"name": "teen_process", "version": "2.3.2", "description": "A grown up version of <PERSON><PERSON>'s spawn/exec", "keywords": ["child_process", "process management", "subprocess", "spawn", "exec", "process", "child"], "bugs": {"url": "https://github.com/appium/node-teen_process/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/node-teen_process.git"}, "license": "Apache-2.0", "author": "Appium Contributors", "main": "index.js", "bin": {}, "directories": {"lib": "lib"}, "files": ["index.js", "lib", "build/lib"], "scripts": {"build": "tsc -b", "clean": "npm run build -- --clean", "dev": "npm run build -- --watch", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "lint:types": "tsc", "prepare": "npm run rebuild", "rebuild": "npm run clean && npm run build", "test": "mocha"}, "prettier": {"bracketSpacing": false, "printWidth": 100, "singleQuote": true}, "dependencies": {"bluebird": "^3.7.2", "lodash": "^4.17.21", "shell-quote": "^1.8.1", "source-map-support": "^0.x"}, "devDependencies": {"@appium/eslint-config-appium-ts": "^1.0.0", "@appium/tsconfig": "^0.x", "@appium/types": "^0.x", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@types/bluebird": "^3.5.42", "@types/lodash": "^4.14.202", "@types/node": "^22.0.0", "@types/shell-quote": "^1.7.5", "@types/source-map-support": "^0.x", "@types/ws": "^8.5.10", "chai": "^5.1.1", "chai-as-promised": "^8.0.0", "conventional-changelog-conventionalcommits": "^9.0.0", "mocha": "^11.0.1", "prettier": "^3.1.0", "semantic-release": "^24.0.0", "typescript": "^5.4.1", "ts-node": "^10.9.1"}, "engines": {"node": "^16.13.0 || >=18.0.0", "npm": ">=8"}}