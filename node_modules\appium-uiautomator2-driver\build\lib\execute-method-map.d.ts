export declare const executeMethodMap: {
    readonly 'mobile: dragGesture': {
        readonly command: "mobileDragGesture";
        readonly params: {
            readonly optional: readonly ["elementId", "startX", "startY", "endX", "endY", "speed"];
        };
    };
    readonly 'mobile: flingGesture': {
        readonly command: "mobileFlingGesture";
        readonly params: {
            readonly required: readonly ["direction"];
            readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
        };
    };
    readonly 'mobile: doubleClickGesture': {
        readonly command: "mobileDoubleClickGesture";
        readonly params: {
            readonly optional: readonly ["elementId", "x", "y"];
        };
    };
    readonly 'mobile: clickGesture': {
        readonly command: "mobileClickGesture";
        readonly params: {
            readonly optional: readonly ["elementId", "x", "y"];
        };
    };
    readonly 'mobile: longClickGesture': {
        readonly command: "mobileLongClickGesture";
        readonly params: {
            readonly optional: readonly ["elementId", "x", "y", "duration"];
        };
    };
    readonly 'mobile: pinchCloseGesture': {
        readonly command: "mobilePinchCloseGesture";
        readonly params: {
            readonly required: readonly ["percent"];
            readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
        };
    };
    readonly 'mobile: pinchOpenGesture': {
        readonly command: "mobilePinchOpenGesture";
        readonly params: {
            readonly required: readonly ["percent"];
            readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
        };
    };
    readonly 'mobile: swipeGesture': {
        readonly command: "mobileSwipeGesture";
        readonly params: {
            readonly required: readonly ["direction", "percent"];
            readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
        };
    };
    readonly 'mobile: scrollGesture': {
        readonly command: "mobileScrollGesture";
        readonly params: {
            readonly required: readonly ["direction", "percent"];
            readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
        };
    };
    readonly 'mobile: scrollBackTo': {
        readonly command: "mobileScrollBackTo";
        readonly params: {
            readonly required: readonly ["elementId", "elementToId"];
        };
    };
    readonly 'mobile: scroll': {
        readonly command: "mobileScroll";
        readonly params: {
            readonly required: readonly ["strategy", "selector"];
            readonly optional: readonly ["elementId", "maxSwipes"];
        };
    };
    readonly 'mobile: viewportScreenshot': {
        readonly command: "mobileViewportScreenshot";
    };
    readonly 'mobile: viewportRect': {
        readonly command: "mobileViewPortRect";
    };
    readonly 'mobile: deepLink': {
        readonly command: "mobileDeepLink";
        readonly params: {
            readonly required: readonly ["url"];
            readonly optional: readonly ["package", "waitForLaunch"];
        };
    };
    readonly 'mobile: acceptAlert': {
        readonly command: "mobileAcceptAlert";
        readonly params: {
            readonly optional: readonly ["buttonLabel"];
        };
    };
    readonly 'mobile: dismissAlert': {
        readonly command: "mobileDismissAlert";
        readonly params: {
            readonly optional: readonly ["buttonLabel"];
        };
    };
    readonly 'mobile: batteryInfo': {
        readonly command: "mobileGetBatteryInfo";
    };
    readonly 'mobile: deviceInfo': {
        readonly command: "mobileGetDeviceInfo";
    };
    readonly 'mobile: openNotifications': {
        readonly command: "openNotifications";
    };
    readonly 'mobile: type': {
        readonly command: "mobileType";
        readonly params: {
            readonly required: readonly ["text"];
        };
    };
    readonly 'mobile: replaceElementValue': {
        readonly command: "mobileReplaceElementValue";
        readonly params: {
            readonly required: readonly ["elementId", "text"];
        };
    };
    readonly 'mobile: installMultipleApks': {
        readonly command: "mobileInstallMultipleApks";
        readonly params: {
            readonly required: readonly ["apks"];
            readonly optional: readonly ["options"];
        };
    };
    readonly 'mobile: pressKey': {
        readonly command: "mobilePressKey";
        readonly params: {
            readonly required: readonly ["keycode"];
            readonly optional: readonly ["metastate", "flags", "isLongPress"];
        };
    };
    readonly 'mobile: screenshots': {
        readonly command: "mobileScreenshots";
        readonly params: {
            readonly optional: readonly ["displayId"];
        };
    };
    readonly 'mobile: scheduleAction': {
        readonly command: "mobileScheduleAction";
        readonly params: {
            readonly required: readonly ["name", "steps"];
            readonly optional: readonly ["maxPass", "maxFail", "times", "intervalMs", "maxHistoryItems"];
        };
    };
    readonly 'mobile: getActionHistory': {
        readonly command: "mobileGetActionHistory";
        readonly params: {
            readonly required: readonly ["name"];
        };
    };
    readonly 'mobile: unscheduleAction': {
        readonly command: "mobileUnscheduleAction";
        readonly params: {
            readonly required: readonly ["name"];
        };
    };
    readonly 'mobile: setClipboard': {
        readonly command: "setClipboard";
        readonly params: {
            readonly required: readonly ["content"];
            readonly optional: readonly ["contentType", "label"];
        };
    };
    readonly 'mobile: getClipboard': {
        readonly command: "getClipboard";
    };
    readonly 'mobile: shell': {
        readonly command: "mobileShell";
        readonly params: {
            readonly required: readonly ["command"];
            readonly optional: readonly ["args", "timeout", "includeStderr"];
        };
    };
    readonly 'mobile: execEmuConsoleCommand': {
        readonly command: "mobileExecEmuConsoleCommand";
        readonly params: {
            readonly required: readonly ["command"];
            readonly optional: readonly ["execTimeout", "connTimeout", "initTimeout"];
        };
    };
    readonly 'mobile: startLogsBroadcast': {
        readonly command: "mobileStartLogsBroadcast";
    };
    readonly 'mobile: stopLogsBroadcast': {
        readonly command: "mobileStopLogsBroadcast";
    };
    readonly 'mobile: changePermissions': {
        readonly command: "mobileChangePermissions";
        readonly params: {
            readonly required: readonly ["permissions"];
            readonly optional: readonly ["appPackage", "action", "target"];
        };
    };
    readonly 'mobile: getPermissions': {
        readonly command: "mobileGetPermissions";
        readonly params: {
            readonly optional: readonly ["type", "appPackage"];
        };
    };
    readonly 'mobile: performEditorAction': {
        readonly command: "mobilePerformEditorAction";
        readonly params: {
            readonly required: readonly ["action"];
        };
    };
    readonly 'mobile: getDeviceTime': {
        readonly command: "mobileGetDeviceTime";
        readonly params: {
            readonly optional: readonly ["format"];
        };
    };
    readonly 'mobile: startScreenStreaming': {
        readonly command: "mobileStartScreenStreaming";
        readonly params: {
            readonly optional: readonly ["width", "height", "bitRate", "host", "port", "pathname", "tcpPort", "quality", "considerRotation", "logPipelineDetails"];
        };
    };
    readonly 'mobile: stopScreenStreaming': {
        readonly command: "mobileStopScreenStreaming";
    };
    readonly 'mobile: getNotifications': {
        readonly command: "mobileGetNotifications";
    };
    readonly 'mobile: listSms': {
        readonly command: "mobileListSms";
    };
    readonly 'mobile: pushFile': {
        readonly command: "pushFile";
        readonly params: {
            readonly required: readonly ["remotePath", "payload"];
        };
    };
    readonly 'mobile: pullFolder': {
        readonly command: "pullFolder";
        readonly params: {
            readonly required: readonly ["remotePath"];
        };
    };
    readonly 'mobile: pullFile': {
        readonly command: "pullFile";
        readonly params: {
            readonly required: readonly ["remotePath"];
        };
    };
    readonly 'mobile: deleteFile': {
        readonly command: "mobileDeleteFile";
        readonly params: {
            readonly required: readonly ["remotePath"];
        };
    };
    readonly 'mobile: isAppInstalled': {
        readonly command: "mobileIsAppInstalled";
        readonly params: {
            readonly required: readonly ["appId"];
            readonly optional: readonly ["user"];
        };
    };
    readonly 'mobile: queryAppState': {
        readonly command: "queryAppState";
        readonly params: {
            readonly required: readonly ["appId"];
        };
    };
    readonly 'mobile: activateApp': {
        readonly command: "activateApp";
        readonly params: {
            readonly required: readonly ["appId"];
        };
    };
    readonly 'mobile: removeApp': {
        readonly command: "mobileRemoveApp";
        readonly params: {
            readonly required: readonly ["appId"];
            readonly optional: readonly ["timeout", "keepData", "skipInstallCheck"];
        };
    };
    readonly 'mobile: terminateApp': {
        readonly command: "mobileTerminateApp";
        readonly params: {
            readonly required: readonly ["appId"];
            readonly optional: readonly ["timeout"];
        };
    };
    readonly 'mobile: installApp': {
        readonly command: "mobileInstallApp";
        readonly params: {
            readonly required: readonly ["appPath"];
            readonly optional: readonly ["checkVersion", "timeout", "allowTestPackages", "useSdcard", "grantPermissions", "replace", "noIncremental"];
        };
    };
    readonly 'mobile: clearApp': {
        readonly command: "mobileClearApp";
        readonly params: {
            readonly required: readonly ["appId"];
        };
    };
    readonly 'mobile: backgroundApp': {
        readonly command: "mobileBackgroundApp";
        readonly params: {
            readonly optional: readonly ["seconds"];
        };
    };
    readonly 'mobile: startService': {
        readonly command: "mobileStartService";
        readonly params: {
            readonly optional: readonly ["foreground", "user", "intent", "action", "package", "uri", "mimeType", "identifier", "component", "categories", "extras", "flags"];
        };
    };
    readonly 'mobile: stopService': {
        readonly command: "mobileStopService";
        readonly params: {
            readonly optional: readonly ["user", "intent", "action", "package", "uri", "mimeType", "identifier", "component", "categories", "extras", "flags"];
        };
    };
    readonly 'mobile: startActivity': {
        readonly command: "mobileStartActivity";
        readonly params: {
            readonly optional: readonly ["wait", "stop", "windowingMode", "activityType", "display", "user", "intent", "action", "package", "uri", "mimeType", "identifier", "component", "categories", "extras", "flags"];
        };
    };
    readonly 'mobile: broadcast': {
        readonly command: "mobileBroadcast";
        readonly params: {
            readonly optional: readonly ["receiverPermission", "allowBackgroundActivityStarts", "user", "intent", "action", "package", "uri", "mimeType", "identifier", "component", "categories", "extras", "flags"];
        };
    };
    readonly 'mobile: getContexts': {
        readonly command: "mobileGetContexts";
        readonly params: {
            readonly optional: readonly ["waitForWebviewMs"];
        };
    };
    readonly 'mobile: lock': {
        readonly command: "lock";
        readonly params: {
            readonly optional: readonly ["seconds"];
        };
    };
    readonly 'mobile: unlock': {
        readonly command: "mobileUnlock";
        readonly params: {
            readonly optional: readonly ["key", "type", "strategy", "timeoutMs"];
        };
    };
    readonly 'mobile: isLocked': {
        readonly command: "isLocked";
    };
    readonly 'mobile: refreshGpsCache': {
        readonly command: "mobileRefreshGpsCache";
        readonly params: {
            readonly optional: readonly ["timeoutMs"];
        };
    };
    readonly 'mobile: startMediaProjectionRecording': {
        readonly command: "mobileStartMediaProjectionRecording";
        readonly params: {
            readonly optional: readonly ["resolution", "priority", "maxDurationSec", "filename"];
        };
    };
    readonly 'mobile: isMediaProjectionRecordingRunning': {
        readonly command: "mobileIsMediaProjectionRecordingRunning";
    };
    readonly 'mobile: stopMediaProjectionRecording': {
        readonly command: "mobileStopMediaProjectionRecording";
        readonly params: {
            readonly optional: readonly ["remotePath", "user", "pass", "method", "headers", "fileFieldName", "formFields", "uploadTimeout"];
        };
    };
    readonly 'mobile: getConnectivity': {
        readonly command: "mobileGetConnectivity";
        readonly params: {
            readonly optional: readonly ["services"];
        };
    };
    readonly 'mobile: setConnectivity': {
        readonly command: "mobileSetConnectivity";
        readonly params: {
            readonly optional: readonly ["wifi", "data", "airplaneMode"];
        };
    };
    readonly 'mobile: hideKeyboard': {
        readonly command: "hideKeyboard";
    };
    readonly 'mobile: isKeyboardShown': {
        readonly command: "isKeyboardShown";
    };
    readonly 'mobile: deviceidle': {
        readonly command: "mobileDeviceidle";
        readonly params: {
            readonly required: readonly ["action"];
            readonly optional: readonly ["packages"];
        };
    };
    readonly 'mobile: bluetooth': {
        readonly command: "mobileBluetooth";
        readonly params: {
            readonly required: readonly ["action"];
        };
    };
    readonly 'mobile: nfc': {
        readonly command: "mobileNfc";
        readonly params: {
            readonly required: readonly ["action"];
        };
    };
    readonly 'mobile: setUiMode': {
        readonly command: "mobileSetUiMode";
        readonly params: {
            readonly required: readonly ["mode", "value"];
        };
    };
    readonly 'mobile: getUiMode': {
        readonly command: "mobileGetUiMode";
        readonly params: {
            readonly required: readonly ["mode"];
        };
    };
    readonly 'mobile: injectEmulatorCameraImage': {
        readonly command: "mobileInjectEmulatorCameraImage";
        readonly params: {
            readonly required: readonly ["payload"];
        };
    };
    readonly 'mobile: sendTrimMemory': {
        readonly command: "mobileSendTrimMemory";
        readonly params: {
            readonly required: readonly ["pkg", "level"];
        };
    };
    readonly 'mobile: getPerformanceData': {
        readonly command: "mobileGetPerformanceData";
        readonly params: {
            readonly required: readonly ["packageName", "dataType"];
        };
    };
    readonly 'mobile: getPerformanceDataTypes': {
        readonly command: "getPerformanceDataTypes";
    };
    readonly 'mobile: toggleGps': {
        readonly command: "toggleLocationServices";
    };
    readonly 'mobile: isGpsEnabled': {
        readonly command: "isLocationServicesEnabled";
    };
    readonly 'mobile: getDisplayDensity': {
        readonly command: "getDisplayDensity";
    };
    readonly 'mobile: getSystemBars': {
        readonly command: "getSystemBars";
    };
    readonly 'mobile: statusBar': {
        readonly command: "mobilePerformStatusBarCommand";
        readonly params: {
            readonly required: readonly ["command"];
            readonly optional: readonly ["component"];
        };
    };
    readonly 'mobile: fingerprint': {
        readonly command: "mobileFingerprint";
        readonly params: {
            readonly required: readonly ["fingerprintId"];
        };
    };
    readonly 'mobile: sendSms': {
        readonly command: "mobileSendSms";
        readonly params: {
            readonly required: readonly ["phoneNumber", "message"];
        };
    };
    readonly 'mobile: gsmCall': {
        readonly command: "mobileGsmCall";
        readonly params: {
            readonly required: readonly ["phoneNumber", "action"];
        };
    };
    readonly 'mobile: gsmSignal': {
        readonly command: "mobileGsmSignal";
        readonly params: {
            readonly required: readonly ["strength"];
        };
    };
    readonly 'mobile: gsmVoice': {
        readonly command: "mobileGsmVoice";
        readonly params: {
            readonly required: readonly ["state"];
        };
    };
    readonly 'mobile: powerAc': {
        readonly command: "mobilePowerAc";
        readonly params: {
            readonly required: readonly ["state"];
        };
    };
    readonly 'mobile: powerCapacity': {
        readonly command: "mobilePowerCapacity";
        readonly params: {
            readonly required: readonly ["percent"];
        };
    };
    readonly 'mobile: networkSpeed': {
        readonly command: "mobileNetworkSpeed";
        readonly params: {
            readonly required: readonly ["speed"];
        };
    };
    readonly 'mobile: sensorSet': {
        readonly command: "sensorSet";
        readonly params: {
            readonly required: readonly ["sensorType", "value"];
        };
    };
    readonly 'mobile: getCurrentActivity': {
        readonly command: "getCurrentActivity";
    };
    readonly 'mobile: getCurrentPackage': {
        readonly command: "getCurrentPackage";
    };
    readonly 'mobile: setGeolocation': {
        readonly command: "mobileSetGeolocation";
        readonly params: {
            readonly required: readonly ["latitude", "longitude"];
            readonly optional: readonly ["altitude", "satellites", "speed", "bearing", "accuracy"];
        };
    };
    readonly 'mobile: getGeolocation': {
        readonly command: "mobileGetGeolocation";
    };
    readonly 'mobile: resetGeolocation': {
        readonly command: "mobileResetGeolocation";
    };
    readonly 'mobile: getAppStrings': {
        readonly command: "getStrings";
        readonly params: {
            readonly optional: readonly ["language"];
        };
    };
};
//# sourceMappingURL=execute-method-map.d.ts.map