declare namespace _default {
    export { extractAllTo };
    export { readEntries };
    export { toInMemoryZip };
    export { assertValidZip };
    export { toArchive };
}
export default _default;
export type ExtractAllOptions = {
    /**
     * The encoding to use for extracted file names.
     * For ZIP archives created on MacOS it is usually expected to be `utf8`.
     * By default it is autodetected based on the entry metadata and is only needed to be set explicitly
     * if the particular archive does not comply to the standards, which leads to corrupted file names
     * after extraction. Only applicable if system unzip binary is NOT being used.
     */
    fileNamesEncoding?: string | undefined;
    /**
     * If true, attempt to use system unzip; if this fails,
     * fallback to the JS unzip implementation.
     */
    useSystemUnzip?: boolean | undefined;
};
export type ZipEntry = {
    /**
     * The actual entry instance
     */
    entry: yauzl.Entry;
    /**
     * An async function, which accepts one parameter.
     * This parameter contains the destination folder path to which this function is going to extract the entry.
     */
    extractEntryTo: Function;
};
export type ZipOptions = {
    /**
     * Whether to encode
     * the resulting archive to a base64-encoded string
     */
    encodeToBase64?: boolean | undefined;
    /**
     * Whether to log the actual
     * archiver performance
     */
    isMetered?: boolean | undefined;
    /**
     * The maximum size of
     * the resulting archive in bytes. This is set to 1GB by default, because
     * Appium limits the maximum HTTP body size to 1GB. Also, the NodeJS heap
     * size must be enough to keep the resulting object (usually this size is
     * limited to 1.4 GB)
     */
    maxSize?: number | undefined;
    /**
     * The compression level. The maximum
     * level is 9 (the best compression, worst performance). The minimum
     * compression level is 0 (no compression).
     */
    level?: number | undefined;
};
export type ZipCompressionOptions = {
    /**
     * [9] - Compression level in range 0..9
     * (greater numbers mean better compression, but longer processing time)
     */
    level: number;
};
export type ZipSourceOptions = {
    /**
     * - GLOB pattern for compression
     */
    pattern?: string | undefined;
    /**
     * - The source root folder (the parent folder of
     * the destination file by default)
     */
    cwd?: string | undefined;
    /**
     * - The list of ignored patterns
     */
    ignore?: string[] | undefined;
};
/**
 * @typedef ExtractAllOptions
 * @property {string} [fileNamesEncoding] The encoding to use for extracted file names.
 * For ZIP archives created on MacOS it is usually expected to be `utf8`.
 * By default it is autodetected based on the entry metadata and is only needed to be set explicitly
 * if the particular archive does not comply to the standards, which leads to corrupted file names
 * after extraction. Only applicable if system unzip binary is NOT being used.
 * @property {boolean} [useSystemUnzip] If true, attempt to use system unzip; if this fails,
 * fallback to the JS unzip implementation.
 */
/**
 * Extract zipfile to a directory
 *
 * @param {string} zipFilePath The full path to the source ZIP file
 * @param {string} destDir The full path to the destination folder
 * @param {ExtractAllOptions} [opts]
 */
export function extractAllTo(zipFilePath: string, destDir: string, opts?: ExtractAllOptions): Promise<void>;
/**
 * @typedef ZipEntry
 * @property {yauzl.Entry} entry The actual entry instance
 * @property {function} extractEntryTo An async function, which accepts one parameter.
 * This parameter contains the destination folder path to which this function is going to extract the entry.
 */
/**
 * Get entries for a zip folder
 *
 * @param {string} zipFilePath The full path to the source ZIP file
 * @param {function} onEntry Callback when entry is read.
 * The callback is expected to accept one argument of ZipEntry type.
 * The iteration through the source zip file will bi terminated as soon as
 * the result of this function equals to `false`.
 */
export function readEntries(zipFilePath: string, onEntry: Function): Promise<any>;
/**
 * @typedef ZipOptions
 * @property {boolean} [encodeToBase64=false] Whether to encode
 * the resulting archive to a base64-encoded string
 * @property {boolean} [isMetered=true] Whether to log the actual
 * archiver performance
 * @property {number} [maxSize=1073741824] The maximum size of
 * the resulting archive in bytes. This is set to 1GB by default, because
 * Appium limits the maximum HTTP body size to 1GB. Also, the NodeJS heap
 * size must be enough to keep the resulting object (usually this size is
 * limited to 1.4 GB)
 * @property {number} [level=9] The compression level. The maximum
 * level is 9 (the best compression, worst performance). The minimum
 * compression level is 0 (no compression).
 */
/**
 * Converts contents of local directory to an in-memory .zip buffer
 *
 * @param {string} srcPath The full path to the folder or file being zipped
 * @param {ZipOptions} opts Zipping options
 * @returns {Promise<Buffer>} Zipped (and encoded if `encodeToBase64` is truthy)
 * content of the source path as memory buffer
 * @throws {Error} if there was an error while reading the source
 * or the source is too big
 */
export function toInMemoryZip(srcPath: string, opts?: ZipOptions): Promise<Buffer>;
/**
 * Extract a single zip entry to a directory
 *
 * @param {yauzl.ZipFile} zipFile The source ZIP stream
 * @param {yauzl.Entry} entry The entry instance
 * @param {string} destDir The full path to the destination folder
 */
export function _extractEntryTo(zipFile: yauzl.ZipFile, entry: yauzl.Entry, destDir: string): Promise<[any, any] | undefined>;
/**
 * Verifies whether the given file is a valid ZIP archive
 *
 * @param {string} filePath - Full path to the file
 * @throws {Error} If the file does not exist or is not a valid ZIP archive
 */
export function assertValidZip(filePath: string): Promise<boolean>;
/**
 * @typedef ZipCompressionOptions
 * @property {number} level [9] - Compression level in range 0..9
 * (greater numbers mean better compression, but longer processing time)
 */
/**
 * @typedef ZipSourceOptions
 * @property {string} [pattern='**\/*'] - GLOB pattern for compression
 * @property {string} [cwd] - The source root folder (the parent folder of
 * the destination file by default)
 * @property {string[]} [ignore] - The list of ignored patterns
 */
/**
 * Creates an archive based on the given glob pattern
 *
 * @param {string} dstPath - The resulting archive path
 * @param {ZipSourceOptions} src - Source options
 * @param {ZipCompressionOptions} opts - Compression options
 * @throws {Error} If there was an error while creating the archive
 */
export function toArchive(dstPath: string, src?: ZipSourceOptions, opts?: ZipCompressionOptions): Promise<any>;
import yauzl from 'yauzl';
//# sourceMappingURL=zip.d.ts.map