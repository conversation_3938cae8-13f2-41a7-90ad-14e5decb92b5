{"name": "@wdio/spec-reporter", "version": "9.17.0", "description": "A WebdriverIO plugin to report in spec style", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/webdriverio/webdriverio/tree/main/packages/wdio-spec-reporter", "license": "MIT", "engines": {"node": ">=18.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/webdriverio/webdriverio.git", "directory": "packages/wdio-spec-reporter"}, "keywords": ["webdriver", "wdio", "wdio-reporter"], "bugs": {"url": "https://github.com/webdriverio/webdriverio/issues"}, "type": "module", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.js"}}, "typeScriptVersion": "3.8.3", "dependencies": {"@wdio/reporter": "9.17.0", "@wdio/types": "9.16.2", "chalk": "^5.1.2", "easy-table": "^1.2.0", "pretty-ms": "^9.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "796df04f0f5c744d5ef4bafd3759995fa4829d6f"}