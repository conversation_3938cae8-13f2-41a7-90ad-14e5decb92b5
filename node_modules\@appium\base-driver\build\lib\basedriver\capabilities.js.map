{"version": 3, "file": "capabilities.js", "sourceRoot": "", "sources": ["../../../lib/basedriver/capabilities.ts"], "names": [], "mappings": ";;;;;;AAqCA,8BA0BC;AAKD,oCA4CC;AAyBD,sCAEC;AAOD,kDAwCC;AAKD,kDAgBC;AAMD,8BAkGC;AAKD,kDA0BC;AAMD,sEAoDC;AAMD,oDAcC;AAzZD,oDAAuB;AACvB,iDAAyC;AACzC,6CAAqC;AACrC,sDAA2B;AAC3B,+CAA0C;AAE7B,QAAA,oBAAoB,GAAG,SAAS,CAAC;AACjC,QAAA,wBAAwB,GAAG,GAAG,4BAAoB,SAAS,CAAC;AAczE;;;;GAIG;AACH,SAAgB,SAAS,CAMvB,UAA+B,EAAa,EAC5C,YAAmC,EAAe;IAElD,MAAM,MAAM,GAAG,CAAC;QACd,GAAG,OAAO;KACX,CAAuC,CAAC;IAEzC,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QACtD,kGAAkG;QAClG,IAAI,CAAC,gBAAC,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,aAAa,IAAI,uCAAuC,IAAI,CAAC,SAAS,CACpE,OAAO,CACR,oBAAoB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CACzD,CAAC;QACJ,CAAC;QACD,MAAM,CAAC,IAA2B,CAAC,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAC1B,IAAqB,EACrB,cAA6B,EAAO,EACpC,OAAqC,EAAE;IAEvC,MAAM,EAAC,sBAAsB,EAAC,GAAG,IAAI,CAAC;IAEtC,IAAI,CAAC,gBAAC,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,eAAM,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;IACjE,CAAC;IAED,gEAAgE;IAChE,WAAW,GAAG,CACZ,gBAAC,CAAC,SAAS,CACT,WAAW,EACX,sBAAsB;QACpB,CAAC,CAAC,qCAAqC;YACrC,CAAC,UAAU,EAAE,EAAE,CAAC,gBAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC;QAChD,CAAC,CAAC,qCAAqC;YACrC,CAAC,UAAU,EAAE,EAAE;gBACb,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACjC,OAAO,EAAC,GAAG,gBAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,QAAQ,EAAE,EAAC,UAAU,EAAE,KAAK,EAAC,EAAC,CAAC;gBAC5E,CAAC;gBACD,OAAO,UAAU,CAAC;YACpB,CAAC,CACN,CACG,CAAC;IAEP,MAAM,gBAAgB,GAAG,wBAAS,CAAC,QAAQ,CAAC,gBAAC,CAAC,MAAM,CAAC,IAAI,EAAE,cAAI,CAAC,QAAQ,CAAC,EAAE,WAAW,EAAE;QACtF,YAAY,EAAE,KAAK;KACpB,CAAC,CAAC;IAEH,IAAI,gBAAgB,EAAE,CAAC;QACrB,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC/D,KAAK,MAAM,MAAM,IAAK,OAAoB,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QACD,MAAM,IAAI,eAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,cAAc;IACd,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACU,QAAA,aAAa,GAAG,MAAM,CAAC,MAAM,CACxC,IAAI,GAAG,CACL,CAAC;IACC,aAAa;IACb,gBAAgB;IAChB,cAAc;IACd,qBAAqB;IACrB,kBAAkB;IAClB,OAAO;IACP,eAAe;IACf,UAAU;IACV,yBAAyB;IACzB,cAAc;CACf,CAAwC,CAC1C,CACF,CAAC;AAEF,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,qBAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AAExF,SAAgB,aAAa,CAAC,GAAW;IACvC,OAAO,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;AACpD,CAAC;AAED;;;;GAIG;AACH,SAAgB,mBAAmB,CAAwB,IAAuB;IAChF,wCAAwC;IACxC,qDAAqD;IACrD,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,gBAAC,CAAC,SAAS,CAAC,gBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CACxE,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,4BAAoB,CAAC,CAC7C,CAAC;IAEF,8DAA8D;IAC9D,MAAM,YAAY,GAAG,CAAC,gBAAC,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,CAAoB,CAAC;IACxE,MAAM,eAAe,GAAa,EAAE,CAAC;IAErC,iCAAiC;IACjC,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;QACvC,MAAM,eAAe,GAAG,WAAW,CAAC,SAAS,CAAC,4BAAoB,CAAC,MAAM,CAAiC,CAAC;QAE3G,yGAAyG;QACzG,IAAI,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC;YACnC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,IAAI,gBAAC,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC;gBAC3C,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,gBAAG,CAAC,IAAI,CACN,wBAAwB,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;oBAC9D,qBAAqB,eAAe,IAAI,YAAY,CAAC,eAAe,CAAC,GAAG,CAC3E,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,2JAA2J;IAC3J,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/B,gBAAG,CAAC,IAAI,CACN,oBAAoB,IAAI,CAAC,SAAS,CAChC,eAAe,CAChB,gEAAgE,CAClE,CAAC;IACJ,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CACjC,EACE,WAAW,GAAG,EAAE,EAChB,UAAU,GAAG,EAAE,EACI;IAErB,OAAO,gBAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,GAAG,UAAU,CAAC,CAAC;SACzC,MAAM,CACL,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE,CAAC;QACxB,GAAG,cAAc;QACjB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;KAChF,EACD,EAAE,CACH;SACA,IAAI,EAAE;SACN,KAAK,EAAE,CAAC;AACb,CAAC;AAED;;;GAGG;AACH,SAAgB,SAAS,CACvB,IAAwB,EACxB,cAA6B,EAAO,EACpC,qBAA0C,IAAI;IAE9C,gEAAgE;IAChE,IAAI,CAAC,gBAAC,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,4GAA4G,CAC7G,CAAC;IACJ,CAAC;IAED,oFAAoF;IACpF,wFAAwF;IACxF,MAAM,EACJ,WAAW,EAAE,YAAY,GAAG,EAAuB,EAAE,wEAAwE;IAC7H,UAAU,EAAE,iBAAiB,GAAG,CAAC,EAAE,CAAwB,EAAE,uFAAuF;MACrJ,GAAG,IAAI,CAAC;IAET,2DAA2D;IAC3D,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;QAClC,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,6IAA6I,CAC9I,CAAC;IACJ,CAAC;IAED,6FAA6F;IAC7F,mGAAmG;IACnG,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,gBAAG,CAAC,IAAI,CACN,gGAAgG;YAC9F,sDAAsD,CACzD,CAAC;QACF,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED,uFAAuF;IACvF,MAAM,eAAe,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAClD,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;QAChC,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,2GAA2G,eAAe,EAAE,CAC7H,CAAC;IACJ,CAAC;IAED,0CAA0C;IAC1C,IAAI,oBAAoB,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC;IAC7D,MAAM,yBAAyB,GAAsB,iBAAiB,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IAEhG,iKAAiK;IACjK,IAAI,kBAAkB,EAAE,CAAC;QACvB,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,EAAE,WAAW,EAAE;YACrE,sBAAsB,EAAE,IAAI;SAC7B,CAAC,CAAC;IACL,CAAC;IACD,2FAA2F;IAC3F,wDAAwD;IACxD,MAAM,mBAAmB,GAAG,gBAAC,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,IAAI,oBAAoB,CAAM,CAAC;IAEhG,0GAA0G;IAC1G,MAAM,gBAAgB,GAAa,EAAE,CAAC;IACtC,MAAM,uBAAuB,GAAG,gBAAC,CAAC,OAAO,CACvC,yBAAyB,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;QAC/C,IAAI,CAAC;YACH,2BAA2B;YAC3B,OAAO,kBAAkB;gBACvB,CAAC,CAAC,YAAY,CAAC,cAAc,EAAE,mBAAmB,CAAC;gBACnD,CAAC,CAAC,cAAc,CAAC;QACrB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,CAAC,CACkB,CAAC;IAEvB;;;OAGG;IACH,IAAI,WAAW,GAAiC,IAAI,CAAC;IACrD,KAAK,MAAM,cAAc,IAAI,uBAAuB,EAAE,CAAC;QACrD,IAAI,CAAC;YACH,WAAW,GAAG,SAAS,CAAC,oBAAoB,EAAE,cAAc,CAAiC,CAAC;YAC9F,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM;YACR,CAAC;QACH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,gBAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACtB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED,yCAAyC;IACzC,OAAO;QACL,YAAY;QACZ,iBAAiB;QACjB,uBAAuB;QACvB,WAAW;QACX,gBAAgB;KACjB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAIjC,OAAgB,EAChB,cAA6B,EAAO,EACpC,qBAA0C,IAAI;IAE9C,MAAM,EAAC,WAAW,EAAE,gBAAgB,EAAC,GAAG,SAAS,CAAC,OAAO,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC;IAE5F,0CAA0C;IAC1C,IAAI,CAAC,cAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QAChC,IAAI,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnE,kIAAkI;YAClI,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,6CAA6C,IAAI,CAAC,SAAS,CACzD,OAAO,CACR,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACtC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,kDAAkD;YAClD,MAAM,IAAI,eAAM,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,OAAO,CAAC,WAAW,IAAI,EAAE,CAAoB,CAAC;AAChD,CAAC;AAED;;;GAGG;AACH,SAAgB,6BAA6B,CAAwB,GAAsB;IACzF,MAAM,aAAa,GAAG,GAAG,CAAC,gCAAwB,CAAC,CAAC;IACpD,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,IAAI,CAAC,gBAAC,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC;QACpC,MAAM,IAAI,eAAM,CAAC,sBAAsB,CACrC,OAAO,gCAAwB,+BAA+B,CAC/D,CAAC;IACJ,CAAC;IACD,IAAI,gBAAC,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;QAC7B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,gBAAG,CAAC,KAAK,CACP,SAAS,gCAAwB,wDAAwD,CAC1F,CAAC;IAEF;;OAEG;IACH,MAAM,qBAAqB,GAAG,CAAC,OAAe,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,4BAAoB,CAAC,CAAC;IAC7F,MAAM,kBAAkB,GAAG,CAAC,OAAe,EAAE,EAAE;QAC7C,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,eAAM,CAAC,sBAAsB,CACrC,uBAAuB,gCAAwB,sBAAsB,OAAO,iBAAiB,CAC9F,CAAC;QACJ,CAAC;QACD,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,eAAM,CAAC,sBAAsB,CACrC,GAAG,gCAAwB,qDAAqD,OAAO,iBAAiB,CACzG,CAAC;QACJ,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IACF,MAAM,mBAAmB,GAAG,IAAA,gBAAC,EAAC,aAAa,CAAC;SACzC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAW,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;SACxD,OAAO,CAAC,CAAC,KAAK,EAAE,GAAW,EAAE,EAAE,CAAC,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,4BAAoB,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACrG,KAAK,EAAE,CAAC;IACX,qEAAqE;IACrE,MAAM,eAAe,GAAG,gBAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAC3F,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/B,gBAAG,CAAC,IAAI,CACN,6BAA6B,gCAAwB,uBAAuB;YAC1E,kCAAkC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,CACtE,CAAC;IACJ,CAAC;IACD,OAAO,gBAAC,CAAC,SAAS,CAAC;QACjB,GAAG,gBAAC,CAAC,IAAI,CAAC,GAAG,EAAE,gCAAwB,CAAsB;QAC7D,GAAG,mBAAmB;KACvB,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAgB,oBAAoB,CAAwB,YAAgC;IAC1F,MAAM,MAAM,GAAG,EAAwB,CAAC;IACxC,MAAM,EAAC,WAAW,EAAE,UAAU,EAAC,GAAG,YAAY,CAAC;IAC/C,IAAI,gBAAC,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC;QACjC,MAAM,CAAC,WAAW,GAAG,6BAA6B,CAAC,WAAW,CAAC,CAAC;IAClE,CAAC;SAAM,IAAI,aAAa,IAAI,YAAY,EAAE,CAAC;QACzC,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;IACnC,CAAC;IACD,IAAI,gBAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IACpE,CAAC;SAAM,IAAI,YAAY,IAAI,YAAY,EAAE,CAAC;QACxC,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;IACjC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}