{"name": "parse-listing", "id": "parse-listing", "version": "1.1.2", "description": "Small library to parse file listings into JavaScript objects", "keywords": ["parse", "ls", "list", "listing", "ftp", "unix", "dos"], "author": "<PERSON><PERSON> <<EMAIL>> (http://sergimansilla.com)", "homepage": "https://github.com/sergi/parse-listing", "repository": {"type": "git", "url": "https://github.com/sergi/parse-listing.git"}, "bugs": {"url": "https://github.com/sergi/parse-listing/issues"}, "dependencies": {}, "devDependencies": {"mocha": "^1.10.0"}, "main": "src/parser.js", "engines": {"node": ">=0.6.21"}, "scripts": {"test": "mocha -R spec -t 5000"}, "licenses": [{"type": "MIT", "url": "https://github.com/sergi/parse-listing/blob/master/LICENSE"}]}