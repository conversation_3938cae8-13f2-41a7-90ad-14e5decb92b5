export const SETTINGS_HELPER_ID: "io.appium.settings";
export const SETTINGS_HELPER_MAIN_ACTIVITY: ".Settings";
export const CLIPBOARD_RECEIVER: "io.appium.settings/.receivers.ClipboardReceiver";
export const CLIPBOARD_RETRIEVAL_ACTION: "io.appium.settings.clipboard.get";
export const APPIUM_IME: "io.appium.settings/.AppiumIME";
export const UNICODE_IME: "io.appium.settings/.UnicodeIME";
export const EMPTY_IME: "io.appium.settings/.EmptyIME";
export const WIFI_CONNECTION_SETTING_RECEIVER: "io.appium.settings/.receivers.WiFiConnectionSettingReceiver";
export const WIFI_CONNECTION_SETTING_ACTION: "io.appium.settings.wifi";
export const DATA_CONNECTION_SETTING_RECEIVER: "io.appium.settings/.receivers.DataConnectionSettingReceiver";
export const DATA_CONNECTION_SETTING_ACTION: "io.appium.settings.data_connection";
export const BLUETOOTH_SETTING_RECEIVER: "io.appium.settings/.receivers.BluetoothConnectionSettingReceiver";
export const BLUETOOTH_SETTING_ACTION: "io.appium.settings.bluetooth";
export const BLUETOOTH_UNPAIR_RECEIVER: "io.appium.settings/.receivers.UnpairBluetoothDevicesReceiver";
export const BLUETOOTH_UNPAIR_ACTION: "io.appium.settings.unpair_bluetooth";
export const ANIMATION_SETTING_RECEIVER: "io.appium.settings/.receivers.AnimationSettingReceiver";
export const ANIMATION_SETTING_ACTION: "io.appium.settings.animation";
export const LOCALE_SETTING_RECEIVER: "io.appium.settings/.receivers.LocaleSettingReceiver";
export const LOCALE_SETTING_ACTION: "io.appium.settings.locale";
export const LOCALES_LIST_SETTING_RECEIVER: "io.appium.settings/.receivers.LocalesReader";
export const LOCALES_LIST_SETTING_ACTION: "io.appium.settings.list_locales";
export const LOCATION_SERVICE: "io.appium.settings/.LocationService";
export const LOCATION_RECEIVER: "io.appium.settings/.receivers.LocationInfoReceiver";
export const LOCATION_RETRIEVAL_ACTION: "io.appium.settings.location";
export const SMS_LIST_RECEIVER: "io.appium.settings/.receivers.SmsReader";
export const SMS_LIST_RETRIEVAL_ACTION: "io.appium.settings.sms.read";
export const MEDIA_SCAN_RECEIVER: "io.appium.settings/.receivers.MediaScannerReceiver";
export const MEDIA_SCAN_ACTION: "io.appium.settings.scan_media";
export const SETTING_NOTIFICATIONS_LISTENER_SERVICE: "io.appium.settings/.NLService";
export const NOTIFICATIONS_RETRIEVAL_ACTION: "io.appium.settings.notifications";
export const RECORDING_SERVICE_NAME: "io.appium.settings/.recorder.RecorderService";
export const RECORDING_ACTIVITY_NAME: "io.appium.settings/io.appium.settings.Settings";
export const RECORDING_ACTION_START: "io.appium.settings.recording.ACTION_START";
export const RECORDING_ACTION_STOP: "io.appium.settings.recording.ACTION_STOP";
//# sourceMappingURL=constants.d.ts.map