{"version": 3, "file": "handler.d.ts", "sourceRoot": "", "sources": ["../../src/bidi/handler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,KAAK,KAAK,KAAK,MAAM,iBAAiB,CAAA;AAC7C,OAAO,KAAK,KAAK,MAAM,MAAM,kBAAkB,CAAA;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAA;AAEpC,qBAAa,WAAY,SAAQ,QAAQ;IACrC;;;;;QAKI;IACE,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC;IASnF;;;;;QAKI;IACE,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC;IAStF;;;;;QAKI;IACE,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASxE;;;;;QAKI;IACE,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,0BAA0B,GAAG,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC;IASxG;;;;;QAKI;IACE,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,4BAA4B,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASjG;;;;;QAKI;IACE,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS1E;;;;;QAKI;IACE,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,kCAAkC,GAAG,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC;IAShI;;;;;QAKI;IACE,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC;IASvG;;;;;QAKI;IACE,sBAAsB,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;IASrG;;;;;QAKI;IACE,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,kCAAkC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS7G;;;;;QAKI;IACE,2BAA2B,CAAC,MAAM,EAAE,MAAM,CAAC,qCAAqC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASnH;;;;;QAKI;IACE,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,iCAAiC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS3G;;;;;QAKI;IACE,gCAAgC,CAAC,MAAM,EAAE,MAAM,CAAC,0CAA0C,GAAG,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC;IASxJ;;;;;QAKI;IACE,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,8BAA8B,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASrG;;;;;QAKI;IACE,qBAAqB,CAAC,MAAM,EAAE,MAAM,CAAC,+BAA+B,GAAG,OAAO,CAAC,KAAK,CAAC,2BAA2B,CAAC;IASvH;;;;;QAKI;IACE,sBAAsB,CAAC,MAAM,EAAE,MAAM,CAAC,gCAAgC,GAAG,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;IAS1H;;;;;QAKI;IACE,+BAA+B,CAAC,MAAM,EAAE,MAAM,CAAC,yCAAyC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS3H;;;;;QAKI;IACE,0BAA0B,CAAC,MAAM,EAAE,MAAM,CAAC,oCAAoC,GAAG,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC;IAStI;;;;;QAKI;IACE,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,iCAAiC,GAAG,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC;IAS7H;;;;;QAKI;IACE,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,8BAA8B,GAAG,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC;IASpH;;;;;QAKI;IACE,qBAAqB,CAAC,MAAM,EAAE,MAAM,CAAC,+BAA+B,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASvG;;;;;QAKI;IACE,0BAA0B,CAAC,MAAM,EAAE,MAAM,CAAC,oCAAoC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASjH;;;;;QAKI;IACE,8BAA8B,CAAC,MAAM,EAAE,MAAM,CAAC,wCAAwC,GAAG,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC;IASlJ;;;;;QAKI;IACE,+BAA+B,CAAC,MAAM,EAAE,MAAM,CAAC,yCAAyC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS3H;;;;;QAKI;IACE,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,6BAA6B,GAAG,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC;IASjH;;;;;QAKI;IACE,sBAAsB,CAAC,MAAM,EAAE,MAAM,CAAC,gCAAgC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASzG;;;;;QAKI;IACE,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,iCAAiC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS3G;;;;;QAKI;IACE,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,iCAAiC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS3G;;;;;QAKI;IACE,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,4BAA4B,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASjG;;;;;QAKI;IACE,sBAAsB,CAAC,MAAM,EAAE,MAAM,CAAC,gCAAgC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASzG;;;;;QAKI;IACE,sBAAsB,CAAC,MAAM,EAAE,MAAM,CAAC,gCAAgC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASzG;;;;;QAKI;IACE,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,iCAAiC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS3G;;;;;QAKI;IACE,sBAAsB,CAAC,MAAM,EAAE,MAAM,CAAC,gCAAgC,GAAG,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;IAS1H;;;;;QAKI;IACE,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,sBAAsB,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASrF;;;;;QAKI;IACE,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,4BAA4B,GAAG,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC;IAS1G;;;;;QAKI;IACE,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,wBAAwB,GAAG,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC;IASlG;;;;;QAKI;IACE,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,yBAAyB,GAAG,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC;IASrG;;;;;QAKI;IACE,yBAAyB,CAAC,MAAM,EAAE,MAAM,CAAC,mCAAmC,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IAS/G;;;;;QAKI;IACE,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,2BAA2B,GAAG,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC;IAS3G;;;;;QAKI;IACE,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,0BAA0B,GAAG,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC;IASxG;;;;;QAKI;IACE,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,8BAA8B,GAAG,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC;IASpH;;;;;QAKI;IACE,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,6BAA6B,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASnG;;;;;QAKI;IACE,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,6BAA6B,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASnG;;;;;QAKI;IACE,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,uBAAuB,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;IASvF;;;;;QAKI;IACE,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,6BAA6B,GAAG,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC;IASjH;;;;;QAKI;IACE,qBAAqB,CAAC,MAAM,EAAE,MAAM,CAAC,+BAA+B,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;CAQ1G"}