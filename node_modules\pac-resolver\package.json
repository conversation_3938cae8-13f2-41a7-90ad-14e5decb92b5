{"name": "pac-resolver", "version": "7.0.1", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"degenerator": "^5.0.0", "netmask": "^2.0.2"}, "devDependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "@types/jest": "^29.5.2", "@types/netmask": "^1.0.30", "@types/node": "^14.18.52", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.1.6", "tsconfig": "0.0.0"}, "repository": {"type": "git", "url": "https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-resolver"}, "engines": {"node": ">= 14"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}}