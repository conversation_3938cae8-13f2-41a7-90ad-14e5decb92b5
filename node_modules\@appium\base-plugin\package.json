{"name": "@appium/base-plugin", "version": "2.3.7", "description": "The base plugin used to create Appium 2.0 plugins", "keywords": ["automation", "javascript", "selenium", "webdriver", "ios", "android", "firefoxos", "testing"], "homepage": "https://appium.io", "bugs": {"url": "https://github.com/appium/appium/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/appium.git", "directory": "packages/base-plugin"}, "license": "Apache-2.0", "author": "https://github.com/appium", "types": "./build/lib/plugin.d.ts", "directories": {"lib": "./lib"}, "files": ["lib", "build", "index.js", "tsconfig.json", "!build/tsconfig.tsbuildinfo"], "scripts": {"test": "npm run test:unit", "test:smoke": "node ./index.js", "test:unit": "mocha \"./test/unit/**/*.spec.js\""}, "dependencies": {"@appium/base-driver": "^9.18.0", "@appium/support": "^6.1.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "publishConfig": {"access": "public"}, "gitHead": "c8fe4412525f7e1fa237813cf83fe7d98f0125eb", "tags": ["appium"]}