import type { Options } from '@wdio/types';
import type { RemoteConfig } from './types.js';
export declare const DEFAULTS: Options.Definition<Required<RemoteConfig>>;
export declare const ELEMENT_KEY = "element-6066-11e4-a52e-4f735466cecf";
export declare const SHADOW_ELEMENT_KEY = "shadow-6066-11e4-a52e-4f735466cecf";
export declare const BASE_64_REGEX: RegExp;
export declare const BASE_64_SAFE_STRING_TO_PROCESS_LENGTH = 200000;
export declare const APPIUM_MASKING_HEADER: {
    'x-appium-is-sensitive': string;
};
//# sourceMappingURL=constants.d.ts.map