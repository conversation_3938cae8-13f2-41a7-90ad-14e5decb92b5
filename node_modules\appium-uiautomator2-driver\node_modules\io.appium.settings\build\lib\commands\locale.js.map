{"version": 3, "file": "locale.js", "sourceRoot": "", "sources": ["../../../lib/commands/locale.js"], "names": [], "mappings": ";;;;;AA0DA,0CAgCC;AAeD,oDAaC;AAtHD,oDAAuB;AACvB,4CAA0C;AAC1C,wDAAyB;AACzB,kDAKyB;AAEzB;;;;;;;;;;GAUG;AACH,KAAK,UAAU,uBAAuB,CAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI;IACtE,MAAM,MAAM,GAAG;QACb,IAAI,EAAE,oCAAqB;QAC3B,IAAI,EAAE,sCAAuB;QAC7B,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE;QACtC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,WAAW,EAAE;KACzC,CAAC;IACF,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;QACvC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,KAAK,KAAK,CAAC,IAAI,gBAAC,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACxF,sFAAsF;gBACtF,kDAAkD;gBAClD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAC,uBAAuB,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAC,CAAC,CAAC;gBAC/E,SAAS;YACX,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;;;;;;;GAUG;AACI,KAAK,UAAU,eAAe,CAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI;IACrE,IAAI,gBAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;IACD,IAAI,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC1C,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IACxC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;QACtC,MAAM,CAAC,cAAc,EAAE,aAAa,CAAC,GAAG,MAAM,kBAAC,CAAC,GAAG,CAAC;YAClD,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE;YAC5B,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE;SAC5B,CAAC,CAAC;QACH,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,UAAU,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,sBAAU,EAAE,sBAAsB,WAAW,2BAA2B,UAAU,GAAG,CAAC,CAAC;QACtG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,sBAAU,EAAE,qBAAqB,UAAU,0BAA0B,SAAS,GAAG,CAAC,CAAC;QAClG,IAAI,UAAU,KAAK,WAAW,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;YAC3D,MAAM,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC;QAEnD,qBAAqB;QACrB,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,SAAS,EAAE,CAAC;QAClG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,sBAAU,EAAE,oBAAoB,SAAS,yBAAyB,UAAU,GAAG,CAAC,CAAC;QAChG,IAAI,UAAU,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;YACzD,MAAM,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;;GAKG;AAEH;;;;;GAKG;AACI,KAAK,UAAU,oBAAoB;IACxC,MAAM,MAAM,GAAG;QACb,IAAI,EAAE,0CAA2B;QACjC,IAAI,EAAE,4CAA6B;KACpC,CAAC;IACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;IAC3E,MAAM,KAAK,GAAG,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CACb,gGAAgG,CACjG,CAAC;IACJ,CAAC;IACD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;AACtE,CAAC"}