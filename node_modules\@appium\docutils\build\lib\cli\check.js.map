{"version": 3, "file": "check.js", "sourceRoot": "", "sources": ["../../../lib/cli/check.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;AAgDH,8CAsCC;AApFD,6CAAyC;AACzC,oDAAuB;AAEvB,sCAAoC;AAEpC,MAAM,GAAG,GAAG,IAAA,kBAAS,EAAC,OAAO,CAAC,CAAC;AAE/B;;;;GAIG;AACH,KAAK,UAAU,aAAa,CAAC,KAAwB;IACnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAC9B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,EAAE,EAAE,CAAC,CAAC,EAAC,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,YAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAC7E,CAAC;IACF,MAAM,OAAO,GAAG,gBAAC,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC3C,OAAO,gBAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,gBAAC,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC9D,CAAC;AAgBD;;;;;;;;;;;GAWG;AACI,KAAK,UAAU,iBAAiB,CACrC,IAAO,EACP,KAAa,EACb,IAA6B;IAE7B,MAAM,WAAW,GAAG,gBAAC,CAAC,IAAI,CACxB,gBAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,GAAY,EAAE,EAAU,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,CAAe,CAC/F,CAAC;IAEF,gGAAgG;IAChG,0FAA0F;IAC1F,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,OAAO,sCAAsC,CAAC;IAChD,CAAC;IAED,MAAM,YAAY,GAAsB,gBAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClE,EAAE;QACF,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,4CAA4C;KACrE,CAAC,CAAC,CAAC;IAEJ,GAAG,CAAC,KAAK,CACP,kCAAkC,EAClC,cAAI,CAAC,SAAS,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,EAC3C,gBAAC,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,CAC5B,CAAC;IAEF,MAAM,YAAY,GAAG,MAAM,aAAa,CAAC,YAAY,CAAC,CAAC;IAEvD,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;QACxB,OAAO,YAAY;aAChB,GAAG,CACF,CAAC,EAAC,EAAE,EAAE,IAAI,EAAC,EAAE,EAAE,CACb,mCAAmC,gBAAC,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,IAAI,kBAAkB,CAChF;aACA,IAAI,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}