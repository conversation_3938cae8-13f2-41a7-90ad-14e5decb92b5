{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../lib/utils.js"], "names": [], "mappings": ";;;;;;AAkCA,kDAKC;AA6BD,0DAgHC;AAQD,oDAQC;AAOD,oDAEC;AAgBD,8CAGC;AASD,wCA6CC;AAyBD,oCAqBC;AAQD,kDAEC;AAQD,gDAEC;AAQD,wDAEC;AAQD,kDAEC;AAQD,kDAEC;AASD,0CAYC;AASD,0BA+CC;AAQD,sCAEC;AA7cD,oDAAuB;AACvB,sDAA8B;AAC9B,qDAK6B;AAC7B,+BAAqC;AACrC,6CAAyC;AACzC,gDAAwB;AACxB,2CAA0F;AAC1F,sDAAyB;AAEzB,MAAM,iBAAiB,GAAG,QAAQ,CAAC;AACnC,MAAM,uBAAuB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,2BAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AAC/E,QAAA,eAAe,GAAG,SAAS,CAAC;AAC5B,QAAA,eAAe,GAAG,IAAI,CAAC;AACvB,QAAA,UAAU,GAAG,YAAE,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAE5D;;;;;;GAMG;AACH,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AAEzC;;;;GAIG;AACH,SAAgB,mBAAmB;IACjC,OAAO,IAAI,oBAAM,CAAC,sBAAsB,CACtC,oGAAoG;QACpG,oEAAoE,CACrE,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACU,QAAA,OAAO,GAAG,gBAAC,CAAC,IAAI,CAC3B,gBAAC,CAAC,YAAY;AACZ,8EAA8E,CAAC,CAAC,cAAI,CAAC,EACrF,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,WAAW,EAAC,CACnD,EACD,CAAC,GAAG,IAAI,EAAE,EAAE;IACV,gBAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACvB,CAAC,CACF,CAAC;AAEF;;;;;;;;;;;GAWG;AACH,SAAgB,uBAAuB,CACrC,kBAAkB,EAClB,eAAe,EACf,WAAW,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,EACnC,mBAAmB,GAAG,EAAE;IAExB,0DAA0D;IAC1D,MAAM,UAAU,GACd,gBAAC,CAAC,aAAa,CAAC,eAAe,CAAC;QAChC,CAAC,gBAAC,CAAC,GAAG,CAAC,eAAe,EAAE,aAAa,CAAC,IAAI,gBAAC,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,CAAC;IAClF,MAAM,aAAa,GAAG,gBAAC,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;IAC1D,IAAI,WAAW,GAAG,iDAAiD,CAAC,CAAC,EAAE,CAAC,CAAC;IACzE,8DAA8D;IAC9D,IAAI,wBAAwB,CAAC;IAC7B,iEAAiE;IACjE,IAAI,2BAA2B,CAAC;IAEhC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,6BAA6B,CAAC,CAAC;YACpC,QAAQ,EAAE,uBAAS,CAAC,GAAG;YACvB,KAAK,EAAE,mBAAmB,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,MAAM,EAAC,GAAG,EAAC,GAAG,uBAAS,CAAC;IACxB,MAAM,QAAQ,GAAG,GAAG,CAAC;IAErB,mDAAmD;IACnD,kBAAkB,GAAG,gBAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;IACrD,eAAe,GAAG,gBAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;IAC/C,mBAAmB,GAAG,gBAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;IAEvD,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;QACpC,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,MAAM,CAAC,aAAa,EAAE,eAAe,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC9E,IAAI,eAAe,GAAG,KAAK,CAAC;gBAC5B,4DAA4D;gBAC5D,KAAK,MAAM,eAAe,IAAI,eAAe,CAAC,UAAU,IAAI,EAAE,EAAE,CAAC;oBAC/D,IACE,gBAAC,CAAC,aAAa,CAAC,eAAe,CAAC;wBAChC,gBAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EAC/E,CAAC;wBACD,eAAe,GAAG,IAAI,CAAC;wBACvB,MAAM;oBACR,CAAC;gBACH,CAAC;gBACD,6DAA6D;gBAC7D,eAAe;oBACb,eAAe;wBACf,CAAC,gBAAC,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC;4BAC3C,gBAAC,CAAC,GAAG,CACH,oBAAoB,CAAC,eAAe,CAAC,WAAW,CAAC,EACjD,kBAAkB,CAAC,aAAa,CAAC,CAClC,CAAC,CAAC;gBACP,IAAI,eAAe,EAAE,CAAC;oBACpB,0DAA0D;oBAC1D,SAAS;gBACX,CAAC;gBAED,0DAA0D;gBAC1D,IAAI,gBAAC,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC1C,eAAe,CAAC,UAAU,GAAG,+CAA+C,CAAC,CAAC;wBAC5E,EAAC,CAAC,aAAa,CAAC,EAAE,eAAe,EAAC;qBACnC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;gBACjE,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,aAAa,EAAE,CAAC;YAClB,kBAAkB,GAAG;gBACnB,GAAG,oBAAoB,CAAC,mBAAmB,CAAC;gBAC5C,GAAG,kBAAkB;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,mBAAmB;IACnB,IAAI,aAAa,EAAE,CAAC;QAClB,2BAA2B,GAAG,EAAC,GAAG,kBAAkB,EAAC,CAAC;IACxD,CAAC;IAED,eAAe;IACf,IAAI,UAAU,EAAE,CAAC;QACf,2EAA2E;QAC3E,0EAA0E;QAC1E,IAAI,CAAC;YACH,WAAW,GAAG,IAAA,iCAAmB,EAAC,eAAe,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,IAAI,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAClE,OAAO,+BAA+B,CAAC,CAAC;gBACtC,WAAW;gBACX,2BAA2B;gBAC3B,wBAAwB;gBACxB,QAAQ;gBACR,KAAK;aACN,CAAC,CAAC;QACL,CAAC;QAED,8FAA8F;QAC9F,wBAAwB,GAAG;YACzB,WAAW,EAAE,EAAC,GAAG,oBAAoB,CAAC,WAAW,CAAC,EAAC;YACnD,UAAU,EAAE,CAAC,EAAE,CAAC;SACjB,CAAC;IACJ,CAAC;IAED,OAAO,oCAAoC,CAAC,CAAC;QAC3C,WAAW;QACX,2BAA2B;QAC3B,wBAAwB;QACxB,QAAQ;KACT,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAAC,IAAI;IACvC,OAAO,gCAAgC,CAAC,CACtC,gBAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CACzB,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjE,CAAC,CAAC,GAAG;QACL,CAAC,CAAC,GAAG,iBAAiB,IAAI,GAAG,EAAE,CAClC,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB,CAAC,IAAI;IACvC,OAAO,8BAA8B,CAAC,CAAC,gBAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/F,CAAC;AAED;;;GAGG;AACH,SAAS,kBAAkB,CAAC,GAAG;IAC7B,MAAM,MAAM,GAAG,GAAG,iBAAiB,GAAG,CAAC;IACvC,OAAO,gBAAC,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACxE,CAAC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,OAAO;IACvC,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,OAAO,eAAe,CAAC,IAAI,EAAE,CAAC;IACzD,OAAO,OAAO,CAAC,OAAO,CAAC;AACzB,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc;IAC5B,MAAM,QAAQ,GAAG,cAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC9D,IAAI,CAAC,QAAQ,IAAI,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;QAClE,OAAO;IACT,CAAC;IACD,MAAM,eAAe,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAE/C,MAAM,mBAAmB,GAAG,GAAG,EAAE;QAC/B,IAAI,CAAC;YACH,wDAAwD;YACxD,gEAAgE;YAChE,2CAA2C;YAC3C,2DAA2D;YAC3D,+BAA+B;YAE/B,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,eAAe,CAAC;QACxC,IAAI,mBAAmB,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,GAAG,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;QAC/B,CAAC;QACD,OAAO;IACT,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC;IAClE,IAAI,aAAa,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,GAAG,CAAC;QACxC,OAAO;IACT,CAAC;IAED,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC;IAC3D,IAAI,mBAAmB,EAAE,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,GAAG,CAAC;IAC1C,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,gBAAC,CAAC,OAAO,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC;IACzF,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,SAAgB,YAAY,CAAC,IAAI;IAC/B,IAAI,CAAC,gBAAC,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3C,IAAI,KAAK,CAAC;QACV,IAAI,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,gBAAC,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,gCAAgC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAChE,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACjC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IACD,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;QAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;IACxC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,IAAI;IACtC,OAAO,IAAI,CAAC,UAAU,KAAK,6BAAiB,CAAC;AAC/C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,IAAI;IACrC,OAAO,IAAI,CAAC,UAAU,KAAK,4BAAgB,CAAC;AAC9C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,sBAAsB,CAAC,IAAI;IACzC,OAAO,IAAI,CAAC,UAAU,KAAK,uBAAW,IAAI,IAAI,CAAC,UAAU,KAAK,uBAAW,CAAC;AAC5E,CAAC;AAED;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,IAAI;IACtC,OAAO,IAAI,CAAC,UAAU,KAAK,uBAAW,CAAC;AACzC,CAAC;AAED;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,IAAI;IACtC,OAAO,IAAI,CAAC,UAAU,KAAK,uBAAW,CAAC;AACzC,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAAE,MAAM,GAAG,IAAI;IAC5C,IAAI,WAAW,GAAG,IAAI,CAAC;IACvB,wDAAwD;IACxD,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACjB,WAAW,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAC5B,CAAC;SAAM,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,WAAW,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAC5B,CAAC;IACD,6DAA6D;IAC7D,OAAO,gBAAC,CAAC,OAAO,CAAC,gBAAC,CAAC,MAAM,CAAC,iBAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAChE,6DAA6D;SAC5D,MAAM,CAAC,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE,CAAC,CAAC,WAAW,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACvF,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,OAAO,CAAC,GAAG,EAAE,IAAI,GAAG,IAAI;IACvC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACtD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC5B,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC;QAClB,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC;IAClB,CAAC;IACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QACxB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACd,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;gBACV,CAAC,IAAI,CAAC,CAAC;YACT,CAAC;iBACC,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC;gBACpB,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBACvB,CAAC,IAAI,CAAC,CAAC;gBACP,EAAE,CAAC,CAAC;gBACR,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YACrB,CAAC;iBAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC;gBACtC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAChB,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;gBACnC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtB,CAAC,IAAI,CAAC,CAAC;gBACP,EAAE,CAAC,CAAC;gBACR,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBACvB,CAAC,IAAI,CAAC,CAAC;gBACP,EAAE,CAAC,CAAC;gBACR,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACxC,CAAC,IAAI,CAAC,CAAC;gBACP,EAAE,CAAC,CAAC;gBACR,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACP,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gBACxB,CAAC,IAAI,CAAC,CAAC;gBACP,EAAE,CAAC,CAAC;gBACR,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBACvB,CAAC,IAAI,CAAC,CAAC;gBACP,EAAE,CAAC,CAAC;gBACR,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YACrB,CAAC;YACD,CAAC,IAAI,CAAC,CAAC;YACJ,EAAE,CAAC,CAAC;QACR,CAAC;QACD,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACpC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,OAAO;IACnC,OAAO,CAAC,uBAAe,EAAE,uBAAe,EAAE,IAAI,uBAAe,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtF,CAAC;AAED;;;GAGG;AAEH;;;;;;;;GAQG;AAEH;;;;;;;;;;GAUG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;GAEG;AAEH;;;;;;;;;GASG;AAEH;;;;GAIG;AAEH;;;;GAIG"}