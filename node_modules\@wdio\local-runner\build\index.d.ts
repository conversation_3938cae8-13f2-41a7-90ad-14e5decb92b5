import { WritableStreamBuffer } from 'stream-buffers';
import type { Workers } from '@wdio/types';
import WorkerInstance from './worker.js';
export type { WorkerInstance };
export interface RunArgs extends Workers.WorkerRunPayload {
    command: string;
    args: Workers.WorkerMessageArgs;
}
export default class LocalRunner {
    private _options;
    protected _config: WebdriverIO.Config;
    workerPool: Record<string, WorkerInstance>;
    stdout: WritableStreamBuffer;
    stderr: WritableStreamBuffer;
    constructor(_options: never, _config: WebdriverIO.Config);
    /**
     * nothing to initialize when running locally
     */
    initialize(): void;
    getWorkerCount(): number;
    run({ command, args, ...workerOptions }: RunArgs): Promise<WorkerInstance>;
    /**
     * shutdown all worker processes
     *
     * @return {Promise}  resolves when all worker have been shutdown or
     *                    a timeout was reached
     */
    shutdown(): Promise<boolean>;
}
//# sourceMappingURL=index.d.ts.map