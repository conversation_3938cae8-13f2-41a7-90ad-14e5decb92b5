"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtensionCore = void 0;
const support_1 = require("@appium/support");
const node_events_1 = require("node:events");
const constants_1 = require("../constants");
const protocol_1 = require("../protocol");
const bidi_commands_1 = require("../protocol/bidi-commands");
const lodash_1 = __importDefault(require("lodash"));
const helpers_1 = require("./helpers");
class ExtensionCore {
    constructor(logPrefix) {
        this.bidiCommands = bidi_commands_1.BIDI_COMMANDS;
        this._logPrefix = logPrefix;
        this.bidiEventSubs = {};
        this.eventEmitter = new node_events_1.EventEmitter();
    }
    get log() {
        if (!this._log) {
            this.updateLogPrefix(this._logPrefix ?? (0, helpers_1.generateDriverLogPrefix)(this));
        }
        return this._log;
    }
    updateLogPrefix(logPrefix) {
        this._log = support_1.logger.getLogger(logPrefix);
    }
    updateBidiCommands(cmds) {
        const overlappingKeys = lodash_1.default.intersection(Object.keys(cmds), Object.keys(this.bidiCommands));
        if (overlappingKeys.length) {
            this.log.warn(`Overwriting existing bidi modules: ${JSON.stringify(overlappingKeys)}. This may not be intended!`);
        }
        this.bidiCommands = {
            ...this.bidiCommands,
            ...cmds,
        };
    }
    doesBidiCommandExist(moduleName, methodName) {
        try {
            this.ensureBidiCommandExists(moduleName, methodName);
        }
        catch {
            return false;
        }
        return true;
    }
    ensureBidiCommandExists(moduleName, methodName) {
        // if we don't get a valid format for bidi command name, reject
        if (!moduleName || !methodName) {
            throw new protocol_1.errors.UnknownCommandError(`Did not receive a valid BiDi module and method name ` +
                `of the form moduleName.methodName. Instead received ` +
                `'${moduleName}.${methodName}'`);
        }
        // if the command module or method isn't part of our spec, reject
        if (!(this.bidiCommands[moduleName]?.[methodName])) {
            throw new protocol_1.errors.UnknownCommandError();
        }
        const { command } = this.bidiCommands[moduleName][methodName];
        // if the command method isn't part of our spec, also reject
        if (!command) {
            throw new protocol_1.errors.UnknownCommandError();
        }
        // If the driver doesn't have this command, it must not be implemented
        if (!this[command]) {
            throw new protocol_1.errors.NotYetImplementedError();
        }
    }
    async executeBidiCommand(bidiCmd, bidiParams, next, driver) {
        const handlerType = (next && driver) ? 'plugin' : 'driver';
        const [moduleName, methodName] = bidiCmd.split('.');
        this.ensureBidiCommandExists(moduleName, methodName);
        const { command, params } = this.bidiCommands[moduleName][methodName];
        // TODO improve param parsing and error messages along the lines of what we have in the http
        // handlers
        const args = [];
        if (params?.required?.length) {
            for (const requiredParam of params.required) {
                if (lodash_1.default.isUndefined(bidiParams[requiredParam])) {
                    throw new protocol_1.errors.InvalidArgumentError(`The ${requiredParam} parameter was required but you omitted it`);
                }
                args.push(bidiParams[requiredParam]);
            }
        }
        if (params?.optional?.length) {
            for (const optionalParam of params.optional) {
                args.push(bidiParams[optionalParam]);
            }
        }
        const logParams = lodash_1.default.truncate(JSON.stringify(bidiParams), { length: constants_1.MAX_LOG_BODY_LENGTH });
        this.log.debug(`Executing bidi command '${bidiCmd}' with params ${logParams} by passing to ${handlerType} ` +
            `method '${command}'`);
        // call the handler with the signature appropriate to extension type (plugin or driver)
        const response = (next && driver) ? await this[command](next, driver, ...args) : await this[command](...args);
        const finalResponse = lodash_1.default.isUndefined(response) ? {} : response;
        this.log.debug(`Responding to bidi command '${bidiCmd}' with ` +
            `${lodash_1.default.truncate(JSON.stringify(finalResponse), { length: constants_1.MAX_LOG_BODY_LENGTH })}`);
        return finalResponse;
    }
}
exports.ExtensionCore = ExtensionCore;
//# sourceMappingURL=extension-core.js.map