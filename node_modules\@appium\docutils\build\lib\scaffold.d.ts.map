{"version": 3, "file": "scaffold.d.ts", "sourceRoot": "", "sources": ["../../lib/scaffold.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAMH,OAAO,EAAC,qBAAqB,EAAC,MAAM,UAAU,CAAC;AAC/C,OAAO,EAAC,SAAS,EAAE,UAAU,EAAC,MAAM,WAAW,CAAC;AA8BhD;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,IAAI,SAAS,mBAAmB,IAAI,IAAI,CACnE,IAAI,EACJ,MAAM,mBAAmB,CAC1B,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,YAAY,CAAC,IAAI,SAAS,mBAAmB,EAAE,CAAC,SAAS,UAAU,IAAI,CACjF,IAAI,EAAE,IAAI,KACP,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;AAEpC;;;;;;;;GAQG;AACH,wBAAgB,kBAAkB,CAAC,IAAI,SAAS,mBAAmB,EAAE,CAAC,SAAS,UAAU,EACvF,eAAe,EAAE,MAAM,EACvB,cAAc,EAAE,CAAC,EACjB,WAAW,EAAE,MAAM,EACnB,EACE,SAAsB,EACtB,WAAwB,EACxB,SAAyB,GAC1B,GAAE,yBAAyB,CAAC,IAAI,EAAE,CAAC,CAAM,GACzC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CA6EvB;AAED;;;GAGG;AACH,MAAM,MAAM,uBAAuB,CAAC,IAAI,SAAS,mBAAmB,EAAE,CAAC,SAAS,SAAS,IAAI,CAC3F,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,EACpB,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,EAC5B,GAAG,EAAE,QAAQ,CAAC,qBAAqB,CAAC,KACjC,CAAC,CAAC;AAEP;;GAEG;AACH,MAAM,MAAM,wBAAwB,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,CAAC;AAEjE;;GAEG;AACH,MAAM,MAAM,sBAAsB,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,MAAM,CAAC;AAE/D;;GAEG;AACH,MAAM,WAAW,yBAAyB,CAAC,IAAI,SAAS,mBAAmB,EAAE,CAAC,SAAS,SAAS;IAC9F;;OAEG;IACH,SAAS,CAAC,EAAE,uBAAuB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7C;;OAEG;IACH,WAAW,CAAC,EAAE,wBAAwB,CAAC,CAAC,CAAC,CAAC;IAC1C;;OAEG;IACH,SAAS,CAAC,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAAC;CACvC;AAED;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB,CAAC,CAAC;IACnC;;OAEG;IACH,OAAO,EAAE,CAAC,CAAC;IACX;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd"}