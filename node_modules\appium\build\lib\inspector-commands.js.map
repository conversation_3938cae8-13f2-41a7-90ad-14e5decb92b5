{"version": 3, "file": "inspector-commands.js", "sourceRoot": "", "sources": ["../../lib/inspector-commands.ts"], "names": [], "mappings": ";;;;;AAwBA,oCAsBC;AAED,wCAgBC;AAhED,oDAAuB;AACvB,qDAG6B;AAoBtB,KAAK,UAAU,YAAY,CAAqB,SAAkB;IACvE,IAAI,mBAAmB,GAAmB,EAAE,CAAC;IAC7C,IAAI,kBAAkB,GAAkB,EAAE,CAAC;IAC3C,IAAI,oBAAoB,GAAmC,EAAE,CAAC;IAC9D,IAAI,kBAAkB,GAAkC,EAAE,CAAC;IAC3D,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,WAAwC,CAAC;QAC/F,mBAAmB,GAAG,WAAW,EAAE,YAAY,IAAI,EAAE,CAAC;QACtD,kBAAkB,GAAG,WAAW,EAAE,eAAe,IAAI,EAAE,CAAC;QACxD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;aACpD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAA0B,CAAC,CAAC;QAC5C,oBAAoB,GAAG,gBAAC,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7F,kBAAkB,GAAG,gBAAC,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAChG,CAAC;IACD,OAAO;QACL,IAAI,EAAE;YACJ,IAAI,EAAE,2BAA2B,CAAC,wBAAe,CAAC;YAClD,MAAM,EAAE,2BAA2B,CAAC,mBAAmB,CAAC;YACxD,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC,gBAAC,CAAC,SAAS,CAAC,oBAAoB,EAAE,2BAA2B,CAAC,CAAC,CAAC,CAAC,SAAS;SAC3G;QACD,IAAI,EAAE,iBAAiB,CAAC,2BAAkB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;KACpF,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,cAAc,CAAqB,SAAkB;IACzE,IAAI,sBAAsB,GAA0B,EAAE,CAAC;IACvD,IAAI,uBAAuB,GAA0C,EAAE,CAAC;IACxE,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,WAAwC,CAAC;QAC/F,sBAAsB,GAAG,WAAW,EAAE,gBAAgB,IAAI,EAAE,CAAC;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;aACpD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAA0B,CAAC,CAAC;QAC5C,uBAAuB,GAAG,gBAAC,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACtG,CAAC;IACD,OAAO;QACL,IAAI,EAAE;YACJ,MAAM,EAAE,8BAA8B,CAAC,sBAAsB,CAAC;YAC9D,OAAO,EAAE,uBAAuB,CAAC,CAAC,CAAC,gBAAC,CAAC,SAAS,CAAC,uBAAuB,EAAE,8BAA8B,CAAC,CAAC,CAAC,CAAC,SAAS;SACpH;KACF,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAiC;IAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO;IACT,CAAC;IAED,MAAM,sBAAsB,GAAG,CAAC,CAAM,EAAE,UAAmB,EAAoC,EAAE;QAC/F,MAAM,aAAa,GAAG,gBAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,gDAAgD;QAChD,8BAA8B;QAC9B,0CAA0C;QAC1C,MAAM,QAAQ,GAAG,UAAU,IAAI,CAAC,aAAa,CAAC;QAC9C,OAAO;YACL,IAAI;YACJ,QAAQ;SACT,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,cAAc,GAA2B,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;SACnE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACtD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gBAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM,cAAc,GAA2B,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;SACnE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACvD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gBAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,OAAO,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM;QACnD,CAAC,CAAC,CAAC,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC;QACxC,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC;AAED,SAAS,2BAA2B,CAAE,EAAkB;IACtD,MAAM,GAAG,GAA6C,EAAE,CAAC;IACzD,KAAK,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/C,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAChD,UAAU,CAAC,MAAM,CAAC,GAAG;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC;aAChD,CAAC;QACJ,CAAC;QACD,GAAG,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC;IAC5B,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,8BAA8B,CAAC,GAA0B;IAChE,MAAM,MAAM,GAA6B,EAAE,CAAC;IAC5C,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC;SACzC,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iBAAiB,CACxB,aAA4B,EAC5B,eAA8B,EAC9B,gBAA+C;IAE/C,MAAM,mBAAmB,GAAG,CAAC,MAAoC,EAAsC,EAAE;QACvG,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,MAAM,sBAAsB,GAAG,CAAC,CAAM,EAAE,UAAmB,EAAoC,EAAE;YAC/F,MAAM,aAAa,GAAG,gBAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,OAAO;YACT,CAAC;YAED,gDAAgD;YAChD,8BAA8B;YAC9B,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,UAAU,IAAI,CAAC,aAAa,CAAC;YAC9C,OAAO;gBACL,IAAI;gBACJ,QAAQ;aACT,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,cAAc,GAA2B,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;aACnE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACjD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gBAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,cAAc,GAA2B,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;aACnE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aAClD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gBAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM;YACnD,CAAC,CAAC,CAAC,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC;YACxC,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC,CAAC;IAEF,MAAM,2BAA2B,GAAG,CAAC,EAAiB,EAA8C,EAAE;QACpG,MAAM,GAAG,GAA+C,EAAE,CAAC;QAC3D,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;YAC/C,MAAM,WAAW,GAAG,EAAE,CAAC;YACvB,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/C,WAAW,CAAC,IAAI,CAAC,GAAG;oBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,MAAM,EAAE,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC;iBACzC,CAAC;YACJ,CAAC;YACD,GAAG,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC;QAC5B,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IAEF,OAAO;QACL,IAAI,EAAE,2BAA2B,CAAC,aAAa,CAAC;QAChD,MAAM,EAAE,2BAA2B,CAAC,eAAe,CAAC;QACpD,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC,gBAAC,CAAC,SAAS,CAAC,gBAAgB,EAAE,2BAA2B,CAAC,CAAC,CAAC,CAAC,SAAS;KACnG,CAAC;AACJ,CAAC"}