export default codes;
export namespace codes {
    namespace Success {
        let code: number;
        let summary: string;
    }
    namespace NoSuchDriver {
        let code_1: number;
        export { code_1 as code };
        let summary_1: string;
        export { summary_1 as summary };
    }
    namespace NoSuchElement {
        let code_2: number;
        export { code_2 as code };
        let summary_2: string;
        export { summary_2 as summary };
    }
    namespace NoSuchFrame {
        let code_3: number;
        export { code_3 as code };
        let summary_3: string;
        export { summary_3 as summary };
    }
    namespace UnknownCommand {
        let code_4: number;
        export { code_4 as code };
        let summary_4: string;
        export { summary_4 as summary };
    }
    namespace StaleElementReference {
        let code_5: number;
        export { code_5 as code };
        let summary_5: string;
        export { summary_5 as summary };
    }
    namespace ElementNotVisible {
        let code_6: number;
        export { code_6 as code };
        let summary_6: string;
        export { summary_6 as summary };
    }
    namespace InvalidElementState {
        let code_7: number;
        export { code_7 as code };
        let summary_7: string;
        export { summary_7 as summary };
    }
    namespace UnknownError {
        let code_8: number;
        export { code_8 as code };
        let summary_8: string;
        export { summary_8 as summary };
    }
    namespace ElementIsNotSelectable {
        let code_9: number;
        export { code_9 as code };
        let summary_9: string;
        export { summary_9 as summary };
    }
    namespace JavaScriptError {
        let code_10: number;
        export { code_10 as code };
        let summary_10: string;
        export { summary_10 as summary };
    }
    namespace XPathLookupError {
        let code_11: number;
        export { code_11 as code };
        let summary_11: string;
        export { summary_11 as summary };
    }
    namespace Timeout {
        let code_12: number;
        export { code_12 as code };
        let summary_12: string;
        export { summary_12 as summary };
    }
    namespace NoSuchWindow {
        let code_13: number;
        export { code_13 as code };
        let summary_13: string;
        export { summary_13 as summary };
    }
    namespace InvalidCookieDomain {
        let code_14: number;
        export { code_14 as code };
        let summary_14: string;
        export { summary_14 as summary };
    }
    namespace UnableToSetCookie {
        let code_15: number;
        export { code_15 as code };
        let summary_15: string;
        export { summary_15 as summary };
    }
    namespace UnexpectedAlertOpen {
        let code_16: number;
        export { code_16 as code };
        let summary_16: string;
        export { summary_16 as summary };
    }
    namespace NoAlertOpenError {
        let code_17: number;
        export { code_17 as code };
        let summary_17: string;
        export { summary_17 as summary };
    }
    namespace ScriptTimeout {
        let code_18: number;
        export { code_18 as code };
        let summary_18: string;
        export { summary_18 as summary };
    }
    namespace InvalidElementCoordinates {
        let code_19: number;
        export { code_19 as code };
        let summary_19: string;
        export { summary_19 as summary };
    }
    namespace IMENotAvailable {
        let code_20: number;
        export { code_20 as code };
        let summary_20: string;
        export { summary_20 as summary };
    }
    namespace IMEEngineActivationFailed {
        let code_21: number;
        export { code_21 as code };
        let summary_21: string;
        export { summary_21 as summary };
    }
    namespace InvalidSelector {
        let code_22: number;
        export { code_22 as code };
        let summary_22: string;
        export { summary_22 as summary };
    }
    namespace SessionNotCreatedException {
        let code_23: number;
        export { code_23 as code };
        let summary_23: string;
        export { summary_23 as summary };
    }
    namespace MoveTargetOutOfBounds {
        let code_24: number;
        export { code_24 as code };
        let summary_24: string;
        export { summary_24 as summary };
    }
    namespace NoSuchContext {
        let code_25: number;
        export { code_25 as code };
        let summary_25: string;
        export { summary_25 as summary };
    }
}
export function getSummaryByCode(code: any): string;
//# sourceMappingURL=status.d.ts.map