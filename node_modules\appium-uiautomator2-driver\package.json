{"name": "appium-uiautomator2-driver", "description": "UiAutomator2 integration for Appium", "keywords": ["appium", "uiautomator2", "automated testing", "android"], "version": "4.2.5", "bugs": {"url": "https://github.com/appium/appium-uiautomator2-driver/issues"}, "author": "Appium Contributors", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/appium/appium-uiautomator2-driver.git"}, "main": "./build/index.js", "types": "./build/index.d.ts", "bin": {}, "directories": {"lib": "lib"}, "files": ["index.js", "lib", "build", "scripts", "CHANGELOG.md", "LICENSE", "npm-shrinkwrap.json"], "scripts": {"build": "tsc -b", "clean": "npm run build -- --clean", "dev": "npm run build -- --watch", "e2e-test:commands": "mocha --exit --timeout 10m \"./test/functional/commands\"", "e2e-test:commands:find": "mocha --exit --timeout 10m \"./test/functional/commands/find\"", "e2e-test:commands:general": "mocha --exit --timeout 10m \"./test/functional/commands/general\"", "e2e-test:commands:keyboard": "mocha --exit --timeout 10m \"./test/functional/commands/keyboard\"", "e2e-test:driver": "mocha --exit --timeout 10m \"./test/functional/driver-e2e-specs.js\"", "lint": "eslint .", "lint:commit": "commitlint", "lint:fix": "npm run lint -- --fix", "prepare": "npm run rebuild", "rebuild": "npm run clean; npm run build", "format": "prettier -w ./lib", "reset": "node ./scripts/reset.js", "test": "mocha --exit --timeout 1m \"./test/unit/**/*-specs.js\""}, "prettier": {"bracketSpacing": false, "printWidth": 100, "singleQuote": true}, "dependencies": {"appium-adb": "^12.12.0", "appium-android-driver": "^10.3.10", "appium-uiautomator2-server": "^7.4.0", "asyncbox": "^3.0.0", "axios": "^1.6.5", "bluebird": "^3.5.1", "css-selector-parser": "^3.0.0", "io.appium.settings": "^5.14.3", "lodash": "^4.17.4", "portscanner": "^2.2.0", "source-map-support": "^0.x", "teen_process": "^2.2.0", "type-fest": "^4.4.0"}, "devDependencies": {"@appium/docutils": "^1.0.1", "@appium/eslint-config-appium-ts": "^1.x", "@appium/support": "^6.0.0", "@appium/test-support": "^3.0.0", "@appium/tsconfig": "^0.x", "@appium/types": "^0.x", "@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@types/bluebird": "^3.5.38", "@types/lodash": "^4.14.194", "@types/mocha": "^10.0.1", "@types/node": "^24.0.0", "@types/portscanner": "^2.1.1", "@types/semver": "^7.5.0", "@types/source-map-support": "^0.x", "@types/teen_process": "^2.0.0", "@types/ws": "^8.5.4", "@xmldom/xmldom": "^0.x", "android-apidemos": "^4.1.1", "appium": "^2.0.0-rc.3", "chai": "^5.1.1", "chai-as-promised": "^8.0.0", "conventional-changelog-conventionalcommits": "^9.0.0", "mocha": "^11.0.1", "prettier": "^3.0.3", "rimraf": "^5.0.0", "semantic-release": "^24.0.0", "sharp": "^0.x", "sinon": "^21.0.0", "ts-node": "^10.9.1", "typescript": "^5.4.2", "unzipper": "^0.x", "webdriverio": "^9.0.9", "xpath": "^0.x"}, "peerDependencies": {"appium": "^2.4.1 || ^3.0.0-beta.0"}, "engines": {"node": ">=14", "npm": ">=8"}, "appium": {"driverName": "uiautomator2", "automationName": "UiAutomator2", "platformNames": ["Android"], "mainClass": "AndroidUiautomator2Driver", "scripts": {"reset": "scripts/reset.js"}, "doctor": {"checks": ["./build/lib/doctor/required-checks.js", "./build/lib/doctor/optional-checks.js"]}}, "typedoc": {"entryPoint": "index.js"}}