"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BIDI_COMMANDS = void 0;
const SUBSCRIPTION_REQUEST_PARAMS = /** @type {const} */ ({
    required: ['events'],
    optional: ['contexts'],
});
exports.BIDI_COMMANDS = ({
    session: {
        subscribe: {
            command: 'bidiSubscribe',
            params: SUBSCRIPTION_REQUEST_PARAMS,
        },
        unsubscribe: {
            command: 'bidiUnsubscribe',
            params: SUBSCRIPTION_REQUEST_PARAMS,
        },
        status: {
            command: 'bidiStatus',
            params: {},
        }
    },
    browsingContext: {
        navigate: {
            command: 'bidiNavigate',
            params: {
                required: ['context', 'url'],
                optional: ['wait'],
            },
        },
    },
});
// TODO add definitions for all bidi commands.
// spec link: https://w3c.github.io/webdriver-bidi/
//# sourceMappingURL=bidi-commands.js.map