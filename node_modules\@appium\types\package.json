{"name": "@appium/types", "version": "0.26.0", "description": "Various type declarations used across Appium", "keywords": ["automation", "javascript", "selenium", "webdriver", "ios", "android", "firefoxos", "testing"], "homepage": "https://appium.io", "bugs": {"url": "https://github.com/appium/appium/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/appium.git", "directory": "packages/types"}, "license": "Apache-2.0", "author": "https://github.com/appium", "types": "./build/lib/index.d.ts", "files": ["build", "lib", "index.js", "tsconfig.json", "!build/tsconfig.tsbuildinfo"], "scripts": {"build": "node ./scripts/generate-schema-types.js", "clean": "git checkout -- ./lib/appium-config.ts || true", "test:smoke": "node ./index.js", "test:types": "tsd"}, "dependencies": {"@appium/logger": "^1.7.1", "@appium/schema": "^0.8.1", "@appium/tsconfig": "^0.3.5", "type-fest": "4.41.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "publishConfig": {"access": "public"}, "gitHead": "c8fe4412525f7e1fa237813cf83fe7d98f0125eb"}