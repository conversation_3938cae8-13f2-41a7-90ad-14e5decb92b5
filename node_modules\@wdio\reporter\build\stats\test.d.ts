import RunnableStats from './runnable.js';
import type { Argument } from '../types.js';
export interface Test {
    type: 'test:start' | 'test:pass' | 'test:fail' | 'test:retry' | 'test:pending' | 'test:end' | 'test:skip';
    title: string;
    parent: string;
    fullTitle: string;
    pending: boolean;
    file?: string;
    body?: string;
    duration?: number;
    cid: string;
    specs: string[];
    uid: string;
    pendingReason?: string;
    error?: Error;
    errors?: Error[];
    retries?: number;
    argument?: string | Argument;
    state?: string;
}
interface Output {
    command: string;
    params: unknown[];
    method: 'PUT' | 'POST' | 'GET' | 'DELETE';
    endpoint: string;
    body: {};
    result: {
        value: string | null;
    };
    sessionId: string;
    cid: string;
    type: 'command' | 'result';
}
/**
 * TestStats class
 * captures data on a test.
 */
export default class TestStats extends RunnableStats {
    uid: string;
    cid: string;
    title: string;
    currentTest?: string;
    fullTitle: string;
    output: Output[];
    argument?: string | Argument;
    retries?: number;
    parent: string;
    /**
     * initial test state is pending
     * the state can change to the following: passed, skipped, failed
     */
    state: 'pending' | 'passed' | 'skipped' | 'failed';
    pendingReason?: string;
    errors?: Error[];
    error?: Error;
    body?: string;
    constructor(test: Test);
    pass(): void;
    skip(reason: string): void;
    fail(errors?: Error[]): void;
    private _stringifyDiffObjs;
}
export {};
//# sourceMappingURL=test.d.ts.map