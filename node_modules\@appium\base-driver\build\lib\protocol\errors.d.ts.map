{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../../lib/protocol/errors.js"], "names": [], "mappings": "AAsCA;IACE,mEASC;IAPC,gBAA4B;IAC5B,WAAuC;IAIvC,eAAyD;IACzD,iBAAuB;IAOzB,2BAEC;IAND,sBAEC;IAMD;;;;;OAKG;IACH,kBAJW,MAAM,GAAC,MAAM,GAEX,OAAO,eAAe,EAAE,wBAAwB,CAa5D;CACF;AAMD;IACE,sBAEC;IAED,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAUhB;CACF;AAED;IACE,sBAEC;IACD,uBAEC;IACD,oCAEC;IACD;;;OAGG;IACH,sBAFW,MAAM,EAWhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;;OAGG;IACH,sBAFW,MAAM,EAYhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;;OAGG;IACH,sBAFW,MAAM,EAWhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAWhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;;OAGG;IACH,sBAFW,MAAM,EAWhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD,iCAQC;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAUhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,uBAEC;IACD,oCAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,uBAEC;IACD,oCAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAWhB;CACF;AAED;IACE,sBAEC;IACD,uBAEC;IACD,oCAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAUhB;CACF;AAED;IACE,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAUhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,uBAEC;IACD,oCAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAWhB;CACF;AAED;IACE,sBAEC;IACD,uBAEC;IACD,oCAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,uBAEC;IACD,oCAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAWhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAUhB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;;OAGG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;CAAyD;AAEzD;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAShB;CACF;AAED;CAA8E;AAE9E;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,sBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD,0BAYC;CACF;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAShB;CACF;AAED;IACE,sBAEC;IACD;;;OAGG;IACH,sBAFW,MAAM,EAIhB;CACF;AAED;IACE,sBAEC;IACD;;;OAGG;IACH,sBAFW,MAAM,EAOhB;CACF;AAGD;CAOC;AACD;CAOC;AAED;IACE,sBAEC;IACD,oCAEC;IACD,uBAEC;IACD;;OAEG;IACH,kBAFW,MAAM,EAShB;CACF;AAkCD;IACE,uBAEC;IACD,qEAOC;IADC,2BAA4C;CAE/C;AAED;;;;;GAKG;AACH;IACE,2DA2BC;IATC,eAA4C;IAI1C,SAAiC;IAGjC,YAA8B;IAIlC,gCAYC;CACF;6BAyOa,MAAM,GAAG;IAAC,KAAK,EAAE,mBAAmB,CAAC;IAAC,MAAM,CAAC,EAAE,MAAM,CAAA;CAAE;;;;;;;YAYvD,MAAM;WACN,MAAM,GAAC,MAAM;aACb,MAAM;;AAxsCpB;IACE,8BAwBC;CACF;+CAjC4C,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4hChE;;;;;;GAMG;AACH,4BALqB,CAAC,SAAR,KAAM,OACT,GAAG,QACH,OAAO,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,GAC9B,GAAG,IAAI,CAAC,CAoBpB;AAjCD,kDAOC;AA4BD;;;;;GAKG;AACH,iDAJW,MAAM,UACN,MAAM,MAAO,GACZ,aAAa,CAYxB;AAED;;;;;;GAMG;AACH,2CALW,MAAM,WACN,MAAM,eACL,MAAM,OAAA,GACN,aAAa,CAaxB;AAWD;;;GAGG;AACH,4CAFW,aAAa,GAAC,YAAY,SAsCpC;AAED;;;GAGG;AACH,+CAFW,aAAa,0JAkCvB"}