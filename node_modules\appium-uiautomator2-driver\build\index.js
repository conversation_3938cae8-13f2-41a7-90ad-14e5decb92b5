"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AndroidUiautomator2Driver = void 0;
const source_map_support_1 = require("source-map-support");
(0, source_map_support_1.install)();
const driver_1 = require("./lib/driver");
Object.defineProperty(exports, "AndroidUiautomator2Driver", { enumerable: true, get: function () { return driver_1.AndroidUiautomator2Driver; } });
exports.default = driver_1.AndroidUiautomator2Driver;
//# sourceMappingURL=index.js.map