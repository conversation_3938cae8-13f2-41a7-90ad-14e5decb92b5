// Test reporter for stdout.
    // The only one supported by default is 'dot'
    // see also: https://webdriver.io/docs/dot-reporter<%
if (reporters.length) { %>
    reporters: [<%- reporters.map(reporter => {
        if (reporter === 'allure') {
            return `['${reporter}', {outputDir: 'allure-results'}]`
        } else {
            return `'${reporter}'`
        }
    }) %>],<% } else { %>
    // reporters: ['dot'],<%
    }
%>
