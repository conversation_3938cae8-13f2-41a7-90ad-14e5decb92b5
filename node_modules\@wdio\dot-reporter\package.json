{"name": "@wdio/dot-reporter", "version": "9.17.0", "description": "A WebdriverIO plugin to report in dot style", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/webdriverio/webdriverio/tree/main/packages/wdio-dot-reporter", "license": "MIT", "engines": {"node": ">=18.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/webdriverio/webdriverio.git", "directory": "packages/wdio-dot-reporter"}, "keywords": ["webdriver", "wdio", "wdio-reporter"], "bugs": {"url": "https://github.com/webdriverio/webdriverio/issues"}, "type": "module", "exports": {".": {"import": "./build/index.js", "types": "./build/index.d.ts"}}, "typeScriptVersion": "3.8.3", "dependencies": {"@wdio/reporter": "9.17.0", "@wdio/types": "9.16.2", "chalk": "^5.0.1"}, "devDependencies": {"@types/tmp": "^0.2.3", "tmp": "^0.2.1"}, "publishConfig": {"access": "public"}, "gitHead": "796df04f0f5c744d5ef4bafd3759995fa4829d6f"}