{"version": 3, "file": "npm.js", "sourceRoot": "", "sources": ["../../lib/npm.js"], "names": [], "mappings": ";AAAA,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEZ,gDAAwB;AACxB,+CAAiC;AACjC,+BAA0C;AAC1C,+CAAkC;AAClC,6BAAwB;AACxB,6CAA+B;AAC/B,iDAAmC;AACnC,gEAAuC;AAEvC;;;GAGG;AACU,QAAA,uBAAuB,GAAG,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAErF;;GAEG;AACU,QAAA,8BAA8B,GAAG,cAAI,CAAC,IAAI,CAAC,+BAAuB,EAAE,eAAe,CAAC,CAAC;AAElG;;GAEG;AACH,MAAa,GAAG;IACd;;;;OAIG;IACH,uBAAuB,CAAC,GAAG;QACzB,OAAO,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,sCAA8B,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,GAAG,EAAE;QACvC,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAC,GAAG,IAAI,CAAC;QAEnC,oDAAoD;QACpD,qCAAqC;QACrC,MAAM,mBAAmB,GAAG;YAC1B,GAAG,QAAQ;YACX,8CAA8C;YAC9C,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,IAAI,QAAQ,CAAC,KAAK;YAC3C,GAAG;SACJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAClB,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtB,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;QACtD,IAAI,MAAM,GAAG,KAAK,IAAI,EAAE,CAAC,MAAM,IAAA,mBAAI,EAAC,MAAM,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;QACvE,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,OAAO,GAAG,MAAM,CAAC;YACvB,MAAM,GAAG,KAAK,IAAI,EAAE,CAAC,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC;QAClD,CAAC;QAED,kFAAkF;QAClF,IAAI,GAAG,CAAC;QACR,IAAI,CAAC;YACH,MAAM,EAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAC,GAAG,MAAM,MAAM,EAAE,CAAC;YAC9C,GAAG,GAAG,EAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAC,CAAC;YAC7B,qEAAqE;YACrE,sEAAsE;YACtE,6BAA6B;YAC7B,IAAI,CAAC;gBACH,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC;YAAC,MAAM,CAAC,CAAA,CAAC;QACZ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,EACJ,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EAAE,EACX,IAAI,GAAG,IAAI,GACZ,GAAG,+CAA+C,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,GAAG,GAAG,IAAI,KAAK,CACnB,gBAAgB,IAAI,CAAC,IAAI,CACvB,GAAG,CACJ,sBAAsB,IAAI,iBAAiB,MAAM,CAAC,IAAI,EAAE,gBAAgB,MAAM,CAAC,IAAI,EAAE,EAAE,CACzF,CAAC;YACF,MAAM,GAAG,CAAC;QACZ,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG;QAC7B,IAAI,CAAC;YACH,OAAO,CACL,CACE,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC,EAAE;gBAC1C,IAAI,EAAE,IAAI;gBACV,GAAG;aACJ,CAAC,CACH,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI,CACvB,CAAC;QACJ,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnC,MAAM,GAAG,CAAC;YACZ,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,2BAA2B,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU;QACpD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,CAClB,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE;gBACzC,IAAI,EAAE,IAAI;gBACV,GAAG;aACJ,CAAC,CACH,CAAC,IAAI,CAAC;YACP,OAAO,IAAI,CAAC,gCAAgC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnC,MAAM,GAAG,CAAC;YACZ,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG;QACjB,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAED;;;;;;;;;OASG;IACH,gCAAgC,CAAC,UAAU,EAAE,WAAW;QACtD,IAAI,cAAc,GAAG,IAAI,CAAC;QAC1B,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;QACtF,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,UAAU,GAAG,CAAC,CAAC;QACrE,CAAC;QACD,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YACjF,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBACxB,SAAS;YACX,CAAC;YACD,iDAAiD;YACjD,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrC,SAAS;YACX,CAAC;YACD,gFAAgF;YAChF,IAAI,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxC,SAAS;YACX,CAAC;YACD,oFAAoF;YACpF,IAAI,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;gBACvC,SAAS;YACX,CAAC;YACD,yFAAyF;YACzF,kDAAkD;YAClD,IAAI,cAAc,KAAK,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxE,cAAc,GAAG,UAAU,CAAC;YAC9B,CAAC;QACH,CAAC;QACD,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;QAC3C,CAAC;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,EAAE,EAAC,OAAO,EAAE,WAAW,EAAC;QAC1D,kBAAkB;QAClB,IAAI,YAAY,CAAC;QACjB,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QACpD,IAAI,CAAC;YACH,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,OAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC1B,YAAY,GAAG,EAAE,CAAC;gBAClB,MAAM,OAAE,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAClF,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;QAClE,IAAI,CAAC,CAAC,MAAM,IAAA,yBAAmB,EAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACtC,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;gBACtC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClC,CAAC;YACD,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,GAAG,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QACzD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW,EAAE,UAAU,CAAC,EAAE;YAC7D,GAAG;YACH,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC;SAC5C,CAAC,CAAC;QAEH,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACb,2EAA2E;YAC3E,uBAAuB;YACvB,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,4EAA4E;QAC5E,2FAA2F;QAC3F,kGAAkG;QAClG,qBAAqB;QACrB,MAAM,WAAW,GAAG,IAAA,sBAAW,EAAC,GAAG,EAAE,GAAG,OAAO,eAAe,CAAC,CAAC;QAChE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,OAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACvD,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAChC,OAAO,EAAC,WAAW,EAAE,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,GAAG,EAAC,CAAC;QACvD,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,KAAK,CACb,6DAA6D;gBAC3D,uDAAuD;gBACvD,WAAW,CACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG;QAC7B,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,EAAE;YAClC,GAAG;YACH,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC;SAC5C,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,GAAG,EAAE;QACpC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE;YACjD,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;YAClB,IAAI,EAAE,IAAI;SACX,CAAC,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;CACF;AAjQD,kBAiQC;AAEY,QAAA,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;AAE7B;;;;;GAKG;AAEH;;;;;;GAMG;AAEH;;GAEG;AAEH;;;;GAIG"}