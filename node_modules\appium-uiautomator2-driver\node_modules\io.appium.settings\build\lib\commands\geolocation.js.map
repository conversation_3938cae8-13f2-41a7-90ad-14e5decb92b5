{"version": 3, "file": "geolocation.js", "sourceRoot": "", "sources": ["../../../lib/commands/geolocation.js"], "names": [], "mappings": ";;;;;AA+CA,wCAgFC;AASD,wCAiBC;AAgBD,0DAkDC;AA3ND,oDAAuB;AACvB,kDAIyB;AACzB,+CAA0C;AAC1C,wDAAyB;AACzB,4CAA0C;AAE1C,MAAM,wBAAwB,GAAG,EAAE,CAAC;AACpC,MAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,MAAM,oBAAoB,GAAG,iBAAiB,CAAC;AAC/C,MAAM,wBAAwB,GAAG;IAC/B,yEAAyE;IACzE,4EAA4E;CAC7E,CAAC;AAEF,MAAM,uBAAuB,GAAG,6CAA6C,CAAC;AAE9E;;;;;;;;;;;;;;;;GAgBG;AAEH;;;;;;;;GAQG;AACI,KAAK,UAAU,cAAc,CAAE,QAAQ,EAAE,UAAU,GAAG,KAAK;IAChE,MAAM,mBAAmB,GAAG,CAAC,SAAS,EAAE,UAAU,GAAG,IAAI,EAAE,EAAE;QAC3D,IAAI,gBAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;YACjC,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,GAAG,SAAS,mBAAmB,CAAC,CAAC;YACnD,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,gBAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,GAAG,SAAS,2CAA2C;gBACrE,IAAI,QAAQ,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IACF,MAAM,SAAS,GAAG,qBAAqB,CAAC,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;IAC3E,MAAM,QAAQ,GAAG,qBAAqB,CAAC,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC;IACzE,MAAM,QAAQ,GAAG,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACxD,MAAM,KAAK,GAAG,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD,MAAM,OAAO,GAAG,mBAAmB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IACtD,MAAM,QAAQ,GAAG,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACxD,IAAI,UAAU,EAAE,CAAC;QACf,uBAAuB;QACvB,MAAM,IAAI,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACnC,IAAI,CAAC,gBAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtB,CAAC;QACD,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;YACpE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,IAAI,CAAC,GAAG,gBAAgB,EAAE,CAAC,CAAC;YACnC,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,gBAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,IAAI,CAAC,GAAG,gBAAgB,EAAE,CAAC,CAAC;YACnC,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,IAAI,CAAC,GAAG,wBAAwB,EAAE,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC;QACD,MAAM,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,CAAC;QACtC,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QACvD,6EAA6E;QAC7E,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/F,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,GAAG;YACX,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,cAAc;YACxF,IAAI,EAAE,WAAW,EAAE,SAAS;YAC5B,IAAI,EAAE,UAAU,EAAE,QAAQ;SAC3B,CAAC;QACF,IAAI,CAAC,gBAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,gBAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YACpB,IAAI,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,oCAAoC,CAAC,CAAC;YAChE,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,gBAAC,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,uCAAuC,CAAC,CAAC;YACtE,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,gBAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvB,IAAI,gBAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,oCAAoC,CAAC,CAAC;YACnE,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,+BAAgB,CAAC,CAAC;QAC5B,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;AACH,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,cAAc;IAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC;QACvC,IAAI,EAAE,gCAAiB;QACvB,IAAI,EAAE,wCAAyB;KAChC,EAAE,sBAAsB,EAAE,IAAI,CAAC,CAAC;IAEjC,MAAM,KAAK,GAAG,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,oEAAoE,MAAM,EAAE,CAAC,CAAC;IAChG,CAAC;IACD,MAAM,QAAQ,GAAG;QACf,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;QAClB,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;QACnB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;KACnB,CAAC;IACF,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,sBAAU,EAAE,wBAAwB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC/E,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;;;;;;;GAaG;AACI,KAAK,UAAU,uBAAuB,CAAE,SAAS,GAAG,KAAK;IAC9D,MAAM,IAAI,CAAC,cAAc,CAAC,EAAC,uBAAuB,EAAE,IAAI,EAAC,CAAC,CAAC;IAE3D,IAAI,aAAa,CAAC;IAClB,IAAI,iBAAiB,CAAC;IAEtB,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;QAClB,MAAM,GAAG,GAAG;YACV,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW;YAClC,QAAQ,EAAE,IAAI,EAAE,oBAAoB;SACrC,CAAC;QACF,aAAa,GAAG,IAAI,yBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,eAAe,GAAG,+CAA+C,SAAS,cAAc;YAC5F,wFAAwF;YACxF,wFAAwF;YACxF,wBAAwB,CAAC;QAC3B,iBAAiB,GAAG,IAAI,kBAAC,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC5C,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YAEhE,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACnE,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC5E,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjF,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;QACH,MAAM,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED,MAAM,IAAI,CAAC,cAAc,CAAC;QACxB,IAAI,EAAE,gCAAiB;QACvB,IAAI,EAAE,wCAAyB;QAC/B,MAAM,EAAE,aAAa,EAAE,MAAM;KAC9B,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;IAE/B,IAAI,aAAa,IAAI,iBAAiB,EAAE,CAAC;QACvC,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,sBAAU,EAAE,iBAAiB,SAAS,sCAAsC,CAAC,CAAC;QAC7F,IAAI,CAAC;YACH,MAAM,iBAAiB,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAU,EAAE,sDAAsD;gBAC9E,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;gBAAS,CAAC;YACT,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;gBAC5B,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAU,EAAE,sFAAsF,CAAC,CAAC;IACpH,CAAC;AACH,CAAC"}