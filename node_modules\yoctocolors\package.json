{"name": "yoctocolors", "version": "2.1.1", "description": "The smallest and fastest command-line coloring package on the internet", "license": "MIT", "repository": "sindresorhus/yoctocolors", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "base.js", "base.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@jonahsnider/benchmark": "^5.0.3", "ansi-colors": "^4.1.3", "ava": "^6.1.3", "chalk": "^5.3.0", "cli-color": "^2.0.4", "colorette": "^2.0.20", "kleur": "^4.1.5", "nanocolors": "^0.2.13", "picocolors": "^1.0.1", "tsd": "^0.31.0", "xo": "^0.58.0"}, "ava": {"environmentVariables": {"FORCE_COLOR": "1"}}}