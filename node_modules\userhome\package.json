{"name": "userhome", "description": "A cross-platform path to the user's home", "version": "1.0.1", "homepage": "https://github.com/shama/userhome", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dontkry.com"}, "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/shama/userhome.git"}, "bugs": {"url": "https://github.com/shama/userhome/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "keywords": ["userhome", "user", "home", "homedir", "path"], "devDependencies": {"tape": "^5.9.0"}}