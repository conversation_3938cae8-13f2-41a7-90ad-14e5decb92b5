{"version": 3, "file": "command.d.ts", "sourceRoot": "", "sources": ["../../lib/command.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,eAAe,EAAE,6BAA6B,EAAC,MAAM,WAAW,CAAC;AACzE,OAAO,EAAC,MAAM,EAAE,aAAa,EAAC,MAAM,UAAU,CAAC;AAC/C,OAAO,EAAC,MAAM,EAAE,aAAa,EAAC,MAAM,UAAU,CAAC;AAC/C,OAAO,EAAC,YAAY,EAAC,MAAM,QAAQ,CAAC;AAEpC;;GAEG;AACH,MAAM,WAAW,aAAa;IAC5B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,6BAA6B,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC5E,QAAQ,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,6BAA6B,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC5E,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,KAAK,OAAO,GAAG,MAAM,GAAG,SAAS,CAAC;IACxE,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC;CAC9B;AACD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,MAAM,SAAS,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,MAAM,GAC/D,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAC5B,CAAC,SAAS,MAAM,GAChB,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAC5B,KAAK,CAAC;AAEV;;GAEG;AACH,MAAM,WAAW,eAAe,CAAC,CAAC,SAAS,MAAM;IAC/C,CAAC,GAAG,EAAE,MAAM,GAAG;QACb,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;KAC7B,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC5B;;OAEG;IACH,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;IAC9B;;OAEG;IACH,QAAQ,CAAC,aAAa,CAAC,EAAE,aAAa,CAAC;IACvC;;OAEG;IACH,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;IAC9B;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;CACxB;AAED;;GAEG;AACH,MAAM,WAAW,eAAe,CAAC,CAAC,SAAS,MAAM,EAAE,CAAC,SAAS,OAAO,GAAG,OAAO,CAC5E,SAAQ,aAAa;IACrB;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,IAAI,GAAG,MAAM,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;CAChG;AAED;;GAEG;AACH,MAAM,WAAW,eAAe,CAAC,CAAC,SAAS,MAAM,CAAE,SAAQ,aAAa;IACtE;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;CACtE;AAED;;GAEG;AACH,MAAM,WAAW,eAAe,CAAC,CAAC,SAAS,MAAM;IAC/C,CAAC,GAAG,EAAE,MAAM,GAAG;QACb,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;KAC7B,CAAC;CACH;AAED,MAAM,WAAW,gBAAgB,CAAC,GAAG,SAAS,MAAM,GAAG,MAAM;IAC3D,OAAO,EAAE,MAAM,eAAe,CAC5B,QAAQ,CAAC,GAAG,CAAC,EACb,GAAG,SAAS,MAAM,GAAG,aAAa,GAAG,GAAG,SAAS,MAAM,GAAG,aAAa,GAAG,KAAK,CAChF,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC,MAAM,CAAC,EAAE;QACP,QAAQ,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;QACjC,QAAQ,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;KAClC,CAAC;IAEF;;OAEG;IACH,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;IAE9B;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;CACxB;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB,CAAC,CAAC,SAAS,MAAM,CAAE,SAAQ,oBAAoB;IACpF,OAAO,EAAE,MAAM,eAAe,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;CAClD;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB,CAAC,CAAC,SAAS,MAAM,CAAE,SAAQ,oBAAoB;IACpF,OAAO,EAAE,MAAM,eAAe,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;CAClD;AAED;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,MAAM,GACtE,QAAQ,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,GACjD,CAAC,SAAS,MAAM,GAChB,QAAQ,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,GACjD,KAAK,CAAC;AAEV,MAAM,WAAW,gBAAgB;IAC/B,QAAQ,CAAC,EAAE,SAAS,MAAM,EAAE,CAAC;IAC7B,QAAQ,CAAC,EAAE,SAAS,MAAM,EAAE,CAAC;CAC9B;AAED,MAAM,WAAW,aAAc,SAAQ,oBAAoB;IACzD,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,gBAAgB,CAAC;CAC3B;AAED,MAAM,WAAW,aAAa;IAC5B,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC;CAC5B;AAED,MAAM,WAAW,aAAa;IAC5B,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC;CAC5B;AAGD,MAAM,WAAW,0BAA0B;IACzC,EAAE,EAAE,MAAM,CAAC;IACX,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;CACpB;AAED,MAAM,WAAW,cAAc;IAC7B,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;CACpB;AAED,MAAM,WAAW,0BAA2B,SAAQ,0BAA0B;IAC5E,IAAI,EAAE,SAAS,CAAC;IAChB,MAAM,EAAE,cAAc,CAAC;CACxB;AAED,MAAM,WAAW,wBAAyB,SAAQ,0BAA0B;IAC1E,IAAI,EAAE,OAAO,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,QAAQ,EAAE,OAAO,CAAC;CACnB;AAED,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,CAAC,EAAE,oBAAoB,EAAE,CAAC;CACjC;AAED,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,CAAC,MAAM,EAAE,MAAM,GAAG,eAAe,CAAC;CACnC;AAED,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;IAC/C;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;IACjD;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC,CAAC;CACpE;AAED,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,QAAQ,EAAE,OAAO,CAAC;CACnB;AAED,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,CAAC,EAAE,oBAAoB,EAAE,CAAC;CACjC;AAED,MAAM,WAAW,0BAA0B;IACzC,CAAC,IAAI,EAAE,MAAM,GAAG,eAAe,CAAC;CACjC;AAED,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC;IACjD;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC;IACnD;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC,CAAC;CACtE;AAED,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,IAAI,CAAC,EAAE,eAAe,CAAC;IACvB;;OAEG;IACH,IAAI,CAAC,EAAE,eAAe,CAAC;CACxB;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,MAAM,EAAE,wBAAwB,CAAC;IACjC;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;CACpD;AAED,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,IAAI,CAAC,EAAE,iBAAiB,CAAC;CAC1B"}