{"version": 3, "file": "appium-config-schema.js", "sourceRoot": "", "sources": ["../../lib/appium-config-schema.js"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEU,QAAA,sBAAsB,GAAwB,CAAC;IAC1D,OAAO,EAAE,wCAAwC;IACjD,oBAAoB,EAAE,KAAK;IAC3B,WAAW,EAAE,yCAAyC;IACtD,UAAU,EAAE;QACV,OAAO,EAAE;YACP,WAAW,EAAE,+BAA+B;YAC5C,OAAO,EACL,sGAAsG;YACxG,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,KAAK;SACd;QACD,MAAM,EAAE;YACN,oBAAoB,EAAE,KAAK;YAC3B,WAAW,EAAE,+CAA+C;YAC5D,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,gBAAgB,EAAE,CAAC,GAAG,CAAC;oBACvB,OAAO,EAAE,SAAS;oBAClB,WAAW,EAAE,8CAA8C;oBAC3D,KAAK,EAAE,gBAAgB;oBACvB,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,QAAQ;4BACd,MAAM,EAAE,UAAU;yBACnB;wBACD;4BACE,IAAI,EAAE,QAAQ;4BACd,MAAM,EAAE,MAAM;yBACf;qBACF;iBACF;gBACD,YAAY,EAAE;oBACZ,WAAW,EACT,8EAA8E;oBAChF,KAAK,EAAE,mBAAmB;oBAC1B,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,KAAK;iBACf;gBACD,gBAAgB,EAAE;oBAChB,oBAAoB,EAAE,KAAK;oBAC3B,OAAO,EAAE,EAAE;oBACX,WAAW,EACT,iUAAiU;oBACnU,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;qBACf;oBACD,KAAK,EAAE,uBAAuB;oBAC9B,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,IAAI;iBAClB;gBACD,WAAW,EAAE;oBACX,gBAAgB,EAAE,CAAC,IAAI,CAAC;oBACxB,OAAO,EAAE,EAAE;oBACX,WAAW,EACT,+EAA+E;oBACjF,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,QAAQ;iBACf;gBACD,kBAAkB,EAAE;oBAClB,gBAAgB,EAAE,CAAC,IAAI,CAAC;oBACxB,WAAW,EAAE,kDAAkD;oBAC/D,KAAK,EAAE,yBAAyB;oBAChC,IAAI,EAAE,QAAQ;iBACf;gBACD,eAAe,EAAE;oBACf,gBAAgB,EAAE,CAAC,IAAI,CAAC;oBACxB,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,yCAAyC;oBACtD,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE,sBAAsB;oBAC7B,IAAI,EAAE,SAAS;iBAChB;gBACD,mBAAmB,EAAE;oBACnB,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,gEAAgE;oBAC7E,KAAK,EAAE,0BAA0B;oBACjC,IAAI,EAAE,SAAS;iBAChB;gBACD,sBAAsB,EAAE;oBACtB,QAAQ,EAAE,MAAM;oBAChB,gBAAgB,EAAE,CAAC,IAAI,CAAC;oBACxB,WAAW,EACT,kMAAkM;oBACpM,KAAK,EAAE,6BAA6B;oBACpC,IAAI,EAAE,QAAQ;iBACf;gBACD,eAAe,EAAE;oBACf,QAAQ,EAAE,uCAAuC;oBACjD,oBAAoB,EAAE,KAAK;oBAC3B,OAAO,EAAE,EAAE;oBACX,WAAW,EACT,uWAAuW;oBACzW,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;qBACf;oBACD,KAAK,EAAE,sBAAsB;oBAC7B,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,IAAI;iBAClB;gBACD,MAAM,EAAE;oBACN,WAAW,EACT,+EAA+E;oBACjF,UAAU,EAAE,+DAA+D,CAAC,CAAC,EAAE,CAAC;oBAChF,KAAK,EAAE,eAAe;oBACtB,IAAI,EAAE,QAAQ;iBACf;gBACD,oBAAoB,EAAE;oBACpB,gBAAgB,EAAE,CAAC,IAAI,CAAC;oBACxB,OAAO,EAAE,GAAG;oBACZ,WAAW,EACT,6GAA6G;wBAC7G,sDAAsD;oBACxD,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE,2BAA2B;oBAClC,IAAI,EAAE,SAAS;iBAChB;gBACD,iBAAiB,EAAE;oBACjB,OAAO,EAAE,IAAI;oBACb,WAAW,EACT,0GAA0G;wBAC1G,mFAAmF;wBACnF,uGAAuG;wBACvG,gHAAgH;oBAClH,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE,wBAAwB;oBAC/B,IAAI,EAAE,SAAS;iBAChB;gBACD,gBAAgB,EAAE;oBAChB,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,mCAAmC;oBAChD,KAAK,EAAE,uBAAuB;oBAC9B,IAAI,EAAE,SAAS;iBAChB;gBACD,GAAG,EAAE;oBACH,gBAAgB,EAAE,CAAC,GAAG,CAAC;oBACvB,aAAa,EAAE,SAAS;oBACxB,WAAW,EAAE,mCAAmC;oBAChD,KAAK,EAAE,YAAY;oBACnB,IAAI,EAAE,QAAQ;iBACf;gBACD,aAAa,EAAE;oBACb,WAAW,EAAE,iCAAiC;oBAC9C,KAAK,EAAE,oBAAoB;oBAC3B,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAC,IAAI,EAAE,mBAAmB,EAAC;oBAClC,oBAAoB,EAAE,MAAM;iBAC7B;gBACD,WAAW,EAAE;oBACX,aAAa,EAAE,UAAU;oBACzB,OAAO,EAAE,OAAO;oBAChB,WAAW,EAAE,4BAA4B;oBACzC,IAAI,EAAE;wBACJ,MAAM;wBACN,YAAY;wBACZ,WAAW;wBACX,WAAW;wBACX,YAAY;wBACZ,MAAM;wBACN,YAAY;wBACZ,WAAW;wBACX,WAAW;wBACX,YAAY;wBACZ,OAAO;wBACP,aAAa;wBACb,YAAY;wBACZ,YAAY;wBACZ,aAAa;wBACb,OAAO;wBACP,aAAa;wBACb,YAAY;wBACZ,YAAY;wBACZ,aAAa;qBACd;oBACD,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,QAAQ;iBACf;gBACD,YAAY,EAAE;oBACZ,aAAa,EAAE,WAAW;oBAC1B,OAAO,EAAE,MAAM;oBACf,WAAW,EAAE,oCAAoC;oBACjD,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC;oBACrC,KAAK,EAAE,mBAAmB;oBAC1B,IAAI,EAAE,QAAQ;iBACf;gBACD,eAAe,EAAE;oBACf,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,oCAAoC;oBACjD,KAAK,EAAE,sBAAsB;oBAC7B,IAAI,EAAE,SAAS;iBAChB;gBACD,eAAe,EAAE;oBACf,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,mCAAmC;oBAChD,KAAK,EAAE,sBAAsB;oBAC7B,IAAI,EAAE,SAAS;iBAChB;gBACD,2BAA2B,EAAE;oBAC3B,OAAO,EAAE,CAAC;oBACV,WAAW,EACT,oFAAoF;oBACtF,KAAK,EAAE,kCAAkC;oBACzC,IAAI,EAAE,QAAQ;iBACf;gBACD,2BAA2B,EAAE;oBAC3B,OAAO,EAAE,CAAC;oBACV,WAAW,EACT,oFAAoF;oBACtF,KAAK,EAAE,kCAAkC;oBACzC,IAAI,EAAE,QAAQ;iBACf;gBACD,iBAAiB,EAAE;oBACjB,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,uEAAuE;oBACpF,KAAK,EAAE,wBAAwB;oBAC/B,IAAI,EAAE,SAAS;iBAChB;gBACD,gBAAgB,EAAE;oBAChB,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,qEAAqE;oBAClF,KAAK,EAAE,uBAAuB;oBAC9B,IAAI,EAAE,SAAS;iBAChB;gBACD,UAAU,EAAE;oBACV,QAAQ,EACN,mFAAmF;oBACrF,WAAW,EACT,uHAAuH;oBACzH,KAAK,EAAE,mBAAmB;oBAC1B,IAAI,EAAE,QAAQ;iBACf;gBACD,MAAM,EAAE;oBACN,WAAW,EACT,+EAA+E;oBACjF,UAAU,EAAE,+DAA+D,CAAC,CAAC,EAAE,CAAC;oBAChF,KAAK,EAAE,eAAe;oBACtB,IAAI,EAAE,QAAQ;iBACf;gBACD,IAAI,EAAE;oBACJ,gBAAgB,EAAE,CAAC,GAAG,CAAC;oBACvB,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,mBAAmB;oBAChC,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE,aAAa;oBACpB,IAAI,EAAE,SAAS;iBAChB;gBACD,kBAAkB,EAAE;oBAClB,OAAO,EAAE,KAAK;oBACd,WAAW,EACT,mVAAmV;oBACrV,KAAK,EAAE,yBAAyB;oBAChC,IAAI,EAAE,SAAS;oBACf,aAAa,EAAE,wBAAwB;iBACxC;gBACD,kBAAkB,EAAE;oBAClB,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,qGAAqG;wBAChH,2FAA2F;oBAC7F,KAAK,EAAE,kDAAkD;oBACzD,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,CAAC;iBACX;gBACD,kBAAkB,EAAE;oBAClB,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,uCAAuC;oBACpD,KAAK,EAAE,yBAAyB;oBAChC,IAAI,EAAE,SAAS;iBAChB;gBACD,eAAe,EAAE;oBACf,WAAW,EACT,2FAA2F;oBAC7F,KAAK,EAAE,iBAAiB;oBACxB,aAAa,EAAE,oBAAoB;oBACnC,IAAI,EAAE,QAAQ;iBACf;gBACD,cAAc,EAAE;oBACd,WAAW,EACT,2FAA2F;oBAC7F,KAAK,EAAE,gBAAgB;oBACvB,aAAa,EAAE,YAAY;oBAC3B,IAAI,EAAE,QAAQ;iBACf;gBACD,aAAa,EAAE;oBACb,OAAO,EAAE,KAAK;oBACd,WAAW,EACT,oHAAoH;oBACtH,KAAK,EAAE,oBAAoB;oBAC3B,IAAI,EAAE,SAAS;iBAChB;gBACD,GAAG,EAAE;oBACH,aAAa,EAAE,QAAQ;oBACvB,WAAW,EACT,8HAA8H;oBAChI,KAAK,EAAE,YAAY;oBACnB,IAAI,EAAE,QAAQ;iBACf;gBACD,WAAW,EAAE;oBACX,WAAW,EACT,+GAA+G;oBACjH,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,QAAQ;iBACf;gBACD,aAAa,EAAE;oBACb,oBAAoB,EAClB,gPAAgP;oBAClP,OAAO,EAAE,EAAE;oBACX,WAAW,EACT,qFAAqF;oBACvF,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;qBACf;oBACD,KAAK,EAAE,oBAAoB;oBAC3B,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,IAAI;iBAClB;gBACD,aAAa,EAAE;oBACb,oBAAoB,EAClB,2OAA2O;oBAC7O,OAAO,EAAE,EAAE;oBACX,WAAW,EACT,gHAAgH;oBAClH,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;qBACf;oBACD,KAAK,EAAE,oBAAoB;oBAC3B,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,IAAI;iBAClB;gBACD,OAAO,EAAE;oBACP,QAAQ,EACN,uFAAuF;oBACzF,gBAAgB,EAAE,CAAC,GAAG,CAAC;oBACvB,WAAW,EAAE,4CAA4C;oBACzD,MAAM,EAAE,KAAK;oBACb,KAAK,EAAE,gBAAgB;oBACvB,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,KAAK,EAAE,eAAe;YACtB,IAAI,EAAE,QAAQ;SACf;KACF;IACD,KAAK,EAAE,sBAAsB;IAC7B,IAAI,EAAE,QAAQ;IACd,KAAK,EAAE;QACL,aAAa,EAAE;YACb,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,4BAA4B;YACzC,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,WAAW,EAAE,eAAe;oBAC5B,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,CAAC;YAClB,GAAG,EAAE;gBACH,QAAQ,EAAE,CAAC,SAAS,CAAC;aACtB;SACF;QACD,cAAc,EAAE;YACd,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,oCAAoC;YACjD,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,WAAW,EAAE,wBAAwB;oBACrC,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,OAAO;iBAChB;aACF;YACD,QAAQ,EAAE,CAAC,SAAS,CAAC;YACrB,GAAG,EAAE;gBACH,QAAQ,EAAE,CAAC,MAAM,CAAC;aACnB;SACF;QACD,SAAS,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,oBAAoB;YACjC,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,QAAQ,EAAE;4BACR,WAAW,EAAE,qCAAqC;4BAClD,IAAI,EAAE,QAAQ;4BACd,OAAO,EAAE,YAAY;yBACtB;wBACD,KAAK,EAAE;4BACL,WAAW,EACT,qIAAqI;4BACvI,IAAI,EAAE,QAAQ;4BACd,OAAO,EAAE,0BAA0B;yBACpC;qBACF;iBACF;gBACD;oBACE,KAAK,EAAE,CAAC,EAAC,IAAI,EAAE,uBAAuB,EAAC,EAAE,EAAC,IAAI,EAAE,wBAAwB,EAAC,CAAC;iBAC3E;aACF;SACF;KACF;CACF,CAAC,CAAC"}