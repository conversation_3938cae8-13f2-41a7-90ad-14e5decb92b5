{"version": 3, "file": "fs.js", "sourceRoot": "", "sources": ["../../lib/fs.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFH,oCASC;AAwED,0CAKC;AAqFD,sCAMC;AAjQD,6CAAmC;AACnC,6CAA+B;AAC/B,oDAAuB;AACvB,0DAA6B;AAC7B,sDAA8B;AAC9B,wDAAqE;AAErE,gDAAwB;AACxB,2CAQqB;AACrB,mCAAsC;AACtC,qCAAmC;AAEnC,+CAAkC;AAElC,MAAM,GAAG,GAAG,IAAA,kBAAS,EAAC,IAAI,CAAC,CAAC;AAE5B;;;;GAIG;AACU,QAAA,UAAU,GAAG,gBAAC,CAAC,OAAO,CAAC,iBAAO,CAAC,CAAC;AAE7C;;;;GAIG;AACU,QAAA,aAAa,GAAiC,gBAAC,CAAC,YAAY,CACvE,cAAI,CAAC,SAAS,EACd,EAAC,MAAM,EAAE,CAAC,EAAC,EACX,SAAS,CACV,CAAC;AAEF;;;;;GAKG;AACU,QAAA,cAAc,GAAiC,gBAAC,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,EAAE;IAC1F,MAAM,EAAE,CAAC;CACV,CAAC,CAAC;AAEH;;;;GAIG;AACU,QAAA,aAAa,GAAiC,gBAAC,CAAC,YAAY,CACvE,IAAI,CAAC,SAAS,EACd,CAAC,EACD,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,QAAQ,GAAG,gBAAC,CAAC,OAAO,CAAC,KAAK,EAAE,QAAgB,EAAE,EAAE,CACpD,cAAI,CAAC,KAAK,CAAC,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;IAC9C,YAAY,EAAE,KAAK;IACnB,QAAQ,EAAE,QAAQ;CACnB,CAAC,CACH,CAAC;AAEF;;;;;;GAMG;AACI,KAAK,UAAU,YAAY,CAChC,QAAgB,EAChB,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE;IAEnB,MAAM,MAAM,GAAG,MAAM,IAAA,kBAAU,EAAC,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO;IACT,CAAC;IACD,OAAO,mBAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACrC,CAAC;AAED;;;;;;GAMG;AACU,QAAA,aAAa,GAAG,gBAAC,CAAC,OAAO,CAAC,gBAAC,CAAC,OAAO,CAAC,YAAY,EAAE,2BAAe,CAAC,CAAC,CAAC;AAgBjF,KAAK,UAAU,YAAY,CACzB,GAAW,EACX,SAAmB;IAEnB,MAAM,MAAM,GAAG,MAAM,IAAA,kBAAU,EAAC,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,qBAAa,CACrB,oBAAoB,6BAAiB,SAAS,GAAG,8CAA8C,CAChG,CAAC;IACJ,CAAC;IACD,MAAM,OAAO,GAAG,mBAAI,CAAC,IAAI,CAAC,MAAM,EAAE,6BAAiB,CAAC,CAAC;IACrD,GAAG,CAAC,KAAK,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;IACjD,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,GAAG,GAAG,MAAM,IAAA,kBAAO,EAAC,EAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAC,CAAC,CAAC;QACpD,OAAO,EAAC,GAAG,EAAE,OAAO,EAAC,CAAC;IACxB,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,GAAG,MAAM,IAAA,kBAAO,EAAC,EAAC,GAAG,EAAE,MAAM,EAAC,CAAC,CAAC;QACzC,OAAO,EAAC,GAAG,EAAE,OAAO,EAAC,CAAC;IACxB,CAAC;AACH,CAAC;AAED;;GAEG;AACU,QAAA,eAAe,GAAG,gBAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEvD;;GAEG;AACU,QAAA,SAAS,GAAG,gBAAC,CAAC,OAAO,CAChC,KAAK,EAAuB,QAAgB,EAAc,EAAE,CAC1D,KAAK,CAAC,KAAK,CAAC,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CACnD,CAAC;AAEF;;GAEG;AACU,QAAA,QAAQ,GAAG,gBAAC,CAAC,OAAO,CAC/B,KAAK,EAAuB,QAAgB,EAAc,EAAE,CAC1D,IAAI,CAAC,KAAK,CAAC,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAClD,CAAC;AAEF;;;;GAIG;AACH,SAAgB,eAAe,CAAC,QAAgB,EAAE,OAAkB;IAClE,MAAM,IAAI,GAAW,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IAC3F,OAAO,YAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE;QAClC,QAAQ,EAAE,MAAM;KACjB,CAAC,CAAC;AACL,CAAC;AAID;;GAEG;AACH,MAAM,WAAW,GAAG,gBAAC,CAAC,OAAO,CAAC,YAAE,CAAC,KAAsB,CAAC,CAAC;AAEzD;;GAEG;AACU,QAAA,QAAQ,GAAG,gBAAC,CAAC,OAAO,CAAC,WAAW,EAAE,oBAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;AAE1E;;GAEG;AACH,MAAM,WAAW,GAAG,gBAAC,CAAC,OAAO,CAAC,WAAW,EAAE,uBAAW,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;AAEzE;;GAEG;AACH,MAAM,YAAY,GAAG,gBAAC,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,uBAAW,GAAG,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;AAEhF;;GAEG;AACU,QAAA,iBAAiB,GAAG,gBAAC,CAAC,OAAO,CAAC,KAAK,IAAsB,EAAE;IACtE,sBAAsB;IACtB,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,uBAAW,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;IACnE,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IACD,sDAAsD;IACtD,MAAM,UAAU,GAAG,MAAM,IAAA,kBAAU,GAAE,CAAC;IACtC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,CAAC;QACH,MAAM,IAAA,mBAAI,EAAC,UAAU,EAAE,CAAC,IAAI,EAAE,uBAAW,CAAC,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,QAAQ,GAAG,gBAAC,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;IAC3C,sBAAsB;IACtB,IAAI,QAAQ,GAAG,MAAM,WAAW,CAAC,qBAAS,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;IAC7D,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,uCAAuC;IACvC,MAAM,UAAU,GAAG,MAAM,IAAA,kBAAU,GAAE,CAAC;IACtC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO;IACT,CAAC;IACD,IAAI,CAAC;QACH,sCAAsC;QACtC,uCAAuC;QACvC,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAA,mBAAI,EAAC,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;QACvE,IAAI,MAAM,EAAE,CAAC;YACX,QAAQ,GAAG,mBAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YACnD,IAAI,MAAM,YAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAAC,MAAM,CAAC,CAAA,CAAC;AACZ,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACU,QAAA,UAAU,GAAG,gBAAC,CAAC,OAAO,CACjC,KAAK,IAA4B,EAAE,CAAC,CAAC,MAAM,YAAY,EAAE,CAAC,IAAI,CAAC,MAAM,WAAW,EAAE,CAAC,CACpF,CAAC;AAEF;;GAEG;AACI,KAAK,UAAU,aAAa,CAAC,UAAmB;IACrD,MAAM,eAAe,GAAG,UAAU,IAAI,CAAC,MAAM,IAAA,kBAAU,GAAE,CAAC,CAAC;IAC3D,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,MAAM,IAAI,qBAAa,CAAC,kCAAsB,CAAC,CAAC;IAClD,CAAC;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;;;;;;GAOG;AACU,QAAA,aAAa,GAAG,gBAAC,CAAC,OAAO,CACpC,KAAK,EAAE,QAAgB,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,EAAsB,EAAE;IAClE,IAAI,SAAS,GAAG,CAAC,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAc,CAAC;IACxD,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;QACvB,SAAS,CAAC,QAAQ,GAAG,mBAAI,CAAC,OAAO,CAAC,GAAG,EAAE,mBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;IACrF,CAAC;IACD,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,WAAW,GAAuB,mBAAI,CAAC,OAAO,CAAC,mBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;QAC9F,OAAO,WAAW,EAAE,CAAC;YACnB,MAAM,UAAU,GAAG,CAAC,MAAM,QAAQ,CAAC,WAAW,CAAC,CAAc,CAAC;YAC9D,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,UAAU,CAAC,QAAQ,GAAG,mBAAI,CAAC,OAAO,CAAC,mBAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACnF,GAAG,CAAC,KAAK,CAAC,yBAAyB,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC5D,CAAC;YACD,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,UAAU,CAAC,QAAQ,GAAG,mBAAI,CAAC,OAAO,CAAC,mBAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACnF,GAAG,CAAC,KAAK,CAAC,yBAAyB,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC5D,CAAC;YACD,SAAS,GAAG,gBAAC,CAAC,YAAY,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAClD,WAAW,GAAG,UAAU,CAAC,OAAO;gBAC9B,CAAC,CAAC,mBAAI,CAAC,OAAO,CAAC,mBAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC;gBAC7D,CAAC,CAAC,SAAS,CAAC;QAChB,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CACF,CAAC"}