{"version": 3, "file": "esm.js", "sourceRoot": "", "sources": ["../src/esm.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,mCAAkE;AAClE,6BAA8G;AAC9G,+BAA8B;AAC9B,iCAAgC;AAChC,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC,6CAA6C,CAAC,CAAA;AAEhF,4FAA4F;AAE5F,SAAgB,yBAAyB,CAAE,IAAsB;IAC/D,sEAAsE;IACtE,MAAM,cAAc,GAAG,gBAAQ,iCAC1B,IAAI,KACP,qBAAqB,EAAE,IAAI,IAC3B,CAAA;IAEF,yGAAyG;IACzG,MAAM,yBAAyB,GAAG,aAAa,iCAC1C,qBAAa,CAAC,cAAc,CAAC,MAAM,CAAC,KACvC,YAAY,EAAE,cAAc,CAAC,OAAO,CAAC,YAAY,IACjD,CAAA;IAEF,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;IAE9C,SAAS,6BAA6B,CAAE,MAA0B;QAChE,mGAAmG;QACnG,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAA;QAC3B,OAAO,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,OAAO,CAAA;IAClD,CAAC;IAED,SAAe,OAAO,CAAE,SAAiB,EAAE,OAA4B,EAAE,cAA8B;;YACrG,MAAM,KAAK,GAAG,GAAS,EAAE;gBACvB,MAAM,CAAC,GAAG,MAAM,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;gBAClE,OAAO,CAAC,CAAA;YACV,CAAC,CAAA,CAAA;YAED,MAAM,MAAM,GAAG,WAAQ,CAAC,SAAS,CAAC,CAAA;YAClC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAA;YAE/C,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE;gBAC1C,OAAO,KAAK,EAAE,CAAA;aACf;YAED,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,OAAO,EAAE;gBAC7C,OAAO,KAAK,EAAE,CAAA;aACf;YAED,8DAA8D;YAC9D,IAAI,QAAQ,EAAE;gBACZ,oFAAoF;gBACpF,OAAO,KAAK,EAAE,CAAA;aACf;YAED,sCAAsC;YAEtC,OAAO,yBAAyB,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;QACrF,CAAC;KAAA;IAGD,SAAe,SAAS,CAAE,GAAW,EAAE,OAAW,EAAE,gBAAkC;;YACpF,MAAM,KAAK,GAAG,CAAC,cAAsB,GAAG,EAAE,EAAE,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAA;YAErG,MAAM,MAAM,GAAG,WAAQ,CAAC,GAAG,CAAC,CAAA;YAE5B,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE;gBAC1C,OAAO,KAAK,EAAE,CAAA;aACf;YAED,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAA;YAC3B,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE,2DAA2D,CAAC,CAAA;YAEtF,MAAM,UAAU,GAAG,mBAAa,CAAC,GAAG,CAAC,CAAA;YAErC,sGAAsG;YACtG,MAAM,GAAG,GAAG,cAAO,CAAC,UAAU,CAAC,CAAA;YAC/B,IAAI,GAAG,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBACxD,OAAO,KAAK,CAAC,YAAS,CAAC,mBAAa,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;aAC3D;YAED,OAAO,KAAK,EAAE,CAAA;QAChB,CAAC;KAAA;IAED,SAAe,eAAe,CAAE,MAAuB,EAAE,OAAsC,EAAE,sBAA8C;;YAC7I,MAAM,KAAK,GAAG,GAAG,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAA;YAEnF,MAAM,cAAc,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAEpF,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAA;YACvB,MAAM,MAAM,GAAG,WAAQ,CAAC,GAAG,CAAC,CAAA;YAE5B,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE;gBAC1C,OAAO,KAAK,EAAE,CAAA;aACf;YACD,MAAM,UAAU,GAAG,mBAAa,CAAC,GAAG,CAAC,CAAA;YAErC,IAAI,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBACtC,OAAO,KAAK,EAAE,CAAA;aACf;YAED,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAA;YAEpE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAA;QAC9B,CAAC;KAAA;AACH,CAAC;AA9FD,8DA8FC", "sourcesContent": ["import { register, getExtensions, RegisterOptions } from './index'\nimport { parse as parseUrl, format as formatUrl, UrlWithStringQuery, fileURLToPath, pathToFileURL } from 'url'\nimport { extname } from 'path'\nimport * as assert from 'assert'\nconst { createResolve } = require('../dist-raw/node-esm-resolve-implementation')\n\n// Note: On Windows, URLs look like this: file:///D:/dev/@TypeStrong/ts-node-examples/foo.ts\n\nexport function registerAndCreateEsmHooks (opts?: RegisterOptions) {\n  // Automatically performs registration just like `-r ts-node/register`\n  const tsNodeInstance = register({\n    ...opts,\n    experimentalEsmLoader: true\n  })\n\n  // Custom implementation that considers additional file extensions and automatically adds file extensions\n  const nodeResolveImplementation = createResolve({\n    ...getExtensions(tsNodeInstance.config),\n    preferTsExts: tsNodeInstance.options.preferTsExts\n  })\n\n  return { resolve, getFormat, transformSource }\n\n  function isFileUrlOrNodeStyleSpecifier (parsed: UrlWithStringQuery) {\n    // We only understand file:// URLs, but in node, the specifier can be a node-style `./foo` or `foo`\n    const { protocol } = parsed\n    return protocol === null || protocol === 'file:'\n  }\n\n  async function resolve (specifier: string, context: {parentURL: string}, defaultResolve: typeof resolve): Promise<{url: string}> {\n    const defer = async () => {\n      const r = await defaultResolve(specifier, context, defaultResolve)\n      return r\n    }\n\n    const parsed = parseUrl(specifier)\n    const { pathname, protocol, hostname } = parsed\n\n    if (!isFileUrlOrNodeStyleSpecifier(parsed)) {\n      return defer()\n    }\n\n    if (protocol !== null && protocol !== 'file:') {\n      return defer()\n    }\n\n    // Malformed file:// URL?  We should always see `null` or `''`\n    if (hostname) {\n      // TODO file://./foo sets `hostname` to `'.'`.  Perhaps we should special-case this.\n      return defer()\n    }\n\n    // pathname is the path to be resolved\n\n    return nodeResolveImplementation.defaultResolve(specifier, context, defaultResolve)\n  }\n\n  type Format = 'builtin'\t| 'commonjs' | 'dynamic' | 'json' | 'module' | 'wasm'\n  async function getFormat (url: string, context: {}, defaultGetFormat: typeof getFormat): Promise<{format: Format}> {\n    const defer = (overrideUrl: string = url) => defaultGetFormat(overrideUrl, context, defaultGetFormat)\n\n    const parsed = parseUrl(url)\n\n    if (!isFileUrlOrNodeStyleSpecifier(parsed)) {\n      return defer()\n    }\n\n    const { pathname } = parsed\n    assert(pathname !== null, 'ESM getFormat() hook: URL should never have null pathname')\n\n    const nativePath = fileURLToPath(url)\n\n    // If file has .ts, .tsx, or .jsx extension, then ask node how it would treat this file if it were .js\n    const ext = extname(nativePath)\n    if (ext !== '.js' && !tsNodeInstance.ignored(nativePath)) {\n      return defer(formatUrl(pathToFileURL(nativePath + '.js')))\n    }\n\n    return defer()\n  }\n\n  async function transformSource (source: string | Buffer, context: {url: string, format: Format}, defaultTransformSource: typeof transformSource): Promise<{source: string | Buffer}> {\n    const defer = () => defaultTransformSource(source, context, defaultTransformSource)\n\n    const sourceAsString = typeof source === 'string' ? source : source.toString('utf8')\n\n    const { url } = context\n    const parsed = parseUrl(url)\n\n    if (!isFileUrlOrNodeStyleSpecifier(parsed)) {\n      return defer()\n    }\n    const nativePath = fileURLToPath(url)\n\n    if (tsNodeInstance.ignored(nativePath)) {\n      return defer()\n    }\n\n    const emittedJs = tsNodeInstance.compile(sourceAsString, nativePath)\n\n    return { source: emittedJs }\n  }\n}\n"]}