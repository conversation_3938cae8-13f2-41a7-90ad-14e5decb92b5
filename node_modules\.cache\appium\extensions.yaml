drivers:
  uiautomator2:
    automationName: UiAutomator2
    platformNames:
      - Android
    mainClass: AndroidUiautomator2Driver
    scripts:
      reset: scripts/reset.js
    doctor:
      checks:
        - ./build/lib/doctor/required-checks.js
        - ./build/lib/doctor/optional-checks.js
    pkgName: appium-uiautomator2-driver
    version: 4.2.5
    appiumVersion: ^2.4.1 || ^3.0.0-beta.0
    installType: npm
    installSpec: appium-uiautomator2-driver@4.2.5
    installPath: C:\Users\<USER>\Documents\taller\node_modules\appium-uiautomator2-driver
plugins: {}
schemaRev: 4
