/**
 * retrieved from https://github.com/sindresorhus/ky/blob/3ba40cc6333cf1847c02c51744e22ab7c04407f5/source/utils/normalize.ts#L10
 */
export declare const RETRYABLE_STATUS_CODES: number[];
/**
 * retrieved from https://github.com/sindresorhus/got/blob/89b7fdfd4e7ea4e76258f50b70ae8a1d2aea8125/source/core/options.ts#L392C1-L399C37
 */
export declare const RETRYABLE_ERROR_CODES: string[];
export declare const REG_EXPS: {
    commandName: RegExp;
    execFn: RegExp;
};
//# sourceMappingURL=constants.d.ts.map