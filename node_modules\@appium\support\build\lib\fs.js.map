{"version": 3, "file": "fs.js", "sourceRoot": "", "sources": ["../../lib/fs.js"], "names": [], "mappings": ";AAAA,YAAY;;;;;;AAEZ,wDAAyB;AACzB,oDAA4B;AAC5B,2BAUY;AACZ,+BAA4B;AAC5B,gDAAwB;AACxB,oDAAuB;AACvB,4CAAoB;AACpB,8CAAsB;AACtB,gDAAwB;AACxB,sDAA6B;AAC7B,wDAA+B;AAC/B,0EAAyC;AACzC,kDAA0B;AAC1B,sDAA2B;AAC3B,qCAA+B;AAC/B,qCAAmC;AACnC,iCAAiC;AAEjC,MAAM,QAAQ;AACZ,qFAAqF,CAAC,CACpF,kBAAC,CAAC,SAAS,CAAC,aAAG,CAAC,CACjB,CAAC;AACJ,MAAM,cAAc,GAAG,gBAAC,CAAC,OAAO,CAAC,iBAAM,CAAC,IAAI,CAAC,CAAC;AAE9C,MAAM,EAAE,GAAG;IACT;;;;;;;;OAQG;IACH,KAAK,CAAC,SAAS,CAAC,IAAI;QAClB,IAAI,CAAC;YACH,MAAM,aAAU,CAAC,MAAM,CAAC,IAAI,EAAE,cAAS,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,YAAY,CAAC,IAAI;QACrB,IAAI,CAAC;YACH,IAAI,IAAA,kBAAS,GAAE,EAAE,CAAC;gBAChB,OAAO,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YACD,MAAM,aAAU,CAAC,MAAM,CAAC,IAAI,EAAE,cAAS,CAAC,IAAI,GAAG,cAAS,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,MAAM,CAAC,IAAI;QACf,OAAO,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAM,CAAC,QAAQ;QACnB,OAAO,MAAM,aAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACH,UAAU,CAAC,QAAQ;QACjB,OAAO,IAAA,WAAM,EAAC,QAAQ,EAAE,EAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE;QAC7B,IAAI,CAAC;YACH,OAAO,MAAM,aAAU,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,GAAG,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC3B,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC;IACH,CAAC;IACD;;;;;;;OAOG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,GAAG,EAAE;QAC3C,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,gBAAgB,MAAM,uCAAuC,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,MAAM,QAAQ,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,GAAG,CAAC,QAAQ;QAChB,OAAO,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,EAAE,EAAE,uEAAuE,CAAC,CAAC,kBAAC,CAAC,SAAS,CAAC,YAAE,CAAC,CAAC;IAE7F;;;OAGG;IACH,KAAK,EAAL,eAAK;IAEL;;;OAGG;IACH,IAAI,EAAE,kFAAkF,CAAC,CACvF,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,kBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAA,WAAI,EAAC,OAAO,CAAC,CAAC,CAClF;IAED;;;OAGG;IACH,YAAY,EAAE,2BAAQ;IAEtB;;;;;OAKG;IACH,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,GAAG,MAAM;QACrC,OAAO,MAAM,IAAI,kBAAC,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,gBAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAG,IAAA,qBAAgB,EAAC,QAAQ,CAAC,CAAC;YAC9C,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAC3B,MAAM,CACJ,IAAI,KAAK,CACP,oBAAoB,SAAS,cAAc,QAAQ,sBAAsB,CAAC,CAAC,OAAO,EAAE,CACrF,CACF,CACF,CAAC;YACF,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACzD,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,CAAC,GAAG,EAAE,IAAI;QACZ,OAAO,IAAA,cAAI,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,MAAM,CAAC,GAAG;QACd,OAAO,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;OAOG;IACH,6DAA6D;IAC7D,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ;QACpC,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC;YACH,WAAW,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACnD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,KAAK,CACT,IAAI,GAAG,iCAAiC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,qBAAqB,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CACzF,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC;QACX,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,KAAK,GAAG,IAAI,cAAK,EAAE,CAAC,KAAK,EAAE,CAAC;QAClC,OAAO,MAAM,IAAI,kBAAC,CAAC,UAAU,OAAO,EAAE,MAAM;YAC1C,sBAAsB;YACtB,IAAI,iBAAiB,GAAG,kBAAC,CAAC,OAAO,EAAE,CAAC;YACpC,MAAM,GAAG,IAAA,cAAI,EAAC,GAAG,EAAE;gBACjB,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/B,CAAC,CAAC;YACH,MAAM;iBACH,EAAE,CAAC,MAAM,EAAE,UAAU,IAAI;gBACxB,MAAM,CAAC,KAAK,EAAE,CAAC;gBAEf,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;oBAC9B,SAAS,EAAE,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,cAAc,EAAE,CAAC;gBACnB,CAAC;gBAED,iBAAiB,GAAG,CAAC,KAAK,IAAI,EAAE;oBAC9B,IAAI,CAAC;wBACH,6DAA6D;wBAC7D,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;wBACjE,IAAI,IAAI,EAAE,CAAC;4BACT,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC5B,CAAC;wBACD,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClB,CAAC;oBAAC,OAAO,GAAG,EAAE,CAAC;wBACb,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;oBACrB,CAAC;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC;iBACD,EAAE,CAAC,OAAO,EAAE,UAAU,GAAG,EAAE,IAAI;gBAC9B,gBAAG,CAAC,IAAI,CAAC,+BAA+B,IAAI,CAAC,IAAI,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBACtE,4CAA4C;gBAC5C,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC1B,gBAAG,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;oBACjD,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;YACH,CAAC,CAAC;iBACD,EAAE,CAAC,KAAK,EAAE;gBACT,CAAC,KAAK,IAAI,EAAE;oBACV,IAAI,CAAC;wBACH,MAAM,IAAI,GAAG,MAAM,iBAAiB,CAAC;wBACrC,OAAO,OAAO,CAAC,+BAA+B,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oBACjE,CAAC;oBAAC,OAAO,GAAG,EAAE,CAAC;wBACb,gBAAG,CAAC,IAAI,CAAC,qBAAqB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC7C,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;oBACrB,CAAC;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,OAAO,CAAC;YACT,gBAAG,CAAC,KAAK,CACP,aAAa,IAAA,gBAAS,EAAC,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC,GAAG;gBAC1D,OAAO,IAAA,gBAAS,EAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG;gBAC5C,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAC1D,CAAC;YACF,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACD;;;;;;OAMG;IACH,mBAAmB,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE;QAChC,MAAM,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC;YACH,OAAO,kBAAO,CAAC,IAAI;YACjB,kDAAkD,CAAC,CAAC,EAAC,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,EAAC,CAAC,CACrF,CAAC;QACJ,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,GAAG,CAAC,OAAO,GAAG,gDAAgD,GAAG,UAAU,GAAG,CAAC,OAAO,EAAE,CAAC;YACzF,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IACD;;;;;;OAMG;IACH,QAAQ,CAAC,GAAG;QACV,IAAI,CAAC,GAAG,IAAI,CAAC,cAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,SAAS,CAAC,0DAA0D,CAAC,CAAC;QAClF,CAAC;QACD,MAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,uDAAuD,GAAG,EAAE,CAAC,CAAC;QAChF,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,mCAAmC;IACnC,MAAM,EAAE,aAAU,CAAC,MAAM;IACzB,UAAU,EAAE,aAAU,CAAC,UAAU;IACjC,KAAK,EAAE,aAAU,CAAC,KAAK;IACvB,KAAK,EAAE,kBAAC,CAAC,SAAS,CAAC,UAAK,CAAC;IACzB,SAAS,EAAT,cAAS;IACT,iBAAiB,EAAjB,sBAAiB;IACjB,gBAAgB,EAAhB,qBAAgB;IAChB,KAAK,EAAE,aAAU,CAAC,KAAK;IACvB;;;;OAIG;IACH,IAAI,EAAE,kBAAC,CAAC,SAAS,CAAC,SAAI,CAAC;IACvB,QAAQ,EAAE,aAAU,CAAC,IAAI;IACzB,OAAO,EAAE,aAAU,CAAC,OAAO;IAE3B,IAAI,EAAE;;OAEH,CAAC,EAAC,sBAAuB,CAAC,kBAAC,CAAC,SAAS,CAAC,SAAI,CAAC,CAAC,CAAC;IAChD,QAAQ,EAAE,aAAU,CAAC,QAAQ;IAC7B,QAAQ,EAAE,aAAU,CAAC,QAAQ;IAC7B,QAAQ,EAAE,aAAU,CAAC,QAAQ;IAC7B,MAAM,EAAE,aAAU,CAAC,MAAM;IACzB,IAAI,EAAE,aAAU,CAAC,IAAI;IACrB,OAAO,EAAE,aAAU,CAAC,OAAO;IAC3B,MAAM,EAAE,aAAU,CAAC,MAAM;IACzB,KAAK,EAAE,kBAAC,CAAC,SAAS,CAAC,UAAK,CAAC;IACzB,SAAS,EAAE,aAAU,CAAC,SAAS;IAE/B,mBAAmB;IAEnB;;;OAGG;IACH,IAAI,EAAE,cAAS,CAAC,IAAI;IAEpB;;;OAGG;IACH,IAAI,EAAE,cAAS,CAAC,IAAI;IAEpB;;;OAGG;IACH,IAAI,EAAE,cAAS,CAAC,IAAI;IAEpB;;;OAGG;IACH,IAAI,EAAE,cAAS,CAAC,IAAI;CACrB,CAAC;AAEM,gBAAE;AACV,kBAAe,EAAE,CAAC;AAElB;;;;;;GAMG;AAEH;;;;GAIG;AAEH;;;;;;;;;GASG"}