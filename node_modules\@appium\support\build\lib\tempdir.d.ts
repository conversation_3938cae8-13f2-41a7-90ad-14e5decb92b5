export type Affixes = {
    /**
     * - prefix of the temp directory name
     */
    prefix?: string | undefined;
    /**
     * - suffix of the temp directory name
     */
    suffix?: string | undefined;
};
export type OpenedAffixes = {
    /**
     * - The path to file
     */
    path: string;
    /**
     * - The file descriptor opened
     */
    fd: number;
};
/**
 * @typedef OpenedAffixes
 * @property {string} path - The path to file
 * @property {number} fd - The file descriptor opened
 */
/**
 * Generate a temporary directory in os.tempdir() or process.env.APPIUM_TMP_DIR
 * with arbitrary prefix/suffix for the directory name and return it as open.
 *
 * @param {Affixes} affixes
 * @returns {Promise<OpenedAffixes>}
 */
export function open(affixes: Affixes): Promise<OpenedAffixes>;
/**
 * @typedef Affixes
 * @property {string} [prefix] - prefix of the temp directory name
 * @property {string} [suffix] - suffix of the temp directory name
 */
/**
 * Generate a temporary directory in os.tempdir() or process.env.APPIUM_TMP_DIR
 * with arbitrary prefix/suffix for the directory name.
 *
 * @param {string|Affixes} rawAffixes
 * @param {string} [defaultPrefix]
 * @returns {Promise<string>}  A path to the temporary directory with rawAffixes and defaultPrefix
 */
export function path(rawAffixes: string | Affixes, defaultPrefix?: string): Promise<string>;
/**
 * Generate a temporary directory in os.tempdir() or process.env.APPIUM_TMP_DIR.
 * e.g.
 * - No `process.env.APPIUM_TMP_DIR`: `/var/folders/34/2222sh8n27d6rcp7jqlkw8km0000gn/T/xxxxxxxx.yyyy`
 * - With `process.env.APPIUM_TMP_DIR = '/path/to/root'`: `/path/to/root/xxxxxxxx.yyyy`
 *
 * @returns {Promise<string>} A path to the temporary directory
 */
export function openDir(): Promise<string>;
/**
 * Returns a path to a temporary directory which is defined as static in the same process
 *
 * @returns {Promise<string>} A temp directory path which is defined as static in the same process
 */
export function staticDir(): Promise<string>;
//# sourceMappingURL=tempdir.d.ts.map