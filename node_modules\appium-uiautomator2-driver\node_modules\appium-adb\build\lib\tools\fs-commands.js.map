{"version": 3, "file": "fs-commands.js", "sourceRoot": "", "sources": ["../../../lib/tools/fs-commands.js"], "names": [], "mappings": ";;;;;AAUA,gCAQC;AAYD,gBAcC;AAUD,4BAeC;AASD,wBAEC;AAaD,oBAGC;AAaD,oBAGC;AASD,sBAIC;AA7HD,oDAAuB;AACvB,gDAAwB;AAExB;;;;;;GAMG;AACI,KAAK,UAAU,UAAU,CAAE,UAAU;IAC1C,MAAM,QAAQ,GAAG,UAAU,CAAC;IAC5B,MAAM,QAAQ,GAAG,SAAS,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,eAAe,QAAQ,EAAE,CAAC;IACnF,IAAI,CAAC;QACH,OAAO,gBAAC,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;;;;;;GASG;AACI,KAAK,UAAU,EAAE,CAAE,UAAU,EAAE,IAAI,GAAG,EAAE;IAC7C,IAAI,CAAC;QACH,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC;QACvC,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aAC9B,MAAM,CAAC,OAAO,CAAC;aACf,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC5D,MAAM,GAAG,CAAC;QACZ,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,QAAQ,CAAE,UAAU;IACxC,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACjD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QACD,kCAAkC;QAClC,MAAM,KAAK,GAAG,kDAAkD,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,KAAK,IAAI,gBAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,2CAA2C,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAChC,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,gCAAgC,UAAU,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IACjF,CAAC;AACH,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,MAAM,CAAE,IAAI;IAChC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;;;;;GAUG;AACI,KAAK,UAAU,IAAI,CAAE,SAAS,EAAE,UAAU,EAAE,IAAI;IACrD,MAAM,IAAI,CAAC,KAAK,CAAC,cAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;IACjD,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;;;;GAUG;AACI,KAAK,UAAU,IAAI,CAAE,UAAU,EAAE,SAAS,EAAE,IAAI,GAAG,EAAE;IAC1D,iEAAiE;IACjE,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,EAAC,GAAG,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,KAAK,EAAC,CAAC,CAAC;AACjG,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,KAAK,CAAE,UAAU;IACrC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;QAC3B,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACrE,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AACpD,CAAC"}