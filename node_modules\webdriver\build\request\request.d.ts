import type { Options } from '@wdio/types';
import type { WebDriverResponse, RequestLibResponse, RequestOptions, RequestEventHandler } from './types.js';
import './polyfill.js';
export declare abstract class WebDriverRequest {
    protected abstract fetch(url: URL, opts: RequestInit): Promise<Response>;
    body?: Record<string, unknown>;
    method: string;
    endpoint: string;
    isHubCommand: boolean;
    requiresSessionId: boolean;
    eventHandler: RequestEventHandler;
    abortSignal?: AbortSignal;
    constructor(method: string, endpoint: string, body?: Record<string, unknown>, abortSignal?: AbortSignal, isHubCommand?: boolean, eventHandler?: RequestEventHandler);
    makeRequest(options: RequestOptions, sessionId?: string): Promise<WebDriverResponse<unknown>>;
    createOptions(options: RequestOptions, sessionId?: string, isBrowser?: boolean): Promise<{
        url: URL;
        requestOptions: RequestInit;
    }>;
    protected _libRequest(url: URL, opts: RequestInit): Promise<Options.RequestLibResponse>;
    protected _request(url: URL, fullRequestOptions: RequestInit, transformResponse?: (response: RequestLibResponse, requestOptions: RequestInit) => RequestLibResponse, totalRetryCount?: number, retryCount?: number): Promise<WebDriverResponse>;
}
//# sourceMappingURL=request.d.ts.map