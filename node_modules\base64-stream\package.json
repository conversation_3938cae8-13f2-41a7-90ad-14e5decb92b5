{"name": "base64-stream", "description": "Contains new Node.js v0.10 style stream classes for encoding / decoding Base64 data", "keywords": ["Base64", "stream", "streaming", "piping", "node", "node.js", "encode", "decode"], "author": "<PERSON> <<EMAIL>>", "version": "1.0.0", "repository": {"type": "git", "url": "http://github.com/mazira/base64-stream"}, "scripts": {"test": "mocha --reporter spec"}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "files": ["lib", "index.js"], "engine": "node >= 0.8.0"}