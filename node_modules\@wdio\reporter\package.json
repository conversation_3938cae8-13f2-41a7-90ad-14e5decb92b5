{"name": "@wdio/reporter", "version": "9.17.0", "description": "A WebdriverIO utility to help reporting all events", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/webdriverio/webdriverio/tree/main/packages/wdio-reporter", "license": "MIT", "engines": {"node": ">=18.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/webdriverio/webdriverio.git", "directory": "packages/wdio-reporter"}, "keywords": ["webdriver", "wdio", "wdio-reporter"], "bugs": {"url": "https://github.com/webdriverio/webdriverio/issues"}, "publishConfig": {"access": "public"}, "type": "module", "main": "./build/index.cjs", "module": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.js", "require": "./build/index.cjs"}}, "typeScriptVersion": "3.8.3", "dependencies": {"@types/node": "^20.1.0", "@wdio/logger": "9.16.2", "@wdio/types": "9.16.2", "diff": "^7.0.0", "object-inspect": "^1.12.0"}, "devDependencies": {"@types/diff": "^5.0.2", "@types/object-inspect": "^1.8.1", "@types/supports-color": "^8.1.1", "@types/tmp": "^0.2.3", "tmp": "^0.2.1"}, "gitHead": "796df04f0f5c744d5ef4bafd3759995fa4829d6f"}