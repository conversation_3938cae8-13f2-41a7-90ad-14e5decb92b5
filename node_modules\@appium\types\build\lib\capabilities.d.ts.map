{"version": 3, "file": "capabilities.d.ts", "sourceRoot": "", "sources": ["../../lib/capabilities.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,wBAAwB,EAAC,MAAM,eAAe,CAAC;AACvD,OAAO,EAAC,UAAU,EAAE,WAAW,EAAC,MAAM,UAAU,CAAC;AACjD,OAAO,EAAC,oBAAoB,EAAC,MAAM,iBAAiB,CAAC;AACrD,OAAO,EAAC,OAAO,EAAE,YAAY,EAAC,MAAM,QAAQ,CAAC;AAE7C,OAAO,EAAC,oBAAoB,EAAC,CAAC;AAE9B,MAAM,MAAM,iBAAiB,GAAG,QAAQ,CAAC;AAEzC;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,YAAY,CAAC,wBAAwB,CAAC,CAAC;AAEtE;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,cAAc,CAAC,wBAAwB,CAAC,CAAC;AAE1E;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG,eAAe,CAAC,wBAAwB,CAAC,CAAC;AAE5E;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,SAAS,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,GAC7F,OAAO,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,GAC9C,CAAC,CAAC,WAAW,CAAC,SAAS,aAAa,CAAC,CAAC,CAAC,GACvC,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,GACtB,CAAC,CAAC;AAEN;;;;;;;;GAQG;AACH,MAAM,MAAM,mBAAmB,CAAC,CAAC,SAAS,UAAU,IAAI,CAAC,CAAC,UAAU,CAAC,SAAS,IAAI,GAC9E,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,GAC3B,CAAC,CAAC,UAAU,CAAC,SAAS,IAAI,GAC1B,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,GAC3B,CAAC,CAAC,WAAW,CAAC,SAAS,IAAI,GAC3B,OAAO,GACP,CAAC,CAAC,SAAS,CAAC,SAAS,IAAI,GACzB,MAAM,EAAE,GACR,CAAC,CAAC,UAAU,CAAC,SAAS,IAAI,GAC1B,YAAY,GACZ,OAAO,CAAC;AAEZ;;;;GAIG;AACH,MAAM,MAAM,eAAe,CAAC,CAAC,SAAS,UAAU,IAAI,CAAC,CAAC,UAAU,CAAC,SAC7D,IAAI,GACJ;IAAC,UAAU,EAAE,OAAO,CAAA;CAAC,GACrB,mBAAmB,CAAC,CAAC,CAAC,GACtB,mBAAmB,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AAEvC;;;;GAIG;AACH,MAAM,MAAM,YAAY,CAAC,CAAC,SAAS,YAAY,EAAE,EAAE,SAAS,MAAM,GAAG,iBAAiB,IAAI;KACvF,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,oBAAoB,GACjD,CAAC,GACD,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC5C,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,gBAAgB,CAC1B,CAAC,SAAS,MAAM,EAChB,EAAE,SAAS,MAAM,GAAG,iBAAiB,IACnC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC;AAEjB;;;GAGG;AACH,MAAM,MAAM,iBAAiB,CAAC,CAAC,SAAS,WAAW,IAAI;IACrD,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAChD,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,YAAY,CAAC,CAAC,SAAS,WAAW,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAEvE;;;;GAIG;AACH,MAAM,WAAW,eAAe,CAAC,CAAC,SAAS,WAAW;IACpD,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;IAC/B,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;CACjC;AAED;;;;GAIG;AACH,MAAM,MAAM,cAAc,CAAC,CAAC,SAAS,WAAW,EAAE,EAAE,SAAS,MAAM,GAAG,iBAAiB,IAAI,OAAO,CAChG,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACvC,CAAC;AAEF;;;;;;;;;;;;;;;;;GAiBG;AAEH;;;GAGG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,SAAS,WAAW,GAAG,WAAW,IAAI,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AAEjG;;;;;;;;;;;;;GAaG;AACH,MAAM,MAAM,aAAa,CAAC,CAAC,SAAS,WAAW,GAAG,WAAW,IAAI,mBAAmB,GAClF,eAAe,CAAC,CAAC,CAAC,CAAC;AAErB;;;;GAIG;AACH,MAAM,MAAM,YAAY,CAAC,CAAC,SAAS,WAAW,GAAG,WAAW,IAAI,kBAAkB,GAChF,cAAc,CAAC,CAAC,CAAC,CAAC"}