"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const make_error_1 = require("make-error");
const TS_ERROR_MESSAGE = 'TypeScript compiler encountered syntax errors while transpiling. Errors: ';
class TypeScriptCompileError extends make_error_1.BaseError {
    constructor(message, options) {
        super(message);
        this.name = 'TypeScriptCompileError';
        this.options = options;
        Object.defineProperty(this, 'options', {
            enumerable: false,
        });
    }
    toObject() {
        return {
            message: this.message,
            name: this.name,
            stack: this.stack,
        };
    }
}
exports.default = TypeScriptCompileError;
TypeScriptCompileError.fromError = (error) => {
    const message = [
        'Failed to compile TypeScript: ',
        error.message.replace(TS_ERROR_MESSAGE, ''),
    ].join('');
    return new TypeScriptCompileError(message, error);
};
//# sourceMappingURL=TypeScriptCompileError.js.map