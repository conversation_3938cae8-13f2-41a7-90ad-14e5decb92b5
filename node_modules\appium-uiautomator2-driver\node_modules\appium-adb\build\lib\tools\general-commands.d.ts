/**
 * Get the path to adb executable amd assign it
 * to this.executable.path and this.binaries.adb properties.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<import('../adb.js').ADB>} ADB instance.
 */
export function getAdbWithCorrectAdbPath(this: import("../adb.js").ADB): Promise<import("../adb.js").ADB>;
/**
 * Get the full path to aapt tool and assign it to
 * this.binaries.aapt property
 * @this {import('../adb.js').ADB}
 */
export function initAapt(this: import("../adb.js").ADB): Promise<void>;
/**
 * Get the full path to aapt2 tool and assign it to
 * this.binaries.aapt2 property
 * @this {import('../adb.js').ADB}
 */
export function initAapt2(this: import("../adb.js").ADB): Promise<void>;
/**
 * Get the full path to zipalign tool and assign it to
 * this.binaries.zipalign property
 * @this {import('../adb.js').ADB}
 */
export function initZipAlign(this: import("../adb.js").ADB): Promise<void>;
/**
 * Get the full path to bundletool binary and assign it to
 * this.binaries.bundletool property
 * @this {import('../adb.js').ADB}
 */
export function initBundletool(this: import("../adb.js").ADB): Promise<void>;
/**
 * Retrieve the API level of the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<number>} The API level as integer number, for example 21 for
 *                  Android Lollipop. The result of this method is cached, so all the further
 * calls return the same value as the first one.
 */
export function getApiLevel(this: import("../adb.js").ADB): Promise<number>;
export class getApiLevel {
    _apiLevel: number | undefined;
}
/**
 * Verify whether a device is connected.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if at least one device is visible to adb.
 */
export function isDeviceConnected(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Clear the active text field on the device under test by sending
 * special keyevents to it.
 *
 * @this {import('../adb.js').ADB}
 * @param {number} [length=100] - The maximum length of the text in the field to be cleared.
 */
export function clearTextField(this: import("../adb.js").ADB, length?: number): Promise<void>;
/**
 * Send the special keycode to the device under test in order to emulate
 * Back button tap.
 * @this {import('../adb.js').ADB}
 */
export function back(this: import("../adb.js").ADB): Promise<void>;
/**
 * Send the special keycode to the device under test in order to emulate
 * Home button tap.
 * @this {import('../adb.js').ADB}
 */
export function goToHome(this: import("../adb.js").ADB): Promise<void>;
/**
 * @this {import('../adb.js').ADB}
 * @return {string} the actual path to adb executable.
 */
export function getAdbPath(this: import("../adb.js").ADB): string;
/**
 * Restart the device under test using adb commands.
 *
 * @this {import('../adb.js').ADB}
 * @throws {Error} If start fails.
 */
export function restart(this: import("../adb.js").ADB): Promise<void>;
/**
 * Retrieve the `adb bugreport` command output. This
 * operation may take up to several minutes.
 *
 * @this {import('../adb.js').ADB}
 * @param {number} [timeout=120000] - Command timeout in milliseconds
 * @returns {Promise<string>} Command stdout
 */
export function bugreport(this: import("../adb.js").ADB, timeout?: number): Promise<string>;
/**
 * Initiate screenrecord utility on the device
 *
 * @this {import('../adb.js').ADB}
 * @param {string} destination - Full path to the writable media file destination
 *                               on the device file system.
 * @param {import('./types').ScreenrecordOptions} [options={}]
 * @returns {SubProcess} screenrecord process, which can be then controlled by the client code
 */
export function screenrecord(this: import("../adb.js").ADB, destination: string, options?: import("./types").ScreenrecordOptions): SubProcess;
/**
 * Retrieves the list of features supported by the device under test
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<string[]>} the list of supported feature names or an empty list.
 * An example adb command output:
 * ```
 * cmd
 * ls_v2
 * fixed_push_mkdir
 * shell_v2
 * abb
 * stat_v2
 * apex
 * abb_exec
 * remount_shell
 * fixed_push_symlink_timestamp
 * ```
 * @throws {Error} if there was an error while retrieving the list
 */
export function listFeatures(this: import("../adb.js").ADB): Promise<string[]>;
export class listFeatures {
    _memoizedFeatures: () => Promise<string>;
}
/**
 * Checks the state of streamed install feature.
 * This feature allows to speed up apk installation
 * since it does not require the original apk to be pushed to
 * the device under test first, which also saves space.
 * Although, it is required that both the device under test
 * and the adb server have the mentioned functionality.
 * See https://github.com/aosp-mirror/platform_system_core/blob/master/adb/client/adb_install.cpp
 * for more details
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<boolean>} `true` if the feature is supported by both adb and the
 * device under test
 */
export function isStreamedInstallSupported(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Checks whether incremental install feature is supported by ADB.
 * Read https://developer.android.com/preview/features#incremental
 * for more details on it.
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<boolean>} `true` if the feature is supported by both adb and the
 * device under test
 */
export function isIncrementalInstallSupported(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Takes a screenshot of the given display or the default display.
 *
 * @this {import('../adb.js').ADB}
 * @param {number|string?} displayId A valid display identifier. If
 * no identifier is provided then the screenshot of the default display is returned.
 * Note that only recent Android APIs provide multi-screen support.
 * @returns {Promise<Buffer>} PNG screenshot payload
 */
export function takeScreenshot(this: import("../adb.js").ADB, displayId: number | (string | null)): Promise<Buffer>;
import { SubProcess } from 'teen_process';
//# sourceMappingURL=general-commands.d.ts.map