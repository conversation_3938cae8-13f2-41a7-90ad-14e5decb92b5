rgb2hex ![Test Changes](https://github.com/christian-bromann/rgb2hex/workflows/Test%20Changes/badge.svg) [![Known Vulnerabilities](https://snyk.io/test/github/christian-bromann/rgb2hex/badge.svg?targetFile=package.json)](https://snyk.io/test/github/christian-bromann/rgb2hex?targetFile=package.json)
=======

Parse any rgb or rgba string into a hex color. Lightweight library, no dependencies!

## Installation

via NPM:
```
$ npm install rgb2hex
```

via Bower
```
$ bower install rgb2hex
```

## Usage

Include `rgb2hex.js` in your web app, by loading it as usual:

```html
<script src="rgb2hex.js"></script>
```

### Using NodeJS

```js
var rgb2hex = require('rgb2hex');

console.log(rgb2hex('rgb(210,43,2525)'));
/**
 * returns:
 * {
 *    hex: '#d22bff',
 *    alpha: 1
 * }
 */

console.log(rgb2hex('rgba(12,173,22,.67)'));
/**
 * returns:
 * {
 *    hex: '#d22bff',
 *    alpha: 0.67
 * }
 */
```

### Using RequireJS

rgb2hex can be also loaded with AMD:

```js
require(['rgb2hex'], function (rgb2hex) {
    // ...
});
```

## Contributing
Please fork, add specs, and send pull requests! In lieu of a formal styleguide, take care to
maintain the existing coding style.

## Release History
* 2013-04-22   v0.1.0   first working version
* 2018-05-24   v0.1.1   updated dependencies switch test framework to jest
* 2018-06-13   v0.1.2   Fixes uncontrolled resource consumption vulnerability referenced in #1. ([ref1](https://nodesecurity.io/advisories/647), [ref2](https://snyk.io/vuln/npm:rgb2hex:20180429))
* 2018-06-13   v0.1.3   allow semicolon at the end of an rgb string
* 2018-06-19   v0.1.4   ignore text before or after the color
* 2018-07-04   v0.1.5   Fix stripping of color and regexp
* 2018-07-05   v0.1.6   Prevent Regular Expression Denial of Service attacks
* 2018-07-05   v0.1.7   Minor coverage fix
* 2018-07-05   v0.1.8   Better handle alpha values
* 2018-07-18   v0.1.9   Support transparent colors
* 2019-11-11   v0.1.10  Support multiple decimal places [(#20)](https://github.com/christian-bromann/rgb2hex/pull/20)
* 2020-11-24   v0.2.1   TypeScript support
* 2020-11-25   v0.2.5   Improved TypeScript support