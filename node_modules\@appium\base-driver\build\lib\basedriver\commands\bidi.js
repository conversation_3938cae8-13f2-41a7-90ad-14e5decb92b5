"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mixin_1 = require("./mixin");
const lodash_1 = __importDefault(require("lodash"));
const BidiCommands = {
    async bidiSubscribe(events, contexts = ['']) {
        for (const event of events) {
            this.bidiEventSubs[event] = contexts;
        }
    },
    async bidiUnsubscribe(events, contexts = ['']) {
        for (const event of events) {
            if (this.bidiEventSubs[event]) {
                this.bidiEventSubs[event] = this.bidiEventSubs[event].filter((c) => !contexts.includes(c));
            }
            if (this.bidiEventSubs[event].length === 0) {
                delete this.bidiEventSubs[event];
            }
        }
    },
    async bidiStatus() {
        const result = await this.getStatus();
        if (!lodash_1.default.has(result, 'ready')) {
            //@ts-ignore This is OK
            result.ready = true;
        }
        if (!lodash_1.default.has(result, 'message')) {
            //@ts-ignore This is OK
            result.message = `${this.constructor.name} is ready to accept commands`;
        }
        return result;
    }
};
(0, mixin_1.mixin)(BidiCommands);
//# sourceMappingURL=bidi.js.map