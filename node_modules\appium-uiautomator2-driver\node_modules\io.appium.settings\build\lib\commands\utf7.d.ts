/**
 * RFC 2152 UTF-7 encoding with all optionals.
 *
 * @param {string} str
 * @returns {string}
 */
export function encodeAll(str: string): string;
export namespace imap {
    /**
     * RFC 3501, section 5.1.3 UTF-7 encoding.
     *
     * @param {string} str
     * @returns {string}
     */
    function encode(str: string): string;
    /**
     * RFC 3501, section 5.1.3 UTF-7 decoding.
     *
     * @param {string} str
     * @returns {string}
     */
    function decode(str: string): string;
}
export function encode(str: string, mask?: string | null): string;
export function decode(str: string): string;
//# sourceMappingURL=utf7.d.ts.map