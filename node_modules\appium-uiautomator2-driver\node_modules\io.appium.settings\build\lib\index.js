"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsApp = exports.path = void 0;
const source_map_support_1 = require("source-map-support");
(0, source_map_support_1.install)();
const utils_1 = require("./utils");
exports.path = (0, utils_1.getSettingsApkPath)();
__exportStar(require("./constants"), exports);
var client_1 = require("./client");
Object.defineProperty(exports, "SettingsApp", { enumerable: true, get: function () { return client_1.SettingsApp; } });
//# sourceMappingURL=index.js.map