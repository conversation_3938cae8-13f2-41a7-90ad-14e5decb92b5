/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<string>}
 */
export function getAlertText(this: import("../driver").AndroidUiautomator2Driver): Promise<string>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} [buttonLabel] The name of the button to click in order to accept the alert.
 * If the name is not provided
 * then the script will try to detect the button automatically.
 * @returns {Promise<void>}
 */
export function mobileAcceptAlert(this: import("../driver").AndroidUiautomator2Driver, buttonLabel?: string): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<void>}
 */
export function postAcceptAlert(this: import("../driver").AndroidUiautomator2Driver): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} [buttonLabel] The name of the button to click in order to dismiss the alert.
 * If the name is not provided
 * then the script will try to detect the button automatically.
 * @returns {Promise<void>}
 */
export function mobileDismissAlert(this: import("../driver").AndroidUiautomator2Driver, buttonLabel?: string): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<void>}
 */
export function postDismissAlert(this: import("../driver").AndroidUiautomator2Driver): Promise<void>;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
//# sourceMappingURL=alert.d.ts.map