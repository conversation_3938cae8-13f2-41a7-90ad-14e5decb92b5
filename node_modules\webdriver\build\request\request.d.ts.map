{"version": 3, "file": "request.d.ts", "sourceRoot": "", "sources": ["../../src/request/request.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,aAAa,CAAA;AAM1C,OAAO,KAAK,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,cAAc,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAA;AAM5G,OAAO,eAAe,CAAA;AAgBtB,8BAAsB,gBAAgB;IAClC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC;IAExE,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC9B,MAAM,EAAE,MAAM,CAAA;IACd,QAAQ,EAAE,MAAM,CAAA;IAChB,YAAY,EAAE,OAAO,CAAA;IACrB,iBAAiB,EAAE,OAAO,CAAA;IAC1B,YAAY,EAAE,mBAAmB,CAAA;IACjC,WAAW,CAAC,EAAE,WAAW,CAAA;gBAErB,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC9B,WAAW,CAAC,EAAE,WAAW,EACzB,YAAY,GAAE,OAAe,EAC7B,YAAY,GAAE,mBAAwB;IAWpC,WAAW,CAAE,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,EAAE,MAAM;IAMxD,aAAa,CAAE,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,SAAS,GAAE,OAAe,GAAG,OAAO,CAAC;QAAE,GAAG,EAAE,GAAG,CAAC;QAAC,cAAc,EAAE,WAAW,CAAC;KAAE,CAAC;cA+DlI,WAAW,CAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC;cA+B9E,QAAQ,CACpB,GAAG,EAAE,GAAG,EACR,kBAAkB,EAAE,WAAW,EAC/B,iBAAiB,CAAC,EAAE,CAAC,QAAQ,EAAE,kBAAkB,EAAE,cAAc,EAAE,WAAW,KAAK,kBAAkB,EACrG,eAAe,SAAI,EACnB,UAAU,SAAI,GACf,OAAO,CAAC,iBAAiB,CAAC;CA+HhC"}