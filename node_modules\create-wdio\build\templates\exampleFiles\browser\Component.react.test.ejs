<%
const harnessImport = answers.installTestingLibrary
    ? `import { render, screen, fireEvent } from '@testing-library/react'`
    : `import { createRoot } from 'react-dom/client'`
const renderCommand = answers.installTestingLibrary
    ? `render(<ExampleComponent />)`
    : `const root = createRoot(container)
        root.render(<ExampleComponent />)`
%>
import React from 'react'
import { expect, $ } from '@wdio/globals'
<%- harnessImport %>
<% if (answers.installTestingLibrary) { %>
import * as matchers from '@testing-library/jest-dom/matchers'
expect.extend(matchers)
<% } %>
import ExampleComponent from './Component'

describe('React Component Tests', () => {
    <% if (answers.installTestingLibrary) { %>
    it('should test component with Testing Library', async () => {
        render(<ExampleComponent />)
        const component = screen.getByText(/count is 0/i)
        expect(component).toBeInTheDocument()

        await fireEvent.click(component)
        await fireEvent.click(component)

        expect(screen.getByText(/count is 2/i)).toBeInTheDocument()
    })
    <% } else { %>
    let container<%- answers.isUsingTypeScript ? `: Element` : '' %>

    beforeEach(() => {
        container = document.createElement('div')
        document.body.appendChild(container)
    })

    afterEach(() => {
        container?.remove()
    })
    <% } %>

    it('should test component with WebdriverIO', async () => {
        <%- renderCommand %>

        const component = await $('button*=count is')
        await expect(component).toBePresent()
        await expect(component).toHaveText('count is 0')

        await component.click()
        await component.click()

        await expect(component).toHaveText('count is 2')<%-
        answers.includeVisualTesting ? `
        await expect(component).toMatchElementSnapshot('counterButton')` : '' %>
    })
})
