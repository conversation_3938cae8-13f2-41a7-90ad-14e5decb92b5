{"version": 3, "file": "build.js", "sourceRoot": "", "sources": ["../../../../lib/cli/command/build.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;AAEH,0DAA6B;AAE7B,2CAAgD;AAChD,+CAAyC;AACzC,yCAAuC;AACvC,qCAAqC;AACrC,oCAA2C;AAE3C,MAAM,GAAG,GAAG,IAAA,kBAAS,EAAC,OAAO,CAAC,CAAC;AAE/B,IAAK,iBAKJ;AALD,WAAK,iBAAiB;IACpB,4CAAuB,CAAA;IACvB,kDAA6B,CAAA;IAC7B,iDAA4B,CAAA;IAC5B,iDAA4B,CAAA;AAC9B,CAAC,EALI,iBAAiB,KAAjB,iBAAiB,QAKrB;AAED,MAAM,IAAI,GAAG;IACX,IAAI,EAAE;QACJ,QAAQ,EAAE,yBAAyB;QACnC,KAAK,EAAE,iBAAiB,CAAC,KAAK;QAC9B,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,IAAI;KACd;IACD,UAAU,EAAE;QACV,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,uBAAuB;QACjC,KAAK,EAAE,iBAAiB,CAAC,KAAK;QAC9B,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,mBAAI,CAAC,OAAO;QACpB,OAAO,EAAE,MAAM;QACf,kBAAkB,EAAE,mBAAmB;KACxC;IACD,cAAc,EAAE;QACd,kBAAkB,EAAE,gBAAgB;QACpC,QAAQ,EAAE,sBAAsB;QAChC,KAAK,EAAE,iBAAiB,CAAC,UAAU;QACnC,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,mBAAI,CAAC,OAAO;QACpB,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;KACf;IACD,YAAY,EAAE;QACZ,kBAAkB,EAAE,cAAc;QAClC,WAAW,EAAE,oBAAoB;QACjC,KAAK,EAAE,iBAAiB,CAAC,UAAU;QACnC,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,MAAM,EAAE,mBAAI,CAAC,OAAO;QACpB,IAAI,EAAE,QAAQ;KACf;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,2CAA2C;QACrD,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,MAAM;KAChB;IACD,IAAI,EAAE;QACJ,QAAQ,EAAE,mBAAmB;QAC7B,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,QAAQ;KAClB;IACD,MAAM,EAAE;QACN,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,qBAAqB;QAC/B,OAAO,EAAE,QAAQ;QACjB,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,IAAI;QACjB,KAAK,EAAE,CAAC;QACR,kBAAkB,EAAE,UAAU;KAC/B;IACD,MAAM,EAAE;QACN,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,mBAAmB;QAC7B,OAAO,EAAE,MAAM;QACf,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,IAAI;QACjB,KAAK,EAAE,CAAC;QACR,kBAAkB,EAAE,QAAQ;KAC7B;IACD,eAAe,EAAE;QACf,QAAQ,EAAE,2CAA2C;QACrD,OAAO,EAAE,QAAQ;QACjB,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;KAClB;IACD,OAAO,EAAE;QACP,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,kDAAkD;QAC5D,OAAO,EAAE,QAAQ;QACjB,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;KAClB;IACD,gBAAgB,EAAE;QAChB,QAAQ,EAAE,wCAAwC;QAClD,OAAO,EAAE,QAAQ;QACjB,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAE,6BAA6B;KAClD;IACD,KAAK,EAAE;QACL,QAAQ,EAAE,6DAA6D;QACvE,OAAO,EAAE,QAAQ;QACjB,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAE,QAAQ;KAC7B;IACD,YAAY,EAAE;QACZ,QAAQ,EAAE,yBAAyB;QACnC,OAAO,EAAE,QAAQ;QACjB,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC;QACxC,kBAAkB,EAAE,UAAU;KAC/B;IACD,KAAK,EAAE;QACL,QAAQ,EAAE,0BAA0B;QACpC,KAAK,EAAE,iBAAiB,CAAC,KAAK;QAC9B,IAAI,EAAE,SAAS;KAChB;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,yBAAyB;QACnC,KAAK,EAAE,iBAAiB,CAAC,KAAK;QAC9B,IAAI,EAAE,QAAQ;QACd,kBAAkB,EAAE,MAAM;QAC1B,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;KAClB;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,yBAAyB;QACnC,KAAK,EAAE,iBAAiB,CAAC,KAAK;QAC9B,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,OAAO;QAChB,kBAAkB,EAAE,WAAW;KAChC;CACyC,CAAC;AAI7C,kBAAe;IACb,OAAO,EAAE,OAAO;IAChB,QAAQ,EAAE,mDAAmD;IAC7D,OAAO,CAAC,KAAK;QACX,OAAO,KAAK;aACT,OAAO,CAAC,IAAI,CAAC;aACb,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACpB,gFAAgF;YAChF,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7C,OAAO,2CAA2C,oBAAQ,2HAA2H,IAAI,CAAC,MAAM,6BAA6B,CAAC;YAChO,CAAC;YAED,OAAO,MAAM,IAAA,yBAAiB,EAAC,IAAI,EAAE,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC3E,CAAC,CAAC;aACD,MAAM,CACL,4HAA4H,CAC7H,CAAC;IACN,CAAC;IACD,KAAK,CAAC,OAAO,CAAC,IAAI;QAChB,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7B,MAAM,IAAI,GAAG,IAAA,gBAAS,EAAC,OAAO,CAAC,CAAC;QAChC,GAAG,CAAC,KAAK,CAAC,oCAAoC,EAAE,IAAI,CAAC,CAAC;QACtD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,IAAA,gBAAM,EAAC,IAAI,CAAC,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QACD,GAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7C,CAAC;CACqC,CAAC"}