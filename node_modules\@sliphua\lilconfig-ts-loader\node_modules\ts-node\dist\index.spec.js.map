{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,+BAA6B;AAC7B,iDAAoC;AACpC,+BAA2B;AAC3B,iCAAiC;AACjC,iCAAiC;AACjC,yCAAyC;AAEzC,2BAAsD;AACtD,4CAA2C;AAC3C,mCAA2C;AAE3C,MAAM,aAAa,GAA0B,OAAO,CAAC,gBAAgB,CAAC,CAAA;AACtE,6BAAmC;AAEnC,mCAAoC;AACpC,wCAAuC;AAEvC,MAAM,KAAK,GAAG,SAAS,CAAC,oBAAI,CAAC,CAAA;AAE7B,MAAM,QAAQ,GAAG,WAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;AAC5C,MAAM,OAAO,GAAG,WAAI,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAA;AAC/C,MAAM,QAAQ,GAAG,WAAI,CAAC,QAAQ,EAAE,2BAA2B,CAAC,CAAA;AAC5D,MAAM,eAAe,GAAG,WAAI,CAAC,QAAQ,EAAE,kCAAkC,CAAC,CAAA;AAE1E,MAAM,iBAAiB,GAAG,gFAAgF,CAAA;AAE1G,wDAAwD;AACxD,MAAM,eAAe,GAAG,aAAa,CAAC,WAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAA,CAAC,sBAAsB;AAExF,yCAAyC;AACzC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAuB,EAAS,CAAA;AAE7E,wEAAwE;AACxE,MAAM,CAAC;;QACL,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAA;QACtB,aAAU,CAAC,WAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAA;QAC1C,MAAM,KAAK,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAA;QAC7C,MAAM,eAAe,GAAG,WAAI,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAA;QAC3D,eAAU,CAAC,eAAe,CAAC,IAAI,eAAU,CAAC,eAAe,CAAC,CACzD;QAAA,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC,CAAA;IAC3E,CAAC;CAAA,CAAC,CAAA;AAEF,QAAQ,CAAC,SAAS,EAAE;IAClB,MAAM,GAAG,GAAG,IAAI,QAAQ,gBAAgB,OAAO,GAAG,CAAA;IAClD,MAAM,YAAY,GAAG,IAAI,QAAQ,GAAG,CAAA;IAEpC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEnB,EAAE,CAAC,mCAAmC,EAAE;QACtC,aAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAA;IAC9D,CAAC,CAAC,CAAA;IACF,EAAE,CAAC,mCAAmC,EAAE;QACtC,0FAA0F;QAC1F,kDAAkD;QAElD,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QAElC,4HAA4H;QAC5H,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QAC1C,eAAe,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAA;QAE/C,mHAAmH;QACnH,eAAe,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAA;QAC3C,eAAe,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAA;QAC9C,eAAe,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAA;QACrD,eAAe,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAA;QACxD,eAAe,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAA;QAClD,eAAe,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAA;QAErD,oCAAoC;QACpC,eAAe,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAA;QAC3C,eAAe,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAA;QACjD,eAAe,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAA;QAC1D,eAAe,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAA;QAEtD,8BAA8B;QAC9B,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QACtC,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QAC1C,eAAe,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAA;QACrD,eAAe,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAA;IAC3D,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,KAAK,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,EAAE,CAAC,oBAAoB,EAAE,UAAU,IAAI;YACrC,oBAAI,CAAC,GAAG,GAAG,oBAAoB,EAAE,UAAU,GAAG,EAAE,MAAM;gBACpD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;YACzC,oBAAI,CAAC,GAAG,YAAY,SAAS,EAAE,UAAU,GAAG,EAAE,MAAM;gBAClD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAC1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QACF,EAAE,CAAC,sBAAsB,EAAE,UAAU,IAAI;YACvC,oBAAI,CAAC,GAAG,YAAY,KAAK,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC9C,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAA;gBAChF,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QACF,EAAE,CAAC,mCAAmC,EAAE,UAAU,IAAI;YACpD,oBAAI,CAAC,GAAG,YAAY,MAAM,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC/C,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAC5B,YAAa,eAAe,CAAC,iBAAiB,CAAC,CAAC,OAAQ,IAAI;oBAC5D,QAAS,OAAO,CAAC,OAAQ,IAAI;oBAC7B,aAAc,eAAe,CAAC,oBAAoB,CAAC,CAAC,OAAQ,EAAE,CAC/D,CAAA;gBACD,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yBAAyB,EAAE,UAAU,IAAI;YAC1C,oBAAI,CAAC,yCAAyC,EAAE;gBAC9C,GAAG,EAAE,QAAQ;aACd,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uCAAuC,EAAE,UAAU,IAAI;YACxD,oBAAI,CAAC,GAAG,GAAG,KAAK,WAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE,MAAM;gBACrE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sBAAsB,EAAE,UAAU,IAAI;YACvC,oBAAI,CAAC,GAAG,GAAG,kEAAkE,EAAE,UAAU,GAAG,EAAE,MAAM;gBAClG,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBAEpC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,gDAAgD,EAAE,UAAU,IAAI;YACjE,oBAAI,CAAC,GAAG,GAAG,YAAY,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC5C,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;gBAEnC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,mDAAmD,EAAE,UAAU,IAAI;YACpE,oBAAI,CAAC,iCAAiC,EAAE;gBACtC,GAAG,EAAE,QAAQ;aACd,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;gBAEnC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YACnC,EAAE,CAAC,iBAAiB,EAAE,UAAU,IAAI;gBAClC,oBAAI,CACF;oBACE,GAAG;oBACH,6BAA6B;oBAC7B,4DAA4D;iBAC7D,CAAC,IAAI,CAAC,GAAG,CAAC,EACX,UAAU,GAAG,EAAE,MAAM;oBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAExC,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CACF,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;gBAC1D,oBAAI,CACF;oBACE,GAAG;oBACH,6BAA6B;oBAC7B,wEAAwE;iBACzE,CAAC,IAAI,CAAC,GAAG,CAAC,EACX,UAAU,GAAG,EAAE,MAAM;oBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAExC,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CACF,CAAA;YACH,CAAC,CAAC,CAAA;SACH;QAED,EAAE,CAAC,kBAAkB,EAAE,UAAU,IAAI;YACnC,oBAAI,CACF,GAAG,GAAG,0EAA0E,EAChF,UAAU,GAAG,EAAE,MAAM;gBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAEjC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,2BAA2B,EAAE,UAAU,IAAI;YAC5C,oBAAI,CAAC,GAAG,GAAG,8BAA8B,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC9D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;gBAE3B,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,qBAAqB,EAAE,UAAU,IAAI;YACtC,oBAAI,CAAC,GAAG,GAAG,uEAAuE,EAAE,UAAU,GAAG;gBAC/F,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,CACrC,8CAA8C;oBAC9C,sDAAsD,CACvD,CAAC,CAAA;gBAEF,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,qCAAqC,EAAE,UAAU,IAAI;YACtD,oBAAI,CACF,GAAG,GAAG,iGAAiG,EACvG,UAAU,GAAG;gBACX,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1B,iGAAiG,CAClG,CAAA;gBAED,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAI;YAC/C,oBAAI,CAAC,GAAG,GAAG,cAAc,EAAE,UAAU,GAAG;gBACtC,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC7B,GAAG,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,MAAM;oBAC7C,kDAAkD;oBAClD,oBAAoB;oBACpB,uBAAuB;iBACxB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,mCAAmC,EAAE,UAAU,IAAI;YACpD,oBAAI,CAAC,GAAG,GAAG,+BAA+B,EAAE,UAAU,GAAG;gBACvD,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC7B,GAAG,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,MAAM;oBAC7C,kDAAkD;oBAClD,oBAAoB;iBACrB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,oCAAoC,EAAE,UAAU,IAAI;YACrD,oBAAI,CAAC,GAAG,GAAG,2BAA2B,EAAE,UAAU,GAAG;gBACnD,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAA;gBAElE,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,+CAA+C,EAAE,UAAU,IAAI;YAChE,oBAAI,CAAC,GAAG,GAAG,kCAAkC,EAAE,UAAU,GAAG;gBAC1D,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAA;gBAEnE,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;YAC1D,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE,MAAM;gBACxC,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBAElC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,4BAA4B,EAAE,UAAU,IAAI;YAC7C,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,GAAG,KAAK,EAAE,UAAU,GAAG,EAAE,MAAM;gBAChD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAEjC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACvB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,iCAAiC,EAAE,UAAU,IAAI;YAClD,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,GAAG,6CAA6C,EAAE,UAAU,GAAG,EAAE,MAAM;gBACxF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;gBAEtC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACvB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kEAAkE,EAAE,UAAU,IAAI;YACnF,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,GAAG,gBAAgB,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC3D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CACrB,SAAS;oBACT,aAAa;oBACb,IAAI,CACL,CAAA;gBACD,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0CAA0C,EAAE,UAAU,IAAI;YAC3D,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,GAAG,gBAAgB,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC3D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CACrB,eAAe;oBACf,eAAe;oBACf,kBAAkB;oBAClB,IAAI,CACL,CAAA;gBACD,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,6BAA6B,EAAE,GAAS,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,oBAAW,EAAE,CAAA;YAC/B,MAAM,MAAM,GAAG,IAAI,oBAAW,EAAE,CAAA;YAChC,MAAM,MAAM,GAAG,IAAI,oBAAW,EAAE,CAAA;YAChC,MAAM,WAAW,GAAG,UAAU,CAAC;gBAC7B,KAAK;gBACL,MAAM;gBACN,MAAM;aACP,CAAC,CAAA;YACF,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAA;YACxD,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;YAC/B,WAAW,CAAC,KAAK,EAAE,CAAA;YACnB,KAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;YACzC,KAAK,CAAC,GAAG,EAAE,CAAA;YACX,MAAM,SAAS,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAA;YAChC,MAAM,CAAC,GAAG,EAAE,CAAA;YACZ,MAAM,CAAC,GAAG,EAAE,CAAA;YACZ,aAAM,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YAC5C,aAAM,CAAC,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CACtC,oBAAoB;gBACpB,eAAe;gBACf,kBAAkB;gBAClB,IAAI,CACL,CAAA;QACH,CAAC,CAAA,CAAC,CAAA;QAEF,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAI;YAC/C,oBAAI,CAAC,GAAG,GAAG,sDAAsD,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAA;gBAE9D,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0CAA0C,EAAE,UAAU,IAAI;YAC3D,oBAAI,CAAC,GAAG,GAAG,4CAA4C,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC5E,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBAEpC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uCAAuC,EAAE,UAAU,IAAI;YACxD,oBAAI,CAAC,GAAG,GAAG,4BAA4B,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC5D,aAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC9B,aAAM,CAAC,GAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC9B,GAAG,WAAI,CAAC,SAAS,EAAE,8BAA8B,CAAC,MAAM;oBACxD,kDAAkD;oBAClD,oBAAoB;oBACpB,uBAAuB;iBACxB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,6BAA6B,EAAE,UAAU,IAAI;YAC9C,oBAAI,CAAC,GAAG,GAAG,qBAAqB,EAAE,UAAU,GAAG,EAAE,MAAM;gBACrD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAA;gBAElE,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sDAAsD,EAAE,UAAU,IAAI;YACvE,oBAAI,CAAC,GAAG,GAAG,sBAAsB,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uCAAuC,EAAE,UAAU,IAAI;YACxD,oBAAI,CAAC,GAAG,GAAG,8BAA8B,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC9D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;gBAE/C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kEAAkE,EAAE,UAAU,IAAI;YACnF,oBAAI,CAAC,GAAG,GAAG,+CAA+C,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC/E,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;gBAE/C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uEAAuE,EAAE,UAAU,IAAI;YACxF,oBAAI,CAAC,GAAG,GAAG,8BAA8B,EAAE,EAAE,GAAG,kCAAO,OAAO,CAAC,GAAG,KAAE,sBAAsB,EAAE,MAAM,GAAE,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC3H,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;gBAE/C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,2BAA2B,EAAE,UAAU,IAAI;YAC5C,oBAAI,CAAC,GAAG,GAAG,8BAA8B,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC9D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,YAAY,EAAE;YACrB,EAAE,CAAC,gBAAgB,EAAE,UAAU,IAAI;gBACjC,mEAAmE;gBACnE,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;oBACvE,IAAI,CAAC,IAAI,EAAE,CAAA;iBACZ;qBAAM;oBACL,oBAAI,CAAC,IAAI,QAAQ,2DAA2D,EAAE,UAAU,GAAG,EAAE,MAAM;wBACjG,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;wBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;wBAE3B,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC,CAAC,CAAA;iBACH;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,YAAY,EAAE;YACrB,EAAE,CAAC,oBAAoB,EAAE,UAAU,IAAI;gBACrC,oBAAI,CAAC,IAAI,QAAQ,2DAA2D,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;oBACzG,aAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAA,CAAC,oBAAoB;oBAC3E,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;oBAE3B,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,+BAA+B,EAAE,UAAU,IAAI;gBAChD,oBAAI,CAAC,IAAI,QAAQ,mEAAmE,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;oBACjH,aAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAA,CAAC,iBAAiB;oBAClF,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;oBAE3B,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YACnC,EAAE,CAAC,4BAA4B,EAAE,UAAU,IAAI;gBAC7C,oBAAI,CAAC,GAAG,eAAe,oBAAoB,EAAE,UAAU,GAAG,EAAE,MAAM;oBAChE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;oBAEhC,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YACF,EAAE,CAAC,uEAAuE,EAAE,UAAU,IAAI;gBACxF,IAAI,cAAS,CAAC,WAAI,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC,CAAC,cAAc,EAAE,EAAE;oBACnF,oBAAI,CAAC,GAAG,eAAe,0CAA0C,EAAE,UAAU,GAAG,EAAE,MAAM;wBACtF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;wBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;wBAE3B,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC,CAAC,CAAA;iBACH;qBAAM;oBACL,IAAI,CAAC,IAAI,EAAE,CAAA;iBACZ;YACH,CAAC,CAAC,CAAA;SACH;QAED,QAAQ,CAAC,gDAAgD,EAAE;YACzD,MAAM,QAAQ,GAAG,IAAI,QAAQ,kDAAkD,CAAA;YAE/E,EAAE,CAAC,2CAA2C,EAAE,UAAU,IAAI;gBAC5D,oBAAI,CAAC,GAAG,QAAQ,yCAAyC,EAAE;oBACzD,GAAG,kCACE,OAAO,CAAC,GAAG,KACd,wBAAwB,EAAE,kCAAkC,GAC7D;iBACF,EAAE,UAAU,GAAG,EAAE,MAAM;oBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBACrC,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,yCAAyC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;oBAChI,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;gBAC1D,oBAAI,CAAC,GAAG,QAAQ,yCAAyC,EAAE,UAAU,GAAG,EAAE,MAAM;oBAC9E,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAC9C,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,8CAA8C,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;oBACrI,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAA;oBACrE,aAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oBAC1C,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;oBAC1C,aAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC5C,aAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAAC,CAAC,CAAA;oBAClG,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,yDAAyD,EAAE,UAAU,IAAI;gBAC1E,oBAAI,CAAC,GAAG,QAAQ,8JAA8J,EAAE,UAAU,GAAG,EAAE,MAAM;oBACnM,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAC9C,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,8CAA8C,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;oBACrI,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAA;oBAC3D,aAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oBAC1C,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBACzC,aAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC5C,aAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;wBACpC,WAAI,CAAC,SAAS,EAAE,wCAAwC,CAAC;wBACzD,uCAAuC;qBACxC,CAAC,CAAA;oBACF,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,kDAAkD,EAAE,UAAU,IAAI;gBACnE,oBAAI,CAAC,GAAG,QAAQ,yCAAyC,EAAE;oBACzD,GAAG,kCACE,OAAO,CAAC,GAAG,KACd,cAAc,EAAE,MAAM,EACtB,mBAAmB,EAAE,MAAM,GAC5B;iBACF,EAAE,UAAU,GAAG,EAAE,MAAM;oBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAC9C,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,8CAA8C,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;oBACrI,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAA;oBACrE,aAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBACrC,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;oBAC1C,aAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC5C,aAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAAC,CAAC,CAAA;oBAClG,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,eAAe,EAAE;YACxB,EAAE,CAAC,oBAAoB,EAAE,UAAU,IAAI;gBACrC,oBAAI,CAAC,GAAG,GAAG,oCAAoC,EAAE,UAAU,GAAG,EAAE,MAAM;oBACpE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;oBAE1C,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yEAAyE,EAAE,UAAU,IAAI;YAC1F,oBAAI,CAAC,GAAG,YAAY,0DAA0D,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;gBAC3G,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,qBAAqB,GAAG,cAAc,MAAM,cAAc,MAAM,EAAE,CAAC,CAAA;gBACxF,aAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;oBACvC,QAAQ,EAAE;wBACR,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;wBACxD,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;wBACxD,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;wBACxD,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;qBACzD;oBACD,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;oBACxD,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;oBACxD,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;oBACxD,KAAK,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;iBACzD,CAAC,CAAA;gBACF,IAAI,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,sCAAsC,EAAE;YAC/C,EAAE,CAAC,sBAAsB,EAAE,UAAU,IAAI;gBACvC,oBAAI,CAAC,GAAG,YAAY,4CAA4C,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;oBAC7F,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAC9C,4CAA4C;wBAC5C,mFAAmF;wBACnF,IAAI,CACL,CAAA;oBACD,IAAI,EAAE,CAAA;gBACR,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,qBAAqB,EAAE,UAAU,IAAI;gBACtC,oBAAI,CAAC,GAAG,YAAY,mDAAmD,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;oBACpG,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAC9C,4CAA4C;wBAC5C,mFAAmF;wBACnF,IAAI,CACL,CAAA;oBACD,IAAI,EAAE,CAAA;gBACR,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,UAAU,EAAE;QACnB,IAAI,UAA+B,CAAA;QACnC,IAAI,cAAsB,CAAA;QAC1B,MAAM,CAAC,GAAG,EAAE;YACV,UAAU,GAAG,QAAQ,CAAC;gBACpB,OAAO,EAAE,OAAO;gBAChB,eAAe,EAAE;oBACf,GAAG,EAAE,UAAU;iBAChB;aACF,CAAC,CAAA;YACF,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QACrD,CAAC,CAAC,CAAA;QAEF,SAAS,CAAC,GAAG,EAAE;YACb,sCAAsC;YACtC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC1B,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sCAAsC,EAAE;YACzC,MAAM,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;YAEjC,aAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sCAAsC,EAAE;YACzC,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YAEpC,aAAM,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACjD,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAElE,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YAEpC,aAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC5C,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAElE,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YAEpC,aAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC/C,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAA;YAEpD,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YAEpC,aAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC3C,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAA;QACtD,CAAC,CAAC,CAAA;QAEF,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YACnC,EAAE,CAAC,gCAAgC,EAAE;gBACnC,MAAM,KAAK,GAAa,EAAE,CAAA;gBAE1B,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;gBAEzB,MAAM,SAAS,GAAG;oBAChB,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;oBACzD,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;iBAC1D,CAAA;gBAED,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBACpB,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAA;oBACrB,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE;wBACzC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;wBAEpB,OAAO,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;oBACxC,CAAC,CAAA;gBACH,CAAC,CAAC,CAAA;gBAEF,IAAI;oBACF,aAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;oBACvD,aAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;iBACxD;wBAAS;oBACR,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;iBACzC;gBAED,aAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC1B,WAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC;oBAClC,WAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC;iBACnC,CAAC,CAAA;gBAEF,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;gBAEpC,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAClD,CAAC,CAAC,CAAA;SACH;QAED,EAAE,CAAC,kCAAkC,EAAE;YACrC,MAAM,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAA;YAErC,aAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,6BAA6B,EAAE;YAChC,MAAM,CAAC,GAAG,UAAU,CAAC,kBAAkB,EAAE;gBACvC,WAAW,EAAE,OAAO;aACrB,CAAC,CAAA;YAEF,aAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kCAAkC,EAAE;YACrC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAA;YAEhE,aAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;YACzC,IAAI;gBACF,OAAO,CAAC,gBAAgB,CAAC,CAAA;aAC1B;YAAC,OAAO,KAAK,EAAE;gBACd,aAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC7B,uBAAuB;oBACvB,mBAAmB,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,UAAU;iBAClE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,IAAI,EAAE,CAAA;aACP;QACH,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,IAAI,GAAyC,CAAA;YAC7C,IAAI,QAAgB,CAAA;YAEpB,MAAM,CAAC;gBACL,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAE,CAAA,CAAC,sBAAsB;gBACxD,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAM,EAAE,QAAQ,EAAE,EAAE;oBAChD,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;oBAE3B,CAAC,CAAC,QAAQ,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE;wBAC9C,QAAQ,GAAG,IAAI,CAAA;wBACf,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;oBAC5C,CAAC,CAAA;oBAED,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;gBACzB,CAAC,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,KAAK,CAAC;gBACJ,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA,CAAC,sBAAsB;YACzD,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;gBACzC,IAAI;oBACF,OAAO,CAAC,uBAAuB,CAAC,CAAA;iBACjC;gBAAC,OAAO,KAAK,EAAE;oBACd,aAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAA;iBAChE;gBAED,aAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE5C,IAAI,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAI,OAA4B,CAAA;QAChC,MAAM,CAAC,GAAG,EAAE;YACV,OAAO,GAAG,MAAM,CAAC,EAAE,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAA;QAC7E,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,SAAS,CAAC,CAAA;YACzD,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;YAC3C,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;gBACtC,aAAM,CAAC,OAAO,CAAC,WAAW,CAAC,6BAA6B,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;oBACtF,OAAO,EAAE,YAAY;oBACrB,IAAI,EAAE,aAAa;iBACpB,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YACF,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;gBAC7D,aAAM,CAAC,OAAO,CAAC,WAAW,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;oBACrF,OAAO,EAAE,EAAE;oBACX,IAAI,EAAE,EAAE;iBACT,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,SAAS,WAAW,CAAE,OAAuC,EAAE,OAAiB,EAAE,UAAoB;YACpG,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;gBACzB,aAAM,CAAC,OAAO,CAAC,WAAI,CAAC,SAAS,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,iBAAiB,GAAG,QAAQ,CAAC,CAAA;aAC3F;YACD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;gBAC5B,aAAM,CAAC,OAAO,CAAC,WAAI,CAAC,SAAS,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,GAAG,QAAQ,CAAC,CAAA;aAC1F;QACH,CAAC;QAED,EAAE,CAAC,sFAAsF,EAAE,GAAG,EAAE;YAC9F,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,eAAe,EAAE,EAAG,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAA;YACvE,WAAW,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAA;QAC7F,CAAC,CAAC,CAAA;QACF,EAAE,CAAC,qFAAqF,EAAE,GAAG,EAAE;YAC7F,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAA;YACrF,WAAW,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAA;QAC7F,CAAC,CAAC,CAAA;QACF,EAAE,CAAC,qFAAqF,EAAE,GAAG,EAAE;YAC7F,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAA;YACvG,WAAW,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAA;QAC7F,CAAC,CAAC,CAAA;QACF,EAAE,CAAC,oFAAoF,EAAE,GAAG,EAAE;YAC5F,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAA;YACtG,WAAW,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAA;QAC7F,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,MAAM,GAAG,GAAG,2BAA2B,CAAA;QAEvC,IAAI,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;YACzC,EAAE,CAAC,mCAAmC,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC/C,oBAAI,CAAC,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,cAAc,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;oBACrF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;oBAEpD,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YACF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;gBACzC,oBAAI,CAAC,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,cAAc,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;oBACrF,aAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,aAAM,CAAC,GAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;wBAC9B,GAAG,mBAAa,CAAC,WAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC,MAAM;wBAChE,kDAAkD;wBAClD,oBAAoB;wBACpB,uBAAuB;qBACxB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;oBAEb,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,QAAQ,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBAC/D,EAAE,CAAC,yCAAyC,EAAE,CAAC,IAAI,EAAE,EAAE;oBACrD,oBAAI,CAAC,GAAG,GAAG,oDAAoD,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,4BAA4B,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;wBAC5I,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;wBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;wBAEpD,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;gBACF,EAAE,CAAC,4CAA4C,EAAE,CAAC,IAAI,EAAE,EAAE;oBACxD,oBAAI,CAAC,GAAG,GAAG,wEAAwE,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,4BAA4B,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;wBAChK,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;wBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;wBAEpD,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;gBACF,EAAE,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE,EAAE;oBAC9B,oBAAI,CAAC,GAAG,GAAG,WAAW,EAAE;wBACtB,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,4BAA4B,CAAC;wBAClD,GAAG,kCACE,OAAO,CAAC,GAAG,KACd,YAAY,EAAE,0CAA0C,GACzD;qBACF,EAAE,UAAU,GAAG,EAAE,MAAM;wBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;wBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;wBAEpD,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,+FAA+F,EAAE,UAAU,IAAI;gBAChH,oBAAI,CAAC,GAAG,GAAG,aAAa,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,8BAA8B,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;oBAC/G,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,6DAA6D,CAAC,CAAA;oBAExF,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,sEAAsE,EAAE,UAAU,IAAI;gBACvF,oBAAI,CAAC,GAAG,GAAG,YAAY,EAAE;oBACvB,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,8BAA8B,CAAC;iBACrD,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;oBAC9B,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,4CAA4C;oBAC5C,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAA;oBACxG,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,wDAAwD,EAAE,CAAC,IAAI,EAAE,EAAE;gBACpE,oBAAI,CAAC,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,2BAA2B,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;oBAClG,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;oBAE7C,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,oEAAoE,EAAE,CAAC,IAAI,EAAE,EAAE;gBAChF,oBAAI,CAAC,GAAG,GAAG,0BAA0B,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,6BAA6B,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;oBACnH,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;oBAE3B,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YACF,EAAE,CAAC,yDAAyD,EAAE,CAAC,IAAI,EAAE,EAAE;gBACrE,oBAAI,CAAC,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,6BAA6B,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;oBACpG,IAAI,GAAG,KAAK,IAAI,EAAE;wBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;qBAC/D;oBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAA;oBAC9D,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,mGAAmG,CAAC,CAAC,CAAA;oBAC7I,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,mFAAmF,CAAC,CAAC,CAAA;oBAC7H,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;oBAE3B,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;SACH;QAED,EAAE,CAAC,0JAA0J,EAAE,UAAU,IAAI;YAC3K,oBAAI,CAAC,GAAG,QAAQ,uCAAuC,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC5E,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;gBAErC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA", "sourcesContent": ["import { expect } from 'chai'\nimport { exec } from 'child_process'\nimport { join } from 'path'\nimport semver = require('semver')\nimport ts = require('typescript')\nimport proxyquire = require('proxyquire')\nimport type * as tsNodeTypes from './index'\nimport { unlinkSync, existsSync, lstatSync } from 'fs'\nimport * as promisify from 'util.promisify'\nimport { sync as rimrafSync } from 'rimraf'\nimport type _createRequire from 'create-require'\nconst createRequire: typeof _createRequire = require('create-require')\nimport { pathToFileURL } from 'url'\nimport Module = require('module')\nimport { PassThrough } from 'stream'\nimport * as getStream from 'get-stream'\n\nconst execP = promisify(exec)\n\nconst TEST_DIR = join(__dirname, '../tests')\nconst PROJECT = join(TEST_DIR, 'tsconfig.json')\nconst BIN_PATH = join(TEST_DIR, 'node_modules/.bin/ts-node')\nconst BIN_SCRIPT_PATH = join(TEST_DIR, 'node_modules/.bin/ts-node-script')\n\nconst SOURCE_MAP_REGEXP = /\\/\\/# sourceMappingURL=data:application\\/json;charset=utf\\-8;base64,[\\w\\+]+=*$/\n\n// `createRequire` does not exist on older node versions\nconst testsDirRequire = createRequire(join(TEST_DIR, 'index.js')) // tslint:disable-line\n\n// Set after ts-node is installed locally\nlet { register, create, VERSION, createRepl }: typeof tsNodeTypes = {} as any\n\n// Pack and install ts-node locally, necessary to test package \"exports\"\nbefore(async function () {\n  this.timeout(5 * 60e3)\n  rimrafSync(join(TEST_DIR, 'node_modules'))\n  await execP(`npm install`, { cwd: TEST_DIR })\n  const packageLockPath = join(TEST_DIR, 'package-lock.json')\n  existsSync(packageLockPath) && unlinkSync(packageLockPath)\n  ;({ register, create, VERSION, createRepl } = testsDirRequire('ts-node'))\n})\n\ndescribe('ts-node', function () {\n  const cmd = `\"${BIN_PATH}\" --project \"${PROJECT}\"`\n  const cmdNoProject = `\"${BIN_PATH}\"`\n\n  this.timeout(10000)\n\n  it('should export the correct version', function () {\n    expect(VERSION).to.equal(require('../package.json').version)\n  })\n  it('should export all CJS entrypoints', function () {\n    // Ensure our package.json \"exports\" declaration allows `require()`ing all our entrypoints\n    // https://github.com/TypeStrong/ts-node/pull/1026\n\n    testsDirRequire.resolve('ts-node')\n\n    // only reliably way to ask node for the root path of a dependency is Path.resolve(require.resolve('ts-node/package'), '..')\n    testsDirRequire.resolve('ts-node/package')\n    testsDirRequire.resolve('ts-node/package.json')\n\n    // All bin entrypoints for people who need to augment our CLI: `node -r otherstuff ./node_modules/ts-node/dist/bin`\n    testsDirRequire.resolve('ts-node/dist/bin')\n    testsDirRequire.resolve('ts-node/dist/bin.js')\n    testsDirRequire.resolve('ts-node/dist/bin-transpile')\n    testsDirRequire.resolve('ts-node/dist/bin-transpile.js')\n    testsDirRequire.resolve('ts-node/dist/bin-script')\n    testsDirRequire.resolve('ts-node/dist/bin-script.js')\n\n    // Must be `require()`able obviously\n    testsDirRequire.resolve('ts-node/register')\n    testsDirRequire.resolve('ts-node/register/files')\n    testsDirRequire.resolve('ts-node/register/transpile-only')\n    testsDirRequire.resolve('ts-node/register/type-check')\n\n    // `node --loader ts-node/esm`\n    testsDirRequire.resolve('ts-node/esm')\n    testsDirRequire.resolve('ts-node/esm.mjs')\n    testsDirRequire.resolve('ts-node/esm/transpile-only')\n    testsDirRequire.resolve('ts-node/esm/transpile-only.mjs')\n  })\n\n  describe('cli', function () {\n    this.slow(1000)\n\n    it('should execute cli', function (done) {\n      exec(`${cmd} tests/hello-world`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('shows usage via --help', function (done) {\n      exec(`${cmdNoProject} --help`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.match(/Usage: ts-node /)\n        return done()\n      })\n    })\n    it('shows version via -v', function (done) {\n      exec(`${cmdNoProject} -v`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout.trim()).to.equal('v' + testsDirRequire('ts-node/package').version)\n        return done()\n      })\n    })\n    it('shows version of compiler via -vv', function (done) {\n      exec(`${cmdNoProject} -vv`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout.trim()).to.equal(\n          `ts-node v${ testsDirRequire('ts-node/package').version }\\n` +\n          `node ${ process.version }\\n` +\n          `compiler v${ testsDirRequire('typescript/package').version }`\n        )\n        return done()\n      })\n    })\n\n    it('should register via cli', function (done) {\n      exec(`node -r ts-node/register hello-world.ts`, {\n        cwd: TEST_DIR\n      }, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should execute cli with absolute path', function (done) {\n      exec(`${cmd} \"${join(TEST_DIR, 'hello-world')}\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should print scripts', function (done) {\n      exec(`${cmd} -pe \"import { example } from './tests/complex/index';example()\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('example\\n')\n\n        return done()\n      })\n    })\n\n    it('should provide registered information globally', function (done) {\n      exec(`${cmd} tests/env`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('object\\n')\n\n        return done()\n      })\n    })\n\n    it('should provide registered information on register', function (done) {\n      exec(`node -r ts-node/register env.ts`, {\n        cwd: TEST_DIR\n      }, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('object\\n')\n\n        return done()\n      })\n    })\n\n    if (semver.gte(ts.version, '1.8.0')) {\n      it('should allow js', function (done) {\n        exec(\n          [\n            cmd,\n            '-O \"{\\\\\\\"allowJs\\\\\\\":true}\"',\n            '-pe \"import { main } from \\'./tests/allow-js/run\\';main()\"'\n          ].join(' '),\n          function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('hello world\\n')\n\n            return done()\n          }\n        )\n      })\n\n      it('should include jsx when `allow-js` true', function (done) {\n        exec(\n          [\n            cmd,\n            '-O \"{\\\\\\\"allowJs\\\\\\\":true}\"',\n            '-pe \"import { Foo2 } from \\'./tests/allow-js/with-jsx\\'; Foo2.sayHi()\"'\n          ].join(' '),\n          function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('hello world\\n')\n\n            return done()\n          }\n        )\n      })\n    }\n\n    it('should eval code', function (done) {\n      exec(\n        `${cmd} -e \"import * as m from './tests/module';console.log(m.example('test'))\"`,\n        function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('TEST\\n')\n\n          return done()\n        }\n      )\n    })\n\n    it('should import empty files', function (done) {\n      exec(`${cmd} -e \"import './tests/empty'\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('')\n\n        return done()\n      })\n    })\n\n    it('should throw errors', function (done) {\n      exec(`${cmd} -e \"import * as m from './tests/module';console.log(m.example(123))\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.match(new RegExp(\n          'TS2345: Argument of type \\'(?:number|123)\\' ' +\n          'is not assignable to parameter of type \\'string\\'\\\\.'\n        ))\n\n        return done()\n      })\n    })\n\n    it('should be able to ignore diagnostic', function (done) {\n      exec(\n        `${cmd} --ignore-diagnostics 2345 -e \"import * as m from './tests/module';console.log(m.example(123))\"`,\n        function (err) {\n          if (err === null) {\n            return done('Command was expected to fail, but it succeeded.')\n          }\n\n          expect(err.message).to.match(\n            /TypeError: (?:(?:undefined|foo\\.toUpperCase) is not a function|.*has no method \\'toUpperCase\\')/\n          )\n\n          return done()\n        }\n      )\n    })\n\n    it('should work with source maps', function (done) {\n      exec(`${cmd} tests/throw`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain([\n          `${join(__dirname, '../tests/throw.ts')}:100`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^',\n          'Error: this is a demo'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it('eval should work with source maps', function (done) {\n      exec(`${cmd} -pe \"import './tests/throw'\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain([\n          `${join(__dirname, '../tests/throw.ts')}:100`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it('should support transpile only mode', function (done) {\n      exec(`${cmd} --transpile-only -pe \"x\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain('ReferenceError: x is not defined')\n\n        return done()\n      })\n    })\n\n    it('should throw error even in transpileOnly mode', function (done) {\n      exec(`${cmd} --transpile-only -pe \"console.\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain('error TS1003: Identifier expected')\n\n        return done()\n      })\n    })\n\n    it('should pipe into `ts-node` and evaluate', function (done) {\n      const cp = exec(cmd, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('hello\\n')\n\n        return done()\n      })\n\n      cp.stdin!.end(\"console.log('hello')\")\n    })\n\n    it('should pipe into `ts-node`', function (done) {\n      const cp = exec(`${cmd} -p`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('true\\n')\n\n        return done()\n      })\n\n      cp.stdin!.end('true')\n    })\n\n    it('should pipe into an eval script', function (done) {\n      const cp = exec(`${cmd} --transpile-only -pe \"process.stdin.isTTY\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('undefined\\n')\n\n        return done()\n      })\n\n      cp.stdin!.end('true')\n    })\n\n    it('should run REPL when --interactive passed and stdin is not a TTY', function (done) {\n      const cp = exec(`${cmd} --interactive`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal(\n          '> 123\\n' +\n          'undefined\\n' +\n          '> '\n        )\n        return done()\n      })\n\n      cp.stdin!.end('console.log(\"123\")\\n')\n    })\n\n    it('REPL has command to get type information', function (done) {\n      const cp = exec(`${cmd} --interactive`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal(\n          '> undefined\\n' +\n          '> undefined\\n' +\n          '> const a: 123\\n' +\n          '> '\n        )\n        return done()\n      })\n\n      cp.stdin!.end('\\nconst a = 123\\n.type a')\n    })\n\n    it('REPL can be created via API', async () => {\n      const stdin = new PassThrough()\n      const stdout = new PassThrough()\n      const stderr = new PassThrough()\n      const replService = createRepl({\n        stdin,\n        stdout,\n        stderr\n      })\n      const service = create(replService.evalAwarePartialHost)\n      replService.setService(service)\n      replService.start()\n      stdin.write('\\nconst a = 123\\n.type a\\n')\n      stdin.end()\n      await promisify(setTimeout)(1e3)\n      stdout.end()\n      stderr.end()\n      expect(await getStream(stderr)).to.equal('')\n      expect(await getStream(stdout)).to.equal(\n        '> \\'use strict\\'\\n' +\n        '> undefined\\n' +\n        '> const a: 123\\n' +\n        '> '\n      )\n    })\n\n    it('should support require flags', function (done) {\n      exec(`${cmd} -r ./tests/hello-world -pe \"console.log('success')\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\nsuccess\\nundefined\\n')\n\n        return done()\n      })\n    })\n\n    it('should support require from node modules', function (done) {\n      exec(`${cmd} -r typescript -e \"console.log('success')\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('success\\n')\n\n        return done()\n      })\n    })\n\n    it('should use source maps with react tsx', function (done) {\n      exec(`${cmd} tests/throw-react-tsx.tsx`, function (err, stdout) {\n        expect(err).not.to.equal(null)\n        expect(err!.message).to.contain([\n          `${join(__dirname, '../tests/throw-react-tsx.tsx')}:100`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^',\n          'Error: this is a demo'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it('should allow custom typings', function (done) {\n      exec(`${cmd} tests/custom-types`, function (err, stdout) {\n        expect(err).to.match(/Error: Cannot find module 'does-not-exist'/)\n\n        return done()\n      })\n    })\n\n    it('should preserve `ts-node` context with child process', function (done) {\n      exec(`${cmd} tests/child-process`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should import js before ts by default', function (done) {\n      exec(`${cmd} tests/import-order/compiled`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, JavaScript!\\n')\n\n        return done()\n      })\n    })\n\n    it('should import ts before js when --prefer-ts-exts flag is present', function (done) {\n      exec(`${cmd} --prefer-ts-exts tests/import-order/compiled`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, TypeScript!\\n')\n\n        return done()\n      })\n    })\n\n    it('should import ts before js when TS_NODE_PREFER_TS_EXTS env is present', function (done) {\n      exec(`${cmd} tests/import-order/compiled`, { env: { ...process.env, TS_NODE_PREFER_TS_EXTS: 'true' } }, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, TypeScript!\\n')\n\n        return done()\n      })\n    })\n\n    it('should ignore .d.ts files', function (done) {\n      exec(`${cmd} tests/import-order/importer`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, World!\\n')\n\n        return done()\n      })\n    })\n\n    describe('issue #884', function () {\n      it('should compile', function (done) {\n        // TODO disabled because it consistently fails on Windows on TS 2.7\n        if (process.platform === 'win32' && semver.satisfies(ts.version, '2.7')) {\n          this.skip()\n        } else {\n          exec(`\"${BIN_PATH}\" --project tests/issue-884/tsconfig.json tests/issue-884`, function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('')\n\n            return done()\n          })\n        }\n      })\n    })\n\n    describe('issue #986', function () {\n      it('should not compile', function (done) {\n        exec(`\"${BIN_PATH}\" --project tests/issue-986/tsconfig.json tests/issue-986`, function (err, stdout, stderr) {\n          expect(err).not.to.equal(null)\n          expect(stderr).to.contain('Cannot find name \\'TEST\\'') // TypeScript error.\n          expect(stdout).to.equal('')\n\n          return done()\n        })\n      })\n\n      it('should compile with `--files`', function (done) {\n        exec(`\"${BIN_PATH}\" --files --project tests/issue-986/tsconfig.json tests/issue-986`, function (err, stdout, stderr) {\n          expect(err).not.to.equal(null)\n          expect(stderr).to.contain('ReferenceError: TEST is not defined') // Runtime error.\n          expect(stdout).to.equal('')\n\n          return done()\n        })\n      })\n    })\n\n    if (semver.gte(ts.version, '2.7.0')) {\n      it('should support script mode', function (done) {\n        exec(`${BIN_SCRIPT_PATH} tests/scope/a/log`, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('.ts\\n')\n\n          return done()\n        })\n      })\n      it('should read tsconfig relative to realpath, not symlink, in scriptMode', function (done) {\n        if (lstatSync(join(TEST_DIR, 'main-realpath/symlink/symlink.tsx')).isSymbolicLink()) {\n          exec(`${BIN_SCRIPT_PATH} tests/main-realpath/symlink/symlink.tsx`, function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('')\n\n            return done()\n          })\n        } else {\n          this.skip()\n        }\n      })\n    }\n\n    describe('should read ts-node options from tsconfig.json', function () {\n      const BIN_EXEC = `\"${BIN_PATH}\" --project tests/tsconfig-options/tsconfig.json`\n\n      it('should override compiler options from env', function (done) {\n        exec(`${BIN_EXEC} tests/tsconfig-options/log-options1.js`, {\n          env: {\n            ...process.env,\n            TS_NODE_COMPILER_OPTIONS: '{\"typeRoots\": [\"env-typeroots\"]}'\n          }\n        }, function (err, stdout) {\n          expect(err).to.equal(null)\n          const { config } = JSON.parse(stdout)\n          expect(config.options.typeRoots).to.deep.equal([join(__dirname, '../tests/tsconfig-options/env-typeroots').replace(/\\\\/g, '/')])\n          return done()\n        })\n      })\n\n      it('should use options from `tsconfig.json`', function (done) {\n        exec(`${BIN_EXEC} tests/tsconfig-options/log-options1.js`, function (err, stdout) {\n          expect(err).to.equal(null)\n          const { options, config } = JSON.parse(stdout)\n          expect(config.options.typeRoots).to.deep.equal([join(__dirname, '../tests/tsconfig-options/tsconfig-typeroots').replace(/\\\\/g, '/')])\n          expect(config.options.types).to.deep.equal(['tsconfig-tsnode-types'])\n          expect(options.pretty).to.equal(undefined)\n          expect(options.skipIgnore).to.equal(false)\n          expect(options.transpileOnly).to.equal(true)\n          expect(options.require).to.deep.equal([join(__dirname, '../tests/tsconfig-options/required1.js')])\n          return done()\n        })\n      })\n\n      it('should have flags override / merge with `tsconfig.json`', function (done) {\n        exec(`${BIN_EXEC} --skip-ignore --compiler-options \"{\\\\\"types\\\\\":[\\\\\"flags-types\\\\\"]}\" --require ./tests/tsconfig-options/required2.js tests/tsconfig-options/log-options2.js`, function (err, stdout) {\n          expect(err).to.equal(null)\n          const { options, config } = JSON.parse(stdout)\n          expect(config.options.typeRoots).to.deep.equal([join(__dirname, '../tests/tsconfig-options/tsconfig-typeroots').replace(/\\\\/g, '/')])\n          expect(config.options.types).to.deep.equal(['flags-types'])\n          expect(options.pretty).to.equal(undefined)\n          expect(options.skipIgnore).to.equal(true)\n          expect(options.transpileOnly).to.equal(true)\n          expect(options.require).to.deep.equal([\n            join(__dirname, '../tests/tsconfig-options/required1.js'),\n            './tests/tsconfig-options/required2.js'\n          ])\n          return done()\n        })\n      })\n\n      it('should have `tsconfig.json` override environment', function (done) {\n        exec(`${BIN_EXEC} tests/tsconfig-options/log-options1.js`, {\n          env: {\n            ...process.env,\n            TS_NODE_PRETTY: 'true',\n            TS_NODE_SKIP_IGNORE: 'true'\n          }\n        }, function (err, stdout) {\n          expect(err).to.equal(null)\n          const { options, config } = JSON.parse(stdout)\n          expect(config.options.typeRoots).to.deep.equal([join(__dirname, '../tests/tsconfig-options/tsconfig-typeroots').replace(/\\\\/g, '/')])\n          expect(config.options.types).to.deep.equal(['tsconfig-tsnode-types'])\n          expect(options.pretty).to.equal(true)\n          expect(options.skipIgnore).to.equal(false)\n          expect(options.transpileOnly).to.equal(true)\n          expect(options.require).to.deep.equal([join(__dirname, '../tests/tsconfig-options/required1.js')])\n          return done()\n        })\n      })\n    })\n\n    describe('compiler host', function () {\n      it('should execute cli', function (done) {\n        exec(`${cmd} --compiler-host tests/hello-world`, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('Hello, world!\\n')\n\n          return done()\n        })\n      })\n    })\n\n    it('should transpile files inside a node_modules directory when not ignored', function (done) {\n      exec(`${cmdNoProject} --script-mode tests/from-node-modules/from-node-modules`, function (err, stdout, stderr) {\n        if (err) return done(`Unexpected error: ${err}\\nstdout:\\n${stdout}\\nstderr:\\n${stderr}`)\n        expect(JSON.parse(stdout)).to.deep.equal({\n          external: {\n            tsmri: { name: 'typescript-module-required-internally' },\n            jsmri: { name: 'javascript-module-required-internally' },\n            tsmii: { name: 'typescript-module-imported-internally' },\n            jsmii: { name: 'javascript-module-imported-internally' }\n          },\n          tsmie: { name: 'typescript-module-imported-externally' },\n          jsmie: { name: 'javascript-module-imported-externally' },\n          tsmre: { name: 'typescript-module-required-externally' },\n          jsmre: { name: 'javascript-module-required-externally' }\n        })\n        done()\n      })\n    })\n\n    describe('should respect maxNodeModulesJsDepth', function () {\n      it('for unscoped modules', function (done) {\n        exec(`${cmdNoProject} --script-mode tests/maxnodemodulesjsdepth`, function (err, stdout, stderr) {\n          expect(err).to.not.equal(null)\n          expect(stderr.replace(/\\r\\n/g, '\\n')).to.contain(\n            'TSError: ⨯ Unable to compile TypeScript:\\n' +\n            \"other.ts(4,7): error TS2322: Type 'string' is not assignable to type 'boolean'.\\n\" +\n            '\\n'\n          )\n          done()\n        })\n      })\n\n      it('for @scoped modules', function (done) {\n        exec(`${cmdNoProject} --script-mode tests/maxnodemodulesjsdepth-scoped`, function (err, stdout, stderr) {\n          expect(err).to.not.equal(null)\n          expect(stderr.replace(/\\r\\n/g, '\\n')).to.contain(\n            'TSError: ⨯ Unable to compile TypeScript:\\n' +\n            \"other.ts(7,7): error TS2322: Type 'string' is not assignable to type 'boolean'.\\n\" +\n            '\\n'\n          )\n          done()\n        })\n      })\n    })\n  })\n\n  describe('register', function () {\n    let registered: tsNodeTypes.Service\n    let moduleTestPath: string\n    before(() => {\n      registered = register({\n        project: PROJECT,\n        compilerOptions: {\n          jsx: 'preserve'\n        }\n      })\n      moduleTestPath = require.resolve('../tests/module')\n    })\n\n    afterEach(() => {\n      // Re-enable project after every test.\n      registered.enabled(true)\n    })\n\n    it('should be able to require typescript', function () {\n      const m = require(moduleTestPath)\n\n      expect(m.example('foo')).to.equal('FOO')\n    })\n\n    it('should support dynamically disabling', function () {\n      delete require.cache[moduleTestPath]\n\n      expect(registered.enabled(false)).to.equal(false)\n      expect(() => require(moduleTestPath)).to.throw(/Unexpected token/)\n\n      delete require.cache[moduleTestPath]\n\n      expect(registered.enabled()).to.equal(false)\n      expect(() => require(moduleTestPath)).to.throw(/Unexpected token/)\n\n      delete require.cache[moduleTestPath]\n\n      expect(registered.enabled(true)).to.equal(true)\n      expect(() => require(moduleTestPath)).to.not.throw()\n\n      delete require.cache[moduleTestPath]\n\n      expect(registered.enabled()).to.equal(true)\n      expect(() => require(moduleTestPath)).to.not.throw()\n    })\n\n    if (semver.gte(ts.version, '2.7.0')) {\n      it('should support compiler scopes', function () {\n        const calls: string[] = []\n\n        registered.enabled(false)\n\n        const compilers = [\n          register({ dir: join(TEST_DIR, 'scope/a'), scope: true }),\n          register({ dir: join(TEST_DIR, 'scope/b'), scope: true })\n        ]\n\n        compilers.forEach(c => {\n          const old = c.compile\n          c.compile = (code, fileName, lineOffset) => {\n            calls.push(fileName)\n\n            return old(code, fileName, lineOffset)\n          }\n        })\n\n        try {\n          expect(require('../tests/scope/a').ext).to.equal('.ts')\n          expect(require('../tests/scope/b').ext).to.equal('.ts')\n        } finally {\n          compilers.forEach(c => c.enabled(false))\n        }\n\n        expect(calls).to.deep.equal([\n          join(TEST_DIR, 'scope/a/index.ts'),\n          join(TEST_DIR, 'scope/b/index.ts')\n        ])\n\n        delete require.cache[moduleTestPath]\n\n        expect(() => require(moduleTestPath)).to.throw()\n      })\n    }\n\n    it('should compile through js and ts', function () {\n      const m = require('../tests/complex')\n\n      expect(m.example()).to.equal('example')\n    })\n\n    it('should work with proxyquire', function () {\n      const m = proxyquire('../tests/complex', {\n        './example': 'hello'\n      })\n\n      expect(m.example()).to.equal('hello')\n    })\n\n    it('should work with `require.cache`', function () {\n      const { example1, example2 } = require('../tests/require-cache')\n\n      expect(example1).to.not.equal(example2)\n    })\n\n    it('should use source maps', function (done) {\n      try {\n        require('../tests/throw')\n      } catch (error) {\n        expect(error.stack).to.contain([\n          'Error: this is a demo',\n          `    at Foo.bar (${join(__dirname, '../tests/throw.ts')}:100:18)`\n        ].join('\\n'))\n\n        done()\n      }\n    })\n\n    describe('JSX preserve', () => {\n      let old: (m: Module, filename: string) => any\n      let compiled: string\n\n      before(function () {\n        old = require.extensions['.tsx']! // tslint:disable-line\n        require.extensions['.tsx'] = (m: any, fileName) => { // tslint:disable-line\n          const _compile = m._compile\n\n          m._compile = (code: string, fileName: string) => {\n            compiled = code\n            return _compile.call(this, code, fileName)\n          }\n\n          return old(m, fileName)\n        }\n      })\n\n      after(function () {\n        require.extensions['.tsx'] = old // tslint:disable-line\n      })\n\n      it('should use source maps', function (done) {\n        try {\n          require('../tests/with-jsx.tsx')\n        } catch (error) {\n          expect(error.stack).to.contain('SyntaxError: Unexpected token')\n        }\n\n        expect(compiled).to.match(SOURCE_MAP_REGEXP)\n\n        done()\n      })\n    })\n  })\n\n  describe('create', () => {\n    let service: tsNodeTypes.Service\n    before(() => {\n      service = create({ compilerOptions: { target: 'es5' }, skipProject: true })\n    })\n\n    it('should create generic compiler instances', () => {\n      const output = service.compile('const x = 10', 'test.ts')\n      expect(output).to.contain('var x = 10;')\n    })\n\n    describe('should get type information', () => {\n      it('given position of identifier', () => {\n        expect(service.getTypeInfo('/**jsdoc here*/const x = 10', 'test.ts', 21)).to.deep.equal({\n          comment: 'jsdoc here',\n          name: 'const x: 10'\n        })\n      })\n      it('given position that does not point to an identifier', () => {\n        expect(service.getTypeInfo('/**jsdoc here*/const x = 10', 'test.ts', 0)).to.deep.equal({\n          comment: '',\n          name: ''\n        })\n      })\n    })\n  })\n\n  describe('issue #1098', () => {\n    function testIgnored (ignored: tsNodeTypes.Service['ignored'], allowed: string[], disallowed: string[]) {\n      for (const ext of allowed) {\n        expect(ignored(join(__dirname, `index${ext}`))).equal(false, `should accept ${ext} files`)\n      }\n      for (const ext of disallowed) {\n        expect(ignored(join(__dirname, `index${ext}`))).equal(true, `should ignore ${ext} files`)\n      }\n    }\n\n    it('correctly filters file extensions from the compiler when allowJs=false and jsx=false', () => {\n      const { ignored } = create({ compilerOptions: { }, skipProject: true })\n      testIgnored(ignored, ['.ts', '.d.ts'], ['.js', '.tsx', '.jsx', '.mjs', '.cjs', '.xyz', ''])\n    })\n    it('correctly filters file extensions from the compiler when allowJs=true and jsx=false', () => {\n      const { ignored } = create({ compilerOptions: { allowJs: true }, skipProject: true })\n      testIgnored(ignored, ['.ts', '.js', '.d.ts'], ['.tsx', '.jsx', '.mjs', '.cjs', '.xyz', ''])\n    })\n    it('correctly filters file extensions from the compiler when allowJs=false and jsx=true', () => {\n      const { ignored } = create({ compilerOptions: { allowJs: false, jsx: 'preserve' }, skipProject: true })\n      testIgnored(ignored, ['.ts', '.tsx', '.d.ts'], ['.js', '.jsx', '.mjs', '.cjs', '.xyz', ''])\n    })\n    it('correctly filters file extensions from the compiler when allowJs=true and jsx=true', () => {\n      const { ignored } = create({ compilerOptions: { allowJs: true, jsx: 'preserve' }, skipProject: true })\n      testIgnored(ignored, ['.ts', '.tsx', '.js', '.jsx', '.d.ts'], ['.mjs', '.cjs', '.xyz', ''])\n    })\n  })\n\n  describe('esm', () => {\n    this.slow(1000)\n\n    const cmd = `node --loader ts-node/esm`\n\n    if (semver.gte(process.version, '13.0.0')) {\n      it('should compile and execute as ESM', (done) => {\n        exec(`${cmd} index.ts`, { cwd: join(__dirname, '../tests/esm') }, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('foo bar baz biff libfoo\\n')\n\n          return done()\n        })\n      })\n      it('should use source maps', function (done) {\n        exec(`${cmd} throw.ts`, { cwd: join(__dirname, '../tests/esm') }, function (err, stdout) {\n          expect(err).not.to.equal(null)\n          expect(err!.message).to.contain([\n            `${pathToFileURL(join(__dirname, '../tests/esm/throw.ts'))}:100`,\n            '  bar () { throw new Error(\\'this is a demo\\') }',\n            '                 ^',\n            'Error: this is a demo'\n          ].join('\\n'))\n\n          return done()\n        })\n      })\n\n      describe('supports experimental-specifier-resolution=node', () => {\n        it('via --experimental-specifier-resolution', (done) => {\n          exec(`${cmd} --experimental-specifier-resolution=node index.ts`, { cwd: join(__dirname, '../tests/esm-node-resolver') }, function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('foo bar baz biff libfoo\\n')\n\n            return done()\n          })\n        })\n        it('via --es-module-specifier-resolution alias', (done) => {\n          exec(`${cmd} --experimental-modules --es-module-specifier-resolution=node index.ts`, { cwd: join(__dirname, '../tests/esm-node-resolver') }, function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('foo bar baz biff libfoo\\n')\n\n            return done()\n          })\n        })\n        it('via NODE_OPTIONS', (done) => {\n          exec(`${cmd} index.ts`, {\n            cwd: join(__dirname, '../tests/esm-node-resolver'),\n            env: {\n              ...process.env,\n              NODE_OPTIONS: '--experimental-specifier-resolution=node'\n            }\n          }, function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('foo bar baz biff libfoo\\n')\n\n            return done()\n          })\n        })\n      })\n\n      it('throws ERR_REQUIRE_ESM when attempting to require() an ESM script while ESM loader is enabled', function (done) {\n        exec(`${cmd} ./index.js`, { cwd: join(__dirname, '../tests/esm-err-require-esm') }, function (err, stdout, stderr) {\n          expect(err).to.not.equal(null)\n          expect(stderr).to.contain('Error [ERR_REQUIRE_ESM]: Must use import to load ES Module:')\n\n          return done()\n        })\n      })\n\n      it('defers to fallback loaders when URL should not be handled by ts-node', function (done) {\n        exec(`${cmd} index.mjs`, {\n          cwd: join(__dirname, '../tests/esm-import-http-url')\n        }, function (err, stdout, stderr) {\n          expect(err).to.not.equal(null)\n          // expect error from node's default resolver\n          expect(stderr).to.match(/Error \\[ERR_UNSUPPORTED_ESM_URL_SCHEME\\]:.*(?:\\n.*){0,1}\\n *at defaultResolve/)\n          return done()\n        })\n      })\n\n      it('should bypass import cache when changing search params', (done) => {\n        exec(`${cmd} index.ts`, { cwd: join(__dirname, '../tests/esm-import-cache') }, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('log1\\nlog2\\nlog2\\n')\n\n          return done()\n        })\n      })\n\n      it('should support transpile only mode via dedicated loader entrypoint', (done) => {\n        exec(`${cmd}/transpile-only index.ts`, { cwd: join(__dirname, '../tests/esm-transpile-only') }, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('')\n\n          return done()\n        })\n      })\n      it('should throw type errors without transpile-only enabled', (done) => {\n        exec(`${cmd} index.ts`, { cwd: join(__dirname, '../tests/esm-transpile-only') }, function (err, stdout) {\n          if (err === null) {\n            return done('Command was expected to fail, but it succeeded.')\n          }\n\n          expect(err.message).to.contain('Unable to compile TypeScript')\n          expect(err.message).to.match(new RegExp('TS2345: Argument of type \\'(?:number|1101)\\' is not assignable to parameter of type \\'string\\'\\\\.'))\n          expect(err.message).to.match(new RegExp('TS2322: Type \\'(?:\"hello world\"|string)\\' is not assignable to type \\'number\\'\\\\.'))\n          expect(stdout).to.equal('')\n\n          return done()\n        })\n      })\n    }\n\n    it('executes ESM as CJS, ignoring package.json \"types\" field (for backwards compatibility; should be changed in next major release to throw ERR_REQUIRE_ESM)', function (done) {\n      exec(`${BIN_PATH} ./tests/esm-err-require-esm/index.js`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('CommonJS\\n')\n\n        return done()\n      })\n    })\n  })\n})\n"]}