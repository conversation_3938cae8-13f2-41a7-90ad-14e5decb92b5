{"name": "rgb2hex", "version": "0.2.5", "description": "lightweight rgb/rgba to hex parser", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/christian-bromann/rgb2hex", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/christian-bromann/rgb2hex.git"}, "bugs": {"url": "https://github.com/christian-bromann/rgb2hex/issues"}, "scripts": {"test": "jest", "cover": "jest --coverage", "compile-typescript": "tsc"}, "devDependencies": {"jest": "^26.6.3", "typescript": "^4.1.2"}, "types": "./index.d.ts", "keywords": ["rgb", "rgba", "hex", "color", "parse", "parser"], "jest": {"coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}}}