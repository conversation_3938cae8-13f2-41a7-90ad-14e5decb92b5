{"version": 3, "file": "capabilities.d.ts", "sourceRoot": "", "sources": ["../../../lib/basedriver/capabilities.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,oBAAoB,EACrB,MAAM,eAAe,CAAC;AACvB,OAAO,KAAK,EAEV,cAAc,EACf,MAAM,WAAW,CAAC;AAOnB,eAAO,MAAM,oBAAoB,YAAY,CAAC;AAC9C,eAAO,MAAM,wBAAwB,mBAAmC,CAAC;AAEzE,MAAM,MAAM,UAAU,CAAC,CAAC,SAAS,WAAW,IAAI;IAC9C,iBAAiB,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;IACvC,uBAAuB,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3C,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;IAChC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACpC,gBAAgB,EAAE,MAAM,EAAE,CAAC;CAC5B,CAAC;AACF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,4CAA4C;IAC5C,sBAAsB,CAAC,EAAE,OAAO,CAAC;CAClC,CAAA;AAED;;;;GAIG;AACH,wBAAgB,SAAS,CACvB,CAAC,SAAS,WAAW,EACrB,CAAC,SAAS,WAAW,EACrB,OAAO,SAAS,YAAY,CAAC,CAAC,CAAC,EAC/B,SAAS,SAAS,YAAY,CAAC,CAAC,CAAC,EAEjC,OAAO,GAAE,OAAO,GAAG,SAAyB,EAC5C,SAAS,GAAE,SAAS,GAAG,SAA2B,GACjD,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,CAkBpC;AAED;;GAEG;AACH,wBAAgB,YAAY,CAAC,CAAC,SAAS,WAAW,EAChD,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,EACrB,WAAW,GAAE,CAAC,GAAG,SAAmB,EACpC,IAAI,GAAE,gBAAgB,GAAG,SAAc,GACtC,YAAY,CAAC,CAAC,CAAC,CAwCjB;AAED;;;GAGG;AACH,eAAO,MAAM,aAAa,2CAezB,CAAC;AAIF,wBAAgB,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAElD;AAiDD;;GAEG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,SAAS,WAAW,EACvD,EACE,WAAgB,EAChB,UAAe,EAChB,EAAE,eAAe,CAAC,CAAC,CAAC,GACpB,MAAM,EAAE,CAWV;AAED;;;GAGG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,WAAW,EAC7C,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EACxB,WAAW,GAAE,CAAC,GAAG,SAAmB,EACpC,kBAAkB,GAAE,OAAO,GAAG,SAAgB,GAC7C,UAAU,CAAC,CAAC,CAAC,CA8Ff;AAED;;GAEG;AACH,wBAAgB,mBAAmB,CACjC,CAAC,SAAS,WAAW,EACrB,OAAO,SAAS,eAAe,CAAC,CAAC,CAAC,EAElC,OAAO,EAAE,OAAO,EAChB,WAAW,GAAE,CAAC,GAAG,SAAmB,EACpC,kBAAkB,GAAE,OAAO,GAAG,SAAgB,GAC7C,YAAY,CAAC,CAAC,CAAC,CAmBjB;AAED;;;GAGG;AACH,wBAAgB,6BAA6B,CAAC,CAAC,SAAS,WAAW,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAoD9G;AAED;;;GAGG;AACH,wBAAgB,oBAAoB,CAAC,CAAC,SAAS,WAAW,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAchH"}