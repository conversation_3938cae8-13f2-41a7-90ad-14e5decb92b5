<%- _.import('defineParameterType', '@cucumber/cucumber') %>
<%- _.import('actorCalled, actorInTheSpotlight', '@serenity-js/core') %>

defineParameterType({
    regexp: /[A-Z][a-z]+/,
    transformer(<%- _.param('name', 'string') %>) {
        return actorCalled(name)
    },
    name: 'actor',
})

defineParameterType({
    regexp: /he|she|they|his|her|their/,
    transformer() {
        return actorInTheSpotlight()
    },
    name: 'pronoun',
})
