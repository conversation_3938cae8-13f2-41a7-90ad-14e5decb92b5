/**
 * Set the locale name of the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @param {string} language - Language. The language field is case insensitive, but Locale always canonicalizes to lower case.
 * format: [a-zA-Z]{2,8}. e.g. en, ja : https://developer.android.com/reference/java/util/Locale.html
 * @param {string} country - Country. The country (region) field is case insensitive, but Locale always canonicalizes to upper case.
 * format: [a-zA-Z]{2} | [0-9]{3}. e.g. US, JP : https://developer.android.com/reference/java/util/Locale.html
 * @param {string?} [script=null] - Script. The script field is case insensitive but Locale always canonicalizes to title case.
 * format: [a-zA-Z]{4}. e.g. Hans in zh-Hans-CN : https://developer.android.com/reference/java/util/Locale.html
 */
export function setDeviceLocale(this: import("../client").SettingsApp, language: string, country: string, script?: string | null): Promise<void>;
/**
 * @typedef {Object} SupportedLocale
 * @property {string} language
 * @property {string} country
 * @property {string} [script]
 */
/**
 * Retrieves the list of supported device locales
 *
 * @this {import('../client').SettingsApp}
 * @returns {Promise<SupportedLocale[]>}
 */
export function listSupportedLocales(this: import("../client").SettingsApp): Promise<SupportedLocale[]>;
export type SupportedLocale = {
    language: string;
    country: string;
    script?: string | undefined;
};
//# sourceMappingURL=locale.d.ts.map