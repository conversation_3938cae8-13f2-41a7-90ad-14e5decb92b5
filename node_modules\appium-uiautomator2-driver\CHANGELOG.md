## [4.2.5](https://github.com/appium/appium-uiautomator2-driver/compare/v4.2.4...v4.2.5) (2025-07-06)

### Miscellaneous Chores

* Bump android driver ([01fe469](https://github.com/appium/appium-uiautomator2-driver/commit/01fe46943b875f7b00737bde911932e9bb876118))

## [4.2.4](https://github.com/appium/appium-uiautomator2-driver/compare/v4.2.3...v4.2.4) (2025-06-24)

### Miscellaneous Chores

* Bump android driver ([64c28f2](https://github.com/appium/appium-uiautomator2-driver/commit/64c28f24495028b975e9b33e719c38d6642ff6c8))

## [4.2.3](https://github.com/appium/appium-uiautomator2-driver/compare/v4.2.2...v4.2.3) (2025-04-25)

### Miscellaneous Chores

* add deprecated: true for get clipboard ([#887](https://github.com/appium/appium-uiautomator2-driver/issues/887)) ([19c6417](https://github.com/appium/appium-uiautomator2-driver/commit/19c6417ccfc336899d09d6bdf9b4532ad625babe))

## [4.2.2](https://github.com/appium/appium-uiautomator2-driver/compare/v4.2.1...v4.2.2) (2025-04-23)

### Miscellaneous Chores

* Bump android driver ([d52988e](https://github.com/appium/appium-uiautomator2-driver/commit/d52988ee20cba853d83218fee8cedbdc33c8a91c))

## [4.2.1](https://github.com/appium/appium-uiautomator2-driver/compare/v4.2.0...v4.2.1) (2025-04-16)

### Bug Fixes

* Update deprecated constant usage ([#885](https://github.com/appium/appium-uiautomator2-driver/issues/885)) ([9719cb5](https://github.com/appium/appium-uiautomator2-driver/commit/9719cb5bcffb55f0fd805eadfabf8804dad5c533))

## [4.2.0](https://github.com/appium/appium-uiautomator2-driver/compare/v4.1.5...v4.2.0) (2025-04-08)

### Features

* document the new currentDisplayId setting ([#882](https://github.com/appium/appium-uiautomator2-driver/issues/882)) ([cbb8d93](https://github.com/appium/appium-uiautomator2-driver/commit/cbb8d9305aba36bb5decc03445d596cfb095d6fa))

## [4.1.5](https://github.com/appium/appium-uiautomator2-driver/compare/v4.1.4...v4.1.5) (2025-03-21)

### Miscellaneous Chores

* Bump server version ([#874](https://github.com/appium/appium-uiautomator2-driver/issues/874)) ([b3b0898](https://github.com/appium/appium-uiautomator2-driver/commit/b3b089896e664efa50b0388e44ce1aa0e6af16bd))

## [4.1.4](https://github.com/appium/appium-uiautomator2-driver/compare/v4.1.3...v4.1.4) (2025-03-20)

### Bug Fixes

* Explicitly wait for the server shutdown before killing it forcefully ([#873](https://github.com/appium/appium-uiautomator2-driver/issues/873)) ([d3f50ef](https://github.com/appium/appium-uiautomator2-driver/commit/d3f50efc8d5b73451a1d105b5b684b67d7857326))

## [4.1.3](https://github.com/appium/appium-uiautomator2-driver/compare/v4.1.2...v4.1.3) (2025-03-17)

### Miscellaneous Chores

* remove duplicated mobile: backgroundApp definition from uia2 ([#871](https://github.com/appium/appium-uiautomator2-driver/issues/871)) ([1602c51](https://github.com/appium/appium-uiautomator2-driver/commit/1602c51d94084556fb3b1f2c2bba9e2605d7927a))

## [4.1.2](https://github.com/appium/appium-uiautomator2-driver/compare/v4.1.1...v4.1.2) (2025-03-14)

### Bug Fixes

* the return value of getConnectivity to follow the description ([e36a3cd](https://github.com/appium/appium-uiautomator2-driver/commit/e36a3cd2368459be44a0be76553b0024ba0ce060))

## [4.1.1](https://github.com/appium/appium-uiautomator2-driver/compare/v4.1.0...v4.1.1) (2025-03-10)

### Miscellaneous Chores

* bump io.appium.settings ([#870](https://github.com/appium/appium-uiautomator2-driver/issues/870)) ([3a999e7](https://github.com/appium/appium-uiautomator2-driver/commit/3a999e79ec610f1f3a28bcdb8bebb42ace093ce1))

## [4.1.0](https://github.com/appium/appium-uiautomator2-driver/compare/v4.0.3...v4.1.0) (2025-02-26)

### Features

* add more params in mobile: setGeolocation ([#867](https://github.com/appium/appium-uiautomator2-driver/issues/867)) ([8ead690](https://github.com/appium/appium-uiautomator2-driver/commit/8ead690dc9e8da1571604c4aba146644d6a1c2ab))

## [4.0.3](https://github.com/appium/appium-uiautomator2-driver/compare/v4.0.2...v4.0.3) (2025-02-22)

### Miscellaneous Chores

* Bumo android driver ([b28baaa](https://github.com/appium/appium-uiautomator2-driver/commit/b28baaa668b27d0496dad26cb69991be3b632e3f))

## [4.0.2](https://github.com/appium/appium-uiautomator2-driver/compare/v4.0.1...v4.0.2) (2025-02-21)

### Bug Fixes

* Add base path to proxy options ([#865](https://github.com/appium/appium-uiautomator2-driver/issues/865)) ([30a6ac3](https://github.com/appium/appium-uiautomator2-driver/commit/30a6ac3656b3ae28a07f4e6465fd630de88c16ca))

## [4.0.1](https://github.com/appium/appium-uiautomator2-driver/compare/v4.0.0...v4.0.1) (2025-02-10)

### Miscellaneous Chores

* docs for the new fields from AccessibilityNodeInfo ([#862](https://github.com/appium/appium-uiautomator2-driver/issues/862)) ([815c247](https://github.com/appium/appium-uiautomator2-driver/commit/815c247000e03cceac683e7a4122156e3ea5e2ea))

## [4.0.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.10.0...v4.0.0) (2025-02-01)

### ⚠ BREAKING CHANGES

* The following methods and properties were **removed**:
- mobileSetClipboard -> replaced by setClipboard
- mobileGetClipboard -> replaced by getClipboard
- executeMobile -> replaced by execute (the` script` argument must also be changed to start with `mobile: `)
- mobileCommandsMapping -> replaced by executeMethodsMap
- mobileGetAppStrings -> replaced by getStrings
* The following methods were **changed**:
- mobileDragGesture
- mobileFlingGesture
- mobileDoubleClickGesture
- mobileClickGesture
- mobilePinchOpenGesture
- mobilePinchCloseGesture
- mobileSwipeGesture
- mobileScrollGesture
- mobileScrollBackTo
- mobileScroll
- mobileDeepLink
- mobileAcceptAlert
- mobileDismissAlert
- mobileType
- mobileReplaceElementValue
- mobileInstallMultipleApks
- mobileBackgroundApp
- mobilePressKey
- mobileScreenshots
- mobileScheduleAction
- mobileUnscheduleAction
- mobileGetActionHistory
* The following obsolete type definitions were **removed**:
- DragOptions
- FlingOptions
- ClickOptions
- LongClickOptions
- PinchOptions
- SwipeOptions
- ScrollGestureOptions
- ScrollElementToElementOpts
- ScrollOptions
- DeepLinkOpts
- AcceptAlertOptions
- DismissAlertOptions
- TypingOptions
- ReplaceValueOptions
- InstallMultipleApksOptions
- BackgroundAppOptions
- PressKeyOptions
- ScreenshotsOpts
- ActionArgs
- SetClipboardOpts
- GetAppStringsOptions


Based on https://github.com/appium/appium-android-driver/pull/982

### Features

* Add support of executeMethodMap ([#863](https://github.com/appium/appium-uiautomator2-driver/issues/863)) ([4c45c3a](https://github.com/appium/appium-uiautomator2-driver/commit/4c45c3a85a574cb4d207ab827ce98421812d99f2))

## [3.10.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.9...v3.10.0) (2025-01-24)

### Features

* bump appium-adb to handle screenState=SCREEN_STATE_OFF as locked ([#861](https://github.com/appium/appium-uiautomator2-driver/issues/861)) ([fdd362a](https://github.com/appium/appium-uiautomator2-driver/commit/fdd362a8087c66aa6a0825bb18865a43e2ee5288))

## [3.9.9](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.8...v3.9.9) (2025-01-16)

### Miscellaneous Chores

* bump io.appium.settings to include the broad cast receiver update ([#858](https://github.com/appium/appium-uiautomator2-driver/issues/858)) ([bd8cfe7](https://github.com/appium/appium-uiautomator2-driver/commit/bd8cfe77da8ab3f47a80af7550c0f8ebb9abb695))

## [3.9.8](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.7...v3.9.8) (2025-01-09)

### Miscellaneous Chores

* Add closing log quote ([2398564](https://github.com/appium/appium-uiautomator2-driver/commit/2398564421a8a05490cf077b8f32b8f6ddfb5f32))
* Bump adb ([#857](https://github.com/appium/appium-uiautomator2-driver/issues/857)) ([b443c0a](https://github.com/appium/appium-uiautomator2-driver/commit/b443c0ac13930ea9a0f9008a07538632bbb6e81f))

## [3.9.7](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.6...v3.9.7) (2025-01-05)

### Miscellaneous Chores

* Bump android driver ([f5a326e](https://github.com/appium/appium-uiautomator2-driver/commit/f5a326e385e782f1298d03901b8d4f3168ff0b90))

## [3.9.6](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.5...v3.9.6) (2024-12-27)

### Bug Fixes

* Tune retrieval of package launch info ([#853](https://github.com/appium/appium-uiautomator2-driver/issues/853)) ([90a68b1](https://github.com/appium/appium-uiautomator2-driver/commit/90a68b1d9cba41c28b198a4ee9aa4fa55379acb3))

## [3.9.5](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.4...v3.9.5) (2024-12-14)

### Miscellaneous Chores

* Tune contextUpdated event generation ([#850](https://github.com/appium/appium-uiautomator2-driver/issues/850)) ([324d094](https://github.com/appium/appium-uiautomator2-driver/commit/324d0940767e201e4675f31c2fbca0d34ed7da22))

## [3.9.4](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.3...v3.9.4) (2024-12-13)

### Miscellaneous Chores

* Replace occurrences of the deprecated errorAndThrow API ([#849](https://github.com/appium/appium-uiautomator2-driver/issues/849)) ([c562ca5](https://github.com/appium/appium-uiautomator2-driver/commit/c562ca58897e4df84e78a199b40a552b5659e026))

## [3.9.3](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.2...v3.9.3) (2024-12-11)

### Bug Fixes

* Make package parameter of deep link command optional ([#847](https://github.com/appium/appium-uiautomator2-driver/issues/847)) ([ddc7477](https://github.com/appium/appium-uiautomator2-driver/commit/ddc7477b6c62b93469364ed433b88cbdbb91e4b7))

## [3.9.2](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.1...v3.9.2) (2024-12-09)

### Miscellaneous Chores

* allow compatibility with appium 3 beta ([471e156](https://github.com/appium/appium-uiautomator2-driver/commit/471e156b9b537465b6a0d49cd92c0431be57fd64))

## [3.9.1](https://github.com/appium/appium-uiautomator2-driver/compare/v3.9.0...v3.9.1) (2024-11-27)

### Miscellaneous Chores

* Bump android driver ([1bea8e7](https://github.com/appium/appium-uiautomator2-driver/commit/1bea8e761d941fd2e7c4d4cc1a84570d1786f327))

## [3.9.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.8.3...v3.9.0) (2024-11-18)

### Features

* Add a BiDi event upon context change ([#838](https://github.com/appium/appium-uiautomator2-driver/issues/838)) ([fabbc8d](https://github.com/appium/appium-uiautomator2-driver/commit/fabbc8dad628fe0c9d776a542180711d836ff3be))

## [3.8.3](https://github.com/appium/appium-uiautomator2-driver/compare/v3.8.2...v3.8.3) (2024-11-14)

### Miscellaneous Chores

* Bump android driver ([728270f](https://github.com/appium/appium-uiautomator2-driver/commit/728270f90f6f82e37edb831a4b7e3832a5ea43eb))

## [3.8.2](https://github.com/appium/appium-uiautomator2-driver/compare/v3.8.1...v3.8.2) (2024-11-11)

### Miscellaneous Chores

* bump adb to include supporting activities with unicode chars ([#836](https://github.com/appium/appium-uiautomator2-driver/issues/836)) ([c36962e](https://github.com/appium/appium-uiautomator2-driver/commit/c36962ebc135a1d8f761aa4e1a9bada3488408c0))

## [3.8.1](https://github.com/appium/appium-uiautomator2-driver/compare/v3.8.0...v3.8.1) (2024-10-31)

### Miscellaneous Chores

* Bump android driver ([aeb5809](https://github.com/appium/appium-uiautomator2-driver/commit/aeb58092119ac58a7ff1c761332dd75f028f3e0a))

## [3.8.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.11...v3.8.0) (2024-09-17)

### Features

* Bump appium-chromedriver ([#828](https://github.com/appium/appium-uiautomator2-driver/issues/828)) ([653b140](https://github.com/appium/appium-uiautomator2-driver/commit/653b140b639c03ea8573efc2d3fe0662f60df681))

## [3.7.11](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.10...v3.7.11) (2024-09-13)

### Miscellaneous Chores

* Bump android driver ([c62ae8c](https://github.com/appium/appium-uiautomator2-driver/commit/c62ae8c632fb343e0d724a444994625c6b9a2f94))

## [3.7.10](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.9...v3.7.10) (2024-09-12)

### Miscellaneous Chores

* Bump android driver ([2b9ed23](https://github.com/appium/appium-uiautomator2-driver/commit/2b9ed23bec173c9c8b7683c5de42c04c7256a6f8))

## [3.7.9](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.8...v3.7.9) (2024-09-03)

### Miscellaneous Chores

* Bump server version ([716d364](https://github.com/appium/appium-uiautomator2-driver/commit/716d36404915c8b867b1b90fc9f986f681162357))

## [3.7.8](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.7...v3.7.8) (2024-08-29)

### Miscellaneous Chores

* bump android driver ([3b6b270](https://github.com/appium/appium-uiautomator2-driver/commit/3b6b270150221f84498824bd31904c91c2fafb20))

## [3.7.7](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.6...v3.7.7) (2024-08-14)

### Bug Fixes

* apply the latest io.appium.settings ([a1dfde4](https://github.com/appium/appium-uiautomator2-driver/commit/a1dfde4b3d9ffcfcdd89caa0e9382231970359bb))

## [3.7.6](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.5...v3.7.6) (2024-08-06)

### Miscellaneous Chores

* Bump adb ([36b265e](https://github.com/appium/appium-uiautomator2-driver/commit/36b265e9a39cf089936a145e7fc251a23423fbdb))

## [3.7.5](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.4...v3.7.5) (2024-08-02)

### Miscellaneous Chores

* Replace fancy-log dependency with appium logger ([#809](https://github.com/appium/appium-uiautomator2-driver/issues/809)) ([8ec7aa7](https://github.com/appium/appium-uiautomator2-driver/commit/8ec7aa7eff586a0b3ee898bb20dbded8ed44bc34))

## [3.7.4](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.3...v3.7.4) (2024-07-23)

### Bug Fixes

* stop uia2 process as well in cleanupAutomationLeftovers ([#807](https://github.com/appium/appium-uiautomator2-driver/issues/807)) ([69abc42](https://github.com/appium/appium-uiautomator2-driver/commit/69abc425ff36359ac7b58bead82ece0e0fdd1ab3))

## [3.7.3](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.2...v3.7.3) (2024-07-15)

### Miscellaneous Chores

* Bump android driver ([#806](https://github.com/appium/appium-uiautomator2-driver/issues/806)) ([b109867](https://github.com/appium/appium-uiautomator2-driver/commit/b109867e7e2a06fc301c9e82596d698979a338ee))

## [3.7.2](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.1...v3.7.2) (2024-07-09)

### Miscellaneous Chores

* Remove extra import ([9f2d42d](https://github.com/appium/appium-uiautomator2-driver/commit/9f2d42da50a444cd77d4432151656b2252e2c941))

## [3.7.1](https://github.com/appium/appium-uiautomator2-driver/compare/v3.7.0...v3.7.1) (2024-07-03)

### Bug Fixes

* Update instrumentation process handling logic ([#801](https://github.com/appium/appium-uiautomator2-driver/issues/801)) ([75da896](https://github.com/appium/appium-uiautomator2-driver/commit/75da896701399420b4880a71d0d28db59fa5d322))

## [3.7.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.6.1...v3.7.0) (2024-06-27)

### Features

* Add mobile: wrappers for clipboard APIs ([#800](https://github.com/appium/appium-uiautomator2-driver/issues/800)) ([7789b1d](https://github.com/appium/appium-uiautomator2-driver/commit/7789b1d4636c4ade8a6f28e759acccdfb25b53f8))

## [3.6.1](https://github.com/appium/appium-uiautomator2-driver/compare/v3.6.0...v3.6.1) (2024-06-23)

### Miscellaneous Chores

* Bump chai and chai-as-promised ([#799](https://github.com/appium/appium-uiautomator2-driver/issues/799)) ([35734db](https://github.com/appium/appium-uiautomator2-driver/commit/35734db0225b5d4af3d2aa72c2160149c94b954b))

## [3.6.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.5.7...v3.6.0) (2024-06-22)

### Features

* Document injectedImageProperties capability ([#798](https://github.com/appium/appium-uiautomator2-driver/issues/798)) ([48e3255](https://github.com/appium/appium-uiautomator2-driver/commit/48e3255eff9c3c0a6cd075f2a279ed1937664844))

## [3.5.7](https://github.com/appium/appium-uiautomator2-driver/compare/v3.5.6...v3.5.7) (2024-06-22)

### Bug Fixes

* uninstall installed uia2 servers if they were greater than on-going session ([#796](https://github.com/appium/appium-uiautomator2-driver/issues/796)) ([62b9056](https://github.com/appium/appium-uiautomator2-driver/commit/62b9056f4002d42d16a469709e03f43af6aec5a5))

## [3.5.6](https://github.com/appium/appium-uiautomator2-driver/compare/v3.5.5...v3.5.6) (2024-06-18)

### Miscellaneous Chores

* Bump server version ([91c7707](https://github.com/appium/appium-uiautomator2-driver/commit/91c770761cc5e33f153be8ec18591bc1cd07135f))

## [3.5.5](https://github.com/appium/appium-uiautomator2-driver/compare/v3.5.4...v3.5.5) (2024-06-16)

### Miscellaneous Chores

* bump server version ([8e6ec16](https://github.com/appium/appium-uiautomator2-driver/commit/8e6ec166ce99d65bee84182c5fee1a326460d71f))

## [3.5.4](https://github.com/appium/appium-uiautomator2-driver/compare/v3.5.3...v3.5.4) (2024-06-14)

### Bug Fixes

* Add a workaround for wrong adb exit code on service stop ([#794](https://github.com/appium/appium-uiautomator2-driver/issues/794)) ([31e79ab](https://github.com/appium/appium-uiautomator2-driver/commit/31e79ab059700c2b09297a705ac3b03268ec6b84))

## [3.5.3](https://github.com/appium/appium-uiautomator2-driver/compare/v3.5.2...v3.5.3) (2024-06-10)

### Miscellaneous Chores

* use latest io.appium.settings ([#789](https://github.com/appium/appium-uiautomator2-driver/issues/789)) ([13104ba](https://github.com/appium/appium-uiautomator2-driver/commit/13104ba69d55def1540c782425e62bac9050f8fa))

## [3.5.2](https://github.com/appium/appium-uiautomator2-driver/compare/v3.5.1...v3.5.2) (2024-05-21)


### Bug Fixes

* bring mobile: backgroundApp back ([#783](https://github.com/appium/appium-uiautomator2-driver/issues/783)) ([fcd7033](https://github.com/appium/appium-uiautomator2-driver/commit/fcd70333c5a94c3210f38d01039d2577d24f7b99))

## [3.5.1](https://github.com/appium/appium-uiautomator2-driver/compare/v3.5.0...v3.5.1) (2024-05-16)


### Miscellaneous Chores

* Update dev dependencies ([4224eaa](https://github.com/appium/appium-uiautomator2-driver/commit/4224eaab352a43bef0bbc5e50942c3d1af340cd7))

## [3.5.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.4.0...v3.5.0) (2024-05-12)


### Features

* Align the implementation of execute method map with other android drivers ([#780](https://github.com/appium/appium-uiautomator2-driver/issues/780)) ([d376a9b](https://github.com/appium/appium-uiautomator2-driver/commit/d376a9b344c2138fe89403640733dd489e965606))

## [3.4.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.3.0...v3.4.0) (2024-05-11)


### Features

* Add helpers to control bluetooth and nfc adapters ([#779](https://github.com/appium/appium-uiautomator2-driver/issues/779)) ([a9403ff](https://github.com/appium/appium-uiautomator2-driver/commit/a9403ffcd58560be6af0f8c80b302cf750789a98))

## [3.3.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.2.0...v3.3.0) (2024-05-09)


### Features

* add user option for mobile: isAppInstalled ([#778](https://github.com/appium/appium-uiautomator2-driver/issues/778)) ([2a0e9a5](https://github.com/appium/appium-uiautomator2-driver/commit/2a0e9a5d98a0f14bb69914136df24ff2c7633c02))

## [3.2.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.1.0...v3.2.0) (2024-05-03)


### Features

* Add `mobile: injectEmulatorCameraImage` extension ([#772](https://github.com/appium/appium-uiautomator2-driver/issues/772)) ([768a629](https://github.com/appium/appium-uiautomator2-driver/commit/768a629b1d24e7c96ffc28c9068f4bfe5a2393b4))

## [3.1.0](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.8...v3.1.0) (2024-04-10)


### Features

* Add the support of `timeZone` capability ([#761](https://github.com/appium/appium-uiautomator2-driver/issues/761)) ([f2039d3](https://github.com/appium/appium-uiautomator2-driver/commit/f2039d30c0df22f4720b285c9ca262e2d3f9d24f))

## [3.0.8](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.7...v3.0.8) (2024-04-09)


### Miscellaneous Chores

* Remove extra imports ([11aea03](https://github.com/appium/appium-uiautomator2-driver/commit/11aea03218fcb2e59e07bfcff25ddca86846a36f))

## [3.0.7](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.6...v3.0.7) (2024-04-09)


### Miscellaneous Chores

* Skip signature checks of server packages ([#759](https://github.com/appium/appium-uiautomator2-driver/issues/759)) ([6e43272](https://github.com/appium/appium-uiautomator2-driver/commit/6e4327213d8519f6a0768c2b0516ea01638a70b3))

## [3.0.6](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.5...v3.0.6) (2024-04-08)


### Miscellaneous Chores

* Bump android driver ([1cf44c6](https://github.com/appium/appium-uiautomator2-driver/commit/1cf44c6840f82de9ba59ee5c333b55fb24be70a2))

## [3.0.5](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.4...v3.0.5) (2024-04-01)


### Miscellaneous Chores

* bump appium-android-driver ([2683085](https://github.com/appium/appium-uiautomator2-driver/commit/2683085a2f412673b6ef6721e9b1632d2e5c505c))

## [3.0.4](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.3...v3.0.4) (2024-03-20)


### Bug Fixes

* Skip chromedriver proxy suspend if the proxy is not initialized yet ([#754](https://github.com/appium/appium-uiautomator2-driver/issues/754)) ([a6d9146](https://github.com/appium/appium-uiautomator2-driver/commit/a6d91468dc409bfdac017695ca4fb52a600d15a5))

## [3.0.3](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.2...v3.0.3) (2024-03-18)


### Miscellaneous Chores

* bump android driver ([decda6d](https://github.com/appium/appium-uiautomator2-driver/commit/decda6db2583dcf8d9c290df0f546278f4bca398))

## [3.0.2](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.1...v3.0.2) (2024-03-07)


### Miscellaneous Chores

* bump typescript ([b6f4cec](https://github.com/appium/appium-uiautomator2-driver/commit/b6f4cec5e95ef0d8196ad3134784c9e85cd0363c))

## [3.0.1](https://github.com/appium/appium-uiautomator2-driver/compare/v3.0.0...v3.0.1) (2024-02-23)


### Miscellaneous Chores

* bump uia2 server that has androidx.test.uiautomator:uiautomator:2.3.0 ([#744](https://github.com/appium/appium-uiautomator2-driver/issues/744)) ([56dab5d](https://github.com/appium/appium-uiautomator2-driver/commit/56dab5d4081bf9ca64b8736be636c5210965dc34))

## [3.0.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.45.1...v3.0.0) (2024-02-10)


### ⚠ BREAKING CHANGES

* Removed obsolete MJSONWP touch route handlers. Use W3C actions or gesture shortcuts instead:

- doSwipe
- doDrag
- touchDown
- touchLongClick
- touchMove
- touchUp
- tap
- doPerformMultiAction

### Features

* Removed obsolete MJSONWP touch actions ([#738](https://github.com/appium/appium-uiautomator2-driver/issues/738)) ([520fe26](https://github.com/appium/appium-uiautomator2-driver/commit/520fe262c70883ba18aa01b2a34873516fbc8557))

## [2.45.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.45.0...v2.45.1) (2024-02-10)


### Bug Fixes

* Argument type for performActions API ([#735](https://github.com/appium/appium-uiautomator2-driver/issues/735)) ([d0e7c2f](https://github.com/appium/appium-uiautomator2-driver/commit/d0e7c2f9859de4382cd35366915b1b4a3af34514))

## [2.45.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.44.3...v2.45.0) (2024-02-08)


### Features

* add checkVersion option for mobile:installApp ([#736](https://github.com/appium/appium-uiautomator2-driver/issues/736)) ([95d46f4](https://github.com/appium/appium-uiautomator2-driver/commit/95d46f47cb738a6087da2d5262c0c7b6af1fedd1))

## [2.44.3](https://github.com/appium/appium-uiautomator2-driver/compare/v2.44.2...v2.44.3) (2024-02-07)


### Bug Fixes

* Init adb as the first step upon session creation ([#733](https://github.com/appium/appium-uiautomator2-driver/issues/733)) ([fbe80e9](https://github.com/appium/appium-uiautomator2-driver/commit/fbe80e9df51b84da115184ebe1974760cdd47d67))

## [2.44.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.44.1...v2.44.2) (2024-02-06)


### Bug Fixes

* Detect the browser activity dynamically ([#730](https://github.com/appium/appium-uiautomator2-driver/issues/730)) ([9cc4116](https://github.com/appium/appium-uiautomator2-driver/commit/9cc4116759f0e8d4975c92a6667660bea38609d9))

## [2.44.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.44.0...v2.44.1) (2024-02-06)


### Bug Fixes

* fix mobile: screenshots for WDIO ([#731](https://github.com/appium/appium-uiautomator2-driver/issues/731)) ([ac28c0c](https://github.com/appium/appium-uiautomator2-driver/commit/ac28c0cde3716dd684e088b4d12ca823b4316aaf))

## [2.44.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.43.4...v2.44.0) (2024-02-02)


### Features

* Paralellize session setup steps ([#729](https://github.com/appium/appium-uiautomator2-driver/issues/729)) ([b11356f](https://github.com/appium/appium-uiautomator2-driver/commit/b11356fa075aa71003bc363df050286ee738f4b8))

## [2.43.4](https://github.com/appium/appium-uiautomator2-driver/compare/v2.43.3...v2.43.4) (2024-01-30)


### Miscellaneous Chores

* Bump server version ([f0b0b84](https://github.com/appium/appium-uiautomator2-driver/commit/f0b0b8448e97465921f16064fd14b7cbc9eb4fa6))

## [2.43.3](https://github.com/appium/appium-uiautomator2-driver/compare/v2.43.2...v2.43.3) (2024-01-29)


### Miscellaneous Chores

* Bump android driver ([ffd126d](https://github.com/appium/appium-uiautomator2-driver/commit/ffd126d5eef1d89622b93785348279042c4f832d))

## [2.43.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.43.1...v2.43.2) (2024-01-27)


### Miscellaneous Chores

* Remove husky and commitlint ([#726](https://github.com/appium/appium-uiautomator2-driver/issues/726)) ([e9147ee](https://github.com/appium/appium-uiautomator2-driver/commit/e9147ee98d8e751414cd8c3c3a9986256e66641a))

## [2.43.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.43.0...v2.43.1) (2024-01-26)


### Bug Fixes

* createADB ([#724](https://github.com/appium/appium-uiautomator2-driver/issues/724)) ([fef068a](https://github.com/appium/appium-uiautomator2-driver/commit/fef068a4c5d4513223c1f9e3a5f1ba53c32adec6))

## [2.43.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.42.2...v2.43.0) (2024-01-25)


### Features

* Update android-driver ([#720](https://github.com/appium/appium-uiautomator2-driver/issues/720)) ([f352359](https://github.com/appium/appium-uiautomator2-driver/commit/f35235970904fd89f736b4fdd9a8635858719edb))

## [2.42.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.42.1...v2.42.2) (2024-01-23)


### Bug Fixes

* bump appium android driver to 7.8.3 (for getContexts) ([#718](https://github.com/appium/appium-uiautomator2-driver/issues/718)) ([1bd9358](https://github.com/appium/appium-uiautomator2-driver/commit/1bd9358566a902dcd7062e0f0667a724cc1a1c26))

## [2.42.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.42.0...v2.42.1) (2024-01-14)


### Miscellaneous Chores

* Bump appium-adb ([#716](https://github.com/appium/appium-uiautomator2-driver/issues/716)) ([598f137](https://github.com/appium/appium-uiautomator2-driver/commit/598f137c7bcde8694a189f161397f6cb70c7277c))

## [2.42.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.41.0...v2.42.0) (2024-01-12)


### Features

* Use APIs imported from the io.appium.settings package ([#715](https://github.com/appium/appium-uiautomator2-driver/issues/715)) ([f55625a](https://github.com/appium/appium-uiautomator2-driver/commit/f55625a32afc174cec6d92178ef15bcc0bb0d79f))

## [2.41.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.40.2...v2.41.0) (2024-01-11)


### Features

* Document the helper method to simulate the low-memory event ([#714](https://github.com/appium/appium-uiautomator2-driver/issues/714)) ([d9c9863](https://github.com/appium/appium-uiautomator2-driver/commit/d9c98638296c4d770f9e50d86ec6775cd4b00c2a))

## [2.40.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.40.1...v2.40.2) (2024-01-10)


### Miscellaneous Chores

* Bump server version ([48b6914](https://github.com/appium/appium-uiautomator2-driver/commit/48b691423db1100787ee033a225a7f5211906f8a))

## [2.40.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.40.0...v2.40.1) (2024-01-10)


### Bug Fixes

* io.appium.settings process check as the foreground service check ([#713](https://github.com/appium/appium-uiautomator2-driver/issues/713)) ([4fcdd7b](https://github.com/appium/appium-uiautomator2-driver/commit/4fcdd7b4f96f8fae7f658340c5e228fb85c210cf))

## [2.40.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.39.0...v2.40.0) (2024-01-07)


### Features

* update io.appium.settings to use newer location deps ([#712](https://github.com/appium/appium-uiautomator2-driver/issues/712)) ([0d595aa](https://github.com/appium/appium-uiautomator2-driver/commit/0d595aac2bbadd3756a9ff58f7906ea57da078f1))

## [2.39.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.38.0...v2.39.0) (2024-01-05)


### Features

* Add doctor checks ([#711](https://github.com/appium/appium-uiautomator2-driver/issues/711)) ([0e6c8fe](https://github.com/appium/appium-uiautomator2-driver/commit/0e6c8fed60345125f8ca78d70bb8e934cd50a6bc))

## [2.38.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.37.1...v2.38.0) (2024-01-05)


### Features

* Bump the server version ([#710](https://github.com/appium/appium-uiautomator2-driver/issues/710)) ([4ee3b7e](https://github.com/appium/appium-uiautomator2-driver/commit/4ee3b7eeac77fa53d3e437bc311e31a1d057176e))

## [2.37.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.37.0...v2.37.1) (2024-01-03)


### Bug Fixes

* minor fixes for upcoming androidx.test.uiautomator:uiautomator:2.3.0 ([#708](https://github.com/appium/appium-uiautomator2-driver/issues/708)) ([166ab62](https://github.com/appium/appium-uiautomator2-driver/commit/166ab623e99b02e06b423d1737814896ee685cba))

## [2.37.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.36.0...v2.37.0) (2023-12-30)


### Features

* bump the server's targetSdkVersion from 32 to 33 ([#705](https://github.com/appium/appium-uiautomator2-driver/issues/705)) ([8fda76d](https://github.com/appium/appium-uiautomator2-driver/commit/8fda76d2900d63b51b08c0e3aa912bb11ee8992d))

## [2.36.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.35.0...v2.36.0) (2023-12-26)


### Features

* bump server's targetSdkVersion from 30 to 32 ([#702](https://github.com/appium/appium-uiautomator2-driver/issues/702)) ([e49b991](https://github.com/appium/appium-uiautomator2-driver/commit/e49b991a3be7872f61def86c8c3f1d4815678736))

## [2.35.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.34.2...v2.35.0) (2023-12-18)


### Features

* let mobile: getContexts gives waitForWebviewMs ([#699](https://github.com/appium/appium-uiautomator2-driver/issues/699)) ([c0f90cf](https://github.com/appium/appium-uiautomator2-driver/commit/c0f90cfd8c01d3852fbc3786c6458290441f18ae))

## [2.34.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.34.1...v2.34.2) (2023-12-02)


### Miscellaneous Chores

* update publish.js.yml ([#696](https://github.com/appium/appium-uiautomator2-driver/issues/696)) ([2af984f](https://github.com/appium/appium-uiautomator2-driver/commit/2af984f3d5d289c25d0b4a694f8a057db068c48a))

## [2.34.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.34.0...v2.34.1) (2023-11-08)


### Miscellaneous Chores

* use omit to prune dev and peer ([#693](https://github.com/appium/appium-uiautomator2-driver/issues/693)) ([712cfce](https://github.com/appium/appium-uiautomator2-driver/commit/712cfce64767ae0d0785da49248f9d35bcf15015))

## [2.34.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.33.1...v2.34.0) (2023-11-03)


### Features

* Add support of UiModeManager service commands ([#691](https://github.com/appium/appium-uiautomator2-driver/issues/691)) ([89303f0](https://github.com/appium/appium-uiautomator2-driver/commit/89303f0e77dc7bbc6c7a3e685efd3ecb63370c94))

## [2.33.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.33.0...v2.33.1) (2023-10-31)


### Bug Fixes

* Properly read systemPort value from capabilities ([#689](https://github.com/appium/appium-uiautomator2-driver/issues/689)) ([cfaccb5](https://github.com/appium/appium-uiautomator2-driver/commit/cfaccb528c8a42c4185ca88c2dafbcbbc4ee80c2))

## [2.33.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.32.3...v2.33.0) (2023-10-30)


### Features

* Add hideKeyboard capability ([#686](https://github.com/appium/appium-uiautomator2-driver/issues/686)) ([708f41c](https://github.com/appium/appium-uiautomator2-driver/commit/708f41c087f64f32a022dd34715dc68b4004a852))

## [2.32.3](https://github.com/appium/appium-uiautomator2-driver/compare/v2.32.2...v2.32.3) (2023-10-19)


### Miscellaneous Chores

* Always use latest types ([19754b2](https://github.com/appium/appium-uiautomator2-driver/commit/19754b2b0043b03df729e588a62eec0e571557b3))

## [2.32.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.32.1...v2.32.2) (2023-10-18)


### Miscellaneous Chores

* Remove the obsolete coverage-related stuff ([#677](https://github.com/appium/appium-uiautomator2-driver/issues/677)) ([977816e](https://github.com/appium/appium-uiautomator2-driver/commit/977816e6eaed4780a91225178d0d98aa8fa5c5b5))

## [2.32.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.32.0...v2.32.1) (2023-10-17)


### Miscellaneous Chores

* Remove obsolete driver options ([#676](https://github.com/appium/appium-uiautomator2-driver/issues/676)) ([2a296dc](https://github.com/appium/appium-uiautomator2-driver/commit/2a296dc7a82e686f7cf811db72fe02bd143fca7b))

## [2.32.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.31.4...v2.32.0) (2023-10-17)


### Features

* Add 'mobile: deviceidle' extension ([#673](https://github.com/appium/appium-uiautomator2-driver/issues/673)) ([5c59c10](https://github.com/appium/appium-uiautomator2-driver/commit/5c59c10488a216c3c526c9a1c3326a9aea6d31de))

## [2.31.4](https://github.com/appium/appium-uiautomator2-driver/compare/v2.31.3...v2.31.4) (2023-10-16)


### Miscellaneous Chores

* Normalize execute script names ([#672](https://github.com/appium/appium-uiautomator2-driver/issues/672)) ([ca1d6e9](https://github.com/appium/appium-uiautomator2-driver/commit/ca1d6e94c058cc9ae8511c7b45e4f0c577b83de8))

## [2.31.3](https://github.com/appium/appium-uiautomator2-driver/compare/v2.31.2...v2.31.3) (2023-10-15)


### Bug Fixes

* Make app not required ([#664](https://github.com/appium/appium-uiautomator2-driver/issues/664)) ([636391e](https://github.com/appium/appium-uiautomator2-driver/commit/636391e53313f6c612a87beacfb15d7fdd1fa439))

## [2.31.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.31.1...v2.31.2) (2023-10-15)


### Miscellaneous Chores

* bump css-selector-parser to v3 ([#663](https://github.com/appium/appium-uiautomator2-driver/issues/663)) ([179670e](https://github.com/appium/appium-uiautomator2-driver/commit/179670e152c6c01c88183f539902220b775ff0c4))

## [2.31.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.31.0...v2.31.1) (2023-10-14)


### Miscellaneous Chores

* add error log for this.adb.setHiddenApiPolicy error ([#656](https://github.com/appium/appium-uiautomator2-driver/issues/656)) ([5c076c5](https://github.com/appium/appium-uiautomator2-driver/commit/5c076c5c4ffb565a4d8f44f8f0595e2b19c2e99c))

## [2.31.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.30.0...v2.31.0) (2023-10-14)


### Features

* Bump android driver to v7 ([#662](https://github.com/appium/appium-uiautomator2-driver/issues/662)) ([a54bc4a](https://github.com/appium/appium-uiautomator2-driver/commit/a54bc4a2d45bed3ddd0f632048f928b76609e0b2))

## [2.30.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.11...v2.30.0) (2023-10-13)


### Features

* add all the types ([#635](https://github.com/appium/appium-uiautomator2-driver/issues/635)) ([623f49b](https://github.com/appium/appium-uiautomator2-driver/commit/623f49ba363bcfd930021fb5fa7cfe030b5683fb))

## [2.29.11](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.10...v2.29.11) (2023-10-08)


### Miscellaneous Chores

* Bump appium-adb ([92e9884](https://github.com/appium/appium-uiautomator2-driver/commit/92e98841a2bb5c6dfd7924d55a82bb65194c8b11))

## [2.29.10](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.9...v2.29.10) (2023-09-24)


### Bug Fixes

* release package ([#658](https://github.com/appium/appium-uiautomator2-driver/issues/658)) ([ef483e3](https://github.com/appium/appium-uiautomator2-driver/commit/ef483e34ec9a23c3b63ab76d849b25403435a58e))

## [2.29.9](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.8...v2.29.9) (2023-09-21)


### Bug Fixes

* check if the instrumentation process stops by sending a request ([#655](https://github.com/appium/appium-uiautomator2-driver/issues/655)) ([d42950a](https://github.com/appium/appium-uiautomator2-driver/commit/d42950acb44ad8458932f4617cf110825dab8737))

## [2.29.8](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.7...v2.29.8) (2023-09-14)


### Miscellaneous Chores

* Bump android driver ([a69ca17](https://github.com/appium/appium-uiautomator2-driver/commit/a69ca17842d315751af5cf25b1589148a3d41e2a))

## [2.29.7](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.6...v2.29.7) (2023-09-12)


### Miscellaneous Chores

* Bump modules ([fcb6a3a](https://github.com/appium/appium-uiautomator2-driver/commit/fcb6a3ac19cd8f685a82cb8288879d74368e7f5e))

## [2.29.6](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.5...v2.29.6) (2023-09-07)


### Bug Fixes

* Add missing command accessors ([#652](https://github.com/appium/appium-uiautomator2-driver/issues/652)) ([8e39019](https://github.com/appium/appium-uiautomator2-driver/commit/8e39019f869f9ec520ffe5cf886aa46fe2be487e))

## [2.29.5](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.4...v2.29.5) (2023-08-21)


### Bug Fixes

* upgrade appium-chromedriver to latest ([ecfecbf](https://github.com/appium/appium-uiautomator2-driver/commit/ecfecbf75dce2f037a4344bed065fb538be64cda))

## [2.29.4](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.3...v2.29.4) (2023-08-09)


### Miscellaneous Chores

* Disable app capability presence validation ([#641](https://github.com/appium/appium-uiautomator2-driver/issues/641)) ([7c78543](https://github.com/appium/appium-uiautomator2-driver/commit/7c7854360c142fa8dc846795a13f62203b572b02))

## [2.29.3](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.2...v2.29.3) (2023-07-29)


### Miscellaneous Chores

* Bump chromedriver ([4ec1624](https://github.com/appium/appium-uiautomator2-driver/commit/4ec162400ac02a9ed4720608f0d11d239e9aa9d7))

## [2.29.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.1...v2.29.2) (2023-07-03)


### Miscellaneous Chores

* Bump server version ([583630f](https://github.com/appium/appium-uiautomator2-driver/commit/583630f8d8f9b3f0259a362b50a04313e966631a))

## [2.29.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.29.0...v2.29.1) (2023-06-29)


### Miscellaneous Chores

* Bump the server version ([c202967](https://github.com/appium/appium-uiautomator2-driver/commit/c202967482a3748a12be70edec87fb518766a638))

## [2.29.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.28.1...v2.29.0) (2023-06-25)


### Features

* Allow to take screenshots of multiple Android displays ([#628](https://github.com/appium/appium-uiautomator2-driver/issues/628)) ([3501892](https://github.com/appium/appium-uiautomator2-driver/commit/3501892dcea9fec90dfedb38700888542e3f96f3))

## [2.28.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.28.0...v2.28.1) (2023-06-23)


### Miscellaneous Chores

* Bump appium-adb version ([#632](https://github.com/appium/appium-uiautomator2-driver/issues/632)) ([2897505](https://github.com/appium/appium-uiautomator2-driver/commit/28975051d10dbaf5316a60ab3ed237dd4edb2d4c))

## [2.28.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.27.0...v2.28.0) (2023-06-22)


### Features

* Optimize server packages installation ([#631](https://github.com/appium/appium-uiautomator2-driver/issues/631)) ([ddfceab](https://github.com/appium/appium-uiautomator2-driver/commit/ddfceab31ac64afe67219537f7fe3f8d03bbfb28))

## [2.27.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.26.3...v2.27.0) (2023-06-21)


### Features

* add the description of setting API snapshotMaxDepth ([#629](https://github.com/appium/appium-uiautomator2-driver/issues/629)) ([c74ad63](https://github.com/appium/appium-uiautomator2-driver/commit/c74ad638af2bddd107bb774c76f2683acb7a8a07))

## [2.26.3](https://github.com/appium/appium-uiautomator2-driver/compare/v2.26.2...v2.26.3) (2023-06-21)


### Bug Fixes

* mobila: lock ([#630](https://github.com/appium/appium-uiautomator2-driver/issues/630)) ([de15791](https://github.com/appium/appium-uiautomator2-driver/commit/de157919d02cafdb85218be957b9b7e390efcd78))

## [2.26.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.26.1...v2.26.2) (2023-06-16)


### Miscellaneous Chores

* bump android driver ([ad80174](https://github.com/appium/appium-uiautomator2-driver/commit/ad8017480bba8e3024da87a7ff9a891618fb5e3f))

## [2.26.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.26.0...v2.26.1) (2023-06-13)


### Bug Fixes

* make releaseActions to a no-op ([#625](https://github.com/appium/appium-uiautomator2-driver/issues/625)) ([740f90a](https://github.com/appium/appium-uiautomator2-driver/commit/740f90ae41ad761de6dfab92ef61b279fcad2cef))

## [2.26.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.25.3...v2.26.0) (2023-06-11)


### Features

* Add extensions for scheduled actions ([#616](https://github.com/appium/appium-uiautomator2-driver/issues/616)) ([3e1c24d](https://github.com/appium/appium-uiautomator2-driver/commit/3e1c24da84d7bc2084b32addad305575c252f43b))

## [2.25.3](https://github.com/appium/appium-uiautomator2-driver/compare/v2.25.2...v2.25.3) (2023-06-08)


### Miscellaneous Chores

* add logging for mjpeg broadcast request ([#622](https://github.com/appium/appium-uiautomator2-driver/issues/622)) ([da64bf5](https://github.com/appium/appium-uiautomator2-driver/commit/da64bf5ba2ed1ea95a6c2292d9ea557bd8229f4f))

## [2.25.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.25.1...v2.25.2) (2023-06-02)


### Miscellaneous Chores

* Bump appium-adb ([30ea8ae](https://github.com/appium/appium-uiautomator2-driver/commit/30ea8ae511e241743b5787cc157467eb87a38b1e))

## [2.25.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.25.0...v2.25.1) (2023-05-24)


### Bug Fixes

* Update extension command name ([0e488f4](https://github.com/appium/appium-uiautomator2-driver/commit/0e488f4615ea2dbc9744b6be8ae08faa82eed49e))

## [2.25.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.24.2...v2.25.0) (2023-05-22)


### Features

* Add a wrapper over status bar commands ([#615](https://github.com/appium/appium-uiautomator2-driver/issues/615)) ([7ec9f8c](https://github.com/appium/appium-uiautomator2-driver/commit/7ec9f8c3582820ea37322113024d758b8752b3e5))

## [2.24.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.24.1...v2.24.2) (2023-05-22)


### Bug Fixes

* Mobile method name ([896ade4](https://github.com/appium/appium-uiautomator2-driver/commit/896ade4bc74f049dda813a6ed675141919d25437))

## [2.24.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.24.0...v2.24.1) (2023-05-16)


### Miscellaneous Chores

* Bump android-driver component ([e416860](https://github.com/appium/appium-uiautomator2-driver/commit/e4168608bcfc56440937b1f3bdb10a154392d51b))

## [2.24.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.23.0...v2.24.0) (2023-05-05)


### Features

* Add mobile wrapper for performance data collectors ([#613](https://github.com/appium/appium-uiautomator2-driver/issues/613)) ([cfd13f8](https://github.com/appium/appium-uiautomator2-driver/commit/cfd13f8e3d2cec60c0c0379e8336f3a62753cd96))

## [2.23.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.22.1...v2.23.0) (2023-04-30)


### Features

* Add mobile wrappers for GPS and notification helpers ([#608](https://github.com/appium/appium-uiautomator2-driver/issues/608)) ([97c2989](https://github.com/appium/appium-uiautomator2-driver/commit/97c2989bcf469bb45e735237fe91c4797f2403ea))

## [2.22.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.22.0...v2.22.1) (2023-04-28)


### Bug Fixes

* Extension name ([4804648](https://github.com/appium/appium-uiautomator2-driver/commit/48046485c9b54108310bbcfe85710f6737b2fe3e))

## [2.22.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.21.0...v2.22.0) (2023-04-28)


### Features

* Add more mobile extensions ([#606](https://github.com/appium/appium-uiautomator2-driver/issues/606)) ([55a1e79](https://github.com/appium/appium-uiautomator2-driver/commit/55a1e798ac73c812ad7cc0d8897d330f9d51c2b0))

## [2.21.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.20.0...v2.21.0) (2023-04-27)


### Features

* Add mobile wrappers for getDisplayDensity and getSystemBars extensions ([#605](https://github.com/appium/appium-uiautomator2-driver/issues/605)) ([0e8a4cf](https://github.com/appium/appium-uiautomator2-driver/commit/0e8a4cf24540ec44569422c60c50367c3cc69888))

## [2.20.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.19.0...v2.20.0) (2023-04-26)


### Features

* Add mobile wrappers for returning the current package and activity names ([#604](https://github.com/appium/appium-uiautomator2-driver/issues/604)) ([ad9f21a](https://github.com/appium/appium-uiautomator2-driver/commit/ad9f21a3eeedc75346059306de02a64234fd76ad))

## [2.19.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.18.0...v2.19.0) (2023-04-25)


### Features

* Add mobile wrapper for backgroundApp ([#603](https://github.com/appium/appium-uiautomator2-driver/issues/603)) ([925f775](https://github.com/appium/appium-uiautomator2-driver/commit/925f77598db5a0edd3d253cf0d3477e6ebddbca6))

## [2.18.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.17.0...v2.18.0) (2023-04-20)


### Features

* Add mobile methods for lock and isLocked ([#600](https://github.com/appium/appium-uiautomator2-driver/issues/600)) ([24e90b2](https://github.com/appium/appium-uiautomator2-driver/commit/24e90b217e0e2ab807184c9e28a82e55027e9e65))

## [2.17.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.16.0...v2.17.0) (2023-04-20)


### Features

* Add mobile extension for pressKey action ([#597](https://github.com/appium/appium-uiautomator2-driver/issues/597)) ([093edec](https://github.com/appium/appium-uiautomator2-driver/commit/093edecacb7750f2d74b9abf6d54e20bbd76c1c1))

## [2.16.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.15.0...v2.16.0) (2023-04-20)


### Features

* Add mobile extensions for on-screen keyboard ([#594](https://github.com/appium/appium-uiautomator2-driver/issues/594)) ([09e839e](https://github.com/appium/appium-uiautomator2-driver/commit/09e839e7d85b3845de4f6d3fa12896144e01caa2))

## [2.15.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.14.0...v2.15.0) (2023-04-15)


### Features

* Add 'mobile: getAppStrings' extension ([#593](https://github.com/appium/appium-uiautomator2-driver/issues/593)) ([0705269](https://github.com/appium/appium-uiautomator2-driver/commit/07052696f9926b24a0343e2ab7aa7656cffc860b))

## [2.14.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.13.0...v2.14.0) (2023-04-04)


### Features

* add shouldTerminateApp capability ([#591](https://github.com/appium/appium-uiautomator2-driver/issues/591)) ([853acb3](https://github.com/appium/appium-uiautomator2-driver/commit/853acb3d9848361a86b9d3246fada138f07434ce))

## [2.13.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.12.7...v2.13.0) (2023-04-04)


### Features

* Add setConnectivity and getConnectivity extensions ([#590](https://github.com/appium/appium-uiautomator2-driver/issues/590)) ([f281a40](https://github.com/appium/appium-uiautomator2-driver/commit/f281a400c0097abe20fc8a852f244af775b112b1))

## [2.12.7](https://github.com/appium/appium-uiautomator2-driver/compare/v2.12.6...v2.12.7) (2023-03-24)


### Bug Fixes

* chromedriver version comparison in the chromedriver download ([#587](https://github.com/appium/appium-uiautomator2-driver/issues/587)) ([3aa3c84](https://github.com/appium/appium-uiautomator2-driver/commit/3aa3c848012149d0997b5c0f70e8bcf9daba0328))

## [2.12.6](https://github.com/appium/appium-uiautomator2-driver/compare/v2.12.5...v2.12.6) (2023-02-22)


### Miscellaneous Chores

* Bump android-driver ([77b3120](https://github.com/appium/appium-uiautomator2-driver/commit/77b3120d03c6d8e275f0a43c6f322c79619d5fb6))

## [2.12.5](https://github.com/appium/appium-uiautomator2-driver/compare/v2.12.4...v2.12.5) (2023-02-21)


### Bug Fixes

* actually publish reset script ([a2bc02f](https://github.com/appium/appium-uiautomator2-driver/commit/a2bc02f8956590e3e02b22d75ca412174e6341f9)), closes [appium/appium#501](https://github.com/appium/appium/issues/501)

## [2.12.4](https://github.com/appium/appium-uiautomator2-driver/compare/v2.12.3...v2.12.4) (2023-02-16)


### Miscellaneous Chores

* remove unused unicodeKeyboard as uia2 ([#581](https://github.com/appium/appium-uiautomator2-driver/issues/581)) ([605383f](https://github.com/appium/appium-uiautomator2-driver/commit/605383f581919ab546e62368937295b48a97521e))

## [2.12.3](https://github.com/appium/appium-uiautomator2-driver/compare/v2.12.2...v2.12.3) (2023-02-02)


### Miscellaneous Chores

* bump chromedriver ([21003b3](https://github.com/appium/appium-uiautomator2-driver/commit/21003b30905778b770536bf950b7eebc8a9decf3))

## [2.12.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.12.1...v2.12.2) (2023-01-12)


### Bug Fixes

* specify supported non-standard commands in newMethodMap ([8a51004](https://github.com/appium/appium-uiautomator2-driver/commit/8a5100486cc3d39c0a8f0e77b2eb574ce1a01e04))

## [2.12.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.12.0...v2.12.1) (2022-12-24)


### Miscellaneous Chores

* Bump android-driver ([1527434](https://github.com/appium/appium-uiautomator2-driver/commit/152743477f7968724598b5b80dd06cf01f30d21f))

## [2.12.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.11.1...v2.12.0) (2022-12-08)


### Features

* Add forceAppLaunch capability ([#569](https://github.com/appium/appium-uiautomator2-driver/issues/569)) ([8af938e](https://github.com/appium/appium-uiautomator2-driver/commit/8af938e361ed7f2821756fd3466ea97761714104))

## [2.11.1](https://github.com/appium/appium-uiautomator2-driver/compare/v2.11.0...v2.11.1) (2022-12-01)


### Miscellaneous Chores

* update releaserc ([#566](https://github.com/appium/appium-uiautomator2-driver/issues/566)) ([ffb04bd](https://github.com/appium/appium-uiautomator2-driver/commit/ffb04bda088bb9b21e4b179c985245c1c72da577))

# [2.11.0](https://github.com/appium/appium-uiautomator2-driver/compare/v2.10.2...v2.11.0) (2022-11-30)


### Features

* Add support of updated changePermissions args ([#565](https://github.com/appium/appium-uiautomator2-driver/issues/565)) ([df51a96](https://github.com/appium/appium-uiautomator2-driver/commit/df51a9610c4c33f99feaabe29793003063a26f95))

## [2.10.2](https://github.com/appium/appium-uiautomator2-driver/compare/v2.10.1...v2.10.2) (2022-11-06)
