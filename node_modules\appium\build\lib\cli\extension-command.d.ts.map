{"version": 3, "file": "extension-command.d.ts", "sourceRoot": "", "sources": ["../../../lib/cli/extension-command.js"], "names": [], "mappings": ";;;;;oCAg8B6B,OAAO,SAAtB,aAAc;;;;YAEd,eAAe,CAAC,OAAO,CAAC;;;;UACxB,OAAO;;;;;;;;;eAOP,OAAO;;;;cACP,OAAO;;;;mBACP,MAAM,GAAC,IAAI;;;;yBACX,MAAM,GAAC,IAAI;;;;;;;;;;4BAMZ,OAAO,eAAe,EAAE,aAAa;yBACrC,OAAO,eAAe,EAAE,UAAU;yBAClC,OAAO,eAAe,EAAE,UAAU;sBAIlB,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,SAAS,CAAC,OAAO,CAAC;4BAIzB,OAAO,SAAtB,aAAc,IACf,OAAO,+BAA+B,EAAE,eAAe,CAAC,OAAO,CAAC;wBAIhD,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC;wBAI3B,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC;2BAI3B,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,cAAc,CAAC,OAAO,CAAC;8BAI9B,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,iBAAiB,CAAC,OAAO,CAAC;;;;8BAKjC,OAAO,SAAtB,aAAc,IACf,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC;uCAI9C,OAAO,SAAtB,aAAc,IACf,WAAW,CAAC,OAAO,CAAC,GAAG,qBAAqB;;;;0BAK5B,OAAO,SAAtB,aAAc,IACf,MAAM,CAAC,MAAM,EAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;;;;;;;;iBAMxC,MAAM;;;;;;;;;;;;;;;;;;;;;;iBAUN,MAAM;;;;;;;;;;;;;;;;;;;;;;iBAcN,MAAM;;;;YACN,OAAO;;;;;;;;;YAMP,MAAM,CAAC,MAAM,EAAC,KAAK,CAAC;;;;aACpB,MAAM,CAAC,MAAM,EAAC,YAAY,CAAC;;;;;;;;;UAM3B,MAAM;;;;QACN,MAAM;;;;;;;;;iBAMN,MAAM;;;;;;;;;aAMN,MAAM;;;;aACN,MAAM;;;;;;;;;iBAMN,MAAM;;;;aACN,MAAM;;;;iBACN,OAAO,cAAc,EAAE,WAAW;;;;;;;;;;;;;aAOlC,MAAM;;;;gBACN,MAAM,OAAC;;;;kBACP,MAAM,OAAC;;;;;;;;;iBAMP,MAAM;;;;iBACN,WAAW;;;;;;4BAKI,OAAO,SAAtB,aAAc,IACf,OAAO,SAAS,UAAU,GAAG,cAAc,cAAc,EAAE,aAAa,GAAG,OAAO,SAAS,UAAU,GAAG,cAAc,cAAc,EAAE,aAAa,GAAG,KAAK;;;;;mBAK1J,OAAO;;;;iBACP,OAAO;;;;;;;;;uCAMQ,OAAO,SAAtB,aAAc;iBAEd,MAAM;iBACN,MAAM;SACN,cAAc,CAAC,OAAO,CAAC;iBACvB,WAAW;;0BAIZ,OAAO,cAAc,EAAE,WAAW;AA5jC/C;;GAEG;AACH,kCAF6B,OAAO,SAAtB,aAAc;IAqB1B;;;OAGG;IACH,8BAFW,uBAAuB,CAAC,OAAO,CAAC,EAM1C;IA1BD;;;OAGG;IACH,QAFU,eAAe,CAAC,OAAO,CAAC,CAE3B;IAEP;;;OAGG;IACH,iBAFU,eAAe,CAAC,OAAO,CAAC,CAElB;IAEhB;;;OAGG;IACH,cAFU,OAAO,CAEJ;IAQX,wBAAmD;IAIrD;;OAEG;IACH,oBAEC;IAED;;;;;;;;;OASG;IACH,qCAJW,MAAM,SAMhB;IAED;;;;;OAKG;IACH,cAHW,MAAM,GACL,OAAO,CAAC,MAAM,CAAC,CAS1B;IAED;;;;;OAKG;IACH,uBAJc,aAAc,2CACjB,WAAW,GACV,OAAO,CAAC,aAAa,CAAC,SAAO,CAAC,CAAC,CAsH1C;IAED;;;;;OAKG;IACH,0EAHW,iBAAiB,GACf,OAAO,CAAC,IAAI,CAAC,CAiBzB;IAED;;;;;OAKG;IACH,oDAHW,WAAW,GACV,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CA+JtC;IAED;;;;;OAKG;IACH,6DAHW,iBAAiB,GACf,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CA2B/C;IAED;;;;;;OAMG;IAEH,yBAJW,aAAa,GACX,MAAM,CAKlB;IAED;;;;;OAKG;IACH,uEAHW,0BAA0B,CAAC,OAAO,CAAC,GACjC,iBAAiB,CAAC,OAAO,CAAC,CAuBtC;IAED;;;;;;;;;;;OAWG;IACH,yBALW,OAAO,WAAW,EAAE,WAAW,eAC/B,MAAM,GAEJ,GAAG,IAAI,cAAc,CAAC,OAAO,CAAC,CA4B1C;IAED;;;;;;;OAOG;IAEH,qCAJW,WAAW,CAAC,OAAO,CAAC,eACpB,MAAM,QAKhB;IAED;;;;;;;;;OASG;IACH,4BAHW,aAAa,GACZ,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAoBtC;IAED;;;;;OAKG;IACH,iCAHW,mBAAmB,GAClB,OAAO,CAAC,qBAAqB,CAAC,CAsFzC;IAED;;;;;;OAMG;IACH,6BAHW,MAAM,GACL,OAAO,CAAC,eAAe,CAAC,CA4BnC;IAED;;;;;;;OAOG;IACH,6BAJW,MAAM,WACN,MAAM,GACJ,OAAO,CAAC,IAAI,CAAC,CAYzB;IAED;;;;;;;;OAQG;IACH,oBANW,MAAM,UACN,MAAM,SACN,MAAM,EAAE,SACR,OAAO,eAAe,EAAE,YAAY,GAClC,OAAO,oBAAoB,EAAE,YAAY,CAQrD;IAED;;;;;;;OAOG;IACH,yBALW,aAAa,GACX,OAAO,CAAC,MAAM,CAAC,CA8E3B;IAED;;;;;;;;;;OAUG;IACH,2DAHW,UAAU,GACT,OAAO,CAAC,SAAS,CAAC,CAyG7B;CACF;wBAt7BiD,iBAAiB"}