import type { Capabilities } from '@wdio/types';
import command from './command.js';
import { DEFAULTS } from './constants.js';
import type { BidiHandler } from './bidi/handler.js';
import { getPrototype, getEnvironmentVars, initiateBidi, parseBidiMessage } from './utils.js';
import type { Client, AttachOptions } from './types.js';
export default class WebDriver {
    static newSession(options: Capabilities.RemoteConfig, modifier?: (...args: any[]) => any, userPrototype?: {}, customCommandWrapper?: (...args: any[]) => any): Promise<Client>;
    /**
     * allows user to attach to existing sessions
     */
    static attachToSession(options?: AttachOptions, modifier?: (...args: any[]) => any, userPrototype?: {}, commandWrapper?: (...args: any[]) => any): Client;
    /**
     * Changes The instance session id and browser capabilities for the new session
     * directly into the passed in browser object
     *
     * @param   {object} instance  the object we get from a new browser session.
     * @returns {string}           the new session id of the browser
     */
    static reloadSession(instance: Client & {
        _bidiHandler?: BidiHandler;
    }, newCapabilities?: WebdriverIO.Capabilities): Promise<string>;
    static get WebDriver(): typeof WebDriver;
}
/**
 * Helper methods consumed by webdriverio package
 */
export { getPrototype, DEFAULTS, command, getEnvironmentVars, initiateBidi, parseBidiMessage, WebDriver };
export * from './types.js';
export * from './constants.js';
export * from './bidi/handler.js';
export * as local from './bidi/localTypes.js';
export * as remote from './bidi/remoteTypes.js';
//# sourceMappingURL=index.d.ts.map