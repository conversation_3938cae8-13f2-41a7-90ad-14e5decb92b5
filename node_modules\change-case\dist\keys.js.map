{"version": 3, "file": "keys.js", "sourceRoot": "", "sources": ["../src/keys.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,UAAU,MAAM,YAAY,CAAC;AAEzC,MAAM,QAAQ,GAAG,CAAC,MAAe,EAAE,EAAE,CACnC,MAAM,KAAK,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,CAAC;AAEhD,SAAS,iBAAiB,CAGxB,UAAmE;IAEnE,OAAO,SAAS,UAAU,CACxB,MAAe,EACf,KAAK,GAAG,CAAC,EACT,OAAiB;QAEjB,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC;QAEpD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;SACnE;QAED,MAAM,MAAM,GAA4B,MAAM,CAAC,MAAM,CACnD,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAC9B,CAAC;QAEF,MAAM,CAAC,IAAI,CAAC,MAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAC5C,MAAM,KAAK,GAAI,MAAkC,CAAC,GAAG,CAAC,CAAC;YACvD,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC5C,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;YAC3D,MAAM,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,SAAS,GAAG,iBAAiB,CACxC,UAAU,CAAC,SAAS,CACrB,CAAC;AACF,MAAM,CAAC,MAAM,WAAW,GAAG,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AACrE,MAAM,CAAC,MAAM,YAAY,GAAG,iBAAiB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;AACvE,MAAM,CAAC,MAAM,OAAO,GAAG,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AAC7D,MAAM,CAAC,MAAM,SAAS,GAAG,iBAAiB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AACjE,MAAM,CAAC,MAAM,MAAM,GAAG,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAC3D,MAAM,CAAC,MAAM,SAAS,GAAG,iBAAiB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AACjE,MAAM,CAAC,MAAM,UAAU,GAAG,iBAAiB,CACzC,UAAU,CAAC,UAAU,CACtB,CAAC;AACF,MAAM,CAAC,MAAM,QAAQ,GAAG,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC/D,MAAM,CAAC,MAAM,YAAY,GAAG,iBAAiB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;AACvE,MAAM,CAAC,MAAM,SAAS,GAAG,iBAAiB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC", "sourcesContent": ["import * as changeCase from \"./index.js\";\n\nconst isObject = (object: unknown) =>\n  object !== null && typeof object === \"object\";\n\nfunction changeKeysFactory<\n  Options extends changeCase.Options = changeCase.Options,\n>(\n  changeCase: (input: string, options?: changeCase.Options) => string,\n): (object: unknown, depth?: number, options?: Options) => unknown {\n  return function changeKeys(\n    object: unknown,\n    depth = 1,\n    options?: Options,\n  ): unknown {\n    if (depth === 0 || !isObject(object)) return object;\n\n    if (Array.isArray(object)) {\n      return object.map((item) => changeKeys(item, depth - 1, options));\n    }\n\n    const result: Record<string, unknown> = Object.create(\n      Object.getPrototypeOf(object),\n    );\n\n    Object.keys(object as object).forEach((key) => {\n      const value = (object as Record<string, unknown>)[key];\n      const changedKey = changeCase(key, options);\n      const changedValue = changeKeys(value, depth - 1, options);\n      result[changedKey] = changedValue;\n    });\n\n    return result;\n  };\n}\n\nexport const camelCase = changeKeysFactory<changeCase.PascalCaseOptions>(\n  changeCase.camelCase,\n);\nexport const capitalCase = changeKeysFactory(changeCase.capitalCase);\nexport const constantCase = changeKeysFactory(changeCase.constantCase);\nexport const dotCase = changeKeysFactory(changeCase.dotCase);\nexport const trainCase = changeKeysFactory(changeCase.trainCase);\nexport const noCase = changeKeysFactory(changeCase.noCase);\nexport const kebabCase = changeKeysFactory(changeCase.kebabCase);\nexport const pascalCase = changeKeysFactory<changeCase.PascalCaseOptions>(\n  changeCase.pascalCase,\n);\nexport const pathCase = changeKeysFactory(changeCase.pathCase);\nexport const sentenceCase = changeKeysFactory(changeCase.sentenceCase);\nexport const snakeCase = changeKeysFactory(changeCase.snakeCase);\n"]}