/**
 * Get the particular property of the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} property - The name of the property. This name should
 *                            be known to _adb shell getprop_ tool.
 *
 * @return {Promise<string>} The value of the given property.
 */
export function getDeviceProperty(this: import("../adb.js").ADB, property: string): Promise<string>;
/**
 * Set the particular property of the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} prop - The name of the property. This name should
 *                            be known to _adb shell setprop_ tool.
 * @param {string} val - The new property value.
 * @param {import('./types').SetPropOpts} [opts={}]
 *
 * @throws {error} If _setprop_ utility fails to change property value.
 */
export function setDeviceProperty(this: import("../adb.js").ADB, prop: string, val: string, opts?: import("./types").SetPropOpts): Promise<void>;
/**
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} Current system language on the device under test.
 */
export function getDeviceSysLanguage(this: import("../adb.js").ADB): Promise<string>;
/**
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} Current country name on the device under test.
 */
export function getDeviceSysCountry(this: import("../adb.js").ADB): Promise<string>;
/**
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} Current system locale name on the device under test.
 */
export function getDeviceSysLocale(this: import("../adb.js").ADB): Promise<string>;
/**
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} Current product language name on the device under test.
 */
export function getDeviceProductLanguage(this: import("../adb.js").ADB): Promise<string>;
/**
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} Current product country name on the device under test.
 */
export function getDeviceProductCountry(this: import("../adb.js").ADB): Promise<string>;
/**
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} Current product locale name on the device under test.
 */
export function getDeviceProductLocale(this: import("../adb.js").ADB): Promise<string>;
/**
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} The model name of the device under test.
 */
export function getModel(this: import("../adb.js").ADB): Promise<string>;
/**
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} The manufacturer name of the device under test.
 */
export function getManufacturer(this: import("../adb.js").ADB): Promise<string>;
/**
 * Get the current screen size.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string?>} Device screen size as string in format 'WxH' or
 * _null_ if it cannot be determined.
 */
export function getScreenSize(this: import("../adb.js").ADB): Promise<string | null>;
/**
 * Get the current screen density in dpi
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<number?>} Device screen density as a number or _null_ if it
 * cannot be determined
 */
export function getScreenDensity(this: import("../adb.js").ADB): Promise<number | null>;
/**
 * Setup HTTP proxy in device global settings.
 * Read https://android.googlesource.com/platform/frameworks/base/+/android-9.0.0_r21/core/java/android/provider/Settings.java for each property
 *
 * @this {import('../adb.js').ADB}
 * @param {string} proxyHost - The host name of the proxy.
 * @param {string|number} proxyPort - The port number to be set.
 */
export function setHttpProxy(this: import("../adb.js").ADB, proxyHost: string, proxyPort: string | number): Promise<void>;
/**
 * Delete HTTP proxy in device global settings.
 * Rebooting the test device is necessary to apply the change.
 * @this {import('../adb.js').ADB}
 */
export function deleteHttpProxy(this: import("../adb.js").ADB): Promise<void>;
/**
 * Set device property.
 * [android.provider.Settings]{@link https://developer.android.com/reference/android/provider/Settings.html}
 *
 * @this {import('../adb.js').ADB}
 * @param {string} namespace - one of {system, secure, global}, case-insensitive.
 * @param {string} setting - property name.
 * @param {string|number} value - property value.
 * @return {Promise<string>} command output.
 */
export function setSetting(this: import("../adb.js").ADB, namespace: string, setting: string, value: string | number): Promise<string>;
/**
 * Get device property.
 * [android.provider.Settings]{@link https://developer.android.com/reference/android/provider/Settings.html}
 *
 * @this {import('../adb.js').ADB}
 * @param {string} namespace - one of {system, secure, global}, case-insensitive.
 * @param {string} setting - property name.
 * @return {Promise<string>} property value.
 */
export function getSetting(this: import("../adb.js").ADB, namespace: string, setting: string): Promise<string>;
/**
 * Get tz database time zone formatted timezone
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<string>} TZ database Time Zones format
 * @throws {Error} If any exception is reported by adb shell.
 */
export function getTimeZone(this: import("../adb.js").ADB): Promise<string>;
/**
 * Retrieve the platform version of the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} The platform version as a string, for example '5.0' for
 * Android Lollipop.
 */
export function getPlatformVersion(this: import("../adb.js").ADB): Promise<string>;
/**
 * Retrieve the list of location providers for the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string[]>} The list of available location providers or an empty list.
 */
export function getLocationProviders(this: import("../adb.js").ADB): Promise<string[]>;
/**
 * Toggle the state of GPS location provider.
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} enabled - Whether to enable (true) or disable (false) the GPS provider.
 */
export function toggleGPSLocationProvider(this: import("../adb.js").ADB, enabled: boolean): Promise<void>;
/**
 * Set hidden api policy to manage access to non-SDK APIs.
 * https://developer.android.com/preview/restrictions-non-sdk-interfaces
 *
 * @this {import('../adb.js').ADB}
 * @param {number|string} value - The API enforcement policy.
 *     For Android P
 *     0: Disable non-SDK API usage detection. This will also disable logging, and also break the strict mode API,
 *        detectNonSdkApiUsage(). Not recommended.
 *     1: "Just warn" - permit access to all non-SDK APIs, but keep warnings in the log.
 *        The strict mode API will keep working.
 *     2: Disallow usage of dark grey and black listed APIs.
 *     3: Disallow usage of blacklisted APIs, but allow usage of dark grey listed APIs.
 *
 *     For Android Q
 *     https://developer.android.com/preview/non-sdk-q#enable-non-sdk-access
 *     0: Disable all detection of non-SDK interfaces. Using this setting disables all log messages for non-SDK interface usage
 *        and prevents you from testing your app using the StrictMode API. This setting is not recommended.
 *     1: Enable access to all non-SDK interfaces, but print log messages with warnings for any non-SDK interface usage.
 *        Using this setting also allows you to test your app using the StrictMode API.
 *     2: Disallow usage of non-SDK interfaces that belong to either the black list
 *        or to a restricted greylist for your target API level.
 *
 * @param {boolean} [ignoreError=false] Whether to ignore an exception in 'adb shell settings put global' command
 * @throws {error} If there was an error and ignoreError was true while executing 'adb shell settings put global'
 *                 command on the device under test.
 */
export function setHiddenApiPolicy(this: import("../adb.js").ADB, value: number | string, ignoreError?: boolean): Promise<void>;
/**
 * Reset access to non-SDK APIs to its default setting.
 * https://developer.android.com/preview/restrictions-non-sdk-interfaces
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} [ignoreError=false] Whether to ignore an exception in 'adb shell settings delete global' command
 * @throws {error} If there was an error and ignoreError was true while executing 'adb shell settings delete global'
 *                 command on the device under test.
 */
export function setDefaultHiddenApiPolicy(this: import("../adb.js").ADB, ignoreError?: boolean): Promise<void>;
/**
 * Get the language name of the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} The name of device language.
 */
export function getDeviceLanguage(this: import("../adb.js").ADB): Promise<string>;
/**
 * Get the country name of the device under test.
 *
 * @summary Could only be used for Android API < 23
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} The name of device country.
 */
export function getDeviceCountry(this: import("../adb.js").ADB): Promise<string>;
/**
 * Get the locale name of the device under test.
 *
 * @summary Could only be used for Android API >= 23
 * @this {import('../adb.js').ADB}
 * @return {Promise<string>} The name of device locale.
 */
export function getDeviceLocale(this: import("../adb.js").ADB): Promise<string>;
/**
 * Make sure current device locale is expected or not.
 *
 * @this {import('../adb.js').ADB}
 * @privateRemarks FIXME: language or country is required
 * @param {string} [language] - Language. The language field is case insensitive, but Locale always canonicalizes to lower case.
 * @param {string} [country] - Country. The language field is case insensitive, but Locale always canonicalizes to lower case.
 * @param {string} [script] - Script. The script field is case insensitive but Locale always canonicalizes to title case.
 *
 * @return {Promise<boolean>} If current locale is language and country as arguments, return true.
 */
export function ensureCurrentLocale(this: import("../adb.js").ADB, language?: string, country?: string, script?: string): Promise<boolean>;
/**
 * Change the state of WiFi on the device under test.
 * Only works for real devices since API 30
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} on - True to enable and false to disable it.
 * @param {boolean} [isEmulator=false] - Set it to true if the device under test
 *                                       is an emulator rather than a real device.
 */
export function setWifiState(this: import("../adb.js").ADB, on: boolean, isEmulator?: boolean): Promise<void>;
/**
 * Change the state of Data transfer on the device under test.
 * Only works for real devices since API 30
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} on - True to enable and false to disable it.
 * @param {boolean} [isEmulator=false] - Set it to true if the device under test
 *                                       is an emulator rather than a real device.
 */
export function setDataState(this: import("../adb.js").ADB, on: boolean, isEmulator?: boolean): Promise<void>;
/**
 * Retrieves the list of packages from Doze whitelist on Android 8+
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<string[]>} The list of whitelisted packages. An example output:
 * system,com.android.shell,2000
 * system,com.google.android.cellbroadcastreceiver,10143
 * user,io.appium.settings,10157
 */
export function getDeviceIdleWhitelist(this: import("../adb.js").ADB): Promise<string[]>;
/**
 * Adds an existing package(s) into the Doze whitelist on Android 8+
 *
 * @this {import('../adb.js').ADB}
 * @param  {...string} packages One or more packages to add. If the package
 * already exists in the whitelist then it is only going to be added once.
 * If the package with the given name is not installed/not known then an error
 * will be thrown.
 * @returns {Promise<boolean>} `true` if the command to add package(s) has been executed
 */
export function addToDeviceIdleWhitelist(this: import("../adb.js").ADB, ...packages: string[]): Promise<boolean>;
/**
 * Check the state of Airplane mode on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if Airplane mode is enabled.
 */
export function isAirplaneModeOn(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Change the state of Airplane mode in Settings on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} on - True to enable the Airplane mode in Settings and false to disable it.
 */
export function setAirplaneMode(this: import("../adb.js").ADB, on: boolean): Promise<void>;
/**
 * Change the state of the bluetooth service on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} on - True to enable bluetooth service and false to disable it.
 */
export function setBluetoothOn(this: import("../adb.js").ADB, on: boolean): Promise<void>;
/**
 * Change the state of the NFC service on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} on - True to enable NFC service and false to disable it.
 * @throws {Error} If there was an error while changing the service state
 */
export function setNfcOn(this: import("../adb.js").ADB, on: boolean): Promise<void>;
/**
 * Broadcast the state of Airplane mode on the device under test.
 * This method should be called after {@link #setAirplaneMode}, otherwise
 * the mode change is not going to be applied for the device.
 * ! This API requires root since Android API 24. Since API 30
 * there is a dedicated adb command to change airplane mode state, which
 * does not require to call this one afterwards.
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} on - True to broadcast enable and false to broadcast disable.
 */
export function broadcastAirplaneMode(this: import("../adb.js").ADB, on: boolean): Promise<void>;
/**
 * Check the state of WiFi on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if WiFi is enabled.
 */
export function isWifiOn(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Check the state of Data transfer on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if Data transfer is enabled.
 */
export function isDataOn(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Check the state of animation on the device under test below:
 *   - animator_duration_scale
 *   - transition_animation_scale
 *   - window_animation_scale
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if at least one of animation scale settings
 *                   is not equal to '0.0'.
 */
export function isAnimationOn(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Set animation scale with the given value via adb shell settings command.
 *   - animator_duration_scale
 *   - transition_animation_scale
 *   - window_animation_scale
 * API level 24 and newer OS versions may change the animation, at least emulators are so.
 * API level 28+ real devices checked this worked, but we haven't checked older ones
 * with real devices.
 *
 * @this {import('../adb.js').ADB}
 * @param {number} value Animation scale value (int or float) to set.
 *                       The minimum value of zero disables animations.
 *                       By increasing the value, animations become slower.
 *                       '1' is the system default animation scale.
 * @return {Promise<void>}
 * @throws {Error} If the adb setting command raises an exception.
 */
export function setAnimationScale(this: import("../adb.js").ADB, value: number): Promise<void>;
/**
 * Retrieve current screen orientation of the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<number?>} The current orientation encoded as an integer number.
 */
export function getScreenOrientation(this: import("../adb.js").ADB): Promise<number | null>;
//# sourceMappingURL=device-settings.d.ts.map