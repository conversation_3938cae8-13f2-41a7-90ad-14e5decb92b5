{"version": 3, "file": "apks-utils.js", "sourceRoot": "", "sources": ["../../../lib/tools/apks-utils.js"], "names": [], "mappings": ";;;;;AAmGA,wCA+BC;AASD,sCAWC;AASD,kDAOC;AAUD,kCA8BC;AAUD,wCAEC;AAcD,gDAsBC;AAOD,wDAEC;AAvQD,+CAAoC;AACpC,4CAAmC;AACnC,gDAAwB;AACxB,oDAAuB;AACvB,6CAAoD;AACpD,yCAAqC;AACrC,8CAEuB;AACvB,4DAAmC;AACnC,wDAAyB;AAEzB,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACnC,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,IAAI,MAAM,CAAC;AAClD,uCAAuC;AACvC,MAAM,UAAU,GAAG,IAAI,oBAAQ,CAAC;IAC9B,GAAG,EAAE,EAAE;IACP,OAAO,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,YAAE,CAAC,MAAM,CAAC,qBAAqB,CAAA,CAAC,kBAAkB,CAAC,CAAC;CACtF,CAAC,CAAC;AACH,MAAM,gBAAgB,GAAG,IAAI,oBAAS,EAAE,CAAC;AACzC,MAAM,qBAAqB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAC5C,MAAM,oBAAoB,GAAG,gCAAmB,GAAG,CAAC,CAAC;AAErD,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;IACtB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACrB,OAAO;IACT,CAAC;IAED,MAAM,KAAK,GAAG,uBAAuB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACjE,eAAG,CAAC,KAAK,CAAC,yBAAyB,KAAK,CAAC,MAAM,gBAAgB;QAC7D,cAAI,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3C,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC;YACH,yDAAyD;YACzD,YAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,eAAG,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;;GAYG;AACH,KAAK,UAAU,eAAe,CAAE,IAAI,EAAE,OAAO;IAC3C,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QACxB,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;IACtB,CAAC;IAED,OAAO,MAAM,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;QACrD,wDAAwD;QACxD,oEAAoE;QACpE,kBAAkB;QAClB,MAAM,QAAQ,GAAG,MAAM,YAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,eAAG,CAAC,KAAK,CAAC,eAAe,IAAI,WAAW,QAAQ,EAAE,CAAC,CAAC;QAEpD,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,qBAAqB,CAAA,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC;YAC7F,IAAI,MAAM,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChC,OAAO,UAAU,CAAC;YACpB,CAAC;YACD,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,OAAO,EAAE,CAAC;QACxC,eAAG,CAAC,KAAK,CAAC,oCAAoC,IAAI,SAAS,OAAO,GAAG,CAAC,CAAC;QACvE,MAAM,IAAA,sBAAS,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/B,MAAM,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxF,IAAI,CAAC,MAAM,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CACb,GAAG,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,cAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,wBAAwB,IAAI,YAAY;gBAChG,sDAAsD,CACvD,CAAC;QACJ,CAAC;QACD,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAClC,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;GASG;AACI,KAAK,UAAU,cAAc,CAAE,IAAI,EAAE,QAAQ;IAClD,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;IAC5B,IAAI,GAAG;QACL,MAAM,EAAE,EAAC,6CAA8C,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU;QAClF,GAAG,IAAI;KACR,CAAC;IACF,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IACxB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,GAAG,CAAC,uBAAuB,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAClD,CAAC;IACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,GAAG,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC;IAC7C,CAAC;IACD,eAAG,CAAC,KAAK,CAAC,wCAAwC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC1E,IAAI,MAAM,CAAC;IACX,IAAI,CAAC;QACH,CAAC,EAAC,MAAM,EAAC,GAAG,MAAM,IAAA,mBAAI,EAAC,MAAM,IAAA,yBAAY,GAAE,EAAE,IAAI,EAAE;YACjD,GAAG;YACH,OAAO,EAAE,qBAAqB;SAC/B,CAAC,CAAC,CAAC;QACJ,eAAG,CAAC,KAAK,CAAC,mBAAmB,gBAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAC,MAAM,EAAE,GAAG,EAAC,CAAC,EAAE,CAAC,CAAC;QAClE,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;YACb,eAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;YACb,eAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3C,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,qBAAqB,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,aAAa,CAAE,YAAY;IAC/C,uBAAuB;IACvB,MAAM,IAAI,GAAG;QACX,iBAAiB;QACjB,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;QAC7B,aAAa,EAAE,qBAAqB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QACvD,UAAU,EAAE,YAAY;KACzB,CAAC;IACF,eAAG,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACnE,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,iCAAiC,CAAC,CAAC;IACnE,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,mBAAmB,CAAE,iBAAiB,EAAE,OAAO,GAAG,EAAE;IACxE,MAAM,WAAW,GAAG,IAAA,6BAAgB,EAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,OAAO,CAAC,CAAC;IACxE,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,kBAAkB,EAAE,GAAG,WAAW,EAAE,GAAG,iBAAiB,CAAC,EAAE;QACpF,mCAAmC;QACnC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO;QAC7D,cAAc,EAAE,OAAO,CAAC,cAAc;KACvC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,WAAW,CAAE,IAAI,EAAE,OAAO,GAAG,EAAE;IACnD,MAAM,EACJ,gBAAgB,EAChB,iBAAiB,EACjB,OAAO,GACR,GAAG,OAAO,CAAC;IAEZ,uBAAuB;IACvB,MAAM,IAAI,GAAG;QACX,cAAc;QACd,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;QAC7B,QAAQ,EAAE,IAAI;QACd,kBAAkB,EAAE,GAAG,OAAO,IAAI,oBAAoB,EAAE;QACxD,aAAa,EAAE,qBAAqB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;KACxD,CAAC;IACF,IAAI,iBAAiB,EAAE,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACjC,CAAC;IACD,wBAAwB;IACxB,MAAM,KAAK,GAAG;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,mBAAmB,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,WAAW,EAAE,CAAC;KACvG,CAAC;IACF,IAAI,gBAAgB,EAAE,CAAC;QACrB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,MAAM,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,kBAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACvC,IAAI,gBAAgB,IAAI,OAAO,EAAE,CAAC;QAChC,yFAAyF;QACzF,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,cAAc,CAAE,IAAI;IACxC,OAAO,MAAM,eAAe,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED;;;;;;;;;;;GAWG;AACI,KAAK,UAAU,kBAAkB,CAAE,IAAI,EAAE,QAAQ,GAAG,IAAI;IAC7D,IAAI,QAAQ,EAAE,CAAC;QACb,IAAI,CAAC;YACH,OAAO,MAAM,eAAe,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,eAAG,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACrB,eAAG,CAAC,IAAI,CAAC,+DAA+D,IAAI,WAAW;gBACrF,oCAAoC,CAAC,CAAC;YACxC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,MAAM,gBAAgB,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACzC,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;QACpC,IAAI,CAAC;YACH,OAAO,MAAM,eAAe,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IAED,eAAG,CAAC,IAAI,CAAC,uDAAuD,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI;QAClG,iCAAiC,CAAC,CAAC;IACrC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AACzC,CAAC;AAED;;;;GAIG;AACH,SAAgB,sBAAsB,CAAE,MAAM;IAC5C,OAAO,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrD,CAAC"}