{"name": "spacetrim", "version": "0.11.59", "description": "Spacetrim is trimming string from all 4 sides.", "private": false, "sideEffects": false, "module": "./dist/esm/index.js", "main": "./dist/umd/index.js", "typings": "./dist/esm/typings/index.d.ts", "scripts": {"test": "jest", "lint": "tslint -p tsconfig.json", "build": "rm -rf ./dist && rollup --config rollup.config.ts", "preversion": "npm run lint && npm test", "postversion": "git push && git push --tags"}, "repository": {"type": "git", "url": "https://github.com/hejny/spacetrim"}, "keywords": [], "author": "", "license": "Apache-2.0", "bugs": {"url": "https://github.com/hejny/spacetrim/issues"}, "homepage": "https://github.com/hejny/spacetrim#readme", "dependencies": {}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.0.2", "jest": "27.3.1", "rollup": "2.58.1", "ts-jest": "27.0.7", "tslib": "2.3.1", "tslint": "6.1.3", "tslint-config-prettier": "1.18.0", "typescript": "4.4.4"}, "funding": [{"type": "individual", "url": "https://buymeacoffee.com/hejny"}, {"type": "github", "url": "https://github.com/hejny/spacetrim/blob/main/README.md#%EF%B8%8F-contributing"}]}