<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import { $ } from '@wdio/globals'`
    : `const { $ } = require('@wdio/globals')` %>
<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import Page from './page${answers.esmSupport ? '.js' : ''}';`
    : "const Page = require('./page');" %>

/**
 * sub page containing specific selectors and methods for a specific page
 */
class LoginPage extends Page {
    /**
     * define selectors using getter methods
     */
    <%- answers.isUsingTypeScript ? "public " : "" %>get inputUsername () {
        return $('#username');
    }

    <%- answers.isUsingTypeScript ? "public " : "" %>get inputPassword () {
        return $('#password');
    }

    <%- answers.isUsingTypeScript ? "public " : "" %>get btnSubmit () {
        return $('button[type="submit"]');
    }

    /**
     * a method to encapsule automation code to interact with the page
     * e.g. to login using username and password
     */
    <%- answers.isUsingTypeScript ? "public " : "" %>async login (username<%- answers.isUsingTypeScript ? ": string": "" %>, password<%- answers.isUsingTypeScript ? ": string": "" %>) {
        await this.inputUsername.setValue(username);
        await this.inputPassword.setValue(password);
        await this.btnSubmit.click();
    }

    /**
     * overwrite specific options to adapt it to page object
     */
    <%- answers.isUsingTypeScript ? "public " : "" %>open () {
        return super.open('login');
    }
}

<%- answers.isUsingTypeScript || answers.esmSupport ? "export default": "module.exports =" %> new LoginPage();
