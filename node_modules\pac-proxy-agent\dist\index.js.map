{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAC3B,yCAA2B;AAE3B,+CAAiC;AACjC,mCAA8B;AAC9B,kDAAgC;AAEhC,6BAA0B;AAC1B,2CAA+D;AAI/D,qCAIiB;AACjB,+CAIsB;AACtB,wEAA6D;AAE7D,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,iBAAiB,CAAC,CAAC;AAE7C,MAAM,0BAA0B,GAAG,CAGlC,OAAU,EACT,EAAE;IACH,IACC,OAAO,CAAC,UAAU,KAAK,SAAS;QAChC,OAAO,CAAC,IAAI;QACZ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EACtB;QACD,OAAO;YACN,GAAG,OAAO;YACV,UAAU,EAAE,OAAO,CAAC,IAAI;SACxB,CAAC;KACF;IACD,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAqBF;;;;;;;;;;;GAWG;AACH,MAAa,aAAkC,SAAQ,kBAAK;IAgB3D,YAAY,GAAc,EAAE,IAAgC;QAC3D,KAAK,CAAC,IAAI,CAAC,CAAC;QAqBL,yBAAoB,GAAG,GAAS,EAAE;YACzC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QAClC,CAAC,CAAC;QArBD,0BAA0B;QAC1B,MAAM,MAAM,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;QACxD,IAAI,CAAC,GAAG,GAAG,IAAI,SAAG,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;QAElD,KAAK,CAAC,oCAAoC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE3D,wDAAwD;QACxD,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QAEjC,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;SACnC;IACF,CAAC;IAMD;;;OAGG;IACH,WAAW;QACV,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,IAAI,CACxB,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,oBAAoB,CACzB,CAAC;SACF;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAEO,KAAK,CAAC,YAAY;QACzB,IAAI;YACH,4CAA4C;YAC5C,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACrC,IAAA,+BAAU,GAAE;gBACZ,IAAI,CAAC,WAAW,EAAE;aAClB,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAElE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;gBAChD,KAAK,CACJ,sFAAsF,CACtF,CAAC;gBACF,OAAO,IAAI,CAAC,QAAQ,CAAC;aACrB;YAED,qBAAqB;YACrB,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC9C,IAAI,CAAC,QAAQ,GAAG,IAAA,gCAAiB,EAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAExD,sDAAsD;YACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAEzB,OAAO,IAAI,CAAC,QAAQ,CAAC;SACrB;QAAC,OAAO,GAAY,EAAE;YACtB,IACC,IAAI,CAAC,QAAQ;gBACZ,GAA6B,CAAC,IAAI,KAAK,cAAc,EACrD;gBACD,KAAK,CACJ,4DAA4D,CAC5D,CAAC;gBACF,OAAO,IAAI,CAAC,QAAQ,CAAC;aACrB;YACD,MAAM,GAAG,CAAC;SACV;IACF,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,WAAW;QACxB,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAExC,MAAM,EAAE,GAAG,MAAM,IAAA,gBAAM,EAAC,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACvE,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAEhB,MAAM,GAAG,GAAG,MAAM,IAAA,qBAAQ,EAAC,EAAE,CAAC,CAAC;QAC/B,KAAK,CAAC,gCAAgC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAEpD,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACZ,GAAuB,EACvB,IAAsB;QAEtB,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;QAChC,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,WAAW,CAAC;QAE7D,uDAAuD;QACvD,6CAA6C;QAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAE1C,gCAAgC;QAChC,MAAM,QAAQ,GAAG,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;QACrD,MAAM,IAAI,GACT,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QACnE,MAAM,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9C,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CACxB,IAAI,SAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,QAAQ,KAAK,IAAI,EAAE,CAAC,EACzC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAC7C,CAAC;QAEF,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACtB,IAAI,MAAM,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC;QAEjC,kEAAkE;QAClE,IAAI,CAAC,MAAM,EAAE;YACZ,MAAM,GAAG,QAAQ,CAAC;SAClB;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;aAC5B,IAAI,EAAE;aACN,KAAK,CAAC,UAAU,CAAC;aACjB,MAAM,CAAC,OAAO,CAAC,CAAC;QAElB,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAC9D,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACvB;QAED,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;YAC5B,IAAI,KAAK,GAAiB,IAAI,CAAC;YAC/B,IAAI,MAAM,GAAsB,IAAI,CAAC;YACrC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1C,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAE5C,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACtB,gDAAgD;gBAChD,IAAI,cAAc,EAAE;oBACnB,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC;iBACvD;qBAAM;oBACN,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAC3B;aACD;iBAAM,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACjD,uBAAuB;gBACvB,MAAM,EAAE,eAAe,EAAE,GAAG,wDAAa,mBAAmB,GAAC,CAAC;gBAC9D,KAAK,GAAG,IAAI,eAAe,CAAC,WAAW,MAAM,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aAC5D;iBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAC7B,uBAAuB;gBACvB,MAAM,EAAE,eAAe,EAAE,GAAG,wDAAa,mBAAmB,GAAC,CAAC;gBAC9D,KAAK,GAAG,IAAI,eAAe,CAAC,aAAa,MAAM,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;aAC9D;iBAAM,IACN,IAAI,KAAK,OAAO;gBAChB,IAAI,KAAK,MAAM;gBACf,IAAI,KAAK,OAAO,EACf;gBACD,6BAA6B;gBAC7B,uEAAuE;gBACvE,MAAM,QAAQ,GAAG,GAChB,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAC9B,MAAM,MAAM,EAAE,CAAC;gBACf,IAAI,cAAc,IAAI,WAAW,EAAE;oBAClC,MAAM,EAAE,eAAe,EAAE,GAAG,wDAC3B,mBAAmB,GACnB,CAAC;oBACF,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;iBACjD;qBAAM;oBACN,MAAM,EAAE,cAAc,EAAE,GAAG,wDAAa,kBAAkB,GAAC,CAAC;oBAC5D,KAAK,GAAG,IAAI,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;iBAChD;aACD;YAED,IAAI;gBACH,IAAI,MAAM,EAAE;oBACX,wDAAwD;oBACxD,MAAM,IAAA,aAAI,EAAC,MAAM,EAAE,SAAS,CAAC,CAAC;oBAC9B,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;oBACrC,OAAO,MAAM,CAAC;iBACd;gBACD,IAAI,KAAK,EAAE;oBACV,MAAM,CAAC,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACzC,IAAI,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,EAAE;wBAC/B,MAAM,IAAI,KAAK,CACd,mDAAmD,CACnD,CAAC;qBACF;oBACD,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;oBACxC,OAAO,CAAC,CAAC;iBACT;gBACD,MAAM,IAAI,KAAK,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;aAChE;YAAC,OAAO,GAAG,EAAE;gBACb,KAAK,CAAC,4BAA4B,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;gBAChD,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;aACzC;SACD;QAED,MAAM,IAAI,KAAK,CACd,uDAAuD,IAAI,CAAC,SAAS,CACpE,OAAO,CACP,EAAE,CACH,CAAC;IACH,CAAC;;AAlOe,uBAAS,GAAyB;IACjD,UAAU;IACV,UAAU;IACV,SAAS;IACT,UAAU;IACV,WAAW;CACX,AANwB,CAMvB;AAPU,sCAAa"}