/**
 * Executes bundletool utility with given arguments and returns the actual stdout
 *
 * @this {import('../adb.js').ADB}
 * @param {Array<String>} args - the list of bundletool arguments
 * @param {string} errorMsg - The customized error message string
 * @returns {Promise<string>} the actual command stdout
 * @throws {Error} If bundletool jar does not exist in PATH or there was an error while
 * executing it
 */
export function execBundletool(this: import("../adb.js").ADB, args: Array<string>, errorMsg: string): Promise<string>;
/**
 *
 * @this {import('../adb.js').ADB}
 * @param {string} specLocation - The full path to the generated device spec location
 * @returns {Promise<string>} The same `specLocation` value
 * @throws {Error} If it is not possible to retrieve the spec for the current device
 */
export function getDeviceSpec(this: import("../adb.js").ADB, specLocation: string): Promise<string>;
/**
 * Installs the given apks into the device under test
 *
 * @this {import('../adb.js').ADB}
 * @param {Array<string>} apkPathsToInstall - The full paths to install apks
 * @param {import('./types').InstallMultipleApksOptions} [options={}] - Installation options
 */
export function installMultipleApks(this: import("../adb.js").ADB, apkPathsToInstall: Array<string>, options?: import("./types").InstallMultipleApksOptions): Promise<string>;
/**
 * Installs the given .apks package into the device under test
 *
 * @this {import('../adb.js').ADB}
 * @param {string} apks - The full path to the .apks file
 * @param {import('./types').InstallApksOptions} [options={}] - Installation options
 * @throws {Error} If the .apks bundle cannot be installed
 */
export function installApks(this: import("../adb.js").ADB, apks: string, options?: import("./types").InstallApksOptions): Promise<void>;
/**
 * Extracts and returns the full path to the master .apk file inside the bundle.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} apks - The full path to the .apks file
 * @returns {Promise<string>} The full path to the master bundle .apk
 * @throws {Error} If there was an error while extracting/finding the file
 */
export function extractBaseApk(this: import("../adb.js").ADB, apks: string): Promise<string>;
/**
 * Extracts and returns the full path to the .apk, which contains the corresponding
 * resources for the given language in the .apks bundle.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} apks - The full path to the .apks file
 * @param {?string} [language=null] - The language abbreviation. The default language is
 * going to be selected if it is not set.
 * @returns {Promise<string>} The full path to the corresponding language .apk or the master .apk
 * if language split is not enabled for the bundle.
 * @throws {Error} If there was an error while extracting/finding the file
 */
export function extractLanguageApk(this: import("../adb.js").ADB, apks: string, language?: string | null): Promise<string>;
/**
 *
 * @param {string} output
 * @returns {boolean}
 */
export function isTestPackageOnlyError(output: string): boolean;
//# sourceMappingURL=apks-utils.d.ts.map