{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../src/request/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,aAAa,CAAA;AAE1C,MAAM,WAAW,iBAAiB,CAAC,CAAC,GAAG,OAAO;IAC1C,KAAK,EAAE,CAAC,CAAA;IACR;;;OAGG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,UAAU,CAAC,EAAE,MAAM,CAAA;IAEnB;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,SAAS,CAAC,EAAE,MAAM,CAAA;CACrB;AAED,MAAM,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAA;AAC3D,MAAM,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;AAEpE,MAAM,WAAW,mBAAmB;IAChC,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,iBAAiB,KAAK,IAAI,CAAA;IAC3C,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,eAAe,KAAK,IAAI,CAAA;IAC1C,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,iBAAiB,KAAK,IAAI,CAAA;IACzC,aAAa,CAAC,EAAE,CAAC,EAAE,EAAE,uBAAuB,KAAK,IAAI,CAAA;IACrD,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,QAAQ,KAAK,IAAI,CAAA;CACrC;AAED,MAAM,MAAM,iBAAiB,GAAG,WAAW,CAAA;AAC3C,MAAM,MAAM,eAAe,GAAG;IAAE,MAAM,CAAC,EAAE,OAAO,CAAC;IAAC,KAAK,CAAC,EAAE,KAAK,CAAA;CAAE,CAAA;AACjE,MAAM,MAAM,iBAAiB,GAAG;IAAE,KAAK,EAAE,KAAK,CAAC;IAAC,UAAU,EAAE,MAAM,CAAA;CAAE,CAAA;AACpE,MAAM,MAAM,uBAAuB,GAAG;IAAE,OAAO,EAAE,WAAW,CAAC;IAAC,mBAAmB,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,OAAO,CAAC;IAAC,KAAK,CAAC,EAAE,KAAK,CAAC;IAAC,UAAU,EAAE,MAAM,CAAA;CAAE,CAAA"}