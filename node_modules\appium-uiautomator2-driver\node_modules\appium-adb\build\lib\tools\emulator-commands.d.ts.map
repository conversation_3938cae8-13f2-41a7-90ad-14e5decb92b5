{"version": 3, "file": "emulator-commands.d.ts", "sourceRoot": "", "sources": ["../../../lib/tools/emulator-commands.js"], "names": [], "mappings": "AAsDA;;;;;GAKG;AACH,oEAFY,OAAO,CAAC,OAAO,CAAC,CAK3B;AAED;;;;;GAKG;AACH,sFAIC;AAED;;;;;GAKG;AACH,0EAFW,MAAM,iBAYhB;AAED;;;;;GAKG;AACH,qEAEC;AAED;;;;;GAKG;AACH,+DAFW,OAAO,SAAS,EAAE,aAAa,iBAQzC;AAED;;;;;;;GAOG;AACH,iEAJW,MAAM,SACN,OAAO,SAAS,EAAE,OAAO,iBAcnC;AAED;;;;;GAKG;AACH,uEAFW,MAAM,GAAC,MAAM,iBAQvB;AAED;;;GAGG;AACH,uEAGC;AAED;;;;;;;GAOG;AACH,oEAJW,MAAM,GAAC,MAAM,YACb,MAAM,iBAWhB;AAED;;;;;;;;GAQG;AACH,oEALW,MAAM,GAAC,MAAM,UACb,OAAO,SAAS,EAAE,cAAc,iBAc1C;AAED;;;;;;GAMG;AACH,oEAHW,OAAO,SAAS,EAAE,iBAAiB,iBAY7C;AAED;;;;;;GAMG;AACH,gEAHW,OAAO,SAAS,EAAE,cAAc,iBAW1C;AAED;;;;;;;GAOG;AACH,oEAJW,OAAO,SAAS,EAAE,YAAY,iBAYxC;AAED;;;;;;;;;;;GAWG;AACH,0EARW,MAAM,EAAE,GAAC,MAAM,SAGf,OAAO,SAAS,EAAE,iBAAiB,GACjC,OAAO,CAAC,MAAM,CAAC,CAoF3B;AAED;;;;;;GAMG;AACH,kEAHa,OAAO,CAAC,OAAO,SAAS,EAAE,cAAc,CAAC,CAoBrD;AAED;;;;;;;;;;;;GAYG;AACH,8EATW,MAAM,GAEJ,OAAO,CAAC,OAAO,SAAS,EAAE,YAAY,CAAC,CAoBnD;AAED;;;;;;;GAOG;AACH,sEAJW,MAAM,oBAgBhB;AAED;;;;;;GAMG;AACH,0EAHW,MAAM,GACL,OAAO,CAAC,MAAM,CAAC,CAI1B"}