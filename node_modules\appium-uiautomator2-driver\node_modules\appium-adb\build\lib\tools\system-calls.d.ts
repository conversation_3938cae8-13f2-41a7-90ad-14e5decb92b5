/**
 * Retrieve full path to the given binary.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} binaryName - The name of the binary.
 * @return {Promise<string>} Full path to the given binary including current SDK root.
 */
export function getSdkBinaryPath(this: import("../adb.js").ADB, binaryName: string): Promise<string>;
/**
 * Retrieve full path to the given binary and caches it into `binaries`
 * property of the current ADB instance.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} binaryName - Simple name of a binary file.
 * @return {Promise<string>} Full path to the given binary. The method tries
 *                  to enumerate all the known locations where the binary
 *                  might be located and stops the search as soon as the first
 *                  match is found on the local file system.
 * @throws {Error} If the binary with given name is not present at any
 *                 of known locations or Android SDK is not installed on the
 *                 local file system.
 */
export function getBinaryFromSdkRoot(this: import("../adb.js").ADB, binaryName: string): Promise<string>;
/**
 * Retrieve full path to the given binary.
 * This method does not have cache.
 *
 * @param {string} binaryName - Simple name of a binary file.
 *                              e.g. 'adb', 'android'
 * @return {Promise<string>} Full path to the given binary. The method tries
 *                  to enumerate all the known locations where the binary
 *                  might be located and stops the search as soon as the first
 *                  match is found on the local file system.
 *                  e.g. '/Path/To/Android/sdk/platform-tools/adb'
 * @throws {Error} If the binary with given name is not present at any
 *                 of known locations or Android SDK is not installed on the
 *                 local file system.
 */
export function getAndroidBinaryPath(binaryName: string): Promise<string>;
/**
 * Retrieve full path to a binary file using the standard system lookup tool.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} binaryName - The name of the binary.
 * @return {Promise<string>} Full path to the binary received from 'which'/'where'
 *                  output.
 * @throws {Error} If lookup tool returns non-zero return code.
 */
export function getBinaryFromPath(this: import("../adb.js").ADB, binaryName: string): Promise<string>;
/**
 * Retrieve the list of devices visible to adb.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').ConnectedDevicesOptions} [opts={}] - Additional options mapping.
 * @return {Promise<import('./types').Device[]>} The list of devices or an empty list if
 *                          no devices are connected.
 * @throws {Error} If there was an error while listing devices.
 */
export function getConnectedDevices(this: import("../adb.js").ADB, opts?: import("./types").ConnectedDevicesOptions): Promise<import("./types").Device[]>;
/**
 * Retrieve the list of devices visible to adb within the given timeout.
 *
 * @this {import('../adb.js').ADB}
 * @param {number} timeoutMs - The maximum number of milliseconds to get at least
 *                             one list item.
 * @return {Promise<import('./types').Device[]>} The list of connected devices.
 * @throws {Error} If no connected devices can be detected within the given timeout.
 */
export function getDevicesWithRetry(this: import("../adb.js").ADB, timeoutMs?: number): Promise<import("./types").Device[]>;
/**
 * Kick current connection from host/device side and make it reconnect
 *
 * @this {import('../adb.js').ADB}
 * @param {string} [target=offline] One of possible targets to reconnect:
 * offline, device or null
 * Providing `null` will cause reconnection to happen from the host side.
 *
 * @throws {Error} If either ADB version is too old and does not support this
 * command or there was a failure during reconnect.
 */
export function reconnect(this: import("../adb.js").ADB, target?: string): Promise<void>;
/**
 * Restart adb server, unless _this.suppressKillServer_ property is true.
 *
 * @this {import('../adb.js').ADB}
 */
export function restartAdb(this: import("../adb.js").ADB): Promise<void>;
/**
 * Kill adb server.
 * @this {import('../adb.js').ADB}
 */
export function killServer(this: import("../adb.js").ADB): Promise<void>;
/**
 * Execute the given emulator command using _adb emu_ tool.
 *
 * @this {import('../adb.js').ADB}
 * @param {string[]} cmd - The array of rest command line parameters.
 */
export function adbExecEmu(this: import("../adb.js").ADB, cmd: string[]): Promise<void>;
/**
 * Execute the given adb command.
 *
 * @template {import('teen_process').TeenProcessExecOptions & import('./types').ShellExecOptions & import('./types').SpecialAdbExecOptions} TExecOpts
 * @this {import('../adb.js').ADB}
 * @param {string|string[]} cmd - The array of rest command line parameters
 *                      or a single string parameter.
 * @param {TExecOpts} [opts] Additional options mapping. See
 * {@link https://github.com/appium/node-teen_process}
 * for more details.
 * You can also set the additional `exclusive` param
 * to `true` that assures no other parallel adb commands
 * are going to be executed while the current one is running
 * You can set the `outputFormat` param to `stdout` to receive just the stdout
 * output (default) or `full` to receive the stdout and stderr response from a
 * command with a zero exit code
 * @return {Promise<TExecOpts extends import('./types').TFullOutputOption ? import('teen_process').TeenProcessExecResult : string>}
 * Command's stdout or an object containing stdout and stderr.
 * @throws {Error} If the command returned non-zero exit code.
 */
export function adbExec<TExecOpts extends import("teen_process").TeenProcessExecOptions & import("./types").ShellExecOptions & import("./types").SpecialAdbExecOptions>(this: import("../adb.js").ADB, cmd: string | string[], opts?: TExecOpts): Promise<TExecOpts extends import("./types").TFullOutputOption ? import("teen_process").TeenProcessExecResult<any> : string>;
/**
 * Execute the given command using _adb shell_ prefix.
 *
 * @this {import('../adb.js').ADB}
 * @template {import('./types').ShellExecOptions} TShellExecOpts
 * @param {string|string[]} cmd - The array of rest command line parameters or a single
 *                                      string parameter.
 * @param {TShellExecOpts} [opts] - Additional options mapping.
 * @return {Promise<TShellExecOpts extends import('./types').TFullOutputOption ? import('teen_process').TeenProcessExecResult : string>}
 * Command's stdout.
 * @throws {Error} If the command returned non-zero exit code.
 */
export function shell<TShellExecOpts extends import("./types").ShellExecOptions>(this: import("../adb.js").ADB, cmd: string | string[], opts?: TShellExecOpts): Promise<TShellExecOpts extends import("./types").TFullOutputOption ? import("teen_process").TeenProcessExecResult<any> : string>;
/**
 *
 * @this {import('../adb.js').ADB}
 * @param {string[]} [args=[]]
 * @returns {import('teen_process').SubProcess}
 */
export function createSubProcess(this: import("../adb.js").ADB, args?: string[]): import("teen_process").SubProcess;
/**
 * Retrieve the current adb port.
 * @todo can probably deprecate this now that the logic is just to read this.adbPort
 *
 * @this {import('../adb.js').ADB}
 * @return {number} The current adb port number.
 */
export function getAdbServerPort(this: import("../adb.js").ADB): number;
/**
 * Retrieve the current emulator port from _adb devives_ output.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<number>} The current emulator port.
 * @throws {Error} If there are no connected devices.
 */
export function getEmulatorPort(this: import("../adb.js").ADB): Promise<number>;
/**
 * Retrieve the current emulator port by parsing emulator name string.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} emStr - Emulator name string.
 * @return {number|false} Either the current emulator port or
 * _false_ if port number cannot be parsed.
 */
export function getPortFromEmulatorString(this: import("../adb.js").ADB, emStr: string): number | false;
/**
 * Retrieve the list of currently connected emulators.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').ConnectedDevicesOptions} [opts={}] - Additional options mapping.
 * @return {Promise<import('./types').Device[]>} The list of connected devices.
 */
export function getConnectedEmulators(this: import("../adb.js").ADB, opts?: import("./types").ConnectedDevicesOptions): Promise<import("./types").Device[]>;
/**
 * Set _emulatorPort_ property of the current class.
 *
 * @this {import('../adb.js').ADB}
 * @param {number} emPort - The emulator port to be set.
 */
export function setEmulatorPort(this: import("../adb.js").ADB, emPort: number): void;
export class setEmulatorPort {
    /**
     * Set _emulatorPort_ property of the current class.
     *
     * @this {import('../adb.js').ADB}
     * @param {number} emPort - The emulator port to be set.
     */
    constructor(this: import("../adb.js").ADB, emPort: number);
    emulatorPort: number;
}
/**
 * Set the identifier of the current device (_this.curDeviceId_).
 *
 * @this {import('../adb.js').ADB}
 * @param {string} deviceId - The device identifier.
 */
export function setDeviceId(this: import("../adb.js").ADB, deviceId: string): void;
export class setDeviceId {
    /**
     * Set the identifier of the current device (_this.curDeviceId_).
     *
     * @this {import('../adb.js').ADB}
     * @param {string} deviceId - The device identifier.
     */
    constructor(this: import("../adb.js").ADB, deviceId: string);
    curDeviceId: string;
}
/**
 * Set the the current device object.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').Device} deviceObj - The device object to be set.
 */
export function setDevice(this: import("../adb.js").ADB, deviceObj: import("./types").Device): void;
/**
 * Get the object for the currently running emulator.
 * !!! This method has a side effect - it implicitly changes the
 * `deviceId` (only if AVD with a matching name is found)
 * and `emulatorPort` instance properties.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} avdName - Emulator name.
 * @return {Promise<import('./types').Device|null>} Currently running emulator or _null_.
 */
export function getRunningAVD(this: import("../adb.js").ADB, avdName: string): Promise<import("./types").Device | null>;
/**
 * Get the object for the currently running emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} avdName - Emulator name.
 * @param {number} [timeoutMs=20000] - The maximum number of milliseconds
 *                                     to wait until at least one running AVD object
 *                                     is detected.
 * @return {Promise<import('./types').Device|null>} Currently running emulator or _null_.
 * @throws {Error} If no device has been detected within the timeout.
 */
export function getRunningAVDWithRetry(this: import("../adb.js").ADB, avdName: string, timeoutMs?: number): Promise<import("./types").Device | null>;
/**
 * Shutdown all running emulators by killing their processes.
 *
 * @this {import('../adb.js').ADB}
 * @throws {Error} If killing tool returned non-zero return code.
 */
export function killAllEmulators(this: import("../adb.js").ADB): Promise<void>;
/**
 * Kill emulator with the given name. No error
 * is thrown is given avd does not exist/is not running.
 *
 * @this {import('../adb.js').ADB}
 * @param {string?} [avdName=null] - The name of the emulator to be killed. If empty,
 *                            the current emulator will be killed.
 * @param {number} [timeout=60000] - The amount of time to wait before throwing
 *                                    an exception about unsuccessful killing
 * @return {Promise<boolean>} - True if the emulator was killed, false otherwise.
 * @throws {Error} if there was a failure by killing the emulator
 */
export function killEmulator(this: import("../adb.js").ADB, avdName?: string | null, timeout?: number): Promise<boolean>;
/**
 * Start an emulator with given parameters and wait until it is fully started.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} avdName - The name of an existing emulator.
 * @param {import('./types').AvdLaunchOptions} [opts={}]
 * @returns {Promise<SubProcess>} Emulator subprocess instance
 * @throws {Error} If the emulator fails to start within the given timeout.
 */
export function launchAVD(this: import("../adb.js").ADB, avdName: string, opts?: import("./types").AvdLaunchOptions): Promise<SubProcess>;
/**
 * Check if the current emulator is ready to accept further commands (booting completed).
 *
 * @this {import('../adb.js').ADB}
 * @param {number} [timeoutMs=20000] - The maximum number of milliseconds to wait.
 * @returns {Promise<void>}
 * @throws {Error} If the emulator is not ready within the given timeout.
 */
export function waitForEmulatorReady(this: import("../adb.js").ADB, timeoutMs?: number): Promise<void>;
/**
 * Check if the current device is ready to accept further commands (booting completed).
 *
 * @this {import('../adb.js').ADB}
 * @param {number} [appDeviceReadyTimeout=30] - The maximum number of seconds to wait.
 * @throws {Error} If the device is not ready within the given timeout.
 */
export function waitForDevice(this: import("../adb.js").ADB, appDeviceReadyTimeout?: number): Promise<void>;
export class waitForDevice {
    /**
     * Check if the current device is ready to accept further commands (booting completed).
     *
     * @this {import('../adb.js').ADB}
     * @param {number} [appDeviceReadyTimeout=30] - The maximum number of seconds to wait.
     * @throws {Error} If the device is not ready within the given timeout.
     */
    constructor(this: import("../adb.js").ADB, appDeviceReadyTimeout?: number);
    appDeviceReadyTimeout: number;
}
/**
 * Reboot the current device and wait until it is completed.
 *
 * @this {import('../adb.js').ADB}
 * @param {number} [retries=DEFAULT_ADB_REBOOT_RETRIES] - The maximum number of reboot retries.
 * @throws {Error} If the device failed to reboot and number of retries is exceeded.
 */
export function reboot(this: import("../adb.js").ADB, retries?: number): Promise<void>;
/**
 * Switch adb server root privileges.
 *
 * @this {import('../adb.js').ADB}
 * @param {boolean} isElevated - Should we elevate to to root or unroot? (default true)
 * @return {Promise<import('./types').RootResult>}
 */
export function changeUserPrivileges(this: import("../adb.js").ADB, isElevated: boolean): Promise<import("./types").RootResult>;
/**
 * Switch adb server to root mode
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<import('./types').RootResult>}
 */
export function root(this: import("../adb.js").ADB): Promise<import("./types").RootResult>;
/**
 * Switch adb server to non-root mode.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<import('./types').RootResult>}
 */
export function unroot(this: import("../adb.js").ADB): Promise<import("./types").RootResult>;
/**
 * Checks whether the current user is root
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if the user is root
 * @throws {Error} if there was an error while identifying
 * the user.
 */
export function isRoot(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Installs the given certificate on a rooted real device or
 * an emulator. The emulator must be executed with `-writable-system`
 * command line option and adb daemon should be running in root
 * mode for this method to work properly. The method also requires
 * openssl tool to be available on the destination system.
 * Read https://github.com/appium/appium/issues/10964
 * for more details on this topic
 *
 * @this {import('../adb.js').ADB}
 * @param {Buffer|string} cert - base64-decoded content of the actual certificate
 * represented as a string or a buffer
 * @throws {Error} If openssl tool is not available on the destination system
 * or if there was an error while installing the certificate
 */
export function installMitmCertificate(this: import("../adb.js").ADB, cert: Buffer | string): Promise<void>;
/**
 * Verifies if the given root certificate is already installed on the device.
 *
 * @this {import('../adb.js').ADB}
 * @param {Buffer|string} cert - base64-decoded content of the actual certificate
 * represented as a string or a buffer
 * @throws {Error} If openssl tool is not available on the destination system
 * or if there was an error while checking the certificate
 * @returns {Promise<boolean>} true if the given certificate is already installed
 */
export function isMitmCertificateInstalled(this: import("../adb.js").ADB, cert: Buffer | string): Promise<boolean>;
/**
 * Creates chunks for the given arguments and executes them in `adb shell`.
 * This is faster than calling `adb shell` separately for each arg, however
 * there is a limit for a maximum length of a single adb command. that is why
 * we need all this complicated logic.
 *
 * @this {import('../adb.js').ADB}
 * @param {(x: string) => string[]} argTransformer A function, that receives single argument
 * from the `args` array and transforms it into a shell command. The result
 * of the function must be an array, where each item is a part of a single command.
 * The last item of the array could be ';'. If this is not a semicolon then it is going to
 * be added automatically.
 * @param {string[]} args Array of argument values to create chunks for
 * @throws {Error} If any of the chunks returns non-zero exit code after being executed
 */
export function shellChunks(this: import("../adb.js").ADB, argTransformer: (x: string) => string[], args: string[]): Promise<void>;
/**
 * Transforms the given language and country abbreviations
 * to AVD arguments array
 *
 * @param {?string} language Language name, for example 'fr'
 * @param {?string} country Country name, for example 'CA'
 * @returns {Array<string>} The generated arguments. The
 * resulting array might be empty if both arguments are empty
 */
export function toAvdLocaleArgs(language: string | null, country: string | null): Array<string>;
export const getBinaryNameForOS: typeof _getBinaryNameForOS & _.MemoizedFunction;
/**
 * Reset Telnet authentication token.
 * @see {@link http://tools.android.com/recent/emulator2516releasenotes} for more details.
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<boolean>} If token reset was successful.
 */
export const resetTelnetAuthToken: (() => Promise<boolean>) & _.MemoizedFunction;
/** @type {{STDOUT: 'stdout', FULL: 'full'}} */
export const EXEC_OUTPUT_FORMAT: {
    STDOUT: "stdout";
    FULL: "full";
};
/**
 * Get the adb version. The result of this method is cached.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<import('./types').Version>}
 * @throws {Error} If it is not possible to parse adb binary version.
 */
export const getVersion: (() => Promise<{
    binary: {
        version: semver.SemVer | null;
        build: number;
    };
    bridge: {
        version: semver.SemVer | null;
    };
}>) & _.MemoizedFunction;
/**
 * Retrieves full paths to all 'build-tools' subfolders under the particular
 * SDK root folder
 *
 * @type {(sdkRoot: string) => Promise<string[]>}
 */
export const getBuildToolsDirs: (sdkRoot: string) => Promise<string[]>;
import { SubProcess } from 'teen_process';
/**
 * Retrieve full binary name for the current operating system.
 *
 * @param {string} binaryName - simple binary name, for example 'android'.
 * @return {string} Formatted binary name depending on the current platform,
 *                  for example, 'android.bat' on Windows.
 */
declare function _getBinaryNameForOS(binaryName: string): string;
import _ from 'lodash';
import * as semver from 'semver';
export {};
//# sourceMappingURL=system-calls.d.ts.map