{"version": 3, "sources": ["../../node_modules/leven/index.js", "../../node_modules/jsonpointer/jsonpointer.js", "../../src/index.js", "../../node_modules/@humanwhocodes/momoa/dist/momoa.js", "../../src/utils.js", "../../src/validation-errors/required.js", "../../src/code-frame-columns.js", "../../src/json/utils.js", "../../src/json/get-meta-from-path.js", "../../src/json/get-decorated-data-path.js", "../../src/validation-errors/base.js", "../../src/validation-errors/additional-prop.js", "../../src/validation-errors/enum.js", "../../src/validation-errors/default.js", "../../src/helpers.js"], "sourcesContent": ["'use strict';\nconst array = [];\nconst charCodeCache = [];\n\nconst leven = (left, right) => {\n\tif (left === right) {\n\t\treturn 0;\n\t}\n\n\tconst swap = left;\n\n\t// Swapping the strings if `a` is longer than `b` so we know which one is the\n\t// shortest & which one is the longest\n\tif (left.length > right.length) {\n\t\tleft = right;\n\t\tright = swap;\n\t}\n\n\tlet leftLength = left.length;\n\tlet rightLength = right.length;\n\n\t// Performing suffix trimming:\n\t// We can linearly drop suffix common to both strings since they\n\t// don't increase distance at all\n\t// Note: `~-` is the bitwise way to perform a `- 1` operation\n\twhile (leftLength > 0 && (left.charCodeAt(~-leftLength) === right.charCodeAt(~-rightLength))) {\n\t\tleftLength--;\n\t\trightLength--;\n\t}\n\n\t// Performing prefix trimming\n\t// We can linearly drop prefix common to both strings since they\n\t// don't increase distance at all\n\tlet start = 0;\n\n\twhile (start < leftLength && (left.charCodeAt(start) === right.charCodeAt(start))) {\n\t\tstart++;\n\t}\n\n\tleftLength -= start;\n\trightLength -= start;\n\n\tif (leftLength === 0) {\n\t\treturn rightLength;\n\t}\n\n\tlet bCharCode;\n\tlet result;\n\tlet temp;\n\tlet temp2;\n\tlet i = 0;\n\tlet j = 0;\n\n\twhile (i < leftLength) {\n\t\tcharCodeCache[i] = left.charCodeAt(start + i);\n\t\tarray[i] = ++i;\n\t}\n\n\twhile (j < rightLength) {\n\t\tbCharCode = right.charCodeAt(start + j);\n\t\ttemp = j++;\n\t\tresult = j;\n\n\t\tfor (i = 0; i < leftLength; i++) {\n\t\t\ttemp2 = bCharCode === charCodeCache[i] ? temp : temp + 1;\n\t\t\ttemp = array[i];\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\tresult = array[i] = temp > result ? temp2 > result ? result + 1 : temp2 : temp2 > temp ? temp + 1 : temp2;\n\t\t}\n\t}\n\n\treturn result;\n};\n\nmodule.exports = leven;\n// TODO: Remove this for the next major release\nmodule.exports.default = leven;\n", "var hasExcape = /~/\nvar escapeMatcher = /~[01]/g\nfunction escapeReplacer (m) {\n  switch (m) {\n    case '~1': return '/'\n    case '~0': return '~'\n  }\n  throw new Error('Invalid tilde escape: ' + m)\n}\n\nfunction untilde (str) {\n  if (!hasExcape.test(str)) return str\n  return str.replace(escapeMatcher, escapeReplacer)\n}\n\nfunction setter (obj, pointer, value) {\n  var part\n  var hasNextPart\n\n  for (var p = 1, len = pointer.length; p < len;) {\n    if (pointer[p] === 'constructor' || pointer[p] === 'prototype' || pointer[p] === '__proto__') return obj\n\n    part = untilde(pointer[p++])\n    hasNextPart = len > p\n\n    if (typeof obj[part] === 'undefined') {\n      // support setting of /-\n      if (Array.isArray(obj) && part === '-') {\n        part = obj.length\n      }\n\n      // support nested objects/array when setting values\n      if (hasNextPart) {\n        if ((pointer[p] !== '' && pointer[p] < Infinity) || pointer[p] === '-') obj[part] = []\n        else obj[part] = {}\n      }\n    }\n\n    if (!hasNextPart) break\n    obj = obj[part]\n  }\n\n  var oldValue = obj[part]\n  if (value === undefined) delete obj[part]\n  else obj[part] = value\n  return oldValue\n}\n\nfunction compilePointer (pointer) {\n  if (typeof pointer === 'string') {\n    pointer = pointer.split('/')\n    if (pointer[0] === '') return pointer\n    throw new Error('Invalid JSON pointer.')\n  } else if (Array.isArray(pointer)) {\n    for (const part of pointer) {\n      if (typeof part !== 'string' && typeof part !== 'number') {\n        throw new Error('Invalid JSON pointer. Must be of type string or number.')\n      }\n    }\n    return pointer\n  }\n\n  throw new Error('Invalid JSON pointer.')\n}\n\nfunction get (obj, pointer) {\n  if (typeof obj !== 'object') throw new Error('Invalid input object.')\n  pointer = compilePointer(pointer)\n  var len = pointer.length\n  if (len === 1) return obj\n\n  for (var p = 1; p < len;) {\n    obj = obj[untilde(pointer[p++])]\n    if (len === p) return obj\n    if (typeof obj !== 'object' || obj === null) return undefined\n  }\n}\n\nfunction set (obj, pointer, value) {\n  if (typeof obj !== 'object') throw new Error('Invalid input object.')\n  pointer = compilePointer(pointer)\n  if (pointer.length === 0) throw new Error('Invalid JSON pointer for set.')\n  return setter(obj, pointer, value)\n}\n\nfunction compile (pointer) {\n  var compiled = compilePointer(pointer)\n  return {\n    get: function (object) {\n      return get(object, compiled)\n    },\n    set: function (object, value) {\n      return set(object, compiled, value)\n    }\n  }\n}\n\nexports.get = get\nexports.set = set\nexports.compile = compile\n", "import { parse } from '@humanwhocodes/momoa';\nimport prettify from './helpers';\n\nexport default (schema, data, errors, options = {}) => {\n  const { format = 'cli', indent = null, json = null } = options;\n\n  const jsonRaw = json || JSON.stringify(data, null, indent);\n  const jsonAst = parse(jsonRaw);\n\n  const customErrorToText = error => error.print().join('\\n');\n  const customErrorToStructure = error => error.getError();\n  const customErrors = prettify(errors, {\n    data,\n    schema,\n    jsonAst,\n    jsonRaw,\n  });\n\n  if (format === 'cli') {\n    return customErrors.map(customErrorToText).join('\\n\\n');\n  } else {\n    return customErrors.map(customErrorToStructure);\n  }\n};\n", "/**\n * @fileoverview Character codes.\n * <AUTHOR>\n */\n\nconst CHAR_0 = 48;          // 0\nconst CHAR_1 = 49;          // 1\nconst CHAR_9 = 57;          // 9\nconst CHAR_BACKSLASH = 92;  // \\\nconst CHAR_DOLLAR = 36;     // $\nconst CHAR_DOT = 46;        // .\nconst CHAR_DOUBLE_QUOTE = 34; // \"\nconst CHAR_LOWER_A = 97;    // a\nconst CHAR_LOWER_E = 101;         // e\nconst CHAR_LOWER_F = 102;   // f\nconst CHAR_LOWER_N = 110;   // n\nconst CHAR_LOWER_T = 116;   // t\nconst CHAR_LOWER_U = 117;   // u\nconst CHAR_LOWER_X = 120;   // x\nconst CHAR_LOWER_Z = 122;   // z\nconst CHAR_MINUS = 45;      // -\nconst CHAR_NEWLINE = 10;    // newline\nconst CHAR_PLUS = 43;       // +\nconst CHAR_RETURN = 13;     // return\nconst CHAR_SINGLE_QUOTE = 39; // '\nconst CHAR_SLASH = 47;      // /\nconst CHAR_SPACE = 32;      // space\nconst CHAR_TAB = 9;         // tab\nconst CHAR_UNDERSCORE = 95; // _\nconst CHAR_UPPER_A = 65;    // A\nconst CHAR_UPPER_E = 69;          // E\nconst CHAR_UPPER_F = 70;    // F\nconst CHAR_UPPER_N = 78;    // N\nconst CHAR_UPPER_X = 88;    // X\nconst CHAR_UPPER_Z = 90;    // Z\nconst CHAR_LOWER_B = 98;    // b\nconst CHAR_LOWER_R = 114;   // r\nconst CHAR_LOWER_V = 118;   // v\nconst CHAR_LINE_SEPARATOR = 0x2028;\nconst CHAR_PARAGRAPH_SEPARATOR = 0x2029;\nconst CHAR_LOWER_L = 108;   // l\nconst CHAR_LOWER_S = 115;   // s\nconst CHAR_UPPER_I = 73;    // I\nconst CHAR_STAR = 42;       // *\nconst CHAR_VTAB = 11;            // U+000B Vertical tab\nconst CHAR_FORM_FEED = 12;       // U+000C Form feed\nconst CHAR_NBSP = 160;           // U+00A0 Non-breaking space\nconst CHAR_BOM = 65279;          // U+FEFF\nconst CHAR_NON_BREAKING_SPACE = 160;\nconst CHAR_EN_QUAD = 8192;\nconst CHAR_EM_QUAD = 8193;\nconst CHAR_EN_SPACE = 8194;\nconst CHAR_EM_SPACE = 8195;\nconst CHAR_THREE_PER_EM_SPACE = 8196;\nconst CHAR_FOUR_PER_EM_SPACE = 8197;\nconst CHAR_SIX_PER_EM_SPACE = 8198;\nconst CHAR_FIGURE_SPACE = 8199;\nconst CHAR_PUNCTUATION_SPACE = 8200;\nconst CHAR_THIN_SPACE = 8201;\nconst CHAR_HAIR_SPACE = 8202;\nconst CHAR_NARROW_NO_BREAK_SPACE = 8239;\nconst CHAR_MEDIUM_MATHEMATICAL_SPACE = 8287;\nconst CHAR_IDEOGRAPHIC_SPACE = 12288;\n\n/**\n * @fileoverview JSON syntax helpers\n * <AUTHOR> C. Zakas\n */\n\n\n//-----------------------------------------------------------------------------\n// Types\n//-----------------------------------------------------------------------------\n\n/** @typedef {import(\"./typedefs.ts\").TokenType} TokenType */\n\n//-----------------------------------------------------------------------------\n// Predefined Tokens\n//-----------------------------------------------------------------------------\n\nconst LBRACKET = \"[\";\nconst RBRACKET = \"]\";\nconst LBRACE = \"{\";\nconst RBRACE = \"}\";\nconst COLON = \":\";\nconst COMMA = \",\";\n\nconst TRUE = \"true\";\nconst FALSE = \"false\";\nconst NULL = \"null\";\nconst NAN$1 = \"NaN\";\nconst INFINITY$1 = \"Infinity\";\nconst QUOTE = \"\\\"\";\n\nconst expectedKeywords = new Map([\n    [CHAR_LOWER_T, [CHAR_LOWER_R, CHAR_LOWER_U, CHAR_LOWER_E]],\n    [CHAR_LOWER_F, [CHAR_LOWER_A, CHAR_LOWER_L, CHAR_LOWER_S, CHAR_LOWER_E]],\n    [CHAR_LOWER_N, [CHAR_LOWER_U, CHAR_LOWER_L, CHAR_LOWER_L]]\n]);\n\nconst escapeToChar = new Map([\n    [CHAR_DOUBLE_QUOTE, QUOTE],\n    [CHAR_BACKSLASH, \"\\\\\"],\n    [CHAR_SLASH, \"/\"],\n    [CHAR_LOWER_B, \"\\b\"],\n    [CHAR_LOWER_N, \"\\n\"],\n    [CHAR_LOWER_F, \"\\f\"],\n    [CHAR_LOWER_R, \"\\r\"],\n    [CHAR_LOWER_T, \"\\t\"]\n]);\n\nconst json5EscapeToChar = new Map([\n    ...escapeToChar,\n    [CHAR_LOWER_V, \"\\v\"],\n    [CHAR_0, \"\\0\"]\n]);\n\nconst charToEscape = new Map([\n    [QUOTE, QUOTE],\n    [\"\\\\\", \"\\\\\"],\n    [\"/\", \"/\"],\n    [\"\\b\", \"b\"],\n    [\"\\n\", \"n\"],\n    [\"\\f\", \"f\"],\n    [\"\\r\", \"r\"],\n    [\"\\t\", \"t\"]\n]);\n\nconst json5CharToEscape = new Map([\n    ...charToEscape,\n    [\"\\v\", \"v\"],\n    [\"\\0\", \"0\"],\n    [\"\\u2028\", \"u2028\"],\n    [\"\\u2029\", \"u2029\"]\n]);\n\n/** @type {Map<string,TokenType>} */\nconst knownTokenTypes = new Map([\n    [LBRACKET, \"LBracket\"],\n    [RBRACKET, \"RBracket\"],\n    [LBRACE, \"LBrace\"],\n    [RBRACE, \"RBrace\"],\n    [COLON, \"Colon\"],\n    [COMMA, \"Comma\"],\n    [TRUE, \"Boolean\"],\n    [FALSE, \"Boolean\"],\n    [NULL, \"Null\"]\n]);\n\n/** @type {Map<string,TokenType>} */\nconst knownJSON5TokenTypes = new Map([\n    ...knownTokenTypes,\n    [NAN$1, \"Number\"],\n    [INFINITY$1, \"Number\"]\n]);\n\n// JSON5\nconst json5LineTerminators = new Set([\n    CHAR_NEWLINE,\n    CHAR_RETURN,\n    CHAR_LINE_SEPARATOR,\n    CHAR_PARAGRAPH_SEPARATOR\n]);\n\n/**\n * @fileoverview JSON tokenization/parsing errors\n * <AUTHOR> C. Zakas\n */\n\n//-----------------------------------------------------------------------------\n// Typedefs\n//-----------------------------------------------------------------------------\n\n/** @typedef {import(\"./typedefs.ts\").Location} Location */\n/** @typedef {import(\"./typedefs.ts\").Token} Token */\n\n//-----------------------------------------------------------------------------\n// Errors\n//-----------------------------------------------------------------------------\n\n/**\n * Base class that attaches location to an error.\n */\nclass ErrorWithLocation extends Error {\n\n    /**\n     * Creates a new instance.\n     * @param {string} message The error message to report. \n     * @param {Location} loc The location information for the error.\n     */\n    constructor(message, { line, column, offset }) {\n        super(`${ message } (${ line }:${ column})`);\n\n        /**\n         * The line on which the error occurred.\n         * @type {number}\n         */\n        this.line = line;\n\n        /**\n         * The column on which the error occurred.\n         * @type {number}\n         */\n        this.column = column;\n        \n        /**\n         * The index into the string where the error occurred.\n         * @type {number}\n         */\n        this.offset = offset;\n    }\n\n}\n\n/**\n * Error thrown when an unexpected character is found during tokenizing.\n */\nclass UnexpectedChar extends ErrorWithLocation {\n\n    /**\n     * Creates a new instance.\n     * @param {number} unexpected The character that was found.\n     * @param {Location} loc The location information for the found character.\n     */\n    constructor(unexpected, loc) {\n        super(`Unexpected character '${ String.fromCharCode(unexpected) }' found.`, loc);\n    }\n}\n\n/**\n * Error thrown when an unexpected token is found during parsing.\n */\nclass UnexpectedToken extends ErrorWithLocation {\n\n    /**\n     * Creates a new instance.\n     * @param {Token} token The token that was found. \n     */\n    constructor(token) {\n        super(`Unexpected token ${ token.type } found.`, token.loc.start);\n    }\n}\n\n/**\n * Error thrown when the end of input is found where it isn't expected.\n */\nclass UnexpectedEOF extends ErrorWithLocation {\n\n    /**\n     * Creates a new instance.\n     * @param {Location} loc The location information for the found character.\n     */\n    constructor(loc) {\n        super(\"Unexpected end of input found.\", loc);\n    }\n}\n\n/*\n * The following is extracted from https://github.com/json5/json5/\n *\n * MIT License\n * \n * Copyright (c) 2012-2018 Aseem Kishore, and others.\n * \n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n * \n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n * \n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nconst ID_Start = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC03-\\uDC37\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDF00-\\uDF19]|\\uD806[\\uDCA0-\\uDCDF\\uDCFF\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE83\\uDE86-\\uDE89\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]/;\n// eslint-disable-next-line no-misleading-character-class\nconst ID_Continue = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u08D4-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u09FC\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9-\\u0AFF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D00-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C88\\u1CD0-\\u1CD2\\u1CD4-\\u1CF9\\u1D00-\\u1DF9\\u1DFB-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDDFD\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDEE0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF7A\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCA0-\\uDCA9\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE38-\\uDE3A\\uDE3F\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE6\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC00-\\uDC46\\uDC66-\\uDC6F\\uDC7F-\\uDCBA\\uDCD0-\\uDCE8\\uDCF0-\\uDCF9\\uDD00-\\uDD34\\uDD36-\\uDD3F\\uDD50-\\uDD73\\uDD76\\uDD80-\\uDDC4\\uDDCA-\\uDDCC\\uDDD0-\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE37\\uDE3E\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEEA\\uDEF0-\\uDEF9\\uDF00-\\uDF03\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3C-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF50\\uDF57\\uDF5D-\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74]|\\uD805[\\uDC00-\\uDC4A\\uDC50-\\uDC59\\uDC80-\\uDCC5\\uDCC7\\uDCD0-\\uDCD9\\uDD80-\\uDDB5\\uDDB8-\\uDDC0\\uDDD8-\\uDDDD\\uDE00-\\uDE40\\uDE44\\uDE50-\\uDE59\\uDE80-\\uDEB7\\uDEC0-\\uDEC9\\uDF00-\\uDF19\\uDF1D-\\uDF2B\\uDF30-\\uDF39]|\\uD806[\\uDCA0-\\uDCE9\\uDCFF\\uDE00-\\uDE3E\\uDE47\\uDE50-\\uDE83\\uDE86-\\uDE99\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC36\\uDC38-\\uDC40\\uDC50-\\uDC59\\uDC72-\\uDC8F\\uDC92-\\uDCA7\\uDCA9-\\uDCB6\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD36\\uDD3A\\uDD3C\\uDD3D\\uDD3F-\\uDD47\\uDD50-\\uDD59]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE60-\\uDE69\\uDED0-\\uDEED\\uDEF0-\\uDEF4\\uDF00-\\uDF36\\uDF40-\\uDF43\\uDF50-\\uDF59\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50-\\uDF7E\\uDF8F-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99\\uDC9D\\uDC9E]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB\\uDFCE-\\uDFFF]|\\uD836[\\uDE00-\\uDE36\\uDE3B-\\uDE6C\\uDE75\\uDE84\\uDE9B-\\uDE9F\\uDEA1-\\uDEAF]|\\uD838[\\uDC00-\\uDC06\\uDC08-\\uDC18\\uDC1B-\\uDC21\\uDC23\\uDC24\\uDC26-\\uDC2A]|\\uD83A[\\uDC00-\\uDCC4\\uDCD0-\\uDCD6\\uDD00-\\uDD4A\\uDD50-\\uDD59]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uDB40[\\uDD00-\\uDDEF]/;\n\n/**\n * @fileoverview A charactor code reader.\n * <AUTHOR> C. Zakas\n */\n\n//-----------------------------------------------------------------------------\n// Type Definitions\n//-----------------------------------------------------------------------------\n\n\n//-----------------------------------------------------------------------------\n// Data\n//-----------------------------------------------------------------------------\n\nconst CHAR_CR = 13; // \\r\nconst CHAR_LF = 10; // \\n\n\n//-----------------------------------------------------------------------------\n// CharCodeReader\n//-----------------------------------------------------------------------------\n\n/**\n * A reader that reads character codes from a string.\n */\nclass CharCodeReader {\n\n    /**\n     * The text to read from.\n     * @type {string}\n     */\n    #text = \"\";\n\n    /**\n     * The current line number.\n     * @type {number}\n     */\n    #line = 1;\n\n    /**\n     * The current column number.\n     * @type {number}\n     */\n    #column = 0;\n\n    /**\n     * The current offset in the text.\n     * @type {number}\n     */\n    #offset = -1;\n\n    /**\n     * Whether the last character read was a new line.\n     * @type {boolean}\n     */\n    #newLine = false;\n\n    /**\n     * The last character code read.\n     * @type {number}\n     */\n    #last = -1;\n\n    /**\n     * Whether the reader has ended.\n     * @type {boolean}\n     */\n    #ended = false;\n\n    /**\n     * Creates a new instance.\n     * @param {string} text The text to read from\n     */\n    constructor(text) {\n        this.#text = text;\n    }\n\n    /**\n     * Ends the reader.\n     * @returns {void}\n     */\n    #end() {\n        if (this.#ended) {\n            return;\n        }\n\n        this.#column++;\n        this.#offset++;\n        this.#last = -1;\n        this.#ended = true;\n    }\n\n    /**\n     * Returns the current position of the reader.\n     * @returns {Location} An object with line, column, and offset properties.\n     */\n    locate() {\n        return {\n            line: this.#line,\n            column: this.#column,\n            offset: this.#offset\n        };\n    }\n\n    /**\n     * Reads the next character code in the text.\n     * @returns {number} The next character code, or -1 if there are no more characters.\n     */\n    next() {\n        if (this.#offset >= this.#text.length - 1) {\n            this.#end();\n            return -1;\n        }\n\n        this.#offset++;\n        const charCode = this.#text.charCodeAt(this.#offset);\n\n        if (this.#newLine) {\n            this.#line++;\n            this.#column = 1;\n            this.#newLine = false;\n        } else {\n            this.#column++;\n        }\n\n        if (charCode === CHAR_CR) {\n            this.#newLine = true;\n\n            // if we already see a \\r, just ignore upcoming \\n\n            if (this.peek() === CHAR_LF) {\n                this.#offset++;\n            }\n        } else if (charCode === CHAR_LF) {\n            this.#newLine = true;\n        }\n\n        this.#last = charCode;\n\n        return charCode;\n    }\n\n    /**\n     * Peeks at the next character code in the text.\n     * @returns {number} The next character code, or -1 if there are no more characters.\n     */\n    peek() {\n        if (this.#offset === this.#text.length - 1) {\n            return -1;\n        }\n\n        return this.#text.charCodeAt(this.#offset + 1);\n    }\n\n    /**\n     * Returns the last character code read.\n     * @returns {number} The last character code read.\n     */\n    current() {\n        return this.#last;\n    }\n\n\n}\n\n/**\n * @fileoverview JSON tokenizer\n * <AUTHOR> C. Zakas\n */\n\n\n//-----------------------------------------------------------------------------\n// Typedefs\n//-----------------------------------------------------------------------------\n\n/** @typedef {import(\"./typedefs.ts\").Range} Range */\n/** @typedef {import(\"./typedefs.ts\").TokenizeOptions} TokenizeOptions */\n\n//-----------------------------------------------------------------------------\n// Helpers\n//-----------------------------------------------------------------------------\n\nconst INFINITY = \"Infinity\";\nconst NAN = \"NaN\";\n\nconst keywordStarts = new Set([CHAR_LOWER_T, CHAR_LOWER_F, CHAR_LOWER_N]);\nconst whitespace = new Set([CHAR_SPACE, CHAR_TAB, CHAR_NEWLINE, CHAR_RETURN]);\nconst json5Whitespace = new Set([\n    ...whitespace,\n    CHAR_VTAB,\n    CHAR_FORM_FEED,\n    CHAR_NBSP,\n    CHAR_LINE_SEPARATOR,\n    CHAR_PARAGRAPH_SEPARATOR,\n    CHAR_BOM,\n    CHAR_NON_BREAKING_SPACE,\n    CHAR_EN_QUAD,\n    CHAR_EM_QUAD,\n    CHAR_EN_SPACE,\n    CHAR_EM_SPACE,\n    CHAR_THREE_PER_EM_SPACE,\n    CHAR_FOUR_PER_EM_SPACE,\n    CHAR_SIX_PER_EM_SPACE,\n    CHAR_FIGURE_SPACE,\n    CHAR_PUNCTUATION_SPACE,\n    CHAR_THIN_SPACE,\n    CHAR_HAIR_SPACE,\n    CHAR_NARROW_NO_BREAK_SPACE,\n    CHAR_MEDIUM_MATHEMATICAL_SPACE,\n    CHAR_IDEOGRAPHIC_SPACE,\n]);\n\n\nconst DEFAULT_OPTIONS$1 = {\n    mode: \"json\",\n    ranges: false\n};\n\n// #region Helpers\n\n\n/**\n * Determines if a given character is a decimal digit.\n * @param {number} c The character to check.\n * @returns {boolean} `true` if the character is a digit.\n */\nfunction isDigit(c) {\n    return c >= CHAR_0 && c <= CHAR_9;\n}\n\n/**\n * Determines if a given character is a hexadecimal digit.\n * @param {number} c The character to check.\n * @returns {boolean} `true` if the character is a hexadecimal digit.\n */\nfunction isHexDigit(c) {\n    return isDigit(c) ||\n        c >= CHAR_UPPER_A && c <= CHAR_UPPER_F ||\n        c >= CHAR_LOWER_A && c <= CHAR_LOWER_F;\n}\n\n/**\n * Determines if a given character is a positive digit (1-9).\n * @param {number} c The character to check.\n * @returns {boolean} `true` if the character is a positive digit.\n */\nfunction isPositiveDigit(c) {\n    return c >= CHAR_1 && c <= CHAR_9;\n}\n\n/**\n * Determines if a given character is the start of a keyword.\n * @param {number} c The character to check.\n * @returns {boolean} `true` if the character is the start of a keyword.\n */\nfunction isKeywordStart(c) {\n    return keywordStarts.has(c);\n}\n\n/**\n * Determines if a given character is the start of a number.\n * @param {number} c The character to check.\n * @returns {boolean} `true` if the character is the start of a number.\n */\nfunction isNumberStart(c) {\n    return isDigit(c) || c === CHAR_DOT || c === CHAR_MINUS;\n}\n\n/**\n * Determines if a given character is the start of a JSON5 number.\n * @param {number} c The character to check.\n * @returns {boolean} `true` if the character is the start of a JSON5 number.\n */\nfunction isJSON5NumberStart(c) {\n    return isNumberStart(c) || c === CHAR_PLUS;\n}\n\n/**\n * Determines if a given character is the start of a string.\n * @param {number} c The character to check.\n * @param {boolean} json5 `true` if JSON5 mode is enabled.\n * @returns {boolean} `true` if the character is the start of a string.\n */\nfunction isStringStart(c, json5) {\n    return c === CHAR_DOUBLE_QUOTE || (json5 && c === CHAR_SINGLE_QUOTE);\n}\n\n/**\n * Tests that a given character is a valid first character of a\n * JSON5 identifier\n * @param {number} c The character to check.\n * @returns {boolean} `true` if the character is a valid first character. \n */\nfunction isJSON5IdentifierStart(c) {\n\n    // test simple cases first\n\n    if (c === CHAR_DOLLAR || c === CHAR_UNDERSCORE || c === CHAR_BACKSLASH) {\n        return true;\n    }\n\n    if (c >= CHAR_LOWER_A && c <= CHAR_LOWER_Z || c >= CHAR_UPPER_A && c <= CHAR_UPPER_Z) {\n        return true;\n    }\n\n    if (c === 0x200C || c === 0x200D) {\n        return true;\n    }\n    \n    const ct = String.fromCharCode(c);\n    return ID_Start.test(ct);\n}\n\n/**\n * Tests that a given character is a valid part of a JSON5 identifier.\n * @param {number} c The character to check.\n * @returns {boolean} `true` if the character is a valid part of an identifier.\n */\nfunction isJSON5IdentifierPart(c) {\n\n    // fast path for simple cases\n    if (isJSON5IdentifierStart(c) || isDigit(c)) {\n        return true;\n    }\n\n    const ct = String.fromCharCode(c);\n    return ID_Continue.test(ct);\n}\n\n// #endregion\n\n//-----------------------------------------------------------------------------\n// Main\n//-----------------------------------------------------------------------------\n\n/**\n * Creates an iterator over the tokens representing the source text.\n * @param {string} text The source text to tokenize.\n * @param {TokenizeOptions} options Options for doing the tokenization.\n * @returns {Array<Token>} An iterator over the tokens. \n */\nfunction tokenize(text, options) {\n\n    options = Object.freeze({\n        ...DEFAULT_OPTIONS$1,\n        ...options\n    });\n\n    const json5 = options.mode === \"json5\";\n    const allowComments = options.mode !== \"json\";\n    const reader = new CharCodeReader(text);\n\n    const tokens = [];\n\n    // convenience functions to abstract JSON5-specific logic\n    const isEscapedCharacter = json5 ? json5EscapeToChar.has.bind(json5EscapeToChar) : escapeToChar.has.bind(escapeToChar);\n    const isJSON5LineTerminator = json5 ? json5LineTerminators.has.bind(json5LineTerminators) : () => false;\n    const isJSON5HexEscape = json5 ? c => c === CHAR_LOWER_X : () => false;\n    const isWhitespace = json5 ? json5Whitespace.has.bind(json5Whitespace) : whitespace.has.bind(whitespace);\n\n    /**\n     * Creates a new token.\n     * @param {TokenType} tokenType The type of token to create. \n     * @param {number} length The length of the token. \n     * @param {Location} startLoc The start location for the token.\n     * @param {Location} [endLoc] The end location for the token.\n     * @returns {Token} The token.\n     */\n    function createToken(tokenType, length, startLoc, endLoc) {\n        \n        const endOffset = startLoc.offset + length;\n\n        let range = options.ranges ? {\n            range: /** @type {Range} */ ([startLoc.offset, endOffset])\n        } : undefined;\n\n        return {\n            type: tokenType,\n            loc: {\n                start: startLoc,\n                end: endLoc || {\n                    line: startLoc.line,\n                    column: startLoc.column + length,\n                    offset: endOffset\n                }\n            },\n            ...range\n        };\n    }\n\n    /**\n     * Reads in a character sequence.\n     * @param {string} text The character sequence to read.\n     * @returns {boolean} `true` if the character sequence was read.\n     */\n    function readCharSequence(text) {\n       \n        for (let i = 0; i < text.length; i++) {\n            if (reader.peek() !== text.charCodeAt(i)) {\n                return false;\n            }\n            reader.next();\n        }\n        \n        return true;\n    }\n\n    /**\n     * Reads in a keyword.\n     * @param {number} c The first character of the keyword.\n     * @returns {{value:string, c:number}} The keyword and the next character.\n     * @throws {UnexpectedChar} when the keyword cannot be read.\n     */\n    function readKeyword(c) {\n\n        // get the expected keyword\n        let sequence = expectedKeywords.get(c);\n        let value = String.fromCharCode(c);\n\n        // find the first unexpected character\n        for (let j = 0; j < sequence.length; j++) {\n            const nc = reader.next();\n            if (sequence[j] !== nc) {\n                unexpected(nc);\n            }\n            value += String.fromCharCode(nc);\n        }\n\n        return {\n            value,\n            c: reader.next()\n        };\n    }\n\n    /**\n     * Reads in a JSON5 identifier.\n     * @param {number} c The first character of the identifier.\n     * @returns {{value:string, c:number}} The identifier and the next character.\n     * @throws {UnexpectedChar} when the identifier cannot be read.\n     * @throws {UnexpectedEOF} when EOF is reached before the identifier is finalized.\n     */\n    function readJSON5Identifier(c) {\n            \n        let value = \"\";\n\n        do {\n\n            value += String.fromCharCode(c);\n\n            if (c === CHAR_BACKSLASH) {\n\n                c = reader.next();\n\n                if (c !== CHAR_LOWER_U) {\n                    unexpected(c);\n                }\n\n                value += String.fromCharCode(c);\n\n                const result = readHexDigits(4);\n                value += result.value;\n                c = result.c;\n            }\n\n            c = reader.next();\n\n        } while (c > -1 && isJSON5IdentifierPart(c));\n\n        return { value, c };\n    }\n\n    /**\n     * Reads in a specific number of hex digits.\n     * @param {number} count The number of hex digits to read.\n     * @returns {{value:string, c:number}} The hex digits read and the last character read.\n     */\n    function readHexDigits(count) {\n        let value = \"\";\n\n        for (let i = 0; i < count; i++) {\n            c = reader.next();\n            if (isHexDigit(c)) {\n                value += String.fromCharCode(c);\n                continue;\n            }\n\n            unexpected(c);\n        }\n\n        return { value, c };\n    }\n\n    /**\n     * Reads in a string.\n     * @param {number} c The first character of the string.\n     * @returns {{length:number, c:number}} The length of the string and the next character.\n     */\n    function readString(c) {\n        const delimiter = c;\n        let length = 1;\n        c = reader.next();\n\n        while (c !== -1 && c !== delimiter) {\n\n            // escapes\n            if (c === CHAR_BACKSLASH) {\n                length++;\n                c = reader.next();\n\n                if (isEscapedCharacter(c) || isJSON5LineTerminator(c)) {\n                    length++;\n                } else if (c === CHAR_LOWER_U) {\n                    length++;\n\n                    const result = readHexDigits(4);\n                    length += result.value.length;\n                    c = result.c;\n\n                } else if (isJSON5HexEscape(c)) {\n                    // hex escapes: \\xHH\n                    length++;\n                    \n                    const result = readHexDigits(2);\n                    length += result.value.length;\n                    c = result.c;\n                } else if (!json5) {  // JSON doesn't allow anything else\n                    unexpected(c);\n                }\n            } else {\n                length++;\n            }\n\n            c = reader.next();\n        }\n\n        if (c === -1) {\n            unexpectedEOF();\n        }\n        \n        length++;\n\n        return { length, c: reader.next() };\n    }\n\n    /**\n     * Reads in a number.\n     * @param {number} c The first character of the number.\n     * @returns {{length:number, c:number}} The length of the number and the next character.\n     * @throws {UnexpectedChar} when the number cannot be read.\n     * @throws {UnexpectedEOF} when EOF is reached before the number is finalized.\n     */ \n    function readNumber(c) {\n\n        let length = 0;\n\n        // JSON number may start with a minus but not a plus\n        // JSON5 allows a plus.\n        if (c === CHAR_MINUS || json5 && c === CHAR_PLUS) {\n            length++;\n\n            c = reader.next();\n\n            /*\n             * JSON5 allows Infinity or NaN preceded by a sign.\n             * This blocks handles +Infinity, -Infinity, +NaN, and -NaN.\n             * Standalone Infinity and NaN are handled in `readJSON5Identifier()`\n             */\n            if (json5) {\n\n                if (c === CHAR_UPPER_I && readCharSequence(\"nfinity\")) {\n                    return { length: INFINITY.length + 1, c: reader.next() };\n                }\n\n                if (c === CHAR_UPPER_N && readCharSequence(\"aN\")) {\n                    return { length: NAN.length + 1, c: reader.next() };\n                }\n            }\n\n            // Next digit cannot be zero\n            if (!isDigit(c)) {\n                unexpected(c);\n            }\n\n        }\n\n        /*\n         * In JSON, a zero must be followed by a decimal point or nothing.\n         * In JSON5, a zero can additionally be followed by an `x` indicating\n         * that it's a hexadecimal number.\n         */\n        if (c === CHAR_0) {\n\n            length++;\n\n            c = reader.next();\n\n            // check for a hex number\n            if (json5 && (c === CHAR_LOWER_X || c === CHAR_UPPER_X)) {\n                length++;\n                c = reader.next();\n\n                if (!isHexDigit(c)) {\n                    unexpected(c);\n                }\n\n                do {\n                    length++;\n                    c = reader.next();\n                } while (isHexDigit(c));\n\n            } else if (isDigit(c)) {\n                unexpected(c);\n            }\n\n        } else {\n\n            // JSON5 allows leading decimal points\n            if (!json5 || c !== CHAR_DOT) {\n                if (!isPositiveDigit(c)) {\n                    unexpected(c);\n                }\n\n                do {\n                    length++;\n                    c = reader.next();\n                } while (isDigit(c));\n            }\n        }\n\n        /*\n         * In JSON, a decimal point must be followed by at least one digit.\n         * In JSON5, a decimal point need not be followed by any digits.\n         */\n        if (c === CHAR_DOT) {\n\n            let digitCount = -1;\n\n            do {\n                length++;\n                digitCount++;\n                c = reader.next();\n            } while (isDigit(c));\n\n            if (!json5 && digitCount === 0) {\n                if (c) {\n                    unexpected(c);\n                } else {\n                    unexpectedEOF();\n                }\n            }\n        }\n\n        // Exponent is always last\n        if (c === CHAR_LOWER_E || c === CHAR_UPPER_E) {\n\n            length++;\n            c = reader.next();\n\n            if (c === CHAR_PLUS || c === CHAR_MINUS) {\n                length++;\n                c = reader.next();\n            }\n\n            /*\n             * Must always have a digit in this position to avoid:\n             * 5e\n             * 12E+\n             * 42e-\n             */\n            if (c === -1) {\n                unexpectedEOF();\n            }\n\n            if (!isDigit(c)) {\n                unexpected(c);\n            }\n\n            while (isDigit(c)) {\n                length++;\n                c = reader.next();\n            }\n        }\n\n\n        return { length, c };\n    }\n\n    /**\n     * Reads in either a single-line or multi-line comment.\n     * @param {number} c The first character of the comment.\n     * @returns {{length:number,c:number, multiline:boolean}} The comment info.\n     * @throws {UnexpectedChar} when the comment cannot be read.\n     * @throws {UnexpectedEOF} when EOF is reached before the comment is\n     *      finalized.\n     */\n    function readComment(c) {\n\n        let length = 1;\n\n        // next character determines single- or multi-line\n        c = reader.next();\n\n        // single-line comments\n        if (c === CHAR_SLASH) {\n            \n            do {\n                length += 1;\n                c = reader.next();\n            } while (c > -1 && c !== CHAR_RETURN && c !== CHAR_NEWLINE);\n\n            return { length, c, multiline: false };\n        }\n\n        // multi-line comments\n        if (c === CHAR_STAR) {\n\n            while (c > -1) {\n                length += 1;\n                c = reader.next();\n\n                // check for end of comment\n                if (c === CHAR_STAR) {\n                    length += 1;\n                    c = reader.next();\n                    \n                    //end of comment\n                    if (c === CHAR_SLASH) {\n                        length += 1;\n\n                        /*\n                         * The single-line comment functionality cues up the\n                         * next character, so we do the same here to avoid\n                         * splitting logic later.\n                         */\n                        c = reader.next();\n                        return { length, c, multiline: true };\n                    }\n                }\n            }\n\n            unexpectedEOF();\n            \n        }\n\n        // if we've made it here, there's an invalid character\n        unexpected(c);        \n    }\n\n\n    /**\n     * Convenience function for throwing unexpected character errors.\n     * @param {number} c The unexpected character.\n     * @returns {void}\n     * @throws {UnexpectedChar} always.\n     */\n    function unexpected(c) {\n        throw new UnexpectedChar(c, reader.locate());\n    }\n\n    /**\n     * Convenience function for throwing unexpected EOF errors.\n     * @returns {void}\n     * @throws {UnexpectedEOF} always.\n     */\n    function unexpectedEOF() {\n        throw new UnexpectedEOF(reader.locate());\n    }\n\n    let c = reader.next();\n\n    while (c > -1) {\n\n        while (isWhitespace(c)) {\n            c = reader.next();\n        }\n\n        if (c === -1) {\n            break;\n        }\n\n        const start = reader.locate();\n        const ct = String.fromCharCode(c);\n\n        // check for JSON5 syntax only\n        if (json5) {\n\n            if (knownJSON5TokenTypes.has(ct)) {\n                tokens.push(createToken(knownJSON5TokenTypes.get(ct), 1, start));\n                c = reader.next();\n            } else if (isJSON5IdentifierStart(c)) {\n                const result = readJSON5Identifier(c);\n                let value = result.value;\n                c = result.c;\n\n                if (knownJSON5TokenTypes.has(value)) {\n                    tokens.push(createToken(knownJSON5TokenTypes.get(value), value.length, start));\n                } else {\n                    tokens.push(createToken(\"Identifier\", value.length, start));\n                }\n            } else if (isJSON5NumberStart(c)) {\n                const result = readNumber(c);\n                c = result.c;\n                tokens.push(createToken(\"Number\", result.length, start));\n            } else if (isStringStart(c, json5)) {\n                const result = readString(c);\n                c = result.c;\n                tokens.push(createToken(\"String\", result.length, start, reader.locate()));\n            } else if (c === CHAR_SLASH && allowComments) {\n                const result = readComment(c);\n                c = result.c;\n                tokens.push(createToken(!result.multiline ? \"LineComment\" : \"BlockComment\", result.length, start, reader.locate()));\n            } else {\n                unexpected(c);\n            }\n\n        } else {\n\n            const ct = String.fromCharCode(c);\n\n            // check for JSON/JSONC syntax only\n            if (knownTokenTypes.has(ct)) {\n                tokens.push(createToken(knownTokenTypes.get(ct), 1, start));\n                c = reader.next();\n            } else if (isKeywordStart(c)) {\n                const result = readKeyword(c);\n                let value = result.value;\n                c = result.c;\n                tokens.push(createToken(knownTokenTypes.get(value), value.length, start));\n            } else if (isNumberStart(c)) {\n                const result = readNumber(c);\n                c = result.c;\n                tokens.push(createToken(\"Number\", result.length, start));\n            }\n            else if (isStringStart(c, json5)) {\n                const result = readString(c);\n                c = result.c;\n                tokens.push(createToken(\"String\", result.length, start));\n            } else if (c === CHAR_SLASH && allowComments) {\n                const result = readComment(c);\n                c = result.c;\n                tokens.push(createToken(!result.multiline ? \"LineComment\" : \"BlockComment\", result.length, start, reader.locate()));\n            } else {\n                unexpected(c);\n            }\n        }\n\n        // check for common cases\n\n    }\n\n    return tokens;\n\n}\n\n/**\n * @fileoverview  JSON AST types\n * <AUTHOR> C. Zakas\n */\n\n//-----------------------------------------------------------------------------\n// Typedefs\n//-----------------------------------------------------------------------------\n\n/** @typedef {import(\"./typedefs.ts\").NodeParts} NodeParts */\n/** @typedef {import(\"./typedefs.ts\").DocumentNode} DocumentNode */\n/** @typedef {import(\"./typedefs.ts\").StringNode} StringNode */\n/** @typedef {import(\"./typedefs.ts\").NumberNode} NumberNode */\n/** @typedef {import(\"./typedefs.ts\").BooleanNode} BooleanNode */\n/** @typedef {import(\"./typedefs.ts\").MemberNode} MemberNode */\n/** @typedef {import(\"./typedefs.ts\").ObjectNode} ObjectNode */\n/** @typedef {import(\"./typedefs.ts\").ElementNode} ElementNode */\n/** @typedef {import(\"./typedefs.ts\").ArrayNode} ArrayNode */\n/** @typedef {import(\"./typedefs.ts\").NullNode} NullNode */\n/** @typedef {import(\"./typedefs.ts\").ValueNode} ValueNode */\n/** @typedef {import(\"./typedefs.ts\").IdentifierNode} IdentifierNode */\n/** @typedef {import(\"./typedefs.ts\").NaNNode} NaNNode */\n/** @typedef {import(\"./typedefs.ts\").InfinityNode} InfinityNode */\n/** @typedef {import(\"./typedefs.ts\").Sign} Sign */\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\nconst types = {\n\n    /**\n     * Creates a document node.\n     * @param {ValueNode} body The body of the document.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {DocumentNode} The document node.\n     */\n    document(body, parts = {}) {\n        return {\n            type: \"Document\",\n            body,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates a string node.\n     * @param {string} value The value for the string.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {StringNode} The string node.\n     */\n    string(value, parts = {}) {\n        return {\n            type: \"String\",\n            value,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates a number node.\n     * @param {number} value The value for the number.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {NumberNode} The number node.\n     */\n    number(value, parts = {}) {\n        return {\n            type: \"Number\",\n            value,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates a boolean node.\n     * @param {boolean} value The value for the boolean.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {BooleanNode} The boolean node.\n     */\n    boolean(value, parts = {}) {\n        return {\n            type: \"Boolean\",\n            value,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates a null node.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {NullNode} The null node.\n     */\n    null(parts = {}) {\n        return {\n            type: \"Null\",\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates an array node.\n     * @param {Array<ElementNode>} elements The elements to add.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {ArrayNode} The array node.\n     */\n    array(elements, parts = {}) {\n        return {\n            type: \"Array\",\n            elements,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates an element node.\n     * @param {ValueNode} value The value for the element.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {ElementNode} The element node.\n     */\n    element(value, parts = {}) {\n        return {\n            type: \"Element\",\n            value,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates an object node.\n     * @param {Array<MemberNode>} members The members to add.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {ObjectNode} The object node.\n     */\n    object(members, parts = {}) {\n        return {\n            type: \"Object\",\n            members,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates a member node.\n     * @param {StringNode|IdentifierNode} name The name for the member.\n     * @param {ValueNode} value The value for the member.\n     * @param {NodeParts} parts Additional properties for the node. \n     * @returns {MemberNode} The member node.\n     */\n    member(name, value, parts = {}) {\n        return {\n            type: \"Member\",\n            name,\n            value,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates an identifier node.\n     * @param {string} name The name for the identifier.\n     * @param {NodeParts} parts Additional properties for the node.\n     * @returns {IdentifierNode} The identifier node.\n     */\n    identifier(name, parts = {}) {\n        return {\n            type: \"Identifier\",\n            name,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates a NaN node.\n     * @param {Sign} sign The sign for the Infinity.\n     * @param {NodeParts} parts Additional properties for the node.\n     * @returns {NaNNode} The NaN node.\n     */ \n    nan(sign = \"\", parts = {}) {\n        return {\n            type: \"NaN\",\n            sign,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n    /**\n     * Creates an Infinity node.\n     * @param {Sign} sign The sign for the Infinity.\n     * @param {NodeParts} parts Additional properties for the node.\n     * @returns {InfinityNode} The Infinity node.\n     */\n    infinity(sign = \"\", parts = {}) {\n        return {\n            type: \"Infinity\",\n            sign,\n            loc: parts.loc,\n            ...parts\n        };\n    },\n\n};\n\n/**\n * @fileoverview JSON parser\n * <AUTHOR> C. Zakas\n */\n\n\n//-----------------------------------------------------------------------------\n// Typedefs\n//-----------------------------------------------------------------------------\n\n/** @typedef {import(\"./typedefs.ts\").Node} Node */\n/** @typedef {import(\"./typedefs.ts\").Mode} Mode */\n/** @typedef {import(\"./typedefs.ts\").ParseOptions} ParseOptions */\n\n//-----------------------------------------------------------------------------\n// Helpers\n//-----------------------------------------------------------------------------\n\n/** @type {ParseOptions} */\nconst DEFAULT_OPTIONS = {\n    mode: \"json\",\n    ranges: false,\n    tokens: false\n};\n\n/**\n * Converts a JSON-encoded string into a JavaScript string, interpreting each\n * escape sequence.\n * @param {string} value The text for the token.\n * @param {Token} token The string token to convert into a JavaScript string.\n * @param {boolean} json5 `true` if parsing JSON5, `false` otherwise.\n * @returns {string} A JavaScript string.\n */\nfunction getStringValue(value, token, json5 = false) {\n    \n    let result = \"\";\n    let escapeIndex = value.indexOf(\"\\\\\");\n    let lastIndex = 0;\n\n    // While there are escapes, interpret them to build up the result\n    while (escapeIndex >= 0) {\n\n        // append the text that happened before the escape\n        result += value.slice(lastIndex, escapeIndex);\n\n        // get the character immediately after the \\\n        const escapeChar = value.charAt(escapeIndex + 1);\n        const escapeCharCode = escapeChar.charCodeAt(0);\n        \n        // check for the non-Unicode escape sequences first\n        if (json5 && json5EscapeToChar.has(escapeCharCode)) {\n            result += json5EscapeToChar.get(escapeCharCode);\n            lastIndex = escapeIndex + 2;\n        } else if (escapeToChar.has(escapeCharCode)) {\n            result += escapeToChar.get(escapeCharCode);\n            lastIndex = escapeIndex + 2;\n        } else if (escapeChar === \"u\") {\n            const hexCode = value.slice(escapeIndex + 2, escapeIndex + 6);\n            if (hexCode.length < 4 || /[^0-9a-f]/i.test(hexCode)) {\n                throw new ErrorWithLocation(\n                    `Invalid unicode escape \\\\u${ hexCode}.`,\n                    {\n                        line: token.loc.start.line,\n                        column: token.loc.start.column + escapeIndex,\n                        offset: token.loc.start.offset + escapeIndex\n                    }\n                );\n            }\n\n            result += String.fromCharCode(parseInt(hexCode, 16));\n            lastIndex = escapeIndex + 6;\n        } else if (json5 && escapeChar === \"x\") {\n            const hexCode = value.slice(escapeIndex + 2, escapeIndex + 4);\n            if (hexCode.length < 2 || /[^0-9a-f]/i.test(hexCode)) {\n                throw new ErrorWithLocation(\n                    `Invalid hex escape \\\\x${ hexCode}.`,\n                    {\n                        line: token.loc.start.line,\n                        column: token.loc.start.column + escapeIndex,\n                        offset: token.loc.start.offset + escapeIndex\n                    }\n                );\n            }\n\n            result += String.fromCharCode(parseInt(hexCode, 16));\n            lastIndex = escapeIndex + 4;\n        } else if (json5 && json5LineTerminators.has(escapeCharCode)) {\n            lastIndex = escapeIndex + 2;\n\n            // we also need to skip \\n after a \\r\n            if (escapeChar === \"\\r\" && value.charAt(lastIndex) === \"\\n\") {\n                lastIndex++;\n            }\n            \n        } else {\n            // all characters can be escaped in JSON5\n            if (json5) {\n                result += escapeChar;\n                lastIndex = escapeIndex + 2;\n            } else {\n                throw new ErrorWithLocation(\n                    `Invalid escape \\\\${ escapeChar }.`,\n                    {\n                        line: token.loc.start.line,\n                        column: token.loc.start.column + escapeIndex,\n                        offset: token.loc.start.offset + escapeIndex\n                    }\n                );\n            }\n        }\n\n        // find the next escape sequence\n        escapeIndex = value.indexOf(\"\\\\\", lastIndex);\n    }\n\n    // get the last segment of the string value\n    result += value.slice(lastIndex);\n\n    return result;\n}\n\n/**\n * Gets the JavaScript value represented by a JSON token.\n * @param {string} value The text value of the token.\n * @param {Token} token The JSON token to get a value for.\n * @param {boolean} json5 `true` if parsing JSON5, `false` otherwise.\n * @returns {string|boolean|number} A number, string, or boolean.\n * @throws {TypeError} If an unknown token type is found. \n */\nfunction getLiteralValue(value, token, json5 = false) {\n    switch (token.type) {\n        case \"Boolean\":\n            return value === \"true\";\n            \n        case \"Number\":\n            return Number(value);\n\n        case \"String\":\n            return getStringValue(value.slice(1, -1), token, json5);\n\n        default:\n            throw new TypeError(`Unknown token type \"${token.type}.`);\n    }\n}\n\n//-----------------------------------------------------------------------------\n// Main Function\n//-----------------------------------------------------------------------------\n\n/**\n * \n * @param {string} text The text to parse.\n * @param {ParseOptions} [options] The options object.\n * @returns {DocumentNode} The AST representing the parsed JSON.\n * @throws {Error} When there is a parsing error. \n */\nfunction parse(text, options) {\n\n    options = Object.freeze({\n        ...DEFAULT_OPTIONS,\n        ...options\n    });\n\n    const tokens = tokenize(text, {\n        mode: options.mode,\n        ranges: options.ranges\n    });\n\n    let tokenIndex = 0;\n    const json5 = options.mode === \"json5\";\n\n    /**\n     * Returns the next token knowing there are no comments.\n     * @returns {Token|undefined} The next or undefined if no next token.\n     */\n    function nextNoComments() {\n        return tokens[tokenIndex++];\n    }\n    \n    /**\n     * Returns the next token knowing there are comments to skip.\n     * @returns {Token|undefined} The next or undefined if no next token.\n     */\n    function nextSkipComments() {\n        const nextToken = tokens[tokenIndex++];\n        if (nextToken && nextToken.type.endsWith(\"Comment\")) {\n            return nextSkipComments();\n        }\n\n        return nextToken;\n\n    }\n\n    // determine correct way to evaluate tokens based on presence of comments\n    const next = options.mode === \"json\" ? nextNoComments : nextSkipComments;\n\n    /**\n     * Asserts a token has the given type.\n     * @param {Token} token The token to check.\n     * @param {string} type The token type.\n     * @throws {UnexpectedToken} If the token type isn't expected.\n     * @returns {void}\n     */\n    function assertTokenType(token, type) {\n        if (!token || token.type !== type) {\n            throw new UnexpectedToken(token);\n        }\n    }\n\n    /**\n     * Asserts a token has one of the given types.\n     * @param {Token} token The token to check.\n     * @param {string[]} types The token types.\n     * @returns {void}\n     * @throws {UnexpectedToken} If the token type isn't expected.\n     */ \n    function assertTokenTypes(token, types) {\n        if (!token || !types.includes(token.type)) {\n            throw new UnexpectedToken(token);\n        }\n    }\n\n    /**\n     * Creates a range only if ranges are specified.\n     * @param {Location} start The start offset for the range.\n     * @param {Location} end The end offset for the range.\n     * @returns {{range:[number,number]}|undefined} An object with a \n     */\n    function createRange(start, end) {\n        // @ts-ignore tsc incorrect - options might be undefined\n        return options.ranges ? {\n            range: [start.offset, end.offset]\n        } : undefined;\n    }\n\n    /**\n     * Creates a node for a string, boolean, or number.\n     * @param {Token} token The token representing the literal. \n     * @returns {StringNode|NumberNode|BooleanNode} The node representing\n     *      the value.\n     */\n    function createLiteralNode(token) {\n        const range = createRange(token.loc.start, token.loc.end);\n        const value = getLiteralValue(\n            text.slice(token.loc.start.offset, token.loc.end.offset),\n            token,\n            json5\n        );\n        const loc = {\n            start: {\n                ...token.loc.start\n            },\n            end: {\n                ...token.loc.end\n            }\n        };\n        const parts = { loc, ...range };\n\n        switch (token.type) {\n            case \"String\":\n                return types.string(/** @type {string} */ (value), parts);\n\n            case \"Number\":\n                return types.number(/** @type {number} */ (value), parts);\n                \n            case \"Boolean\":\n                return types.boolean(/** @type {boolean} */ (value), parts);\n\n            default:\n                throw new TypeError(`Unknown token type ${token.type}.`);\n        }\n    }\n\n    /**\n     * Creates a node for a JSON5 identifier.\n     * @param {Token} token The token representing the identifer. \n     * @returns {NaNNode|InfinityNode|IdentifierNode} The node representing\n     *      the value.\n     */\n    function createJSON5IdentifierNode(token) {\n        const range = createRange(token.loc.start, token.loc.end);\n        const identifier = text.slice(token.loc.start.offset, token.loc.end.offset);\n        const loc = {\n            start: {\n                ...token.loc.start\n            },\n            end: {\n                ...token.loc.end\n            }\n        };\n        const parts = { loc, ...range };\n\n        // Check for NaN or Infinity\n        if (token.type !== \"Identifier\") {\n\n            let sign = \"\";\n\n            // check if the first character in the token is a plus or minus\n            if (identifier[0] === \"+\" || identifier[0] === \"-\") {\n                sign = identifier[0];\n            }\n\n            // check if the token is NaN or Infinity\n            return types[identifier.includes(\"NaN\") ? \"nan\" : \"infinity\"](/** @type {Sign} */ (sign), parts);\n        }\n\n        return types.identifier(identifier, parts);\n    }\n\n    /**\n     * Creates a node for a null.\n     * @param {Token} token The token representing null. \n     * @returns {NullNode} The node representing null.\n     */\n    function createNullNode(token) {\n        const range = createRange(token.loc.start, token.loc.end);\n\n        return types.null({\n            loc: {\n                start: {\n                    ...token.loc.start\n                },\n                end: {\n                    ...token.loc.end\n                }\n            },\n            ...range\n        });\n    }\n\n\n    function parseProperty(token) {\n\n        if (json5) {\n            assertTokenTypes(token, [\"String\", \"Identifier\", \"Number\"]);\n        } else {\n            assertTokenType(token, \"String\");\n        }\n\n        // TODO: Clean this up a bit\n        let key = token.type === \"String\"\n            ? /** @type {StringNode} */ (createLiteralNode(token))\n            : /** @type {IdentifierNode|NaNNode|InfinityNode} */ (createJSON5IdentifierNode(token));\n\n        // in JSON5, need to check for NaN and Infinity and create identifier nodes\n        if (json5 && (key.type === \"NaN\" || key.type === \"Infinity\")) {\n\n            // NaN and Infinity cannot be signed and be a property key\n            if (key.sign !== \"\") {\n                throw new UnexpectedToken(token);\n            }\n\n            key = types.identifier(key.type, { loc: key.loc, ...createRange(key.loc.start, key.loc.end) });\n        }\n\n        token = next();\n        assertTokenType(token, \"Colon\");\n        const value = parseValue();\n        const range = createRange(key.loc.start, value.loc.end);\n\n        return types.member(/** @type {StringNode|IdentifierNode} */ (key), value, {\n            loc: {\n                start: {\n                    ...key.loc.start\n                },\n                end: {\n                    ...value.loc.end\n                }\n            },\n            ...range\n        });\n    }\n\n    function parseObject(firstToken) {\n\n        // The first token must be a { or else it's an error\n        assertTokenType(firstToken, \"LBrace\");\n\n        const members = [];\n        let token = next();\n\n        if (token && token.type !== \"RBrace\") {\n            do {\n    \n                // add the value into the array\n                members.push(parseProperty(token));\n    \n                token = next();\n\n                if (!token) {\n                    throw new UnexpectedEOF(members[members.length-1].loc.end);\n                }\n    \n                if (token.type === \"Comma\") {\n                    token = next();\n\n                    /*\n                      * JSON5: Trailing commas are allowed in arrays.\n                      * So we need to check if the token is a comma,\n                      * and if so, then we need to check if the next\n                      * token is a RBracket. If it is, then we need to\n                      * break out of the loop.\n                      */\n                    if (json5 && token.type === \"RBrace\") {\n                        break;\n                    }                      \n                } else {\n                    break;\n                }\n            } while (token);\n        }\n\n        assertTokenType(token, \"RBrace\");\n        const range = createRange(firstToken.loc.start, token.loc.end);\n\n        return types.object(members, {\n            loc: {\n                start: {\n                    ...firstToken.loc.start\n                },\n                end: {\n                    ...token.loc.end\n                }\n            },\n            ...range\n        });\n\n    }\n\n    function parseArray(firstToken) {\n\n        // The first token must be a [ or else it's an error\n        assertTokenType(firstToken, \"LBracket\");\n\n        const elements = [];\n        let token = next();\n        \n        if (token && token.type !== \"RBracket\") {\n\n            do {\n\n                // add the value into the array\n                const value = parseValue(token);\n\n                elements.push(types.element(\n                    value,\n                    { loc: value.loc }\n                ));\n\n                token = next();\n              \n                if (token.type === \"Comma\") {\n                    token = next();\n\n                    /*\n                      * JSON5: Trailing commas are allowed in arrays.\n                      * So we need to check if the token is a comma,\n                      * and if so, then we need to check if the next\n                      * token is a RBracket. If it is, then we need to\n                      * break out of the loop.\n                      */\n                    if (json5 && token.type === \"RBracket\") {\n                        break;\n                    }                    \n                } else {\n                    break;\n                }\n            } while (token);\n        }\n\n        assertTokenType(token, \"RBracket\");\n\n        const range = createRange(firstToken.loc.start, token.loc.end);\n\n        return types.array(elements, {\n            loc: {\n                start: {\n                    ...firstToken.loc.start\n                },\n                end: {\n                    ...token.loc.end\n                }\n            },\n            ...range\n        });\n\n    }\n\n    function parseValue(token) {\n\n        token = token || next();\n        \n        switch (token.type) {\n            case \"String\":\n            case \"Boolean\":\n                return createLiteralNode(token);\n\n            case \"Number\":\n                if (json5) {\n                    let tokenText = text.slice(token.loc.start.offset, token.loc.end.offset);\n                    if (tokenText[0] === \"+\" || tokenText[0] === \"-\") {\n                        tokenText = tokenText.slice(1);\n                    }\n\n                    if (tokenText === \"NaN\" || tokenText === \"Infinity\") {\n                        return createJSON5IdentifierNode(token);\n                    }\n                }\n                return createLiteralNode(token);\n\n            case \"Null\":\n                return createNullNode(token);\n\n            case \"LBrace\":\n                return parseObject(token);\n\n            case \"LBracket\":\n                return parseArray(token);\n\n            default:\n                throw new UnexpectedToken(token);\n        }\n\n    }\n\n    \n    const docBody = parseValue();\n    \n    const unexpectedToken = next();\n    if (unexpectedToken) {\n        throw new UnexpectedToken(unexpectedToken);\n    }\n    \n    \n    const docParts = {\n        loc: {\n            start: {\n                line: 1,\n                column: 1,\n                offset: 0\n            },\n            end: {\n                ...docBody.loc.end\n            }\n        }\n    };\n    \n    if (options.tokens) {\n        docParts.tokens = tokens;\n    }\n\n    if (options.ranges) {\n        docParts.range = [\n            docParts.loc.start.offset,\n            docParts.loc.end.offset\n        ];\n    }\n\n    return types.document(docBody, docParts);\n}\n\n/**\n * @fileoverview Traversal approaches for Momoa JSON AST.\n * <AUTHOR> C. Zakas\n */\n\n//-----------------------------------------------------------------------------\n// Typedefs\n//-----------------------------------------------------------------------------\n\n/** @typedef {import(\"./typedefs.ts\").TraversalPhase} TraversalPhase */\n\n//-----------------------------------------------------------------------------\n// Data\n//-----------------------------------------------------------------------------\n\nconst childKeys = new Map([\n    [\"Document\", [\"body\"]],\n    [\"Object\", [\"members\"]],\n    [\"Member\", [\"name\", \"value\"]],\n    [\"Element\", [\"value\"]],\n    [\"Array\", [\"elements\"]],\n    [\"String\", []],\n    [\"Number\", []],\n    [\"Boolean\", []],\n    [\"Null\", []],\n    [\"NaN\", []],\n    [\"Infinity\", []],\n    [\"Identifier\", []],\n]);\n\n//-----------------------------------------------------------------------------\n// Helpers\n//-----------------------------------------------------------------------------\n\n/**\n * Determines if a given value is an object.\n * @param {*} value The value to check.\n * @returns {boolean} True if the value is an object, false if not. \n */\nfunction isObject(value) {\n    return value && (typeof value === \"object\");\n}\n\n/**\n * Determines if a given value is an AST node.\n * @param {*} value The value to check.\n * @returns {boolean} True if the value is a node, false if not. \n */\nfunction isNode(value) {\n    return isObject(value) && (typeof value.type === \"string\");\n}\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\n/**\n * Traverses an AST from the given node.\n * @param {Node} root The node to traverse from \n * @param {Object} visitor An object with an `enter` and `exit` method. \n */\nfunction traverse(root, visitor) {\n\n    /**\n     * Recursively visits a node.\n     * @param {Node} node The node to visit.\n     * @param {Node} [parent] The parent of the node to visit.\n     * @returns {void}\n     */\n    function visitNode(node, parent) {\n\n        if (typeof visitor.enter === \"function\") {\n            visitor.enter(node, parent);\n        }\n\n        for (const key of childKeys.get(node.type)) {\n            const value = node[key];\n\n            if (isObject(value)) {\n                if (Array.isArray(value)) {\n                    value.forEach(child => visitNode(child, node));\n                } else if (isNode(value)) {\n                    visitNode(value, node);\n                }\n            }\n        }\n\n        if (typeof visitor.exit === \"function\") {\n            visitor.exit(node, parent);\n        }\n    }\n\n    visitNode(root);\n}\n\n/**\n * @callback FilterPredicate\n * @param {{node: Node, parent?: Node, phase: TraversalPhase}} item\n * @param {number} index\n * @param {Array<{node: Node, parent?: Node, phase: TraversalPhase}>} array\n * @returns {boolean}\n */\n\n/**\n * Creates an iterator over the given AST.\n * @param {Node} root The root AST node to traverse. \n * @param {FilterPredicate} [filter] A filter function to determine which steps to\n *      return;\n * @returns {IterableIterator<{node: Node, parent?: Node, phase: TraversalPhase}>} An iterator over the AST.  \n */\nfunction iterator(root, filter = () => true) {\n\n    /** @type {Array<{node: Node, parent?: Node, phase: TraversalPhase}>} */\n    const traversal = [];\n\n    traverse(root, {\n        enter(node, parent) {\n            traversal.push({ node, parent, phase: \"enter\" });\n        },\n        exit(node, parent) {\n            traversal.push({ node, parent, phase: \"exit\" });\n        }\n    });\n\n    return traversal.filter(filter).values();\n}\n\n/**\n * @fileoverview Evaluator for Momoa AST.\n * <AUTHOR> C. Zakas\n */\n\n//-----------------------------------------------------------------------------\n// Typedefs\n//-----------------------------------------------------------------------------\n\n/** @typedef {import(\"./typedefs.ts\").AnyNode} AnyNode */\n/** @typedef {import(\"./typedefs.ts\").JSONValue} JSONValue */\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\n/**\n * Evaluates a Momoa AST node into a JavaScript value.\n * @param {AnyNode} node The node to interpet.\n * @returns {JSONValue} The JavaScript value for the node. \n */\nfunction evaluate(node) {\n    switch (node.type) {\n        case \"String\":\n            return node.value;\n\n        case \"Number\":\n            return node.value;\n\n        case \"Boolean\":\n            return node.value;\n\n        case \"Null\":\n            return null;\n\n        case \"NaN\":\n            return NaN;\n        \n        case \"Infinity\":\n            return node.sign === \"-\" ? -Infinity : Infinity;\n\n        case \"Identifier\":\n            return node.name;\n\n        case \"Array\": {\n            // const arrayNode = /** @type {ArrayNode} */ (node);\n            return node.elements.map(element => evaluate(element.value));\n        }\n\n        case \"Object\": {\n\n            /** @type {{[property: string]: JSONValue}} */\n            const object = {};\n\n            node.members.forEach(member => {\n                object[/** @type {string} */ (evaluate(member.name))] = evaluate(member.value);\n            });    \n\n            return object;\n        }    \n\n        case \"Document\": {\n            return evaluate(node.body);\n        }\n\n        case \"Element\":\n            throw new Error(\"Cannot evaluate array element outside of an array.\");\n\n        case \"Member\":\n            throw new Error(\"Cannot evaluate object member outside of an object.\");\n\n        default:\n            // @ts-ignore tsc doesn't know about the type property here?\n            throw new Error(`Unknown node type ${ node.type }.`);\n    }\n}\n\n/**\n * @fileoverview Printer for Momoa AST.\n * <AUTHOR> C. Zakas\n */\n\n\n//-----------------------------------------------------------------------------\n// Typedefs\n//-----------------------------------------------------------------------------\n\n\n//-----------------------------------------------------------------------------\n// Helpers\n//-----------------------------------------------------------------------------\n\n/**\n * Prints the string representation of a Boolean node.\n * @param {BooleanNode} node The node to print.\n * @returns {string} The boolean value.\n */\nfunction printBoolean(node) {\n    return node.value ? \"true\" : \"false\";\n}\n\n/**\n * Prints the string representation of a null node.\n * @returns {string} The string \"null\".\n */\nfunction printNull() {\n    return \"null\";\n}\n\n/**\n * Prints the string representation of a number node.\n * @param {NumberNode} node The node to print.\n * @returns {string} The number value.\n */\nfunction printNumber(node) {\n    return node.value.toString();\n}\n\n/**\n * Prints the string representation of a NaN node.\n * @returns {string} The string \"NaN\".\n */\nfunction printNaN() {\n    return \"NaN\";\n}\n\n/**\n * Prints the string representation of an Infinity node.\n * @param {InfinityNode} node The node to print.\n * @returns {string} The string \"Infinity\" or \"-Infinity\".\n */\nfunction printInfinity(node) {\n    return node.sign + \"Infinity\";\n}\n\n/**\n * Prints the string representation of a string node.\n * @param {StringNode} node The node to print.\n * @returns {string} The string value.\n */\nfunction printString(node) {\n\n    let result = \"\\\"\";\n\n    // escape all characters that need escaping\n    for (const c of node.value) {\n\n        const newChar = json5CharToEscape.get(c);\n\n        if (newChar) {\n            result += \"\\\\\" + newChar;\n            continue;\n        }\n\n        // if it's a double quote, escape it\n        if (c === \"\\\"\") {\n            result += \"\\\\\\\"\";\n            continue;\n        }\n\n        // if it's a control character, escape it\n        if (c < \" \" || c === \"\\u007F\") {\n            const hex = c.codePointAt(0).toString(16).toUpperCase();\n            result += `\\\\u${\"0000\".substring(hex.length)}${hex}`;\n            continue;\n        }\n\n        // otherwise, just add the character\n        result += c;\n\n    }\n    \n    return result + \"\\\"\";\n}\n\n/**\n * Prints the string representation of an identifier node.\n * @param {IdentifierNode} node The node to print.\n * @returns {string} The identifier name.\n */\nfunction printIdentifier(node) {\n    return node.name;\n}\n\n/**\n * Prints the string representation of an array node.\n * @param {ArrayNode} node The node to print.\n * @param {string} indent The string to use for indentation.\n * @param {number} indentLevel The current level of indentation.\n * @returns {string} The array value.\n */\nfunction printArray(node, indent, indentLevel) {\n    const newLine = indent ? \"\\n\" : \"\";\n    const indentString = indent.repeat(indentLevel);\n    const elementIndentString = indent.repeat(indentLevel + 1);\n\n    return `[${newLine}${\n        node.elements.map(element =>\n            `${elementIndentString}${printValue(element.value, indent, indentLevel + 1)}`\n        ).join(`,${newLine}`)\n    }${newLine}${indentString}]`;\n}\n\n/**\n * Prints the string representation of a member node.\n * @param {MemberNode} node The node to print.\n * @param {string} indent The string to use for indentation.\n * @param {number} indentLevel The current level of indentation.\n * @returns {string} The member value.\n */\nfunction printMember(node, indent, indentLevel) {\n    const space = indent ? \" \" : \"\";\n    return `${printValue(node.name, indent, indentLevel)}:${space}${printValue(node.value, indent, indentLevel + 1)}`;\n}\n\n/**\n * Prints the string representation of an object node.\n * @param {ObjectNode} node The node to print.\n * @param {string} indent The string to use for indentation.\n * @param {number} indentLevel The current level of indentation.\n * @returns {string} The object value.\n */\nfunction printObject(node, indent, indentLevel) {\n    const newLine = indent ? \"\\n\" : \"\";\n    const indentString = indent.repeat(indentLevel);\n    const memberIndentString = indent.repeat(indentLevel + 1);\n\n    return `{${newLine}${\n        node.members.map(member => \n            `${memberIndentString}${printMember(member, indent, indentLevel)}`\n        ).join(`,${newLine}`)\n    }${newLine}${indentString}}`;\n}\n\n/**\n * Prints the string representation of a node.\n * @param {AnyNode} node The node to print.\n * @param {string} indentString The string to use for indentation.\n * @param {number} indentLevel The current level of indentation.\n * @returns {string} The string representation of the node.\n * @throws {TypeError} If the node type is unknown.\n\n */\nfunction printValue(node, indentString, indentLevel) {\n    switch (node.type) {\n        case \"String\":\n            return printString(node);\n        case \"Number\":\n            return printNumber(node);\n        case \"Boolean\":\n            return printBoolean(node);\n        case \"Null\":\n            return printNull();\n        case \"NaN\":\n            return printNaN();\n        case \"Infinity\":\n            return printInfinity(node);\n        case \"Identifier\":\n            return printIdentifier(node);\n        case \"Array\":\n            return printArray(node, indentString, indentLevel);\n        case \"Object\":\n            return printObject(node, indentString, indentLevel);\n        case \"Document\":\n            return printValue(node.body, indentString, indentLevel);\n        default:\n            throw new TypeError(`Unknown node type: ${node.type}`);\n    }\n}\n\n\n//-----------------------------------------------------------------------------\n// Exports\n//-----------------------------------------------------------------------------\n\n/**\n * Converts a Momoa AST back into a JSON string.\n * @param {AnyNode} node The node to print.\n * @param {Object} options Options for the print.\n * @param {number} [options.indent=0] The number of spaces to indent each line. If\n *      greater than 0, then newlines and indents will be added to output. \n * @returns {string} The JSON representation of the AST.\n */\nfunction print(node, { indent = 0 } = {}) {\n\n    const indentLevel = 0;\n    const indentString = \" \".repeat(indent);\n\n    return printValue(node, indentString, indentLevel);\n}\n\nexport { evaluate, iterator, parse, print, tokenize, traverse, types, childKeys as visitorKeys };\n", "// Basic\nconst eq = x => y => x === y;\nconst not = fn => x => !fn(x);\n\nconst getValues = o => Object.values(o);\n\nexport const notUndefined = x => x !== undefined;\n\n// Error\nconst isXError = x => error => error.keyword === x;\nexport const isRequiredError = isXError('required');\nexport const isAnyOfError = isXError('anyOf');\nexport const isEnumError = isXError('enum');\nexport const getErrors = node =>\n  node && node.errors\n    ? node.errors.map(e =>\n        e.keyword === 'errorMessage'\n          ? { ...e.params.errors[0], message: e.message }\n          : e\n      )\n    : [];\n\n// Node\nexport const getChildren = node => (node && getValues(node.children)) || [];\n\nexport const getSiblings = parent => node =>\n  getChildren(parent).filter(not(eq(node)));\n\nexport const concatAll = xs => ys => ys.reduce((zs, z) => zs.concat(z), xs);\n", "import { bold, red, magenta } from 'kleur/colors';\nimport BaseValidationError from './base';\n\nconst REQUIRED = bold('REQUIRED');\n\nexport default class RequiredValidationError extends BaseValidationError {\n  getLocation(dataPath = this.instancePath) {\n    const { start } = super.getLocation(dataPath);\n    return { start };\n  }\n\n  print() {\n    const { message, params } = this.options;\n    const line = red(`${REQUIRED} ${message}`);\n    const output = [`${line}\\n`];\n\n    return output.concat(\n      this.getCodeFrame(`${magenta(params.missingProperty)} is missing here!`)\n    );\n  }\n\n  getError() {\n    const { message } = this.options;\n\n    return {\n      ...this.getLocation(),\n      error: `${this.getDecoratedPath()} ${message}`,\n      path: this.instancePath,\n    };\n  }\n}\n", "/**\n * @typedef {Object} Location\n * @property {number} line\n * @property {number} column\n */\n\n/**\n * @typedef {Object} NodeLocation\n * @property {Location} [end]\n * @property {Location} start\n */\n\n/**\n * @typedef {Object} Options\n * @property {string} [message]\n */\n\n/**\n * @typedef {Record<number, true | [number, number] | undefined>} MarkerLines\n */\n\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * @param {NodeLocation} loc\n * @param {string[]} source\n * @returns {{start: number, end: number, markerLines: MarkerLines}}\n */\nfunction getMarkerLines(loc, source) {\n  /** @type {Location} */\n  const startLoc = {\n    ...loc.start,\n  };\n  /** @type {Location} */\n  const endLoc = {\n    ...startLoc,\n    ...loc.end,\n  };\n  const linesAbove = 2;\n  const linesBelow = 3;\n  const startLine = startLoc.line;\n  const startColumn = startLoc.column;\n  const endLine = endLoc.line;\n  const endColumn = endLoc.column;\n\n  const start = Math.max(startLine - (linesAbove + 1), 0);\n  const end = Math.min(source.length, endLine + linesBelow);\n  const lineDiff = endLine - startLine;\n\n  /** @type {MarkerLines} */\n  const markerLines = {};\n\n  if (lineDiff) {\n    for (let i = 0; i <= lineDiff; i++) {\n      const lineNumber = i + startLine;\n\n      if (!startColumn) {\n        markerLines[lineNumber] = true;\n      } else if (i === 0) {\n        const sourceLength = source[lineNumber - 1].length;\n\n        markerLines[lineNumber] = [startColumn, sourceLength - startColumn + 1];\n      } else if (i === lineDiff) {\n        markerLines[lineNumber] = [0, endColumn];\n      } else {\n        const sourceLength = source[lineNumber - i].length;\n\n        markerLines[lineNumber] = [0, sourceLength];\n      }\n    }\n  } else {\n    if (startColumn === endColumn) {\n      if (startColumn) {\n        markerLines[startLine] = [startColumn, 0];\n      } else {\n        markerLines[startLine] = true;\n      }\n    } else {\n      markerLines[startLine] = [startColumn, endColumn - startColumn];\n    }\n  }\n\n  return { start, end, markerLines };\n}\n\n/**\n * @param {string} rawLines\n * @param {NodeLocation} loc\n * @param {Options} [opts]\n * @returns {string}\n */\nexport function codeFrameColumns(rawLines, loc, opts = {}) {\n  const lines = rawLines.split(NEWLINE);\n  const { start, end, markerLines } = getMarkerLines(loc, lines);\n  const numberMaxWidth = String(end).length;\n\n  return rawLines\n    .split(NEWLINE, end)\n    .slice(start, end)\n    .map((line, index) => {\n      const number = start + 1 + index;\n      const paddedNumber = ` ${String(number)}`.slice(-numberMaxWidth);\n      const gutter = ` ${paddedNumber} |`;\n      const hasMarker = markerLines[number];\n      const lastMarkerLine = !markerLines[number + 1];\n      if (hasMarker) {\n        let markerLine = '';\n        if (Array.isArray(hasMarker)) {\n          const markerSpacing = line\n            .slice(0, Math.max(hasMarker[0] - 1, 0))\n            .replace(/[^\\t]/g, ' ');\n          const numberOfMarkers = hasMarker[1] || 1;\n\n          markerLine = [\n            '\\n ',\n            gutter.replace(/\\d/g, ' '),\n            ' ',\n            markerSpacing,\n            '^'.repeat(numberOfMarkers),\n          ].join('');\n\n          if (lastMarkerLine && opts.message) {\n            markerLine += ' ' + opts.message;\n          }\n        }\n        return [\n          '>',\n          gutter,\n          line.length > 0 ? ` ${line}` : '',\n          markerLine,\n        ].join('');\n      } else {\n        return [' ', gutter, line.length > 0 ? ` ${line}` : ''].join('');\n      }\n    })\n    .join('\\n');\n}\n", "// TODO: Better error handling\nexport const getPointers = dataPath => {\n  return dataPath\n    .split('/')\n    .slice(1)\n    .map(pointer => pointer.split('~1').join('/').split('~0').join('~'));\n};\n", "import { getPointers } from './utils';\n\nexport default function getMetaFromPath(\n  jsonAst,\n  dataPath,\n  includeIdentifierLocation\n) {\n  const pointers = getPointers(dataPath);\n  const lastPointerIndex = pointers.length - 1;\n  return pointers.reduce((obj, pointer, idx) => {\n    switch (obj.type) {\n      case 'Object': {\n        const filtered = obj.members.filter(\n          child => child.name.value === pointer\n        );\n        if (filtered.length !== 1) {\n          throw new Error(`Couldn't find property ${pointer} of ${dataPath}`);\n        }\n\n        const { name, value } = filtered[0];\n        return includeIdentifierLocation && idx === lastPointerIndex\n          ? name\n          : value;\n      }\n      case 'Array':\n        return obj.elements[pointer].value;\n      default:\n        // eslint-disable-next-line no-console\n        console.log(obj);\n    }\n  }, jsonAst.body);\n}\n", "import { getPointers } from './utils';\n\nexport default function getDecoratedDataPath(jsonAst, dataPath) {\n  let decoratedPath = '';\n  getPointers(dataPath).reduce((obj, pointer) => {\n    switch (obj.type) {\n      case 'Element':\n        obj = obj.value;\n      /* eslint-disable-next-line no-fallthrough -- explicitly want fallthrough here */\n      case 'Object': {\n        decoratedPath += `/${pointer}`;\n        const filtered = obj.members.filter(\n          child => child.name.value === pointer\n        );\n        if (filtered.length !== 1) {\n          throw new Error(`Couldn't find property ${pointer} of ${dataPath}`);\n        }\n        return filtered[0].value;\n      }\n      case 'Array': {\n        decoratedPath += `/${pointer}${getTypeName(obj.elements[pointer])}`;\n        return obj.elements[pointer];\n      }\n      default:\n        // eslint-disable-next-line no-console\n        console.log(obj);\n    }\n  }, jsonAst.body);\n  return decoratedPath;\n}\n\nfunction getTypeName(obj) {\n  if (!obj || !obj.elements) {\n    return '';\n  }\n  const type = obj.elements.filter(\n    child => child && child.name && child.name.value === 'type'\n  );\n\n  if (!type.length) {\n    return '';\n  }\n\n  return (type[0].value && `:${type[0].value.value}`) || '';\n}\n", "import { codeFrameColumns } from '../code-frame-columns';\nimport { getMetaFromPath, getDecoratedDataPath } from '../json';\n\nexport default class BaseValidationError {\n  constructor(\n    options = { isIdentifierLocation: false },\n    { data, schema, jsonAst, jsonRaw }\n  ) {\n    this.options = options;\n    this.data = data;\n    this.schema = schema;\n    this.jsonAst = jsonAst;\n    this.jsonRaw = jsonRaw;\n  }\n\n  getLocation(dataPath = this.instancePath) {\n    const { isIdentifierLocation, isSkipEndLocation } = this.options;\n    const { loc } = getMetaFromPath(\n      this.jsonAst,\n      dataPath,\n      isIdentifierLocation\n    );\n    return {\n      start: loc.start,\n      end: isSkipEndLocation ? undefined : loc.end,\n    };\n  }\n\n  getDecoratedPath(dataPath = this.instancePath) {\n    const decoratedPath = getDecoratedDataPath(this.jsonAst, dataPath);\n    return decoratedPath;\n  }\n\n  getCodeFrame(message, dataPath = this.instancePath) {\n    return codeFrameColumns(this.jsonRaw, this.getLocation(dataPath), {\n      message,\n    });\n  }\n\n  /**\n   * @return {string}\n   */\n  get instancePath() {\n    return typeof this.options.instancePath !== 'undefined'\n      ? this.options.instancePath\n      : this.options.dataPath;\n  }\n\n  print() {\n    throw new Error(\n      `Implement the 'print' method inside ${this.constructor.name}!`\n    );\n  }\n\n  getError() {\n    throw new Error(\n      `Implement the 'getError' method inside ${this.constructor.name}!`\n    );\n  }\n}\n", "import { bold, red, magenta } from 'kleur/colors';\nimport BaseValidationError from './base';\n\nconst ADDITIONAL_PROPERTY = bold('ADDITIONAL PROPERTY');\n\nexport default class AdditionalPropValidationError extends BaseValidationError {\n  constructor(...args) {\n    super(...args);\n    this.options.isIdentifierLocation = true;\n  }\n\n  print() {\n    const { message, params } = this.options;\n    const line = red(`${ADDITIONAL_PROPERTY} ${message}`);\n    const output = [`${line}\\n`];\n\n    return output.concat(\n      this.getCodeFrame(\n        `${magenta(params.additionalProperty)} is not expected to be here!`,\n        `${this.instancePath}/${params.additionalProperty}`\n      )\n    );\n  }\n\n  getError() {\n    const { params } = this.options;\n\n    return {\n      ...this.getLocation(`${this.instancePath}/${params.additionalProperty}`),\n      error: `${this.getDecoratedPath()} Property ${\n        params.additionalProperty\n      } is not expected to be here`,\n      path: this.instancePath,\n    };\n  }\n}\n", "import { bold, red, magenta } from 'kleur/colors';\nimport leven from 'leven';\nimport pointer from 'jsonpointer';\nimport BaseValidationError from './base';\n\nconst ENUM = bold('ENUM');\n\nexport default class EnumValidationError extends BaseValidationError {\n  print() {\n    const {\n      message,\n      params: { allowedValues },\n    } = this.options;\n    const bestMatch = this.findBestMatch();\n\n    const line1 = red(`${ENUM} ${message}`);\n    const line2 = red(`(${allowedValues.join(', ')})`);\n    const output = [line1, `${line2}\\n`];\n\n    return output.concat(\n      this.getCodeFrame(\n        bestMatch !== null\n          ? `Did you mean ${magenta(bestMatch)} here?`\n          : `Unexpected value, should be equal to one of the allowed values`\n      )\n    );\n  }\n\n  getError() {\n    const { message, params } = this.options;\n    const bestMatch = this.findBestMatch();\n    const allowedValues = params.allowedValues.join(', ');\n\n    const output = {\n      ...this.getLocation(),\n      error: `${this.getDecoratedPath()} ${message}: ${allowedValues}`,\n      path: this.instancePath,\n    };\n\n    if (bestMatch !== null) {\n      output.suggestion = `Did you mean ${bestMatch}?`;\n    }\n\n    return output;\n  }\n\n  findBestMatch() {\n    const {\n      params: { allowedValues },\n    } = this.options;\n\n    const currentValue =\n      this.instancePath === ''\n        ? this.data\n        : pointer.get(this.data, this.instancePath);\n\n    if (!currentValue) {\n      return null;\n    }\n\n    const bestMatch = allowedValues\n      .map(value => ({\n        value,\n        weight: leven(value, currentValue.toString()),\n      }))\n      .sort((x, y) =>\n        x.weight > y.weight ? 1 : x.weight < y.weight ? -1 : 0\n      )[0];\n\n    return allowedValues.length === 1 ||\n      bestMatch.weight < bestMatch.value.length\n      ? bestMatch.value\n      : null;\n  }\n}\n", "import { bold, red, magenta } from 'kleur/colors';\nimport BaseValidationError from './base';\n\nexport default class DefaultValidationError extends BaseValidationError {\n  print() {\n    const { keyword, message } = this.options;\n    const line = red(`${bold(keyword.toUpperCase())} ${message}`);\n    const output = [`${line}\\n`];\n\n    return output.concat(this.getCodeFrame(`${magenta(keyword)} ${message}`));\n  }\n\n  getError() {\n    const { keyword, message } = this.options;\n\n    return {\n      ...this.getLocation(),\n      error: `${this.getDecoratedPath()}: ${keyword} ${message}`,\n      path: this.instancePath,\n    };\n  }\n}\n", "import {\n  getC<PERSON>dren,\n  getErrors,\n  getSiblings,\n  isAnyOfError,\n  isEnumError,\n  isRequiredError,\n  concatAll,\n  notUndefined,\n} from './utils';\nimport {\n  AdditionalPropValidationError,\n  RequiredValidationError,\n  EnumValidationError,\n  DefaultValidationError,\n} from './validation-errors/index';\n\nconst JSON_POINTERS_REGEX = /\\/[\\w_-]+(\\/\\d+)?/g;\n\n// Make a tree of errors from ajv errors array\nexport function makeTree(ajvErrors = []) {\n  const root = { children: {} };\n  ajvErrors.forEach(ajvError => {\n    const instancePath =\n      typeof ajvError.instancePath !== 'undefined'\n        ? ajvError.instancePath\n        : ajvError.dataPath;\n\n    // `dataPath === ''` is root\n    const paths =\n      instancePath === '' ? [''] : instancePath.match(JSON_POINTERS_REGEX);\n    paths &&\n      paths.reduce((obj, path, i) => {\n        obj.children[path] = obj.children[path] || { children: {}, errors: [] };\n        if (i === paths.length - 1) {\n          obj.children[path].errors.push(ajvError);\n        }\n        return obj.children[path];\n      }, root);\n  });\n  return root;\n}\n\nexport function filterRedundantErrors(root, parent, key) {\n  /**\n   * If there is a `required` error then we can just skip everythig else.\n   * And, also `required` should have more priority than `anyOf`. @see #8\n   */\n  getErrors(root).forEach(error => {\n    if (isRequiredError(error)) {\n      root.errors = [error];\n      root.children = {};\n    }\n  });\n\n  /**\n   * If there is an `anyOf` error that means we have more meaningful errors\n   * inside children. So we will just remove all errors from this level.\n   *\n   * If there are no children, then we don't delete the errors since we should\n   * have at least one error to report.\n   */\n  if (getErrors(root).some(isAnyOfError)) {\n    if (Object.keys(root.children).length > 0) {\n      delete root.errors;\n    }\n  }\n\n  /**\n   * If all errors are `enum` and siblings have any error then we can safely\n   * ignore the node.\n   *\n   * **CAUTION**\n   * Need explicit `root.errors` check because `[].every(fn) === true`\n   * https://en.wikipedia.org/wiki/Vacuous_truth#Vacuous_truths_in_mathematics\n   */\n  if (root.errors && root.errors.length && getErrors(root).every(isEnumError)) {\n    if (\n      getSiblings(parent)(root)\n        // Remove any reference which becomes `undefined` later\n        .filter(notUndefined)\n        .some(getErrors)\n    ) {\n      delete parent.children[key];\n    }\n  }\n\n  Object.entries(root.children).forEach(([key, child]) =>\n    filterRedundantErrors(child, root, key)\n  );\n}\n\nexport function createErrorInstances(root, options) {\n  const errors = getErrors(root);\n  if (errors.length && errors.every(isEnumError)) {\n    const uniqueValues = new Set(\n      concatAll([])(errors.map(e => e.params.allowedValues))\n    );\n    const allowedValues = [...uniqueValues];\n    const error = errors[0];\n    return [\n      new EnumValidationError(\n        {\n          ...error,\n          params: { allowedValues },\n        },\n        options\n      ),\n    ];\n  } else {\n    return concatAll(\n      errors.reduce((ret, error) => {\n        switch (error.keyword) {\n          case 'additionalProperties':\n            return ret.concat(\n              new AdditionalPropValidationError(error, options)\n            );\n          case 'enum':\n            return ret.concat(new EnumValidationError(error, options));\n          case 'required':\n            return ret.concat(new RequiredValidationError(error, options));\n          default:\n            return ret.concat(new DefaultValidationError(error, options));\n        }\n      }, [])\n    )(getChildren(root).map(child => createErrorInstances(child, options)));\n  }\n}\n\nexport default (ajvErrors, options) => {\n  const tree = makeTree(ajvErrors || []);\n  filterRedundantErrors(tree);\n  return createErrorInstances(tree, options);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,gCAAAA,UAAAC,SAAA;AAAA;AACA,QAAM,QAAQ,CAAC;AACf,QAAM,gBAAgB,CAAC;AAEvB,QAAMC,SAAQ,CAAC,MAAM,UAAU;AAC9B,UAAI,SAAS,OAAO;AACnB,eAAO;AAAA,MACR;AAEA,YAAM,OAAO;AAIb,UAAI,KAAK,SAAS,MAAM,QAAQ;AAC/B,eAAO;AACP,gBAAQ;AAAA,MACT;AAEA,UAAI,aAAa,KAAK;AACtB,UAAI,cAAc,MAAM;AAMxB,aAAO,aAAa,KAAM,KAAK,WAAW,CAAC,CAAC,UAAU,MAAM,MAAM,WAAW,CAAC,CAAC,WAAW,GAAI;AAC7F;AACA;AAAA,MACD;AAKA,UAAI,QAAQ;AAEZ,aAAO,QAAQ,cAAe,KAAK,WAAW,KAAK,MAAM,MAAM,WAAW,KAAK,GAAI;AAClF;AAAA,MACD;AAEA,oBAAc;AACd,qBAAe;AAEf,UAAI,eAAe,GAAG;AACrB,eAAO;AAAA,MACR;AAEA,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,IAAI;AACR,UAAI,IAAI;AAER,aAAO,IAAI,YAAY;AACtB,sBAAc,CAAC,IAAI,KAAK,WAAW,QAAQ,CAAC;AAC5C,cAAM,CAAC,IAAI,EAAE;AAAA,MACd;AAEA,aAAO,IAAI,aAAa;AACvB,oBAAY,MAAM,WAAW,QAAQ,CAAC;AACtC,eAAO;AACP,iBAAS;AAET,aAAK,IAAI,GAAG,IAAI,YAAY,KAAK;AAChC,kBAAQ,cAAc,cAAc,CAAC,IAAI,OAAO,OAAO;AACvD,iBAAO,MAAM,CAAC;AAEd,mBAAS,MAAM,CAAC,IAAI,OAAO,SAAS,QAAQ,SAAS,SAAS,IAAI,QAAQ,QAAQ,OAAO,OAAO,IAAI;AAAA,QACrG;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,IAAAD,QAAO,UAAUC;AAEjB,IAAAD,QAAO,QAAQ,UAAUC;AAAA;AAAA;;;AC5EzB;AAAA,4CAAAC,UAAA;AAAA,QAAI,YAAY;AAChB,QAAI,gBAAgB;AACpB,aAAS,eAAgB,GAAG;AAC1B,cAAQ,GAAG;AAAA,QACT,KAAK;AAAM,iBAAO;AAAA,QAClB,KAAK;AAAM,iBAAO;AAAA,MACpB;AACA,YAAM,IAAI,MAAM,2BAA2B,CAAC;AAAA,IAC9C;AAEA,aAAS,QAAS,KAAK;AACrB,UAAI,CAAC,UAAU,KAAK,GAAG,EAAG,QAAO;AACjC,aAAO,IAAI,QAAQ,eAAe,cAAc;AAAA,IAClD;AAEA,aAAS,OAAQ,KAAKC,UAAS,OAAO;AACpC,UAAI;AACJ,UAAI;AAEJ,eAAS,IAAI,GAAG,MAAMA,SAAQ,QAAQ,IAAI,OAAM;AAC9C,YAAIA,SAAQ,CAAC,MAAM,iBAAiBA,SAAQ,CAAC,MAAM,eAAeA,SAAQ,CAAC,MAAM,YAAa,QAAO;AAErG,eAAO,QAAQA,SAAQ,GAAG,CAAC;AAC3B,sBAAc,MAAM;AAEpB,YAAI,OAAO,IAAI,IAAI,MAAM,aAAa;AAEpC,cAAI,MAAM,QAAQ,GAAG,KAAK,SAAS,KAAK;AACtC,mBAAO,IAAI;AAAA,UACb;AAGA,cAAI,aAAa;AACf,gBAAKA,SAAQ,CAAC,MAAM,MAAMA,SAAQ,CAAC,IAAI,YAAaA,SAAQ,CAAC,MAAM,IAAK,KAAI,IAAI,IAAI,CAAC;AAAA,gBAChF,KAAI,IAAI,IAAI,CAAC;AAAA,UACpB;AAAA,QACF;AAEA,YAAI,CAAC,YAAa;AAClB,cAAM,IAAI,IAAI;AAAA,MAChB;AAEA,UAAI,WAAW,IAAI,IAAI;AACvB,UAAI,UAAU,OAAW,QAAO,IAAI,IAAI;AAAA,UACnC,KAAI,IAAI,IAAI;AACjB,aAAO;AAAA,IACT;AAEA,aAAS,eAAgBA,UAAS;AAChC,UAAI,OAAOA,aAAY,UAAU;AAC/B,QAAAA,WAAUA,SAAQ,MAAM,GAAG;AAC3B,YAAIA,SAAQ,CAAC,MAAM,GAAI,QAAOA;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC,WAAW,MAAM,QAAQA,QAAO,GAAG;AACjC,mBAAW,QAAQA,UAAS;AAC1B,cAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;AACxD,kBAAM,IAAI,MAAM,yDAAyD;AAAA,UAC3E;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAEA,aAAS,IAAK,KAAKA,UAAS;AAC1B,UAAI,OAAO,QAAQ,SAAU,OAAM,IAAI,MAAM,uBAAuB;AACpE,MAAAA,WAAU,eAAeA,QAAO;AAChC,UAAI,MAAMA,SAAQ;AAClB,UAAI,QAAQ,EAAG,QAAO;AAEtB,eAAS,IAAI,GAAG,IAAI,OAAM;AACxB,cAAM,IAAI,QAAQA,SAAQ,GAAG,CAAC,CAAC;AAC/B,YAAI,QAAQ,EAAG,QAAO;AACtB,YAAI,OAAO,QAAQ,YAAY,QAAQ,KAAM,QAAO;AAAA,MACtD;AAAA,IACF;AAEA,aAAS,IAAK,KAAKA,UAAS,OAAO;AACjC,UAAI,OAAO,QAAQ,SAAU,OAAM,IAAI,MAAM,uBAAuB;AACpE,MAAAA,WAAU,eAAeA,QAAO;AAChC,UAAIA,SAAQ,WAAW,EAAG,OAAM,IAAI,MAAM,+BAA+B;AACzE,aAAO,OAAO,KAAKA,UAAS,KAAK;AAAA,IACnC;AAEA,aAAS,QAASA,UAAS;AACzB,UAAI,WAAW,eAAeA,QAAO;AACrC,aAAO;AAAA,QACL,KAAK,SAAU,QAAQ;AACrB,iBAAO,IAAI,QAAQ,QAAQ;AAAA,QAC7B;AAAA,QACA,KAAK,SAAU,QAAQ,OAAO;AAC5B,iBAAO,IAAI,QAAQ,UAAU,KAAK;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAEA,IAAAD,SAAQ,MAAM;AACd,IAAAA,SAAQ,MAAM;AACd,IAAAA,SAAQ,UAAU;AAAA;AAAA;;;ACnGlB;AAAA;AAAA;AAAA;AAAA;;;ACKA,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,iBAAiB;AACvB,IAAM,cAAc;AACpB,IAAM,WAAW;AACjB,IAAM,oBAAoB;AAC1B,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,sBAAsB;AAC5B,IAAM,2BAA2B;AACjC,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,iBAAiB;AACvB,IAAM,YAAY;AAClB,IAAM,WAAW;AACjB,IAAM,0BAA0B;AAChC,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,0BAA0B;AAChC,IAAM,yBAAyB;AAC/B,IAAM,wBAAwB;AAC9B,IAAM,oBAAoB;AAC1B,IAAM,yBAAyB;AAC/B,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,6BAA6B;AACnC,IAAM,iCAAiC;AACvC,IAAM,yBAAyB;AAkB/B,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,QAAQ;AAEd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,aAAa;AACnB,IAAM,QAAQ;AAEd,IAAM,mBAAmB,oBAAI,IAAI;AAAA,EAC7B,CAAC,cAAc,CAAC,cAAc,cAAc,YAAY,CAAC;AAAA,EACzD,CAAC,cAAc,CAAC,cAAc,cAAc,cAAc,YAAY,CAAC;AAAA,EACvE,CAAC,cAAc,CAAC,cAAc,cAAc,YAAY,CAAC;AAC7D,CAAC;AAED,IAAM,eAAe,oBAAI,IAAI;AAAA,EACzB,CAAC,mBAAmB,KAAK;AAAA,EACzB,CAAC,gBAAgB,IAAI;AAAA,EACrB,CAAC,YAAY,GAAG;AAAA,EAChB,CAAC,cAAc,IAAI;AAAA,EACnB,CAAC,cAAc,IAAI;AAAA,EACnB,CAAC,cAAc,IAAI;AAAA,EACnB,CAAC,cAAc,IAAI;AAAA,EACnB,CAAC,cAAc,GAAI;AACvB,CAAC;AAED,IAAM,oBAAoB,IAAI,IAAI;AAAA,EAC9B,GAAG;AAAA,EACH,CAAC,cAAc,IAAI;AAAA,EACnB,CAAC,QAAQ,IAAI;AACjB,CAAC;AAED,IAAM,eAAe,oBAAI,IAAI;AAAA,EACzB,CAAC,OAAO,KAAK;AAAA,EACb,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,KAAM,GAAG;AACd,CAAC;AAED,IAAM,oBAAoB,IAAI,IAAI;AAAA,EAC9B,GAAG;AAAA,EACH,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,UAAU,OAAO;AAAA,EAClB,CAAC,UAAU,OAAO;AACtB,CAAC;AAGD,IAAM,kBAAkB,oBAAI,IAAI;AAAA,EAC5B,CAAC,UAAU,UAAU;AAAA,EACrB,CAAC,UAAU,UAAU;AAAA,EACrB,CAAC,QAAQ,QAAQ;AAAA,EACjB,CAAC,QAAQ,QAAQ;AAAA,EACjB,CAAC,OAAO,OAAO;AAAA,EACf,CAAC,OAAO,OAAO;AAAA,EACf,CAAC,MAAM,SAAS;AAAA,EAChB,CAAC,OAAO,SAAS;AAAA,EACjB,CAAC,MAAM,MAAM;AACjB,CAAC;AAGD,IAAM,uBAAuB,IAAI,IAAI;AAAA,EACjC,GAAG;AAAA,EACH,CAAC,OAAO,QAAQ;AAAA,EAChB,CAAC,YAAY,QAAQ;AACzB,CAAC;AAGD,IAAM,uBAAuB,oBAAI,IAAI;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAqBD,IAAM,oBAAN,cAAgC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlC,YAAY,SAAS,EAAE,MAAM,QAAQ,OAAO,GAAG;AAC3C,UAAM,GAAI,OAAQ,KAAM,IAAK,IAAK,MAAM,GAAG;AAM3C,SAAK,OAAO;AAMZ,SAAK,SAAS;AAMd,SAAK,SAAS;AAAA,EAClB;AAEJ;AAKA,IAAM,iBAAN,cAA6B,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO3C,YAAY,YAAY,KAAK;AACzB,UAAM,yBAA0B,OAAO,aAAa,UAAU,CAAE,YAAY,GAAG;AAAA,EACnF;AACJ;AAKA,IAAM,kBAAN,cAA8B,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5C,YAAY,OAAO;AACf,UAAM,oBAAqB,MAAM,IAAK,WAAW,MAAM,IAAI,KAAK;AAAA,EACpE;AACJ;AAKA,IAAM,gBAAN,cAA4B,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1C,YAAY,KAAK;AACb,UAAM,kCAAkC,GAAG;AAAA,EAC/C;AACJ;AAgBA,IAAM,WAAW;AAEjB,IAAM,cAAc;AAgBpB,IAAM,UAAU;AAChB,IAAM,UAAU;AAShB,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAMT,YAAY,MAAM;AACd,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,QAAI,KAAK,QAAQ;AACb;AAAA,IACJ;AAEA,SAAK;AACL,SAAK;AACL,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,WAAO;AAAA,MACH,MAAM,KAAK;AAAA,MACX,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,IACjB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,QAAI,KAAK,WAAW,KAAK,MAAM,SAAS,GAAG;AACvC,WAAK,KAAK;AACV,aAAO;AAAA,IACX;AAEA,SAAK;AACL,UAAM,WAAW,KAAK,MAAM,WAAW,KAAK,OAAO;AAEnD,QAAI,KAAK,UAAU;AACf,WAAK;AACL,WAAK,UAAU;AACf,WAAK,WAAW;AAAA,IACpB,OAAO;AACH,WAAK;AAAA,IACT;AAEA,QAAI,aAAa,SAAS;AACtB,WAAK,WAAW;AAGhB,UAAI,KAAK,KAAK,MAAM,SAAS;AACzB,aAAK;AAAA,MACT;AAAA,IACJ,WAAW,aAAa,SAAS;AAC7B,WAAK,WAAW;AAAA,IACpB;AAEA,SAAK,QAAQ;AAEb,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,QAAI,KAAK,YAAY,KAAK,MAAM,SAAS,GAAG;AACxC,aAAO;AAAA,IACX;AAEA,WAAO,KAAK,MAAM,WAAW,KAAK,UAAU,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACN,WAAO,KAAK;AAAA,EAChB;AAGJ;AAmBA,IAAM,WAAW;AACjB,IAAM,MAAM;AAEZ,IAAM,gBAAgB,oBAAI,IAAI,CAAC,cAAc,cAAc,YAAY,CAAC;AACxE,IAAM,aAAa,oBAAI,IAAI,CAAC,YAAY,UAAU,cAAc,WAAW,CAAC;AAC5E,IAAM,kBAAkB,oBAAI,IAAI;AAAA,EAC5B,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAGD,IAAM,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,QAAQ;AACZ;AAUA,SAAS,QAAQ,GAAG;AAChB,SAAO,KAAK,UAAU,KAAK;AAC/B;AAOA,SAAS,WAAW,GAAG;AACnB,SAAO,QAAQ,CAAC,KACZ,KAAK,gBAAgB,KAAK,gBAC1B,KAAK,gBAAgB,KAAK;AAClC;AAOA,SAAS,gBAAgB,GAAG;AACxB,SAAO,KAAK,UAAU,KAAK;AAC/B;AAOA,SAAS,eAAe,GAAG;AACvB,SAAO,cAAc,IAAI,CAAC;AAC9B;AAOA,SAAS,cAAc,GAAG;AACtB,SAAO,QAAQ,CAAC,KAAK,MAAM,YAAY,MAAM;AACjD;AAOA,SAAS,mBAAmB,GAAG;AAC3B,SAAO,cAAc,CAAC,KAAK,MAAM;AACrC;AAQA,SAAS,cAAc,GAAG,OAAO;AAC7B,SAAO,MAAM,qBAAsB,SAAS,MAAM;AACtD;AAQA,SAAS,uBAAuB,GAAG;AAI/B,MAAI,MAAM,eAAe,MAAM,mBAAmB,MAAM,gBAAgB;AACpE,WAAO;AAAA,EACX;AAEA,MAAI,KAAK,gBAAgB,KAAK,gBAAgB,KAAK,gBAAgB,KAAK,cAAc;AAClF,WAAO;AAAA,EACX;AAEA,MAAI,MAAM,QAAU,MAAM,MAAQ;AAC9B,WAAO;AAAA,EACX;AAEA,QAAM,KAAK,OAAO,aAAa,CAAC;AAChC,SAAO,SAAS,KAAK,EAAE;AAC3B;AAOA,SAAS,sBAAsB,GAAG;AAG9B,MAAI,uBAAuB,CAAC,KAAK,QAAQ,CAAC,GAAG;AACzC,WAAO;AAAA,EACX;AAEA,QAAM,KAAK,OAAO,aAAa,CAAC;AAChC,SAAO,YAAY,KAAK,EAAE;AAC9B;AAcA,SAAS,SAAS,MAAM,SAAS;AAE7B,YAAU,OAAO,OAAO;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AAED,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,gBAAgB,QAAQ,SAAS;AACvC,QAAM,SAAS,IAAI,eAAe,IAAI;AAEtC,QAAM,SAAS,CAAC;AAGhB,QAAM,qBAAqB,QAAQ,kBAAkB,IAAI,KAAK,iBAAiB,IAAI,aAAa,IAAI,KAAK,YAAY;AACrH,QAAM,wBAAwB,QAAQ,qBAAqB,IAAI,KAAK,oBAAoB,IAAI,MAAM;AAClG,QAAM,mBAAmB,QAAQ,CAAAE,OAAKA,OAAM,eAAe,MAAM;AACjE,QAAM,eAAe,QAAQ,gBAAgB,IAAI,KAAK,eAAe,IAAI,WAAW,IAAI,KAAK,UAAU;AAUvG,WAAS,YAAY,WAAW,QAAQ,UAAU,QAAQ;AAEtD,UAAM,YAAY,SAAS,SAAS;AAEpC,QAAI,QAAQ,QAAQ,SAAS;AAAA,MACzB;AAAA;AAAA,QAA6B,CAAC,SAAS,QAAQ,SAAS;AAAA;AAAA,IAC5D,IAAI;AAEJ,WAAO;AAAA,MACH,MAAM;AAAA,MACN,KAAK;AAAA,QACD,OAAO;AAAA,QACP,KAAK,UAAU;AAAA,UACX,MAAM,SAAS;AAAA,UACf,QAAQ,SAAS,SAAS;AAAA,UAC1B,QAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,MACA,GAAG;AAAA,IACP;AAAA,EACJ;AAOA,WAAS,iBAAiBC,OAAM;AAE5B,aAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,KAAK;AAClC,UAAI,OAAO,KAAK,MAAMA,MAAK,WAAW,CAAC,GAAG;AACtC,eAAO;AAAA,MACX;AACA,aAAO,KAAK;AAAA,IAChB;AAEA,WAAO;AAAA,EACX;AAQA,WAAS,YAAYD,IAAG;AAGpB,QAAI,WAAW,iBAAiB,IAAIA,EAAC;AACrC,QAAI,QAAQ,OAAO,aAAaA,EAAC;AAGjC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAM,KAAK,OAAO,KAAK;AACvB,UAAI,SAAS,CAAC,MAAM,IAAI;AACpB,mBAAW,EAAE;AAAA,MACjB;AACA,eAAS,OAAO,aAAa,EAAE;AAAA,IACnC;AAEA,WAAO;AAAA,MACH;AAAA,MACA,GAAG,OAAO,KAAK;AAAA,IACnB;AAAA,EACJ;AASA,WAAS,oBAAoBA,IAAG;AAE5B,QAAI,QAAQ;AAEZ,OAAG;AAEC,eAAS,OAAO,aAAaA,EAAC;AAE9B,UAAIA,OAAM,gBAAgB;AAEtB,QAAAA,KAAI,OAAO,KAAK;AAEhB,YAAIA,OAAM,cAAc;AACpB,qBAAWA,EAAC;AAAA,QAChB;AAEA,iBAAS,OAAO,aAAaA,EAAC;AAE9B,cAAM,SAAS,cAAc,CAAC;AAC9B,iBAAS,OAAO;AAChB,QAAAA,KAAI,OAAO;AAAA,MACf;AAEA,MAAAA,KAAI,OAAO,KAAK;AAAA,IAEpB,SAASA,KAAI,MAAM,sBAAsBA,EAAC;AAE1C,WAAO,EAAE,OAAO,GAAAA,GAAE;AAAA,EACtB;AAOA,WAAS,cAAc,OAAO;AAC1B,QAAI,QAAQ;AAEZ,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,UAAI,OAAO,KAAK;AAChB,UAAI,WAAW,CAAC,GAAG;AACf,iBAAS,OAAO,aAAa,CAAC;AAC9B;AAAA,MACJ;AAEA,iBAAW,CAAC;AAAA,IAChB;AAEA,WAAO,EAAE,OAAO,EAAE;AAAA,EACtB;AAOA,WAAS,WAAWA,IAAG;AACnB,UAAM,YAAYA;AAClB,QAAI,SAAS;AACb,IAAAA,KAAI,OAAO,KAAK;AAEhB,WAAOA,OAAM,MAAMA,OAAM,WAAW;AAGhC,UAAIA,OAAM,gBAAgB;AACtB;AACA,QAAAA,KAAI,OAAO,KAAK;AAEhB,YAAI,mBAAmBA,EAAC,KAAK,sBAAsBA,EAAC,GAAG;AACnD;AAAA,QACJ,WAAWA,OAAM,cAAc;AAC3B;AAEA,gBAAM,SAAS,cAAc,CAAC;AAC9B,oBAAU,OAAO,MAAM;AACvB,UAAAA,KAAI,OAAO;AAAA,QAEf,WAAW,iBAAiBA,EAAC,GAAG;AAE5B;AAEA,gBAAM,SAAS,cAAc,CAAC;AAC9B,oBAAU,OAAO,MAAM;AACvB,UAAAA,KAAI,OAAO;AAAA,QACf,WAAW,CAAC,OAAO;AACf,qBAAWA,EAAC;AAAA,QAChB;AAAA,MACJ,OAAO;AACH;AAAA,MACJ;AAEA,MAAAA,KAAI,OAAO,KAAK;AAAA,IACpB;AAEA,QAAIA,OAAM,IAAI;AACV,oBAAc;AAAA,IAClB;AAEA;AAEA,WAAO,EAAE,QAAQ,GAAG,OAAO,KAAK,EAAE;AAAA,EACtC;AASA,WAAS,WAAWA,IAAG;AAEnB,QAAI,SAAS;AAIb,QAAIA,OAAM,cAAc,SAASA,OAAM,WAAW;AAC9C;AAEA,MAAAA,KAAI,OAAO,KAAK;AAOhB,UAAI,OAAO;AAEP,YAAIA,OAAM,gBAAgB,iBAAiB,SAAS,GAAG;AACnD,iBAAO,EAAE,QAAQ,SAAS,SAAS,GAAG,GAAG,OAAO,KAAK,EAAE;AAAA,QAC3D;AAEA,YAAIA,OAAM,gBAAgB,iBAAiB,IAAI,GAAG;AAC9C,iBAAO,EAAE,QAAQ,IAAI,SAAS,GAAG,GAAG,OAAO,KAAK,EAAE;AAAA,QACtD;AAAA,MACJ;AAGA,UAAI,CAAC,QAAQA,EAAC,GAAG;AACb,mBAAWA,EAAC;AAAA,MAChB;AAAA,IAEJ;AAOA,QAAIA,OAAM,QAAQ;AAEd;AAEA,MAAAA,KAAI,OAAO,KAAK;AAGhB,UAAI,UAAUA,OAAM,gBAAgBA,OAAM,eAAe;AACrD;AACA,QAAAA,KAAI,OAAO,KAAK;AAEhB,YAAI,CAAC,WAAWA,EAAC,GAAG;AAChB,qBAAWA,EAAC;AAAA,QAChB;AAEA,WAAG;AACC;AACA,UAAAA,KAAI,OAAO,KAAK;AAAA,QACpB,SAAS,WAAWA,EAAC;AAAA,MAEzB,WAAW,QAAQA,EAAC,GAAG;AACnB,mBAAWA,EAAC;AAAA,MAChB;AAAA,IAEJ,OAAO;AAGH,UAAI,CAAC,SAASA,OAAM,UAAU;AAC1B,YAAI,CAAC,gBAAgBA,EAAC,GAAG;AACrB,qBAAWA,EAAC;AAAA,QAChB;AAEA,WAAG;AACC;AACA,UAAAA,KAAI,OAAO,KAAK;AAAA,QACpB,SAAS,QAAQA,EAAC;AAAA,MACtB;AAAA,IACJ;AAMA,QAAIA,OAAM,UAAU;AAEhB,UAAI,aAAa;AAEjB,SAAG;AACC;AACA;AACA,QAAAA,KAAI,OAAO,KAAK;AAAA,MACpB,SAAS,QAAQA,EAAC;AAElB,UAAI,CAAC,SAAS,eAAe,GAAG;AAC5B,YAAIA,IAAG;AACH,qBAAWA,EAAC;AAAA,QAChB,OAAO;AACH,wBAAc;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AAGA,QAAIA,OAAM,gBAAgBA,OAAM,cAAc;AAE1C;AACA,MAAAA,KAAI,OAAO,KAAK;AAEhB,UAAIA,OAAM,aAAaA,OAAM,YAAY;AACrC;AACA,QAAAA,KAAI,OAAO,KAAK;AAAA,MACpB;AAQA,UAAIA,OAAM,IAAI;AACV,sBAAc;AAAA,MAClB;AAEA,UAAI,CAAC,QAAQA,EAAC,GAAG;AACb,mBAAWA,EAAC;AAAA,MAChB;AAEA,aAAO,QAAQA,EAAC,GAAG;AACf;AACA,QAAAA,KAAI,OAAO,KAAK;AAAA,MACpB;AAAA,IACJ;AAGA,WAAO,EAAE,QAAQ,GAAAA,GAAE;AAAA,EACvB;AAUA,WAAS,YAAYA,IAAG;AAEpB,QAAI,SAAS;AAGb,IAAAA,KAAI,OAAO,KAAK;AAGhB,QAAIA,OAAM,YAAY;AAElB,SAAG;AACC,kBAAU;AACV,QAAAA,KAAI,OAAO,KAAK;AAAA,MACpB,SAASA,KAAI,MAAMA,OAAM,eAAeA,OAAM;AAE9C,aAAO,EAAE,QAAQ,GAAAA,IAAG,WAAW,MAAM;AAAA,IACzC;AAGA,QAAIA,OAAM,WAAW;AAEjB,aAAOA,KAAI,IAAI;AACX,kBAAU;AACV,QAAAA,KAAI,OAAO,KAAK;AAGhB,YAAIA,OAAM,WAAW;AACjB,oBAAU;AACV,UAAAA,KAAI,OAAO,KAAK;AAGhB,cAAIA,OAAM,YAAY;AAClB,sBAAU;AAOV,YAAAA,KAAI,OAAO,KAAK;AAChB,mBAAO,EAAE,QAAQ,GAAAA,IAAG,WAAW,KAAK;AAAA,UACxC;AAAA,QACJ;AAAA,MACJ;AAEA,oBAAc;AAAA,IAElB;AAGA,eAAWA,EAAC;AAAA,EAChB;AASA,WAAS,WAAWA,IAAG;AACnB,UAAM,IAAI,eAAeA,IAAG,OAAO,OAAO,CAAC;AAAA,EAC/C;AAOA,WAAS,gBAAgB;AACrB,UAAM,IAAI,cAAc,OAAO,OAAO,CAAC;AAAA,EAC3C;AAEA,MAAI,IAAI,OAAO,KAAK;AAEpB,SAAO,IAAI,IAAI;AAEX,WAAO,aAAa,CAAC,GAAG;AACpB,UAAI,OAAO,KAAK;AAAA,IACpB;AAEA,QAAI,MAAM,IAAI;AACV;AAAA,IACJ;AAEA,UAAM,QAAQ,OAAO,OAAO;AAC5B,UAAM,KAAK,OAAO,aAAa,CAAC;AAGhC,QAAI,OAAO;AAEP,UAAI,qBAAqB,IAAI,EAAE,GAAG;AAC9B,eAAO,KAAK,YAAY,qBAAqB,IAAI,EAAE,GAAG,GAAG,KAAK,CAAC;AAC/D,YAAI,OAAO,KAAK;AAAA,MACpB,WAAW,uBAAuB,CAAC,GAAG;AAClC,cAAM,SAAS,oBAAoB,CAAC;AACpC,YAAI,QAAQ,OAAO;AACnB,YAAI,OAAO;AAEX,YAAI,qBAAqB,IAAI,KAAK,GAAG;AACjC,iBAAO,KAAK,YAAY,qBAAqB,IAAI,KAAK,GAAG,MAAM,QAAQ,KAAK,CAAC;AAAA,QACjF,OAAO;AACH,iBAAO,KAAK,YAAY,cAAc,MAAM,QAAQ,KAAK,CAAC;AAAA,QAC9D;AAAA,MACJ,WAAW,mBAAmB,CAAC,GAAG;AAC9B,cAAM,SAAS,WAAW,CAAC;AAC3B,YAAI,OAAO;AACX,eAAO,KAAK,YAAY,UAAU,OAAO,QAAQ,KAAK,CAAC;AAAA,MAC3D,WAAW,cAAc,GAAG,KAAK,GAAG;AAChC,cAAM,SAAS,WAAW,CAAC;AAC3B,YAAI,OAAO;AACX,eAAO,KAAK,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,OAAO,CAAC,CAAC;AAAA,MAC5E,WAAW,MAAM,cAAc,eAAe;AAC1C,cAAM,SAAS,YAAY,CAAC;AAC5B,YAAI,OAAO;AACX,eAAO,KAAK,YAAY,CAAC,OAAO,YAAY,gBAAgB,gBAAgB,OAAO,QAAQ,OAAO,OAAO,OAAO,CAAC,CAAC;AAAA,MACtH,OAAO;AACH,mBAAW,CAAC;AAAA,MAChB;AAAA,IAEJ,OAAO;AAEH,YAAME,MAAK,OAAO,aAAa,CAAC;AAGhC,UAAI,gBAAgB,IAAIA,GAAE,GAAG;AACzB,eAAO,KAAK,YAAY,gBAAgB,IAAIA,GAAE,GAAG,GAAG,KAAK,CAAC;AAC1D,YAAI,OAAO,KAAK;AAAA,MACpB,WAAW,eAAe,CAAC,GAAG;AAC1B,cAAM,SAAS,YAAY,CAAC;AAC5B,YAAI,QAAQ,OAAO;AACnB,YAAI,OAAO;AACX,eAAO,KAAK,YAAY,gBAAgB,IAAI,KAAK,GAAG,MAAM,QAAQ,KAAK,CAAC;AAAA,MAC5E,WAAW,cAAc,CAAC,GAAG;AACzB,cAAM,SAAS,WAAW,CAAC;AAC3B,YAAI,OAAO;AACX,eAAO,KAAK,YAAY,UAAU,OAAO,QAAQ,KAAK,CAAC;AAAA,MAC3D,WACS,cAAc,GAAG,KAAK,GAAG;AAC9B,cAAM,SAAS,WAAW,CAAC;AAC3B,YAAI,OAAO;AACX,eAAO,KAAK,YAAY,UAAU,OAAO,QAAQ,KAAK,CAAC;AAAA,MAC3D,WAAW,MAAM,cAAc,eAAe;AAC1C,cAAM,SAAS,YAAY,CAAC;AAC5B,YAAI,OAAO;AACX,eAAO,KAAK,YAAY,CAAC,OAAO,YAAY,gBAAgB,gBAAgB,OAAO,QAAQ,OAAO,OAAO,OAAO,CAAC,CAAC;AAAA,MACtH,OAAO;AACH,mBAAW,CAAC;AAAA,MAChB;AAAA,IACJ;AAAA,EAIJ;AAEA,SAAO;AAEX;AA+BA,IAAM,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQV,SAAS,MAAM,QAAQ,CAAC,GAAG;AACvB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO,QAAQ,CAAC,GAAG;AACtB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO,QAAQ,CAAC,GAAG;AACtB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,OAAO,QAAQ,CAAC,GAAG;AACvB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,QAAQ,CAAC,GAAG;AACb,WAAO;AAAA,MACH,MAAM;AAAA,MACN,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,UAAU,QAAQ,CAAC,GAAG;AACxB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,OAAO,QAAQ,CAAC,GAAG;AACvB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,QAAQ,CAAC,GAAG;AACxB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,MAAM,OAAO,QAAQ,CAAC,GAAG;AAC5B,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,MAAM,QAAQ,CAAC,GAAG;AACzB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO,IAAI,QAAQ,CAAC,GAAG;AACvB,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO,IAAI,QAAQ,CAAC,GAAG;AAC5B,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,MACA,KAAK,MAAM;AAAA,MACX,GAAG;AAAA,IACP;AAAA,EACJ;AAEJ;AAqBA,IAAM,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACZ;AAUA,SAAS,eAAe,OAAO,OAAO,QAAQ,OAAO;AAEjD,MAAI,SAAS;AACb,MAAI,cAAc,MAAM,QAAQ,IAAI;AACpC,MAAI,YAAY;AAGhB,SAAO,eAAe,GAAG;AAGrB,cAAU,MAAM,MAAM,WAAW,WAAW;AAG5C,UAAM,aAAa,MAAM,OAAO,cAAc,CAAC;AAC/C,UAAM,iBAAiB,WAAW,WAAW,CAAC;AAG9C,QAAI,SAAS,kBAAkB,IAAI,cAAc,GAAG;AAChD,gBAAU,kBAAkB,IAAI,cAAc;AAC9C,kBAAY,cAAc;AAAA,IAC9B,WAAW,aAAa,IAAI,cAAc,GAAG;AACzC,gBAAU,aAAa,IAAI,cAAc;AACzC,kBAAY,cAAc;AAAA,IAC9B,WAAW,eAAe,KAAK;AAC3B,YAAM,UAAU,MAAM,MAAM,cAAc,GAAG,cAAc,CAAC;AAC5D,UAAI,QAAQ,SAAS,KAAK,aAAa,KAAK,OAAO,GAAG;AAClD,cAAM,IAAI;AAAA,UACN,6BAA8B,OAAO;AAAA,UACrC;AAAA,YACI,MAAM,MAAM,IAAI,MAAM;AAAA,YACtB,QAAQ,MAAM,IAAI,MAAM,SAAS;AAAA,YACjC,QAAQ,MAAM,IAAI,MAAM,SAAS;AAAA,UACrC;AAAA,QACJ;AAAA,MACJ;AAEA,gBAAU,OAAO,aAAa,SAAS,SAAS,EAAE,CAAC;AACnD,kBAAY,cAAc;AAAA,IAC9B,WAAW,SAAS,eAAe,KAAK;AACpC,YAAM,UAAU,MAAM,MAAM,cAAc,GAAG,cAAc,CAAC;AAC5D,UAAI,QAAQ,SAAS,KAAK,aAAa,KAAK,OAAO,GAAG;AAClD,cAAM,IAAI;AAAA,UACN,yBAA0B,OAAO;AAAA,UACjC;AAAA,YACI,MAAM,MAAM,IAAI,MAAM;AAAA,YACtB,QAAQ,MAAM,IAAI,MAAM,SAAS;AAAA,YACjC,QAAQ,MAAM,IAAI,MAAM,SAAS;AAAA,UACrC;AAAA,QACJ;AAAA,MACJ;AAEA,gBAAU,OAAO,aAAa,SAAS,SAAS,EAAE,CAAC;AACnD,kBAAY,cAAc;AAAA,IAC9B,WAAW,SAAS,qBAAqB,IAAI,cAAc,GAAG;AAC1D,kBAAY,cAAc;AAG1B,UAAI,eAAe,QAAQ,MAAM,OAAO,SAAS,MAAM,MAAM;AACzD;AAAA,MACJ;AAAA,IAEJ,OAAO;AAEH,UAAI,OAAO;AACP,kBAAU;AACV,oBAAY,cAAc;AAAA,MAC9B,OAAO;AACH,cAAM,IAAI;AAAA,UACN,oBAAqB,UAAW;AAAA,UAChC;AAAA,YACI,MAAM,MAAM,IAAI,MAAM;AAAA,YACtB,QAAQ,MAAM,IAAI,MAAM,SAAS;AAAA,YACjC,QAAQ,MAAM,IAAI,MAAM,SAAS;AAAA,UACrC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAGA,kBAAc,MAAM,QAAQ,MAAM,SAAS;AAAA,EAC/C;AAGA,YAAU,MAAM,MAAM,SAAS;AAE/B,SAAO;AACX;AAUA,SAAS,gBAAgB,OAAO,OAAO,QAAQ,OAAO;AAClD,UAAQ,MAAM,MAAM;AAAA,IAChB,KAAK;AACD,aAAO,UAAU;AAAA,IAErB,KAAK;AACD,aAAO,OAAO,KAAK;AAAA,IAEvB,KAAK;AACD,aAAO,eAAe,MAAM,MAAM,GAAG,EAAE,GAAG,OAAO,KAAK;AAAA,IAE1D;AACI,YAAM,IAAI,UAAU,uBAAuB,MAAM,IAAI,GAAG;AAAA,EAChE;AACJ;AAaA,SAAS,MAAM,MAAM,SAAS;AAE1B,YAAU,OAAO,OAAO;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AAED,QAAM,SAAS,SAAS,MAAM;AAAA,IAC1B,MAAM,QAAQ;AAAA,IACd,QAAQ,QAAQ;AAAA,EACpB,CAAC;AAED,MAAI,aAAa;AACjB,QAAM,QAAQ,QAAQ,SAAS;AAM/B,WAAS,iBAAiB;AACtB,WAAO,OAAO,YAAY;AAAA,EAC9B;AAMA,WAAS,mBAAmB;AACxB,UAAM,YAAY,OAAO,YAAY;AACrC,QAAI,aAAa,UAAU,KAAK,SAAS,SAAS,GAAG;AACjD,aAAO,iBAAiB;AAAA,IAC5B;AAEA,WAAO;AAAA,EAEX;AAGA,QAAM,OAAO,QAAQ,SAAS,SAAS,iBAAiB;AASxD,WAAS,gBAAgB,OAAO,MAAM;AAClC,QAAI,CAAC,SAAS,MAAM,SAAS,MAAM;AAC/B,YAAM,IAAI,gBAAgB,KAAK;AAAA,IACnC;AAAA,EACJ;AASA,WAAS,iBAAiB,OAAOC,QAAO;AACpC,QAAI,CAAC,SAAS,CAACA,OAAM,SAAS,MAAM,IAAI,GAAG;AACvC,YAAM,IAAI,gBAAgB,KAAK;AAAA,IACnC;AAAA,EACJ;AAQA,WAAS,YAAY,OAAO,KAAK;AAE7B,WAAO,QAAQ,SAAS;AAAA,MACpB,OAAO,CAAC,MAAM,QAAQ,IAAI,MAAM;AAAA,IACpC,IAAI;AAAA,EACR;AAQA,WAAS,kBAAkB,OAAO;AAC9B,UAAM,QAAQ,YAAY,MAAM,IAAI,OAAO,MAAM,IAAI,GAAG;AACxD,UAAM,QAAQ;AAAA,MACV,KAAK,MAAM,MAAM,IAAI,MAAM,QAAQ,MAAM,IAAI,IAAI,MAAM;AAAA,MACvD;AAAA,MACA;AAAA,IACJ;AACA,UAAM,MAAM;AAAA,MACR,OAAO;AAAA,QACH,GAAG,MAAM,IAAI;AAAA,MACjB;AAAA,MACA,KAAK;AAAA,QACD,GAAG,MAAM,IAAI;AAAA,MACjB;AAAA,IACJ;AACA,UAAM,QAAQ,EAAE,KAAK,GAAG,MAAM;AAE9B,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,eAAO,MAAM;AAAA;AAAA,UAA8B;AAAA,UAAQ;AAAA,QAAK;AAAA,MAE5D,KAAK;AACD,eAAO,MAAM;AAAA;AAAA,UAA8B;AAAA,UAAQ;AAAA,QAAK;AAAA,MAE5D,KAAK;AACD,eAAO,MAAM;AAAA;AAAA,UAAgC;AAAA,UAAQ;AAAA,QAAK;AAAA,MAE9D;AACI,cAAM,IAAI,UAAU,sBAAsB,MAAM,IAAI,GAAG;AAAA,IAC/D;AAAA,EACJ;AAQA,WAAS,0BAA0B,OAAO;AACtC,UAAM,QAAQ,YAAY,MAAM,IAAI,OAAO,MAAM,IAAI,GAAG;AACxD,UAAM,aAAa,KAAK,MAAM,MAAM,IAAI,MAAM,QAAQ,MAAM,IAAI,IAAI,MAAM;AAC1E,UAAM,MAAM;AAAA,MACR,OAAO;AAAA,QACH,GAAG,MAAM,IAAI;AAAA,MACjB;AAAA,MACA,KAAK;AAAA,QACD,GAAG,MAAM,IAAI;AAAA,MACjB;AAAA,IACJ;AACA,UAAM,QAAQ,EAAE,KAAK,GAAG,MAAM;AAG9B,QAAI,MAAM,SAAS,cAAc;AAE7B,UAAI,OAAO;AAGX,UAAI,WAAW,CAAC,MAAM,OAAO,WAAW,CAAC,MAAM,KAAK;AAChD,eAAO,WAAW,CAAC;AAAA,MACvB;AAGA,aAAO,MAAM,WAAW,SAAS,KAAK,IAAI,QAAQ,UAAU;AAAA;AAAA,QAAuB;AAAA,QAAO;AAAA,MAAK;AAAA,IACnG;AAEA,WAAO,MAAM,WAAW,YAAY,KAAK;AAAA,EAC7C;AAOA,WAAS,eAAe,OAAO;AAC3B,UAAM,QAAQ,YAAY,MAAM,IAAI,OAAO,MAAM,IAAI,GAAG;AAExD,WAAO,MAAM,KAAK;AAAA,MACd,KAAK;AAAA,QACD,OAAO;AAAA,UACH,GAAG,MAAM,IAAI;AAAA,QACjB;AAAA,QACA,KAAK;AAAA,UACD,GAAG,MAAM,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAGA,WAAS,cAAc,OAAO;AAE1B,QAAI,OAAO;AACP,uBAAiB,OAAO,CAAC,UAAU,cAAc,QAAQ,CAAC;AAAA,IAC9D,OAAO;AACH,sBAAgB,OAAO,QAAQ;AAAA,IACnC;AAGA,QAAI,MAAM,MAAM,SAAS;AAAA;AAAA,MACQ,kBAAkB,KAAK;AAAA;AAAA;AAAA,MACE,0BAA0B,KAAK;AAAA;AAGzF,QAAI,UAAU,IAAI,SAAS,SAAS,IAAI,SAAS,aAAa;AAG1D,UAAI,IAAI,SAAS,IAAI;AACjB,cAAM,IAAI,gBAAgB,KAAK;AAAA,MACnC;AAEA,YAAM,MAAM,WAAW,IAAI,MAAM,EAAE,KAAK,IAAI,KAAK,GAAG,YAAY,IAAI,IAAI,OAAO,IAAI,IAAI,GAAG,EAAE,CAAC;AAAA,IACjG;AAEA,YAAQ,KAAK;AACb,oBAAgB,OAAO,OAAO;AAC9B,UAAM,QAAQ,WAAW;AACzB,UAAM,QAAQ,YAAY,IAAI,IAAI,OAAO,MAAM,IAAI,GAAG;AAEtD,WAAO,MAAM;AAAA;AAAA,MAAiD;AAAA,MAAM;AAAA,MAAO;AAAA,QACvE,KAAK;AAAA,UACD,OAAO;AAAA,YACH,GAAG,IAAI,IAAI;AAAA,UACf;AAAA,UACA,KAAK;AAAA,YACD,GAAG,MAAM,IAAI;AAAA,UACjB;AAAA,QACJ;AAAA,QACA,GAAG;AAAA,MACP;AAAA,IAAC;AAAA,EACL;AAEA,WAAS,YAAY,YAAY;AAG7B,oBAAgB,YAAY,QAAQ;AAEpC,UAAM,UAAU,CAAC;AACjB,QAAI,QAAQ,KAAK;AAEjB,QAAI,SAAS,MAAM,SAAS,UAAU;AAClC,SAAG;AAGC,gBAAQ,KAAK,cAAc,KAAK,CAAC;AAEjC,gBAAQ,KAAK;AAEb,YAAI,CAAC,OAAO;AACR,gBAAM,IAAI,cAAc,QAAQ,QAAQ,SAAO,CAAC,EAAE,IAAI,GAAG;AAAA,QAC7D;AAEA,YAAI,MAAM,SAAS,SAAS;AACxB,kBAAQ,KAAK;AASb,cAAI,SAAS,MAAM,SAAS,UAAU;AAClC;AAAA,UACJ;AAAA,QACJ,OAAO;AACH;AAAA,QACJ;AAAA,MACJ,SAAS;AAAA,IACb;AAEA,oBAAgB,OAAO,QAAQ;AAC/B,UAAM,QAAQ,YAAY,WAAW,IAAI,OAAO,MAAM,IAAI,GAAG;AAE7D,WAAO,MAAM,OAAO,SAAS;AAAA,MACzB,KAAK;AAAA,QACD,OAAO;AAAA,UACH,GAAG,WAAW,IAAI;AAAA,QACtB;AAAA,QACA,KAAK;AAAA,UACD,GAAG,MAAM,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EAEL;AAEA,WAAS,WAAW,YAAY;AAG5B,oBAAgB,YAAY,UAAU;AAEtC,UAAM,WAAW,CAAC;AAClB,QAAI,QAAQ,KAAK;AAEjB,QAAI,SAAS,MAAM,SAAS,YAAY;AAEpC,SAAG;AAGC,cAAM,QAAQ,WAAW,KAAK;AAE9B,iBAAS,KAAK,MAAM;AAAA,UAChB;AAAA,UACA,EAAE,KAAK,MAAM,IAAI;AAAA,QACrB,CAAC;AAED,gBAAQ,KAAK;AAEb,YAAI,MAAM,SAAS,SAAS;AACxB,kBAAQ,KAAK;AASb,cAAI,SAAS,MAAM,SAAS,YAAY;AACpC;AAAA,UACJ;AAAA,QACJ,OAAO;AACH;AAAA,QACJ;AAAA,MACJ,SAAS;AAAA,IACb;AAEA,oBAAgB,OAAO,UAAU;AAEjC,UAAM,QAAQ,YAAY,WAAW,IAAI,OAAO,MAAM,IAAI,GAAG;AAE7D,WAAO,MAAM,MAAM,UAAU;AAAA,MACzB,KAAK;AAAA,QACD,OAAO;AAAA,UACH,GAAG,WAAW,IAAI;AAAA,QACtB;AAAA,QACA,KAAK;AAAA,UACD,GAAG,MAAM,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EAEL;AAEA,WAAS,WAAW,OAAO;AAEvB,YAAQ,SAAS,KAAK;AAEtB,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AACD,eAAO,kBAAkB,KAAK;AAAA,MAElC,KAAK;AACD,YAAI,OAAO;AACP,cAAI,YAAY,KAAK,MAAM,MAAM,IAAI,MAAM,QAAQ,MAAM,IAAI,IAAI,MAAM;AACvE,cAAI,UAAU,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,KAAK;AAC9C,wBAAY,UAAU,MAAM,CAAC;AAAA,UACjC;AAEA,cAAI,cAAc,SAAS,cAAc,YAAY;AACjD,mBAAO,0BAA0B,KAAK;AAAA,UAC1C;AAAA,QACJ;AACA,eAAO,kBAAkB,KAAK;AAAA,MAElC,KAAK;AACD,eAAO,eAAe,KAAK;AAAA,MAE/B,KAAK;AACD,eAAO,YAAY,KAAK;AAAA,MAE5B,KAAK;AACD,eAAO,WAAW,KAAK;AAAA,MAE3B;AACI,cAAM,IAAI,gBAAgB,KAAK;AAAA,IACvC;AAAA,EAEJ;AAGA,QAAM,UAAU,WAAW;AAE3B,QAAM,kBAAkB,KAAK;AAC7B,MAAI,iBAAiB;AACjB,UAAM,IAAI,gBAAgB,eAAe;AAAA,EAC7C;AAGA,QAAM,WAAW;AAAA,IACb,KAAK;AAAA,MACD,OAAO;AAAA,QACH,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,MACZ;AAAA,MACA,KAAK;AAAA,QACD,GAAG,QAAQ,IAAI;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,QAAQ,QAAQ;AAChB,aAAS,SAAS;AAAA,EACtB;AAEA,MAAI,QAAQ,QAAQ;AAChB,aAAS,QAAQ;AAAA,MACb,SAAS,IAAI,MAAM;AAAA,MACnB,SAAS,IAAI,IAAI;AAAA,IACrB;AAAA,EACJ;AAEA,SAAO,MAAM,SAAS,SAAS,QAAQ;AAC3C;;;AC12DA,IAAM,KAAK,OAAK,OAAK,MAAM;AAC3B,IAAM,MAAM,QAAM,OAAK,CAAC,GAAG,CAAC;AAE5B,IAAM,YAAY,OAAK,OAAO,OAAO,CAAC;AAE/B,IAAM,eAAe,OAAK,MAAM;AAGvC,IAAM,WAAW,OAAK,WAAS,MAAM,YAAY;AAC1C,IAAM,kBAAkB,SAAS,UAAU;AAC3C,IAAM,eAAe,SAAS,OAAO;AACrC,IAAM,cAAc,SAAS,MAAM;AACnC,IAAM,YAAY,UACvB,QAAQ,KAAK,SACT,KAAK,OAAO;AAAA,EAAI,OACd,EAAE,YAAY,iBACV,EAAE,GAAG,EAAE,OAAO,OAAO,CAAC,GAAG,SAAS,EAAE,QAAQ,IAC5C;AACN,IACA,CAAC;AAGA,IAAM,cAAc,UAAS,QAAQ,UAAU,KAAK,QAAQ,KAAM,CAAC;AAEnE,IAAM,cAAc,YAAU,UACnC,YAAY,MAAM,EAAE,OAAO,IAAI,GAAG,IAAI,CAAC,CAAC;AAEnC,IAAM,YAAY,QAAM,QAAM,GAAG,OAAO,CAAC,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE;;;AC5B1E,oBAAmC;;;ACqBnC,IAAM,UAAU;AAOhB,SAAS,eAAe,KAAK,QAAQ;AAEnC,QAAM,WAAW;AAAA,IACf,GAAG,IAAI;AAAA,EACT;AAEA,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,GAAG,IAAI;AAAA,EACT;AACA,QAAM,aAAa;AACnB,QAAM,aAAa;AACnB,QAAM,YAAY,SAAS;AAC3B,QAAM,cAAc,SAAS;AAC7B,QAAM,UAAU,OAAO;AACvB,QAAM,YAAY,OAAO;AAEzB,QAAM,QAAQ,KAAK,IAAI,aAAa,aAAa,IAAI,CAAC;AACtD,QAAM,MAAM,KAAK,IAAI,OAAO,QAAQ,UAAU,UAAU;AACxD,QAAM,WAAW,UAAU;AAG3B,QAAM,cAAc,CAAC;AAErB,MAAI,UAAU;AACZ,aAAS,IAAI,GAAG,KAAK,UAAU,KAAK;AAClC,YAAM,aAAa,IAAI;AAEvB,UAAI,CAAC,aAAa;AAChB,oBAAY,UAAU,IAAI;AAAA,MAC5B,WAAW,MAAM,GAAG;AAClB,cAAM,eAAe,OAAO,aAAa,CAAC,EAAE;AAE5C,oBAAY,UAAU,IAAI,CAAC,aAAa,eAAe,cAAc,CAAC;AAAA,MACxE,WAAW,MAAM,UAAU;AACzB,oBAAY,UAAU,IAAI,CAAC,GAAG,SAAS;AAAA,MACzC,OAAO;AACL,cAAM,eAAe,OAAO,aAAa,CAAC,EAAE;AAE5C,oBAAY,UAAU,IAAI,CAAC,GAAG,YAAY;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,gBAAgB,WAAW;AAC7B,UAAI,aAAa;AACf,oBAAY,SAAS,IAAI,CAAC,aAAa,CAAC;AAAA,MAC1C,OAAO;AACL,oBAAY,SAAS,IAAI;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,kBAAY,SAAS,IAAI,CAAC,aAAa,YAAY,WAAW;AAAA,IAChE;AAAA,EACF;AAEA,SAAO,EAAE,OAAO,KAAK,YAAY;AACnC;AAQO,SAAS,iBAAiB,UAAU,KAAK,OAAO,CAAC,GAAG;AACzD,QAAM,QAAQ,SAAS,MAAM,OAAO;AACpC,QAAM,EAAE,OAAO,KAAK,YAAY,IAAI,eAAe,KAAK,KAAK;AAC7D,QAAM,iBAAiB,OAAO,GAAG,EAAE;AAEnC,SAAO,SACJ,MAAM,SAAS,GAAG,EAClB,MAAM,OAAO,GAAG,EAChB,IAAI,CAAC,MAAM,UAAU;AACpB,UAAM,SAAS,QAAQ,IAAI;AAC3B,UAAM,eAAe,IAAI,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,cAAc;AAC/D,UAAM,SAAS,IAAI,YAAY;AAC/B,UAAM,YAAY,YAAY,MAAM;AACpC,UAAM,iBAAiB,CAAC,YAAY,SAAS,CAAC;AAC9C,QAAI,WAAW;AACb,UAAI,aAAa;AACjB,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,cAAM,gBAAgB,KACnB,MAAM,GAAG,KAAK,IAAI,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,EACtC,QAAQ,UAAU,GAAG;AACxB,cAAM,kBAAkB,UAAU,CAAC,KAAK;AAExC,qBAAa;AAAA,UACX;AAAA,UACA,OAAO,QAAQ,OAAO,GAAG;AAAA,UACzB;AAAA,UACA;AAAA,UACA,IAAI,OAAO,eAAe;AAAA,QAC5B,EAAE,KAAK,EAAE;AAET,YAAI,kBAAkB,KAAK,SAAS;AAClC,wBAAc,MAAM,KAAK;AAAA,QAC3B;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,KAAK,SAAS,IAAI,IAAI,IAAI,KAAK;AAAA,QAC/B;AAAA,MACF,EAAE,KAAK,EAAE;AAAA,IACX,OAAO;AACL,aAAO,CAAC,KAAK,QAAQ,KAAK,SAAS,IAAI,IAAI,IAAI,KAAK,EAAE,EAAE,KAAK,EAAE;AAAA,IACjE;AAAA,EACF,CAAC,EACA,KAAK,IAAI;AACd;;;ACvIO,IAAM,cAAc,cAAY;AACrC,SAAO,SACJ,MAAM,GAAG,EACT,MAAM,CAAC,EACP,IAAI,CAAAC,aAAWA,SAAQ,MAAM,IAAI,EAAE,KAAK,GAAG,EAAE,MAAM,IAAI,EAAE,KAAK,GAAG,CAAC;AACvE;;;ACJe,SAAR,gBACL,SACA,UACA,2BACA;AACA,QAAM,WAAW,YAAY,QAAQ;AACrC,QAAM,mBAAmB,SAAS,SAAS;AAC3C,SAAO,SAAS,OAAO,CAAC,KAAKC,UAAS,QAAQ;AAC5C,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK,UAAU;AACb,cAAM,WAAW,IAAI,QAAQ;AAAA,UAC3B,WAAS,MAAM,KAAK,UAAUA;AAAA,QAChC;AACA,YAAI,SAAS,WAAW,GAAG;AACzB,gBAAM,IAAI,MAAM,0BAA0BA,QAAO,OAAO,QAAQ,EAAE;AAAA,QACpE;AAEA,cAAM,EAAE,MAAM,MAAM,IAAI,SAAS,CAAC;AAClC,eAAO,6BAA6B,QAAQ,mBACxC,OACA;AAAA,MACN;AAAA,MACA,KAAK;AACH,eAAO,IAAI,SAASA,QAAO,EAAE;AAAA,MAC/B;AAEE,gBAAQ,IAAI,GAAG;AAAA,IACnB;AAAA,EACF,GAAG,QAAQ,IAAI;AACjB;;;AC7Be,SAAR,qBAAsC,SAAS,UAAU;AAC9D,MAAI,gBAAgB;AACpB,cAAY,QAAQ,EAAE,OAAO,CAAC,KAAKC,aAAY;AAC7C,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK;AACH,cAAM,IAAI;AAAA;AAAA,MAEZ,KAAK,UAAU;AACb,yBAAiB,IAAIA,QAAO;AAC5B,cAAM,WAAW,IAAI,QAAQ;AAAA,UAC3B,WAAS,MAAM,KAAK,UAAUA;AAAA,QAChC;AACA,YAAI,SAAS,WAAW,GAAG;AACzB,gBAAM,IAAI,MAAM,0BAA0BA,QAAO,OAAO,QAAQ,EAAE;AAAA,QACpE;AACA,eAAO,SAAS,CAAC,EAAE;AAAA,MACrB;AAAA,MACA,KAAK,SAAS;AACZ,yBAAiB,IAAIA,QAAO,GAAG,YAAY,IAAI,SAASA,QAAO,CAAC,CAAC;AACjE,eAAO,IAAI,SAASA,QAAO;AAAA,MAC7B;AAAA,MACA;AAEE,gBAAQ,IAAI,GAAG;AAAA,IACnB;AAAA,EACF,GAAG,QAAQ,IAAI;AACf,SAAO;AACT;AAEA,SAAS,YAAY,KAAK;AACxB,MAAI,CAAC,OAAO,CAAC,IAAI,UAAU;AACzB,WAAO;AAAA,EACT;AACA,QAAM,OAAO,IAAI,SAAS;AAAA,IACxB,WAAS,SAAS,MAAM,QAAQ,MAAM,KAAK,UAAU;AAAA,EACvD;AAEA,MAAI,CAAC,KAAK,QAAQ;AAChB,WAAO;AAAA,EACT;AAEA,SAAQ,KAAK,CAAC,EAAE,SAAS,IAAI,KAAK,CAAC,EAAE,MAAM,KAAK,MAAO;AACzD;;;ACzCA,IAAqB,sBAArB,MAAyC;AAAA,EACvC,YACE,UAAU,EAAE,sBAAsB,MAAM,GACxC,EAAE,MAAM,QAAQ,SAAS,QAAQ,GACjC;AACA,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,YAAY,WAAW,KAAK,cAAc;AACxC,UAAM,EAAE,sBAAsB,kBAAkB,IAAI,KAAK;AACzD,UAAM,EAAE,IAAI,IAAI;AAAA,MACd,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO,IAAI;AAAA,MACX,KAAK,oBAAoB,SAAY,IAAI;AAAA,IAC3C;AAAA,EACF;AAAA,EAEA,iBAAiB,WAAW,KAAK,cAAc;AAC7C,UAAM,gBAAgB,qBAAqB,KAAK,SAAS,QAAQ;AACjE,WAAO;AAAA,EACT;AAAA,EAEA,aAAa,SAAS,WAAW,KAAK,cAAc;AAClD,WAAO,iBAAiB,KAAK,SAAS,KAAK,YAAY,QAAQ,GAAG;AAAA,MAChE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO,OAAO,KAAK,QAAQ,iBAAiB,cACxC,KAAK,QAAQ,eACb,KAAK,QAAQ;AAAA,EACnB;AAAA,EAEA,QAAQ;AACN,UAAM,IAAI;AAAA,MACR,uCAAuC,KAAK,YAAY,IAAI;AAAA,IAC9D;AAAA,EACF;AAAA,EAEA,WAAW;AACT,UAAM,IAAI;AAAA,MACR,0CAA0C,KAAK,YAAY,IAAI;AAAA,IACjE;AAAA,EACF;AACF;;;ALxDA,IAAM,eAAW,oBAAK,UAAU;AAEhC,IAAqB,0BAArB,cAAqD,oBAAoB;AAAA,EACvE,YAAY,WAAW,KAAK,cAAc;AACxC,UAAM,EAAE,MAAM,IAAI,MAAM,YAAY,QAAQ;AAC5C,WAAO,EAAE,MAAM;AAAA,EACjB;AAAA,EAEA,QAAQ;AACN,UAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AACjC,UAAM,WAAO,mBAAI,GAAG,QAAQ,IAAI,OAAO,EAAE;AACzC,UAAM,SAAS,CAAC,GAAG,IAAI;AAAA,CAAI;AAE3B,WAAO,OAAO;AAAA,MACZ,KAAK,aAAa,OAAG,uBAAQ,OAAO,eAAe,CAAC,mBAAmB;AAAA,IACzE;AAAA,EACF;AAAA,EAEA,WAAW;AACT,UAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,WAAO;AAAA,MACL,GAAG,KAAK,YAAY;AAAA,MACpB,OAAO,GAAG,KAAK,iBAAiB,CAAC,IAAI,OAAO;AAAA,MAC5C,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;;;AM9BA,IAAAC,iBAAmC;AAGnC,IAAM,0BAAsB,qBAAK,qBAAqB;AAEtD,IAAqB,gCAArB,cAA2D,oBAAoB;AAAA,EAC7E,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,SAAK,QAAQ,uBAAuB;AAAA,EACtC;AAAA,EAEA,QAAQ;AACN,UAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AACjC,UAAM,WAAO,oBAAI,GAAG,mBAAmB,IAAI,OAAO,EAAE;AACpD,UAAM,SAAS,CAAC,GAAG,IAAI;AAAA,CAAI;AAE3B,WAAO,OAAO;AAAA,MACZ,KAAK;AAAA,QACH,OAAG,wBAAQ,OAAO,kBAAkB,CAAC;AAAA,QACrC,GAAG,KAAK,YAAY,IAAI,OAAO,kBAAkB;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW;AACT,UAAM,EAAE,OAAO,IAAI,KAAK;AAExB,WAAO;AAAA,MACL,GAAG,KAAK,YAAY,GAAG,KAAK,YAAY,IAAI,OAAO,kBAAkB,EAAE;AAAA,MACvE,OAAO,GAAG,KAAK,iBAAiB,CAAC,aAC/B,OAAO,kBACT;AAAA,MACA,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;;;ACnCA,IAAAC,iBAAmC;AACnC,mBAAkB;AAClB,yBAAoB;AAGpB,IAAM,WAAO,qBAAK,MAAM;AAExB,IAAqB,sBAArB,cAAiD,oBAAoB;AAAA,EACnE,QAAQ;AACN,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ,EAAE,cAAc;AAAA,IAC1B,IAAI,KAAK;AACT,UAAM,YAAY,KAAK,cAAc;AAErC,UAAM,YAAQ,oBAAI,GAAG,IAAI,IAAI,OAAO,EAAE;AACtC,UAAM,YAAQ,oBAAI,IAAI,cAAc,KAAK,IAAI,CAAC,GAAG;AACjD,UAAM,SAAS,CAAC,OAAO,GAAG,KAAK;AAAA,CAAI;AAEnC,WAAO,OAAO;AAAA,MACZ,KAAK;AAAA,QACH,cAAc,OACV,oBAAgB,wBAAQ,SAAS,CAAC,WAClC;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW;AACT,UAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AACjC,UAAM,YAAY,KAAK,cAAc;AACrC,UAAM,gBAAgB,OAAO,cAAc,KAAK,IAAI;AAEpD,UAAM,SAAS;AAAA,MACb,GAAG,KAAK,YAAY;AAAA,MACpB,OAAO,GAAG,KAAK,iBAAiB,CAAC,IAAI,OAAO,KAAK,aAAa;AAAA,MAC9D,MAAM,KAAK;AAAA,IACb;AAEA,QAAI,cAAc,MAAM;AACtB,aAAO,aAAa,gBAAgB,SAAS;AAAA,IAC/C;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,gBAAgB;AACd,UAAM;AAAA,MACJ,QAAQ,EAAE,cAAc;AAAA,IAC1B,IAAI,KAAK;AAET,UAAM,eACJ,KAAK,iBAAiB,KAClB,KAAK,OACL,mBAAAC,QAAQ,IAAI,KAAK,MAAM,KAAK,YAAY;AAE9C,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,cACf,IAAI,YAAU;AAAA,MACb;AAAA,MACA,YAAQ,aAAAC,SAAM,OAAO,aAAa,SAAS,CAAC;AAAA,IAC9C,EAAE,EACD;AAAA,MAAK,CAAC,GAAG,MACR,EAAE,SAAS,EAAE,SAAS,IAAI,EAAE,SAAS,EAAE,SAAS,KAAK;AAAA,IACvD,EAAE,CAAC;AAEL,WAAO,cAAc,WAAW,KAC9B,UAAU,SAAS,UAAU,MAAM,SACjC,UAAU,QACV;AAAA,EACN;AACF;;;AC1EA,IAAAC,iBAAmC;AAGnC,IAAqB,yBAArB,cAAoD,oBAAoB;AAAA,EACtE,QAAQ;AACN,UAAM,EAAE,SAAS,QAAQ,IAAI,KAAK;AAClC,UAAM,WAAO,oBAAI,OAAG,qBAAK,QAAQ,YAAY,CAAC,CAAC,IAAI,OAAO,EAAE;AAC5D,UAAM,SAAS,CAAC,GAAG,IAAI;AAAA,CAAI;AAE3B,WAAO,OAAO,OAAO,KAAK,aAAa,OAAG,wBAAQ,OAAO,CAAC,IAAI,OAAO,EAAE,CAAC;AAAA,EAC1E;AAAA,EAEA,WAAW;AACT,UAAM,EAAE,SAAS,QAAQ,IAAI,KAAK;AAElC,WAAO;AAAA,MACL,GAAG,KAAK,YAAY;AAAA,MACpB,OAAO,GAAG,KAAK,iBAAiB,CAAC,KAAK,OAAO,IAAI,OAAO;AAAA,MACxD,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;;;ACJA,IAAM,sBAAsB;AAGrB,SAAS,SAAS,YAAY,CAAC,GAAG;AACvC,QAAM,OAAO,EAAE,UAAU,CAAC,EAAE;AAC5B,YAAU,QAAQ,cAAY;AAC5B,UAAM,eACJ,OAAO,SAAS,iBAAiB,cAC7B,SAAS,eACT,SAAS;AAGf,UAAM,QACJ,iBAAiB,KAAK,CAAC,EAAE,IAAI,aAAa,MAAM,mBAAmB;AACrE,aACE,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM;AAC7B,UAAI,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,KAAK,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,EAAE;AACtE,UAAI,MAAM,MAAM,SAAS,GAAG;AAC1B,YAAI,SAAS,IAAI,EAAE,OAAO,KAAK,QAAQ;AAAA,MACzC;AACA,aAAO,IAAI,SAAS,IAAI;AAAA,IAC1B,GAAG,IAAI;AAAA,EACX,CAAC;AACD,SAAO;AACT;AAEO,SAAS,sBAAsB,MAAM,QAAQ,KAAK;AAKvD,YAAU,IAAI,EAAE,QAAQ,WAAS;AAC/B,QAAI,gBAAgB,KAAK,GAAG;AAC1B,WAAK,SAAS,CAAC,KAAK;AACpB,WAAK,WAAW,CAAC;AAAA,IACnB;AAAA,EACF,CAAC;AASD,MAAI,UAAU,IAAI,EAAE,KAAK,YAAY,GAAG;AACtC,QAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,SAAS,GAAG;AACzC,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAUA,MAAI,KAAK,UAAU,KAAK,OAAO,UAAU,UAAU,IAAI,EAAE,MAAM,WAAW,GAAG;AAC3E,QACE,YAAY,MAAM,EAAE,IAAI,EAErB,OAAO,YAAY,EACnB,KAAK,SAAS,GACjB;AACA,aAAO,OAAO,SAAS,GAAG;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO,QAAQ,KAAK,QAAQ,EAAE;AAAA,IAAQ,CAAC,CAACC,MAAK,KAAK,MAChD,sBAAsB,OAAO,MAAMA,IAAG;AAAA,EACxC;AACF;AAEO,SAAS,qBAAqB,MAAM,SAAS;AAClD,QAAM,SAAS,UAAU,IAAI;AAC7B,MAAI,OAAO,UAAU,OAAO,MAAM,WAAW,GAAG;AAC9C,UAAM,eAAe,IAAI;AAAA,MACvB,UAAU,CAAC,CAAC,EAAE,OAAO,IAAI,OAAK,EAAE,OAAO,aAAa,CAAC;AAAA,IACvD;AACA,UAAM,gBAAgB,CAAC,GAAG,YAAY;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,WAAO;AAAA,MACL,IAAI;AAAA,QACF;AAAA,UACE,GAAG;AAAA,UACH,QAAQ,EAAE,cAAc;AAAA,QAC1B;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO;AAAA,MACL,OAAO,OAAO,CAAC,KAAK,UAAU;AAC5B,gBAAQ,MAAM,SAAS;AAAA,UACrB,KAAK;AACH,mBAAO,IAAI;AAAA,cACT,IAAI,8BAA8B,OAAO,OAAO;AAAA,YAClD;AAAA,UACF,KAAK;AACH,mBAAO,IAAI,OAAO,IAAI,oBAAoB,OAAO,OAAO,CAAC;AAAA,UAC3D,KAAK;AACH,mBAAO,IAAI,OAAO,IAAI,wBAAwB,OAAO,OAAO,CAAC;AAAA,UAC/D;AACE,mBAAO,IAAI,OAAO,IAAI,uBAAuB,OAAO,OAAO,CAAC;AAAA,QAChE;AAAA,MACF,GAAG,CAAC,CAAC;AAAA,IACP,EAAE,YAAY,IAAI,EAAE,IAAI,WAAS,qBAAqB,OAAO,OAAO,CAAC,CAAC;AAAA,EACxE;AACF;AAEA,IAAO,kBAAQ,CAAC,WAAW,YAAY;AACrC,QAAM,OAAO,SAAS,aAAa,CAAC,CAAC;AACrC,wBAAsB,IAAI;AAC1B,SAAO,qBAAqB,MAAM,OAAO;AAC3C;;;AZlIA,IAAO,cAAQ,CAAC,QAAQ,MAAM,QAAQ,UAAU,CAAC,MAAM;AACrD,QAAM,EAAE,SAAS,OAAO,SAAS,MAAM,OAAO,KAAK,IAAI;AAEvD,QAAM,UAAU,QAAQ,KAAK,UAAU,MAAM,MAAM,MAAM;AACzD,QAAM,UAAU,MAAM,OAAO;AAE7B,QAAM,oBAAoB,WAAS,MAAM,MAAM,EAAE,KAAK,IAAI;AAC1D,QAAM,yBAAyB,WAAS,MAAM,SAAS;AACvD,QAAM,eAAe,gBAAS,QAAQ;AAAA,IACpC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI,WAAW,OAAO;AACpB,WAAO,aAAa,IAAI,iBAAiB,EAAE,KAAK,MAAM;AAAA,EACxD,OAAO;AACL,WAAO,aAAa,IAAI,sBAAsB;AAAA,EAChD;AACF;", "names": ["exports", "module", "leven", "exports", "pointer", "c", "text", "ct", "types", "pointer", "pointer", "pointer", "import_colors", "import_colors", "pointer", "leven", "import_colors", "key"]}