{"version": 3, "file": "bin.js", "sourceRoot": "", "sources": ["../src/bin.ts"], "names": [], "mappings": ";;;;AAEA,+BAA6C;AAC7C,+BAA8B;AAC9B,iCAAiC;AACjC,2BAA2B;AAC3B,iCAKgB;AAChB,mCAA2D;AAE3D;;GAEG;AACH,SAAgB,IAAI,CAAE,OAAiB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,iBAAsC,EAAE;IACpG,MAAM,IAAI,mCACL,cAAc,GACd,GAAG,CAAC;QACL,wBAAwB;QACxB,QAAQ,EAAE,MAAM;QAChB,eAAe,EAAE,OAAO;QACxB,SAAS,EAAE,OAAO;QAClB,WAAW,EAAE,CAAC,MAAM,CAAC;QAErB,eAAe;QACf,QAAQ,EAAE,OAAO;QACjB,eAAe,EAAE,OAAO;QACxB,WAAW,EAAE,GAAG,CAAC,KAAK;QAEtB,mBAAmB;QACnB,OAAO,EAAE,MAAM;QACf,SAAS,EAAE,OAAO;QAClB,YAAY,EAAE,MAAM;QACpB,oBAAoB,EAAE,aAAK;QAC3B,WAAW,EAAE,MAAM;QACnB,sBAAsB,EAAE,CAAC,MAAM,CAAC;QAChC,UAAU,EAAE,CAAC,MAAM,CAAC;QACpB,kBAAkB,EAAE,OAAO;QAC3B,cAAc,EAAE,OAAO;QACvB,iBAAiB,EAAE,OAAO;QAC1B,UAAU,EAAE,OAAO;QACnB,gBAAgB,EAAE,OAAO;QACzB,eAAe,EAAE,OAAO;QACxB,kBAAkB,EAAE,OAAO;QAC3B,aAAa,EAAE,OAAO;QACtB,QAAQ,EAAE,OAAO;QAEjB,WAAW;QACX,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,kBAAkB;QACxB,IAAI,EAAE,iBAAiB;QACvB,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,sBAAsB;QAC5B,IAAI,EAAE,oBAAoB;KAC3B,EAAE;QACD,IAAI;QACJ,gBAAgB,EAAE,IAAI;KACvB,CAAC,CACH,CAAA;IAED,+CAA+C;IAC/C,4EAA4E;IAC5E,YAAY;IACZ,MAAM,EACJ,OAAO,EAAE,GAAG,EACZ,QAAQ,EAAE,IAAI,GAAG,KAAK,EACtB,eAAe,EAAE,UAAU,GAAG,KAAK,EACnC,WAAW,EAAE,OAAO,GAAG,CAAC,EACxB,WAAW,EAAE,WAAW,GAAG,EAAE,EAC7B,QAAQ,EAAE,IAAI,GAAG,SAAS,EAC1B,SAAS,EAAE,KAAK,GAAG,KAAK,EACxB,eAAe,EAAE,WAAW,GAAG,KAAK,EACpC,SAAS,EAAE,KAAK,EAChB,YAAY,EAAE,QAAQ,EACtB,oBAAoB,EAAE,eAAe,EACrC,WAAW,EAAE,OAAO,EACpB,sBAAsB,EAAE,iBAAiB,EACzC,UAAU,EAAE,MAAM,EAClB,kBAAkB,EAAE,aAAa,EACjC,cAAc,EAAE,SAAS,EACzB,iBAAiB,EAAE,YAAY,EAC/B,UAAU,EAAE,MAAM,EAClB,gBAAgB,EAAE,WAAW,EAC7B,eAAe,EAAE,UAAU,EAC3B,kBAAkB,EAAE,YAAY,EAChC,aAAa,EAAE,QAAQ,EACvB,QAAQ,EAAE,IAAI,EACf,GAAG,IAAI,CAAA;IAER,IAAI,IAAI,EAAE;QACR,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8Bb,CAAC,CAAA;QAEA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;IAED,8BAA8B;IAC9B,IAAI,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,CAAC,GAAG,CAAC,IAAI,eAAO,EAAE,CAAC,CAAA;QAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;IAED,MAAM,GAAG,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAA;IAChC,wFAAwF;IACxF,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IACtE,MAAM,KAAK,GAAG,IAAI,gBAAS,CAAC,UAAU,IAAI,WAAI,CAAC,GAAG,EAAE,oBAAa,CAAC,CAAC,CAAA;IACnE,MAAM,WAAW,GAAG,iBAAU,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;IACzC,MAAM,EAAE,oBAAoB,EAAE,GAAG,WAAW,CAAA;IAE5C,6CAA6C;IAC7C,MAAM,OAAO,GAAG,gBAAQ,CAAC;QACvB,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,UAAU,EAAE,UAAU,CAAC;QACxC,IAAI;QACJ,KAAK;QACL,MAAM;QACN,aAAa;QACb,SAAS;QACT,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,QAAQ;QACR,OAAO;QACP,WAAW;QACX,UAAU;QACV,QAAQ;QACR,iBAAiB;QACjB,eAAe;QACf,OAAO,EAAE,WAAW;QACpB,QAAQ,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;QACxE,UAAU,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;KAC7E,CAAC,CAAA;IAEF,0EAA0E;IAC1E,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;IAE/B,8BAA8B;IAC9B,IAAI,OAAO,IAAI,CAAC,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,YAAY,eAAO,EAAE,CAAC,CAAA;QAClC,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;QACtC,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA;QAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;IAED,iDAAiD;IACjD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACrC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAA;IAC5B,MAAM,CAAC,KAAK,GAAI,MAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;IAEpD,0DAA0D;IAC1D,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAA;IACnG,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAEjF,4DAA4D;IAC5D,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,WAAW,EAAE;QACtC,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;KAC9C;SAAM;QACL,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;YACjB,MAAM,CAAC,OAAO,EAAE,CAAA;SACjB;aAAM;YACL,uEAAuE;YACvE,iCAAiC;YACjC,IAAI,WAAW,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE;gBACtC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;aACxB;iBAAM;gBACL,IAAI,MAAM,GAAG,IAAI,IAAI,EAAE,CAAA;gBACvB,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,MAAM,IAAI,KAAK,CAAC,CAAA;gBAC5D,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAA;aAC/E;SACF;KACF;AACH,CAAC;AAjMD,oBAiMC;AAED;;GAEG;AACH,SAAS,MAAM,CAAE,GAAY,EAAE,UAAoB,EAAE,UAAmB;IACtE,6CAA6C;IAC7C,IAAI,UAAU,EAAE;QACd,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,SAAS,CAAC,4EAA4E,CAAC,CAAA;SAClG;QAED,IAAI,GAAG,EAAE;YACP,MAAM,IAAI,SAAS,CAAC,6CAA6C,CAAC,CAAA;SACnE;QAED,mEAAmE;QACnE,2FAA2F;QAC3F,wGAAwG;QACxG,sFAAsF;QACtF,6DAA6D;QAC7D,6EAA6E;QAC7E,yEAAyE;QACzE,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;QAC3C,MAAM,wBAAwB,GAAa,EAAE,CAAA;QAC7C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,EAAE,sBAAsB;gBACpE,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAClC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,cAAY,CAAC,CAAA,CAAC,sBAAsB;aAC/D;SACF;QACD,IAAI;YACF,OAAO,cAAO,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAA;SAC5C;gBAAS;YACR,KAAK,MAAM,GAAG,IAAI,wBAAwB,EAAE;gBAC1C,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA,CAAC,sBAAsB;aACtD;SACF;KACF;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAE,WAAwB,EAAE,MAAc,EAAE,IAAY,EAAE,SAAkB;IAC9F,IAAI,MAAW,CAEd;IAAC,MAAc,CAAC,UAAU,GAAG,MAAM,CAAC,QAAQ,CAC5C;IAAC,MAAc,CAAC,SAAS,GAAG,cAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CACpD;IAAC,MAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CACxC;IAAC,MAAc,CAAC,MAAM,GAAG,MAAM,CAC/B;IAAC,MAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAEtD,IAAI;QACF,MAAM,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;KACpC;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,eAAO,EAAE;YAC5B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;SAChB;QAED,MAAM,KAAK,CAAA;KACZ;IAED,IAAI,SAAS,EAAE;QACb,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAO,CAAC,MAAM,CAAC,CAAC,CAAA;KACnE;AACH,CAAC;AAED,4BAA4B;AAC5B,SAAS,cAAc,CAAE,MAAW,EAAE,QAAgB;IACpD,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;AAC/D,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;IAC3B,IAAI,EAAE,CAAA;CACP", "sourcesContent": ["#!/usr/bin/env node\n\nimport { join, resolve, dirname } from 'path'\nimport { inspect } from 'util'\nimport Module = require('module')\nimport arg = require('arg')\nimport {\n  EVAL_FILENAME,\n  EvalState,\n  createRepl,\n  ReplService\n } from './repl'\nimport { VERSION, TSError, parse, register } from './index'\n\n/**\n * Main `bin` functionality.\n */\nexport function main (argv: string[] = process.argv.slice(2), entrypointArgs: Record<string, any> = {}) {\n  const args = {\n    ...entrypointArgs,\n    ...arg({\n      // Node.js-like options.\n      '--eval': String,\n      '--interactive': Boolean,\n      '--print': Boolean,\n      '--require': [String],\n\n      // CLI options.\n      '--help': <PERSON><PERSON>an,\n      '--script-mode': <PERSON><PERSON><PERSON>,\n      '--version': arg.COUNT,\n\n      // Project options.\n      '--dir': String,\n      '--files': <PERSON><PERSON>an,\n      '--compiler': String,\n      '--compiler-options': parse,\n      '--project': String,\n      '--ignore-diagnostics': [String],\n      '--ignore': [String],\n      '--transpile-only': <PERSON><PERSON><PERSON>,\n      '--type-check': <PERSON><PERSON><PERSON>,\n      '--compiler-host': <PERSON>olean,\n      '--pretty': <PERSON>olean,\n      '--skip-project': Boolean,\n      '--skip-ignore': Boolean,\n      '--prefer-ts-exts': Boolean,\n      '--log-error': Boolean,\n      '--emit': Boolean,\n\n      // Aliases.\n      '-e': '--eval',\n      '-i': '--interactive',\n      '-p': '--print',\n      '-r': '--require',\n      '-h': '--help',\n      '-s': '--script-mode',\n      '-v': '--version',\n      '-T': '--transpile-only',\n      '-H': '--compiler-host',\n      '-I': '--ignore',\n      '-P': '--project',\n      '-C': '--compiler',\n      '-D': '--ignore-diagnostics',\n      '-O': '--compiler-options'\n    }, {\n      argv,\n      stopAtPositional: true\n    })\n  }\n\n  // Only setting defaults for CLI-specific flags\n  // Anything passed to `register()` can be `undefined`; `create()` will apply\n  // defaults.\n  const {\n    '--dir': dir,\n    '--help': help = false,\n    '--script-mode': scriptMode = false,\n    '--version': version = 0,\n    '--require': argsRequire = [],\n    '--eval': code = undefined,\n    '--print': print = false,\n    '--interactive': interactive = false,\n    '--files': files,\n    '--compiler': compiler,\n    '--compiler-options': compilerOptions,\n    '--project': project,\n    '--ignore-diagnostics': ignoreDiagnostics,\n    '--ignore': ignore,\n    '--transpile-only': transpileOnly,\n    '--type-check': typeCheck,\n    '--compiler-host': compilerHost,\n    '--pretty': pretty,\n    '--skip-project': skipProject,\n    '--skip-ignore': skipIgnore,\n    '--prefer-ts-exts': preferTsExts,\n    '--log-error': logError,\n    '--emit': emit\n  } = args\n\n  if (help) {\n    console.log(`\n  Usage: ts-node [options] [ -e script | script.ts ] [arguments]\n\n  Options:\n\n    -e, --eval [code]              Evaluate code\n    -p, --print                    Print result of \\`--eval\\`\n    -r, --require [path]           Require a node module before execution\n    -i, --interactive              Opens the REPL even if stdin does not appear to be a terminal\n\n    -h, --help                     Print CLI usage\n    -v, --version                  Print module version information\n    -s, --script-mode              Use cwd from <script.ts> instead of current directory\n\n    -T, --transpile-only           Use TypeScript's faster \\`transpileModule\\`\n    -H, --compiler-host            Use TypeScript's compiler host API\n    -I, --ignore [pattern]         Override the path patterns to skip compilation\n    -P, --project [path]           Path to TypeScript JSON project file\n    -C, --compiler [name]          Specify a custom TypeScript compiler\n    -D, --ignore-diagnostics [code] Ignore TypeScript warnings by diagnostic code\n    -O, --compiler-options [opts]   JSON object to merge with compiler options\n\n    --dir                          Specify working directory for config resolution\n    --scope                        Scope compiler to files within \\`cwd\\` only\n    --files                        Load \\`files\\`, \\`include\\` and \\`exclude\\` from \\`tsconfig.json\\` on startup\n    --pretty                       Use pretty diagnostic formatter (usually enabled by default)\n    --skip-project                 Skip reading \\`tsconfig.json\\`\n    --skip-ignore                  Skip \\`--ignore\\` checks\n    --prefer-ts-exts               Prefer importing TypeScript files over JavaScript files\n    --log-error                    Logs TypeScript errors to stderr instead of throwing exceptions\n  `)\n\n    process.exit(0)\n  }\n\n  // Output project information.\n  if (version === 1) {\n    console.log(`v${VERSION}`)\n    process.exit(0)\n  }\n\n  const cwd = dir || process.cwd()\n  /** Unresolved.  May point to a symlink, not realpath.  May be missing file extension */\n  const scriptPath = args._.length ? resolve(cwd, args._[0]) : undefined\n  const state = new EvalState(scriptPath || join(cwd, EVAL_FILENAME))\n  const replService = createRepl({ state })\n  const { evalAwarePartialHost } = replService\n\n  // Register the TypeScript compiler instance.\n  const service = register({\n    dir: getCwd(dir, scriptMode, scriptPath),\n    emit,\n    files,\n    pretty,\n    transpileOnly,\n    typeCheck,\n    compilerHost,\n    ignore,\n    preferTsExts,\n    logError,\n    project,\n    skipProject,\n    skipIgnore,\n    compiler,\n    ignoreDiagnostics,\n    compilerOptions,\n    require: argsRequire,\n    readFile: code !== undefined ? evalAwarePartialHost.readFile : undefined,\n    fileExists: code !== undefined ? evalAwarePartialHost.fileExists : undefined\n  })\n\n  // Bind REPL service to ts-node compiler service (chicken-and-egg problem)\n  replService.setService(service)\n\n  // Output project information.\n  if (version >= 2) {\n    console.log(`ts-node v${VERSION}`)\n    console.log(`node ${process.version}`)\n    console.log(`compiler v${service.ts.version}`)\n    process.exit(0)\n  }\n\n  // Create a local module instance based on `cwd`.\n  const module = new Module(state.path)\n  module.filename = state.path\n  module.paths = (Module as any)._nodeModulePaths(cwd)\n\n  // Prepend `ts-node` arguments to CLI for child processes.\n  process.execArgv.unshift(__filename, ...process.argv.slice(2, process.argv.length - args._.length))\n  process.argv = [process.argv[1]].concat(scriptPath || []).concat(args._.slice(1))\n\n  // Execute the main contents (either eval, script or piped).\n  if (code !== undefined && !interactive) {\n    evalAndExit(replService, module, code, print)\n  } else {\n    if (args._.length) {\n      Module.runMain()\n    } else {\n      // Piping of execution _only_ occurs when no other script is specified.\n      // --interactive flag forces REPL\n      if (interactive || process.stdin.isTTY) {\n        replService.start(code)\n      } else {\n        let buffer = code || ''\n        process.stdin.on('data', (chunk: Buffer) => buffer += chunk)\n        process.stdin.on('end', () => evalAndExit(replService, module, buffer, print))\n      }\n    }\n  }\n}\n\n/**\n * Get project path from args.\n */\nfunction getCwd (dir?: string, scriptMode?: boolean, scriptPath?: string) {\n  // Validate `--script-mode` usage is correct.\n  if (scriptMode) {\n    if (!scriptPath) {\n      throw new TypeError('Script mode must be used with a script name, e.g. `ts-node -s <script.ts>`')\n    }\n\n    if (dir) {\n      throw new TypeError('Script mode cannot be combined with `--dir`')\n    }\n\n    // Use node's own resolution behavior to ensure we follow symlinks.\n    // scriptPath may omit file extension or point to a directory with or without package.json.\n    // This happens before we are registered, so we tell node's resolver to consider ts, tsx, and jsx files.\n    // In extremely rare cases, is is technically possible to resolve the wrong directory,\n    // because we do not yet know preferTsExts, jsx, nor allowJs.\n    // See also, justification why this will not happen in real-world situations:\n    // https://github.com/TypeStrong/ts-node/pull/1009#issuecomment-613017081\n    const exts = ['.js', '.jsx', '.ts', '.tsx']\n    const extsTemporarilyInstalled: string[] = []\n    for (const ext of exts) {\n      if (!hasOwnProperty(require.extensions, ext)) { // tslint:disable-line\n        extsTemporarilyInstalled.push(ext)\n        require.extensions[ext] = function() {} // tslint:disable-line\n      }\n    }\n    try {\n      return dirname(require.resolve(scriptPath))\n    } finally {\n      for (const ext of extsTemporarilyInstalled) {\n        delete require.extensions[ext] // tslint:disable-line\n      }\n    }\n  }\n\n  return dir\n}\n\n/**\n * Evaluate a script.\n */\nfunction evalAndExit (replService: ReplService, module: Module, code: string, isPrinted: boolean) {\n  let result: any\n\n  ;(global as any).__filename = module.filename\n  ;(global as any).__dirname = dirname(module.filename)\n  ;(global as any).exports = module.exports\n  ;(global as any).module = module\n  ;(global as any).require = module.require.bind(module)\n\n  try {\n    result = replService.evalCode(code)\n  } catch (error) {\n    if (error instanceof TSError) {\n      console.error(error)\n      process.exit(1)\n    }\n\n    throw error\n  }\n\n  if (isPrinted) {\n    console.log(typeof result === 'string' ? result : inspect(result))\n  }\n}\n\n/** Safe `hasOwnProperty` */\nfunction hasOwnProperty (object: any, property: string): boolean {\n  return Object.prototype.hasOwnProperty.call(object, property)\n}\n\nif (require.main === module) {\n  main()\n}\n"]}