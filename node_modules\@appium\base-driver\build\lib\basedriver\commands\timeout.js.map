{"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["../../../../lib/basedriver/commands/timeout.ts"], "names": [], "mappings": ";;;;;AAAA,uCAA0C;AAC1C,oDAAuB;AACvB,6CAAqC;AACrC,6CAAsC;AAGtC,mCAA8B;AAO9B,MAAM,WAAW,GAAG,CAAC,CAAC;AAEtB,MAAM,eAAe,GAAqB;IACxC,KAAK,CAAC,QAAQ,CAA6C,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ;QAC7F,IAAI,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,cAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,EAAE,EAAC,CAAC,GAAG,CAAC,CAAC;YAE5E,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,SAAS;oBACZ,MAAM,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;oBACjC,OAAO;gBACT,KAAK,UAAU;oBACb,MAAM,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;oBACnC,OAAO;gBACT,KAAK,WAAW;oBACd,MAAM,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;oBACtC,OAAO;gBACT,KAAK,QAAQ;oBACX,MAAM,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;oBACpC,OAAO;gBACT;oBACE,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,6CAA6C,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,yBAAyB,IAAI,CAAC,SAAS,CAAC;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;SACT,CAAC,GAAG,CACN,CAAC;QACF,IAAI,cAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,mBAAmB;YACjC,QAAQ,EAAE,IAAI,CAAC,cAAc;SAC9B,CAAC;IACJ,CAAC;IAED,WAAW;IACX,KAAK,CAAC,eAAe,CAA6C,EAAE;QAClE,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,mBAAmB,CAA6C,EAAE;QACtE,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,YAAY,CAA6C,EAAE;QAC/D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,CAAC;IAED,WAAW;IACX,6DAA6D;IAC7D,KAAK,CAAC,kBAAkB,CAA6C,EAAE;QACrE,MAAM,IAAI,iBAAM,CAAC,mBAAmB,CAAC,mCAAmC,CAAC,CAAC;IAC5E,CAAC;IAED,6DAA6D;IAC7D,KAAK,CAAC,sBAAsB,CAA6C,EAAE;QACzE,MAAM,IAAI,iBAAM,CAAC,mBAAmB,CAAC,mCAAmC,CAAC,CAAC;IAC5E,CAAC;IAED,SAAS;IACT,6DAA6D;IAC7D,KAAK,CAAC,gBAAgB,CAA6C,EAAE;QACnE,MAAM,IAAI,iBAAM,CAAC,mBAAmB,CAAC,iCAAiC,CAAC,CAAC;IAC1E,CAAC;IAED,6DAA6D;IAC7D,KAAK,CAAC,oBAAoB,CAA6C,EAAE;QACvE,MAAM,IAAI,iBAAM,CAAC,mBAAmB,CAAC,iCAAiC,CAAC,CAAC;IAC1E,CAAC;IAED,UAAU;IACV,KAAK,CAAC,iBAAiB,CAA6C,EAAE;QACpE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,eAAe,CAA6C,EAAU;QAEpE,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;QAC/C,IAAI,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC3D,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzC,IAAI,gBAAC,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;oBACzC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,oBAAoB,CAA6C,EAAU;QACzE,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,8BAA8B,EAAE,IAAI,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACjE,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzC,IAAI,gBAAC,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,CAAC;oBAC9C,MAAM,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,wBAAwB,CAE5B,MAAwC;QAExC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,cAAc,mBAAmB,CAAC,CAAC;QACxE,MAAM,aAAa,GAAG,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;YAC7C,wBAAwB;YACxB,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAEpC,OAAO,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC;QACF,OAAO,MAAM,IAAA,2BAAgB,EAAC,aAAa,EAAE;YAC3C,MAAM,EAAE,IAAI,CAAC,cAAc;YAC3B,UAAU,EAAE,GAAG;YACf,MAAM,EAAE,IAAI,CAAC,GAAG;SACjB,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB,CAA6C,EAAmB;QAClF,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1C,IAAI,gBAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;YAChD,MAAM,IAAI,iBAAM,CAAC,YAAY,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAC;AAEF,IAAA,aAAK,EAAC,eAAe,CAAC,CAAC"}