{"version": 3, "file": "log.d.ts", "sourceRoot": "", "sources": ["../../lib/log.ts"], "names": [], "mappings": "AACA,OAAO,EAAC,YAAY,EAAC,MAAM,aAAa,CAAC;AAMzC,OAAO,KAAK,EACV,aAAa,EACb,WAAW,EACX,MAAM,EACN,QAAQ,EACR,4BAA4B,EAC5B,gBAAgB,EACjB,MAAM,SAAS,CAAC;AACjB,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,aAAa,CAAC;AAC1C,OAAO,EAAC,iBAAiB,EAAC,MAAM,kBAAkB,CAAC;AAEnD,OAAO,EAEL,wBAAwB,EACzB,MAAM,8BAA8B,CAAC;AAgBtC,QAAA,MAAM,qBAAqB,yCAAyC,CAAC;AAIrE,qBAAa,GAAI,SAAQ,YAAa,YAAW,MAAM;IACrD,KAAK,EAAE,QAAQ,GAAG,MAAM,CAAC;IACzB,WAAW,EAAE,WAAW,CAAC;IACzB,YAAY,EAAE,WAAW,CAAC;IAC1B,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,QAAQ,CAAC;IAEjB,aAAa,EAAE,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IACtD,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,OAAO,EAAE,aAAa,EAAE,CAAC;IACzB,MAAM,EAAE,MAAM,CAAC,QAAQ,GAAG,MAAM,EAAE,WAAW,GAAG,SAAS,CAAC,CAAC;IAC3D,OAAO,EAAE,MAAM,CAAC,QAAQ,GAAG,MAAM,EAAE,MAAM,CAAC,CAAC;IAC3C,KAAK,EAAE,MAAM,CAAC,QAAQ,GAAG,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;IAClD,GAAG,EAAE,MAAM,CAAC;IACZ,OAAO,EAAE,OAAO,CAAC;IACjB,yBAAyB,EAAE,wBAAwB,CAAC;IAEpD,OAAO,CAAC,QAAQ,CAAkC;IAClD,OAAO,CAAC,cAAc,CAAS;;IA2B/B,IAAI,MAAM,IAAI,aAAa,EAAE,CAE5B;IAED,IAAI,aAAa,IAAI,MAAM,CAE1B;IAED,IAAI,aAAa,CAAC,KAAK,EAAE,MAAM,EAW9B;IAED,OAAO,CAAC,QAAQ;IAOhB,IAAI,YAAY,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAEzD;IAED,kBAAkB,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI;IAa5E,WAAW,IAAI,IAAI;IAInB,YAAY,IAAI,IAAI;IAKpB,aAAa,IAAI,IAAI;IACrB,cAAc,IAAI,IAAI;IACtB,cAAc,IAAI,IAAI;IACtB,eAAe,IAAI,IAAI;IACvB,eAAe,IAAI,OAAO;IAI1B;;OAEG;IACH,KAAK,IAAI,IAAI;IAIb,MAAM,IAAI,IAAI;IAcd,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAIzD,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAI3D,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAIzD,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAIxD,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAI1D,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAIxD,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAI1D,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAIxD,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAIzD,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAI1D,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IAY5E;;;;;;OAMG;IACH,GAAG,CAAC,KAAK,EAAE,QAAQ,GAAG,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAwCjF;;;;;;;;;;;OAWG;IACG,kCAAkC,CACtC,aAAa,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,gBAAgB,GAClD,OAAO,CAAC,4BAA4B,CAAC;IAQxC,OAAO,CAAC,OAAO;IAuCf,OAAO,CAAC,OAAO;IAqCf,OAAO,CAAC,KAAK;IAWb,OAAO,CAAC,iBAAiB;IAQzB,OAAO,CAAC,kBAAkB;IA0B1B,OAAO,CAAC,aAAa;IACrB,OAAO,CAAC,YAAY;CACrB;AAED,wBAAgB,aAAa,CAAC,CAAC,GAAC,GAAG,EAAE,UAAU,EAAE,CAAC,GAAG;IAAC,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAA;CAAC,CAEhF;AAOD,eAAO,MAAM,UAAU,KAAY,CAAC;AACpC,eAAe,UAAU,CAAC"}