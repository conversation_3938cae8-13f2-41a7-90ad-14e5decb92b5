{"version": 3, "file": "css-converter.js", "sourceRoot": "", "sources": ["../../lib/css-converter.js"], "names": [], "mappings": ";;;;;AAAA,6DAAmD;AACnD,oDAAuB;AACvB,0CAAuC;AACvC,sDAA2B;AAE3B,MAAM,gBAAgB,GAAG,IAAA,kCAAY,EAAC;IACpC,MAAM,EAAE;QACN,aAAa,EAAE;YACb,OAAO,EAAE,QAAQ;YACjB,WAAW,EAAE;gBACX,QAAQ,EAAE,CAAC,KAAK,CAAC;aAClB;SACF;QACD,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC5B,UAAU,EAAE;YACV,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;SACzC;QACD,GAAG,EAAE,IAAI;QACT,UAAU,EAAE,IAAI;QAChB,GAAG,EAAE;YACH,QAAQ,EAAE,IAAI;SACf;KACF;IACD,WAAW,EAAE,IAAI;CAClB,CAAC,CAAC;AAEH,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC,MAAM,kBAAkB,GAAG,qCAAqC,CAAC;AAEjE,MAAM,aAAa,GAAG;IACpB,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW;IAC3D,SAAS,EAAE,gBAAgB,EAAE,YAAY,EAAE,UAAU;CACtD,CAAC;AAEF,MAAM,aAAa,GAAG;IACpB,OAAO,EAAE,UAAU;CACpB,CAAC;AAEF,MAAM,SAAS,GAAG;IAChB,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc;CACjE,CAAC;AAEF,MAAM,SAAS,GAAG;IAChB,GAAG,aAAa;IAChB,GAAG,aAAa;IAChB,GAAG,SAAS;CACb,CAAC;AAEF,mCAAmC;AACnC,MAAM,iBAAiB,GAAG;IACxB,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC,aAAa,EAAE;YACd,qBAAqB,EAAE,cAAc;YACrC,MAAM,EAAE,kBAAkB;SAC3B,CAAC;IACF,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,CAAC;CACzB,CAAC;AAEF;;;;;GAKG;AACH,SAAS,WAAW,CAAE,GAAG;IACvB,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;IACrG,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAED;;;;;;GAMG;AACH,SAAS,cAAc,CAAE,GAAG;IAC1B,qCAAqC;IACrC,MAAM,GAAG,GAAG,gBAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,yFAAyF;IAC9J,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACpC,OAAO,GAAG,CAAC;IACb,CAAC;IACD,wCAAwC;IACxC,MAAM,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,0CAA0C,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AACtF,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,iBAAiB,CAAE,SAAS;IACnC,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAE9C,kDAAkD;IAClD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QACjC,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC;IAED,0DAA0D;IAC1D,KAAK,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,IAAI,iBAAiB,EAAE,CAAC;QAC3D,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,OAAO,YAAY,CAAC;QACtB,CAAC;IACH,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,IAAI,QAAQ,8BAA8B;QACxD,6BAA6B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAAE,IAAI;IAChC,OAAO,WAAW,gBAAC,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;AACnD,CAAC;AAGD,MAAM,YAAY;IAEhB,YAAa,QAAQ,EAAE,GAAG;QACxB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACH,eAAe,CAAE,OAAO;QACtB,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;YACrC,CAAC,CAAC,OAAO;YACT,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,SAAS,OAAO,OAAO,EAAE,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAE,OAAO;QAChB,qCAAqC;QACrC,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;QACvC,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,SAAS,6BAA6B;gBACxE,iEAAiE,SAAS,GAAG,CAAC,CAAC;QACnF,CAAC;QACD,MAAM,QAAQ,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEzC,2CAA2C;QAC3C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,IAAI,QAAQ,+CAA+C;gBACzE,IAAI,CAAC,GAAG,SAAS,EAAE,GAAG,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxD,CAAC;QAED,6CAA6C;QAC7C,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,UAAU,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC;QACtD,CAAC;QAED,4BAA4B;QAC5B,IAAI,KAAK,GAAG,SAAS,IAAI,EAAE,CAAC;QAC5B,IAAI,QAAQ,KAAK,WAAW,EAAE,CAAC;YAC7B,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YACjB,OAAO,IAAI,UAAU,aAAa,CAAC;QACrC,CAAC;QAED,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,GAAG;gBACN,OAAO,IAAI,UAAU,KAAK,KAAK,IAAI,CAAC;YACtC,KAAK,IAAI;gBACP,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/C,OAAO,IAAI,UAAU,aAAa,KAAK,IAAI,CAAC;gBAC9C,CAAC;gBACD,OAAO,IAAI,UAAU,YAAY,gBAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC;YAC7D,KAAK,IAAI;gBACP,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/C,OAAO,IAAI,UAAU,eAAe,KAAK,IAAI,CAAC;gBAChD,CAAC;gBACD,OAAO,IAAI,UAAU,aAAa,gBAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC;YAC9D,KAAK,IAAI;gBACP,OAAO,IAAI,UAAU,YAAY,gBAAC,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC;YAC9D,KAAK,IAAI;gBACP,OAAO,IAAI,UAAU,YAAY,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC;YAClE;gBACE,sEAAsE;gBACtE,MAAM,IAAI,KAAK,CAAC,uCAAuC,OAAO,CAAC,QAAQ,KAAK;oBAC1E,gDAAgD,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,WAAW,CAAE,SAAS;QACpB,wCAAwC;QACxC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC;QAC3C,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,IAAI,SAAS,CAAC,IAAI,IAAI,QAAQ,KAAK;gBACjD,wCAAwC,QAAQ,8CAA8C,CAAC,CAAC;QACpG,CAAC;QAED,MAAM,UAAU,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAEhD,IAAI,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC;QACrE,CAAC;QAED,IAAI,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,UAAU,IAAI,QAAQ,GAAG,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,YAAY,CAAE,OAAO;QACnB,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,mCAAmC;gBACvE,oEAAoE,CAAC,CAAC;QAC1E,CAAC;QAED,uBAAuB;QACvB,MAAM,mBAAmB,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACjD,2DAA2D;QAC3D,8BAA8B;QAC9B,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAC,IAAI,EAAC,EAAE,EAAE,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;QAC7E,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,EAAC,IAAI,EAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QACvD,iEAAiE;QACjE,8BAA8B;QAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAC,IAAI,EAAC,EAAE,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAClE,MAAM,OAAO,GAAG,MAAM,EAAE,IAAI,CAAC;QAC7B,IAAI,OAAO,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,CAAC,OAAO,CAAC,CAAC;YAC/B,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACtB,KAAK,MAAM,aAAa,IAAI,UAAU,EAAE,CAAC;oBACvC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnC,CAAC;gBACD,mBAAmB,CAAC,IAAI,CAAC,eAAe,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,mBAAmB,CAAC,IAAI,CAAC,sBAAsB,OAAO,IAAI,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YAC7B,mBAAmB,CAAC,IAAI,CAAC,sBAAsB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7E,CAAC;QACD,oDAAoD;QACpD,8BAA8B;QAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAC,IAAI,EAAC,EAAE,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAC/D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,EAAC,IAAI,EAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7E,CAAC;QACD,2DAA2D;QAC3D,8BAA8B;QAC9B,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAC,IAAI,EAAC,EAAE,EAAE,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;QAC1E,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,6DAA6D;QAC7D,8BAA8B;QAC9B,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAC,IAAI,EAAC,EAAE,EAAE,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC;QAC/E,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACnC,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,GAAG,EAAE,CAAC;gBACR,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QACD,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACvF,CAAC;QACD,OAAO,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAE,GAAG;QACjB,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC1E,CAAC;IAED;;;;OAIG;IACH,qBAAqB;QACnB,IAAI,MAAM,CAAC;QACX,IAAI,CAAC;YACH,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,gBAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACnB,MAAM,IAAI,eAAM,CAAC,oBAAoB,CAAC,yBAAyB,IAAI,CAAC,QAAQ,eAAe,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;QAC3G,CAAC;QACD,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,gBAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACnB,MAAM,IAAI,eAAM,CAAC,oBAAoB,CAAC,6BAA6B,IAAI,CAAC,QAAQ,eAAe,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;QAC/G,CAAC;IACH,CAAC;CACF;AAED,kBAAe,YAAY,CAAC"}