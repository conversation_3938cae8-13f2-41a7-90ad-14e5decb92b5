/**
 * Retrieves the text content of the device's clipboard.
 * The method works for Android below and above 29.
 * It temorarily enforces the IME setting in order to workaround
 * security limitations if needed.
 * This method only works if Appium Settings v. 2.15+ is installed
 * on the device under test
 *
 * @this {import('../client').SettingsApp}
 * @returns {Promise<string>} The actual content of the main clipboard as
 * base64-encoded string or an empty string if the clipboard is empty
 * @throws {Error} If there was a problem while getting the
 * clipboard contant
 */
export function getClipboard(this: import("../client").SettingsApp): Promise<string>;
//# sourceMappingURL=clipboard.d.ts.map