{"version": 3, "file": "gestures.js", "sourceRoot": "", "sources": ["../../../lib/commands/gestures.js"], "names": [], "mappings": ";;;;;AAiBA,gDASC;AAiBD,wDAUC;AAcD,4DASC;AAmBD,8CAkBC;AAsBD,gDAqBC;AAqBD,0DAmBC;AAqBD,wDAmBC;AAuBD,gDAqBC;AAwBD,kDAuBC;AAmBD,gDAaC;AA0BD,oCAmBC;AApZD,4CAAoC;AACpC,oDAAuB;AACvB,0CAAqC;AAErC;;;;;;;;;;;;GAYG;AACI,KAAK,UAAU,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC;IACtD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,wBAAwB,EACxB,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;KACtB,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACI,KAAK,UAAU,sBAAsB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ;IACpE,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,6BAA6B,EAC7B,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,QAAQ;KACT,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;GAWG;AACI,KAAK,UAAU,wBAAwB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC;IAC5D,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,+BAA+B,EAC/B,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;KACtB,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACI,KAAK,UAAU,iBAAiB,CACrC,SAAS,EACT,MAAM,EACN,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,KAAK;IAEL,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,uBAAuB,EACvB,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;QAC9B,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;QACxB,KAAK;KACN,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACI,KAAK,UAAU,kBAAkB,CACtC,SAAS,EACT,SAAS,EACT,IAAI,EACJ,GAAG,EACH,KAAK,EACL,MAAM,EACN,KAAK;IAEL,OAAO,sBAAsB,CAAC,CAC5B,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,wBAAwB,EACxB,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC;QACtC,SAAS;QACT,KAAK;KACN,CACF,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACI,KAAK,UAAU,uBAAuB,CAC3C,OAAO,EACP,SAAS,EACT,IAAI,EACJ,GAAG,EACH,KAAK,EACL,MAAM,EACN,KAAK;IAEL,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,8BAA8B,EAC9B,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC;QACtC,OAAO;QACP,KAAK;KACN,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACI,KAAK,UAAU,sBAAsB,CAC1C,OAAO,EACP,SAAS,EACT,IAAI,EACJ,GAAG,EACH,KAAK,EACL,MAAM,EACN,KAAK;IAEL,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,6BAA6B,EAC7B,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC;QACtC,OAAO;QACP,KAAK;KACN,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACI,KAAK,UAAU,kBAAkB,CACtC,SAAS,EACT,OAAO,EACP,SAAS,EACT,IAAI,EACJ,GAAG,EACH,KAAK,EACL,MAAM,EACN,KAAK;IAEL,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,wBAAwB,EACxB,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC;QACtC,SAAS;QACT,OAAO;QACP,KAAK;KACN,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACI,KAAK,UAAU,mBAAmB,CACvC,SAAS,EACT,OAAO,EACP,SAAS,EACT,IAAI,EACJ,GAAG,EACH,KAAK,EACL,MAAM,EACN,KAAK;IAEL,OAAO,sBAAsB,CAAC,CAC5B,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,yBAAyB,EACzB,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC;QACtC,SAAS;QACT,OAAO;QACP,KAAK;KACN,CACF,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACI,KAAK,UAAU,kBAAkB,CAAC,SAAS,EAAE,WAAW;IAC7D,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,2DAA2D,CAC5D,CAAC;IACJ,CAAC;IACD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,mBAAmB,cAAI,CAAC,aAAa,CAAC,SAAS,CAAC,cAAc,cAAI,CAAC,aAAa,CAC9E,WAAW,CACZ,EAAE,EACH,MAAM,EACN,EAAE,CACH,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACI,KAAK,UAAU,YAAY,CAChC,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS;IAET,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,uDAAuD,CACxD,CAAC;IACJ,CAAC;IACD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,qBAAqB,EACrB,MAAM,EACN;QACE,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC3B,MAAM,EAAE,EAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAC;KACxC,CACF,CAAC;AACJ,CAAC;AAED,2BAA2B;AAE3B;;;;GAIG;AACH,SAAS,QAAQ,CAAC,OAAO;IACvB,OAAO,OAAO,CAAC,CAAC,CAAC,cAAI,CAAC,WAAW,CAAC,cAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC7E,CAAC;AAED;;;;;GAKG;AACH,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC;IACnB,OAAO,gBAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,gBAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC7D,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM;IACtC,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gBAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,SAAS;QACX,CAAC,CAAC,EAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAC,CAAC;AACjC,CAAC;AAED,aAAa;AAEb;;GAEG"}