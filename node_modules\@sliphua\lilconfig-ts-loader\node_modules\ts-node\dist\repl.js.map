{"version": 3, "file": "repl.js", "sourceRoot": "", "sources": ["../src/repl.ts"], "names": [], "mappings": ";;;AAAA,+BAAgC;AAChC,2BAA4B;AAC5B,+BAA2B;AAC3B,+BAAyC;AACzC,2BAA2B;AAC3B,mCAAyD;AACzD,2BAA2C;AAC3C,qCAAiC;AAGjC;;;GAGG;AACU,QAAA,aAAa,GAAG,WAAW,CAAA;AAkCxC,SAAgB,UAAU,CAAE,UAA6B,EAAE;;IACzD,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;IAC7B,MAAM,KAAK,SAAG,OAAO,CAAC,KAAK,mCAAI,IAAI,SAAS,CAAC,WAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,qBAAa,CAAC,CAAC,CAAA;IAChF,MAAM,oBAAoB,GAAG,0BAA0B,CAAC,KAAK,CAAC,CAAA;IAC9D,MAAM,KAAK,SAAG,OAAO,CAAC,KAAK,mCAAI,OAAO,CAAC,KAAK,CAAA;IAC5C,MAAM,MAAM,SAAG,OAAO,CAAC,MAAM,mCAAI,OAAO,CAAC,MAAM,CAAA;IAC/C,MAAM,MAAM,SAAG,OAAO,CAAC,MAAM,mCAAI,OAAO,CAAC,MAAM,CAAA;IAC/C,MAAM,QAAQ,GAAG,MAAM,KAAK,OAAO,CAAC,MAAM,IAAI,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,iBAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAE/G,MAAM,WAAW,GAAgB;QAC/B,KAAK,QAAE,OAAO,CAAC,KAAK,mCAAI,IAAI,SAAS,CAAC,WAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,qBAAa,CAAC,CAAC;QACzE,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,oBAAoB;QACpB,KAAK;QACL,KAAK;QACL,MAAM;QACN,MAAM;QACN,OAAO,EAAE,QAAQ;KAClB,CAAA;IACD,OAAO,WAAW,CAAA;IAElB,SAAS,UAAU,CAAE,QAAiB;QACpC,OAAO,GAAG,QAAQ,CAAA;IACpB,CAAC;IAED,SAAS,QAAQ,CAAE,IAAY;QAC7B,OAAO,KAAK,CAAC,OAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;IACrC,CAAC;IAED,SAAS,QAAQ,CAAE,IAAY,EAAE,QAAa,EAAE,SAAiB,EAAE,QAAkD;QACnH,IAAI,GAAG,GAAiB,IAAI,CAAA;QAC5B,IAAI,MAAW,CAAA;QAEf,kDAAkD;QAClD,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,QAAQ,CAAC,GAAG,CAAC,CAAA;YACb,OAAM;SACP;QAED,IAAI;YACF,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;SACxB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,eAAO,EAAE;gBAC5B,oDAAoD;gBACpD,IAAI,kBAAW,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;oBACvC,GAAG,GAAG,IAAI,kBAAW,CAAC,KAAK,CAAC,CAAA;iBAC7B;qBAAM;oBACL,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;iBACrB;aACF;iBAAM;gBACL,GAAG,GAAG,KAAK,CAAA;aACZ;SACF;QAED,OAAO,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAC9B,CAAC;IAED,SAAS,KAAK,CAAE,IAAa;QAC3B,0DAA0D;QAC1D,OAAO,SAAS,CAAC,WAAW,EAAE,OAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;IACtD,CAAC;AACH,CAAC;AA/DD,gCA+DC;AAED;;GAEG;AACH,MAAa,SAAS;IAapB,YAAoB,IAAY;QAAZ,SAAI,GAAJ,IAAI,CAAQ;QAZhC,gBAAgB;QAChB,UAAK,GAAG,EAAE,CAAA;QACV,gBAAgB;QAChB,WAAM,GAAG,EAAE,CAAA;QACX,gBAAgB;QAChB,YAAO,GAAG,CAAC,CAAA;QACX,gBAAgB;QAChB,UAAK,GAAG,CAAC,CAAA;IAK2B,CAAC;CACtC;AAdD,8BAcC;AAQD,SAAgB,0BAA0B,CAAE,KAAgB;IAC1D,SAAS,QAAQ,CAAE,IAAY;QAC7B,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC,KAAK,CAAA;QAE3C,IAAI;YACF,OAAO,iBAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;SAClC;QAAC,OAAO,GAAG,EAAE,EAAC,aAAa,EAAC;IAC/B,CAAC;IACD,SAAS,UAAU,CAAE,IAAY;QAC/B,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;YAAE,OAAO,IAAI,CAAA;QAEpC,IAAI;YACF,MAAM,KAAK,GAAG,aAAQ,CAAC,IAAI,CAAC,CAAA;YAC5B,OAAO,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE,CAAA;SACxC;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,KAAK,CAAA;SACb;IACH,CAAC;IACD,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAA;AACjC,CAAC;AAnBD,gEAmBC;AAED;;GAEG;AACH,SAAS,KAAK,CAAE,OAAgB,EAAE,KAAgB,EAAE,KAAa;IAC/D,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;IACzB,MAAM,YAAY,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACvC,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IACrC,IAAI,MAAc,CAAA;IAElB,IAAI;QACF,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAA;KAC1D;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,EAAE,CAAA;QACN,MAAM,GAAG,CAAA;KACV;IAED,qDAAqD;IACrD,MAAM,OAAO,GAAG,gBAAS,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAE/C,IAAI,YAAY,EAAE;QAChB,IAAI,EAAE,CAAA;KACP;SAAM;QACL,KAAK,CAAC,MAAM,GAAG,MAAM,CAAA;KACtB;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;QACvC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAC/D,CAAC,EAAE,SAAS,CAAC,CAAA;AACf,CAAC;AAED;;GAEG;AACH,SAAS,IAAI,CAAE,IAAY,EAAE,QAAgB;IAC3C,MAAM,MAAM,GAAG,IAAI,WAAM,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;IAEvD,OAAO,MAAM,CAAC,gBAAgB,EAAE,CAAA;AAClC,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAE,WAAwB,EAAE,OAAgB,EAAE,KAAgB,EAAE,IAAa;IAC7F,6CAA6C;IAC7C,IAAI,IAAI,EAAE;QACR,IAAI;YACF,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,CAAA;SAClC;QAAC,OAAO,GAAG,EAAE;YACZ,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;SAChB;KACF;IAED,MAAM,IAAI,GAAG,YAAK,CAAC;QACjB,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,WAAW,CAAC,KAAK;QACxB,MAAM,EAAE,WAAW,CAAC,MAAM;QAC1B,kJAAkJ;QAClJ,QAAQ,EAAG,WAAW,CAAC,MAA0B,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAiB,EAAE,EAAE,CAAC;QACvG,IAAI,EAAE,WAAW,CAAC,QAAQ;QAC1B,SAAS,EAAE,IAAI;KAChB,CAAC,CAAA;IAEF,2DAA2D;IAC3D,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;IAEvC,SAAS,KAAK;QACZ,SAAS,EAAE,CAAA;QAEX,yEAAyE;QACzE,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;IAC9C,CAAC;IAED,KAAK,EAAE,CAAA;IACP,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IAEvB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;QACzB,IAAI,EAAE,2CAA2C;QACjD,MAAM,EAAE,UAAU,UAAkB;YAClC,IAAI,CAAC,UAAU,EAAE;gBACf,IAAI,CAAC,aAAa,EAAE,CAAA;gBACpB,OAAM;aACP;YAED,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC1C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAE1F,IAAI,EAAE,CAAA;YAEN,IAAI,IAAI;gBAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,CAAA;YAC9C,IAAI,OAAO;gBAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO,IAAI,CAAC,CAAA;YACpD,IAAI,CAAC,aAAa,EAAE,CAAA;QACtB,CAAC;KACF,CAAC,CAAA;IAEF,iEAAiE;IACjE,IAAI,IAAI,CAAC,YAAY,EAAE;QACrB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,WAAI,CAAC,YAAO,EAAE,EAAE,uBAAuB,CAAC,CAAA;QAE3F,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;YACnC,IAAI,CAAC,GAAG;gBAAE,OAAM;YAEhB,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC,CAAC,CAAA;KACH;AACH,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CAAE,KAAgB,EAAE,KAAa;IAClD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAA;IAC7B,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAA;IACjC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAA;IAC/B,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAA;IAE7B,mDAAmD;IACnD,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC9G,KAAK,CAAC,KAAK,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;KAC/C;IAED,KAAK,CAAC,KAAK,IAAI,KAAK,CAAA;IACpB,KAAK,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC,CAAA;IAC/B,KAAK,CAAC,OAAO,EAAE,CAAA;IAEf,OAAO;QACL,KAAK,CAAC,KAAK,GAAG,SAAS,CAAA;QACvB,KAAK,CAAC,MAAM,GAAG,UAAU,CAAA;QACzB,KAAK,CAAC,OAAO,GAAG,WAAW,CAAA;QAC3B,KAAK,CAAC,KAAK,GAAG,SAAS,CAAA;IACzB,CAAC,CAAA;AACH,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAE,KAAa;IAC/B,IAAI,KAAK,GAAG,CAAC,CAAA;IAEb,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,KAAK,EAAE,CAAA;SACR;KACF;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,MAAM,cAAc,GAAgB,IAAI,GAAG,CAAC;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,CAAC,oFAAoF;CAC1F,CAAC,CAAA;AAEF;;GAEG;AACH,SAAS,aAAa,CAAE,KAAc;IACpC,OAAO,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;AACtE,CAAC", "sourcesContent": ["import { diffLines } from 'diff'\nimport { homedir } from 'os'\nimport { join } from 'path'\nimport { Recoverable, start } from 'repl'\nimport { Script } from 'vm'\nimport { Service, CreateOptions, TSError } from './index'\nimport { readFileSync, statSync } from 'fs'\nimport { Console } from 'console'\nimport * as tty from 'tty'\n\n/**\n * Eval filename for REPL/debug.\n * @internal\n */\nexport const EVAL_FILENAME = `[eval].ts`\n\nexport interface ReplService {\n  readonly state: EvalState\n  /**\n   * Bind this REPL to a ts-node compiler service.  A compiler service must be bound before `eval`-ing code or starting the REPL\n   */\n  setService (service: Service): void\n  evalCode (code: string): void\n  /**\n   * `eval` implementation compatible with node's REPL API\n   */\n  nodeEval (code: string, _context: any, _filename: string, callback: (err: Error | null, result?: any) => any): void\n  evalAwarePartialHost: EvalAwarePartialHost\n  /** Start a node REPL */\n  start (code?: string): void\n  /** @internal */\n  readonly stdin: NodeJS.ReadableStream\n  /** @internal */\n  readonly stdout: NodeJS.WritableStream\n  /** @internal */\n  readonly stderr: NodeJS.WritableStream\n  /** @internal */\n  readonly console: Console\n}\n\nexport interface CreateReplOptions {\n  service?: Service\n  state?: EvalState\n  stdin?: NodeJS.ReadableStream\n  stdout?: NodeJS.WritableStream\n  stderr?: NodeJS.WritableStream\n}\n\nexport function createRepl (options: CreateReplOptions = {}) {\n  let service = options.service\n  const state = options.state ?? new EvalState(join(process.cwd(), EVAL_FILENAME))\n  const evalAwarePartialHost = createEvalAwarePartialHost(state)\n  const stdin = options.stdin ?? process.stdin\n  const stdout = options.stdout ?? process.stdout\n  const stderr = options.stderr ?? process.stderr\n  const _console = stdout === process.stdout && stderr === process.stderr ? console : new Console(stdout, stderr)\n\n  const replService: ReplService = {\n    state: options.state ?? new EvalState(join(process.cwd(), EVAL_FILENAME)),\n    setService,\n    evalCode,\n    nodeEval,\n    evalAwarePartialHost,\n    start,\n    stdin,\n    stdout,\n    stderr,\n    console: _console\n  }\n  return replService\n\n  function setService (_service: Service) {\n    service = _service\n  }\n\n  function evalCode (code: string) {\n    return _eval(service!, state, code)\n  }\n\n  function nodeEval (code: string, _context: any, _filename: string, callback: (err: Error | null, result?: any) => any) {\n    let err: Error | null = null\n    let result: any\n\n    // TODO: Figure out how to handle completion here.\n    if (code === '.scope') {\n      callback(err)\n      return\n    }\n\n    try {\n      result = evalCode(code)\n    } catch (error) {\n      if (error instanceof TSError) {\n        // Support recoverable compilations using >= node 6.\n        if (Recoverable && isRecoverable(error)) {\n          err = new Recoverable(error)\n        } else {\n          console.error(error)\n        }\n      } else {\n        err = error\n      }\n    }\n\n    return callback(err, result)\n  }\n\n  function start (code?: string) {\n    // TODO assert that service is set; remove all ! postfixes\n    return startRepl(replService, service!, state, code)\n  }\n}\n\n/**\n * Eval state management. Stores virtual `[eval].ts` file\n */\nexport class EvalState {\n  /** @internal */\n  input = ''\n  /** @internal */\n  output = ''\n  /** @internal */\n  version = 0\n  /** @internal */\n  lines = 0\n\n  // tslint:disable-next-line:variable-name\n  __tsNodeEvalStateBrand: unknown\n\n  constructor (public path: string) { }\n}\n\n/**\n * Filesystem host functions which are aware of the \"virtual\" [eval].ts file used to compile REPL inputs.\n * Must be passed to `create()` to create a ts-node compiler service which can compile REPL inputs.\n */\nexport type EvalAwarePartialHost = Pick<CreateOptions, 'readFile' | 'fileExists'>\n\nexport function createEvalAwarePartialHost (state: EvalState): EvalAwarePartialHost {\n  function readFile (path: string) {\n    if (path === state.path) return state.input\n\n    try {\n      return readFileSync(path, 'utf8')\n    } catch (err) {/* Ignore. */}\n  }\n  function fileExists (path: string) {\n    if (path === state.path) return true\n\n    try {\n      const stats = statSync(path)\n      return stats.isFile() || stats.isFIFO()\n    } catch (err) {\n      return false\n    }\n  }\n  return { readFile, fileExists }\n}\n\n/**\n * Evaluate the code snippet.\n */\nfunction _eval (service: Service, state: EvalState, input: string) {\n  const lines = state.lines\n  const isCompletion = !/\\n$/.test(input)\n  const undo = appendEval(state, input)\n  let output: string\n\n  try {\n    output = service.compile(state.input, state.path, -lines)\n  } catch (err) {\n    undo()\n    throw err\n  }\n\n  // Use `diff` to check for new JavaScript to execute.\n  const changes = diffLines(state.output, output)\n\n  if (isCompletion) {\n    undo()\n  } else {\n    state.output = output\n  }\n\n  return changes.reduce((result, change) => {\n    return change.added ? exec(change.value, state.path) : result\n  }, undefined)\n}\n\n/**\n * Execute some code.\n */\nfunction exec (code: string, filename: string) {\n  const script = new Script(code, { filename: filename })\n\n  return script.runInThisContext()\n}\n\n/**\n * Start a CLI REPL.\n */\nfunction startRepl (replService: ReplService, service: Service, state: EvalState, code?: string) {\n  // Eval incoming code before the REPL starts.\n  if (code) {\n    try {\n      replService.evalCode(`${code}\\n`)\n    } catch (err) {\n      replService.console.error(err)\n      process.exit(1)\n    }\n  }\n\n  const repl = start({\n    prompt: '> ',\n    input: replService.stdin,\n    output: replService.stdout,\n    // Mimicking node's REPL implementation: https://github.com/nodejs/node/blob/168b22ba073ee1cbf8d0bcb4ded7ff3099335d04/lib/internal/repl.js#L28-L30\n    terminal: (replService.stdout as tty.WriteStream).isTTY && !parseInt(process.env.NODE_NO_READLINE!, 10),\n    eval: replService.nodeEval,\n    useGlobal: true\n  })\n\n  // Bookmark the point where we should reset the REPL state.\n  const resetEval = appendEval(state, '')\n\n  function reset () {\n    resetEval()\n\n    // Hard fix for TypeScript forcing `Object.defineProperty(exports, ...)`.\n    exec('exports = module.exports', state.path)\n  }\n\n  reset()\n  repl.on('reset', reset)\n\n  repl.defineCommand('type', {\n    help: 'Check the type of a TypeScript identifier',\n    action: function (identifier: string) {\n      if (!identifier) {\n        repl.displayPrompt()\n        return\n      }\n\n      const undo = appendEval(state, identifier)\n      const { name, comment } = service.getTypeInfo(state.input, state.path, state.input.length)\n\n      undo()\n\n      if (name) repl.outputStream.write(`${name}\\n`)\n      if (comment) repl.outputStream.write(`${comment}\\n`)\n      repl.displayPrompt()\n    }\n  })\n\n  // Set up REPL history when available natively via node.js >= 11.\n  if (repl.setupHistory) {\n    const historyPath = process.env.TS_NODE_HISTORY || join(homedir(), '.ts_node_repl_history')\n\n    repl.setupHistory(historyPath, err => {\n      if (!err) return\n\n      replService.console.error(err)\n      process.exit(1)\n    })\n  }\n}\n\n/**\n * Append to the eval instance and return an undo function.\n */\nfunction appendEval (state: EvalState, input: string) {\n  const undoInput = state.input\n  const undoVersion = state.version\n  const undoOutput = state.output\n  const undoLines = state.lines\n\n  // Handle ASI issues with TypeScript re-evaluation.\n  if (undoInput.charAt(undoInput.length - 1) === '\\n' && /^\\s*[\\/\\[(`-]/.test(input) && !/;\\s*$/.test(undoInput)) {\n    state.input = `${state.input.slice(0, -1)};\\n`\n  }\n\n  state.input += input\n  state.lines += lineCount(input)\n  state.version++\n\n  return function () {\n    state.input = undoInput\n    state.output = undoOutput\n    state.version = undoVersion\n    state.lines = undoLines\n  }\n}\n\n/**\n * Count the number of lines.\n */\nfunction lineCount (value: string) {\n  let count = 0\n\n  for (const char of value) {\n    if (char === '\\n') {\n      count++\n    }\n  }\n\n  return count\n}\n\nconst RECOVERY_CODES: Set<number> = new Set([\n  1003, // \"Identifier expected.\"\n  1005, // \"')' expected.\"\n  1109, // \"Expression expected.\"\n  1126, // \"Unexpected end of text.\"\n  1160, // \"Unterminated template literal.\"\n  1161, // \"Unterminated regular expression literal.\"\n  2355 // \"A function whose declared type is neither 'void' nor 'any' must return a value.\"\n])\n\n/**\n * Check if a function can recover gracefully.\n */\nfunction isRecoverable (error: TSError) {\n  return error.diagnosticCodes.every(code => RECOVERY_CODES.has(code))\n}\n"]}