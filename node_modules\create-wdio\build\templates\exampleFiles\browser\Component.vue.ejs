<script setup lang="ts">
import { ref } from 'vue'

defineProps<{ msg: string }>()

const count = ref(0)
</script>

<template>
    <div id="root">
        <div>
            <a href="https://webdriver.io/docs/component-testing" target="_blank">
                <img src="https://webdriver.io/assets/images/robot-3677788dd63849c56aa5cb3f332b12d5.svg" className="logo"
                    alt="WebdriverIO logo" />
            </a>
        </div>
        <h1>{{ msg }}</h1>

        <div class="card">
            <button type="button" @click="count++">count is {{ count }}</button>
            <p>
                Edit <code>src/Component.test.<%- answers.isUsingTypeScript ? `ts` : 'js' %></code> and save to test HMR
            </p>
        </div>

        <p class="read-the-docs">Click on the WebdriverIO logo to learn more</p>
    </div>
</template>

<style scoped>
.read-the-docs {
    color: #888;
}
</style>
