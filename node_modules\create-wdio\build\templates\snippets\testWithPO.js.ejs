<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import { expect } from '@wdio/globals'`
    : `const { expect } = require('@wdio/globals')` %>
<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import LoginPage from '${answers.relativePath}/login.page${answers.esmSupport ? '.js' : ''}'`
    : `const LoginPage = require('${answers.relativePath}/login.page')` %>
<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import SecurePage from '${answers.relativePath}/secure.page${answers.esmSupport ? '.js' : ''}'`
    : `const SecurePage = require('${answers.relativePath}/secure.page')` %>

describe('My Login application', () => {
    it('should login with valid credentials', async () => {
        await LoginPage.open()

        await LoginPage.login('tomsmith', 'SuperSecretPassword!')
        await expect(SecurePage.flashAlert).toBeExisting()
        await expect(SecurePage.flashAlert).toHaveText(
            expect.stringContaining('You logged into a secure area!'))<%-
        answers.includeVisualTesting ? `
        await expect(SecurePage.flashAlert).toMatchElementSnapshot('flashAlert')` : '' %>
    })
})
