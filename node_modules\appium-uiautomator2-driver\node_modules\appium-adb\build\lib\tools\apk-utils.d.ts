/**
 * Uninstall the given package from the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The name of the package to be uninstalled.
 * @param {import('./types').UninstallOptions} [options={}] - The set of uninstall options.
 * @return {Promise<boolean>} True if the package was found on the device and
 *                   successfully uninstalled.
 */
export function uninstallApk(this: import("../adb.js").ADB, pkg: string, options?: import("./types").UninstallOptions): Promise<boolean>;
/**
 * Install the package after it was pushed to the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} apkPathOnDevice - The full path to the package on the device file system.
 * @param {import('./types').ShellExecOptions} [opts={}] Additional exec options.
 * @throws {error} If there was a failure during application install.
 */
export function installFromDevicePath(this: import("../adb.js").ADB, apkPathOnDevice: string, opts?: import("./types").ShellExecOptions): Promise<void>;
/**
 * Caches the given APK at a remote location to speed up further APK deployments.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} apkPath - Full path to the apk on the local FS
 * @param {import('./types').CachingOptions} [options={}] - Caching options
 * @returns {Promise<string>} - Full path to the cached apk on the remote file system
 * @throws {Error} if there was a failure while caching the app
 */
export function cacheApk(this: import("../adb.js").ADB, apkPath: string, options?: import("./types").CachingOptions): Promise<string>;
export class cacheApk {
    /**
     * Caches the given APK at a remote location to speed up further APK deployments.
     *
     * @this {import('../adb.js').ADB}
     * @param {string} apkPath - Full path to the apk on the local FS
     * @param {import('./types').CachingOptions} [options={}] - Caching options
     * @returns {Promise<string>} - Full path to the cached apk on the remote file system
     * @throws {Error} if there was a failure while caching the app
     */
    constructor(this: import("../adb.js").ADB, apkPath: string, options?: import("./types").CachingOptions);
    _areExtendedLsOptionsSupported: boolean | undefined;
    remoteAppsCache: LRUCache<string, string, unknown>;
}
/**
 * Install the package from the local file system.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to the local package.
 * @param {import('./types').InstallOptions} [options={}] - The set of installation options.
 * @throws {Error} If an unexpected error happens during install.
 */
export function install(this: import("../adb.js").ADB, appPath: string, options?: import("./types").InstallOptions): Promise<void>;
/**
 * Retrieves the current installation state of the particular application
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - Full path to the application
 * @param {string?} [pkg=null] - Package identifier. If omitted then the script will
 * try to extract it on its own
 * @returns {Promise<import('./types').InstallState>} One of `APP_INSTALL_STATE` constants
 */
export function getApplicationInstallState(this: import("../adb.js").ADB, appPath: string, pkg?: string | null): Promise<import("./types").InstallState>;
/**
 * Install the package from the local file system or upgrade it if an older
 * version of the same package is already installed.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to the local package.
 * @param {string?} [pkg=null] - The name of the installed package. The method will
 * perform faster if it is set.
 * @param {import('./types').InstallOrUpgradeOptions} [options={}] - Set of install options.
 * @throws {Error} If an unexpected error happens during install.
 * @returns {Promise<import('./types').InstallOrUpgradeResult>}
 */
export function installOrUpgrade(this: import("../adb.js").ADB, appPath: string, pkg?: string | null, options?: import("./types").InstallOrUpgradeOptions): Promise<import("./types").InstallOrUpgradeResult>;
/**
 * Extract string resources from the given package on local file system.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to the .apk(s) package.
 * @param {string?} [language=null] - The name of the language to extract the resources for.
 * The default language is used if this equals to `null`
 * @param {string?} [outRoot=null] - The name of the destination folder on the local file system to
 * store the extracted file to. If not provided then the `localPath` property in the returned object
 * will be undefined.
 * @return {Promise<import('./types').ApkStrings>}
 */
export function extractStringsFromApk(this: import("../adb.js").ADB, appPath: string, language?: string | null, outRoot?: string | null): Promise<import("./types").ApkStrings>;
/**
 * Get the package info from local apk file.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to existing .apk(s) package on the local
 *                           file system.
 * @return {Promise<import('./types').AppInfo|{}>} The parsed application information.
 */
export function getApkInfo(this: import("../adb.js").ADB, appPath: string): Promise<import("./types").AppInfo | {}>;
/**
 * Parses apk strings from aapt2 tool output
 *
 * @param {string} rawOutput The actual tool output
 * @param {string} configMarker The config marker. Usually
 * a language abbreviation or an empty string for the default one
 * @returns {Object} Strings ids to values mapping. Plural
 * values are represented as arrays. If no config found for the
 * given marker then an empty mapping is returned.
 */
export function parseAapt2Strings(rawOutput: string, configMarker: string): any;
/**
 * Parses apk strings from aapt tool output
 *
 * @param {string} rawOutput The actual tool output
 * @param {string} configMarker The config marker. Usually
 * a language abbreviation or `(default)`
 * @returns {Object} Strings ids to values mapping. Plural
 * values are represented as arrays. If no config found for the
 * given marker then an empty mapping is returned.
 */
export function parseAaptStrings(rawOutput: string, configMarker: string): any;
export const REMOTE_CACHE_ROOT: "/data/local/tmp/appium_cache";
import { LRUCache } from 'lru-cache';
//# sourceMappingURL=apk-utils.d.ts.map