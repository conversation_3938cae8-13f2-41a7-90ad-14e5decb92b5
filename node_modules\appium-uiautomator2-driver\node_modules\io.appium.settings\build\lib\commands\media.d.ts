/**
 * Performs recursive media scan at the given destination.
 * All successfully scanned items are being added to the device's
 * media library.
 *
 * @this {import('../client').SettingsApp}
 * @param {string} destination File/folder path on the remote device.
 * @throws {Error} If there was an unexpected error by scanning.
 */
export function scanMedia(this: import("../client").SettingsApp, destination: string): Promise<void>;
//# sourceMappingURL=media.d.ts.map