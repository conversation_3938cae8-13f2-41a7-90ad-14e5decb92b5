{"name": "@appium/support", "version": "6.1.1", "description": "Support libs used across appium packages", "keywords": ["automation", "javascript", "selenium", "webdriver", "ios", "android", "firefoxos", "testing"], "homepage": "https://appium.io", "bugs": {"url": "https://github.com/appium/appium/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/appium.git", "directory": "packages/support"}, "license": "Apache-2.0", "author": "https://github.com/appium", "types": "./build/lib/index.d.ts", "directories": {"lib": "lib"}, "files": ["index.js", "lib", "build", "tsconfig.json", "!build/tsconfig.tsbuildinfo"], "scripts": {"test": "npm run test:unit", "test:e2e": "mocha --timeout 20s --slow 10s \"./test/e2e/**/*.spec.js\"", "test:smoke": "node ./index.js", "test:unit": "mocha \"./test/unit/**/*.spec.js\""}, "dependencies": {"@appium/logger": "^1.7.1", "@appium/tsconfig": "^0.3.5", "@appium/types": "^0.26.0", "@colors/colors": "1.6.0", "archiver": "7.0.1", "axios": "1.9.0", "base64-stream": "1.0.0", "bluebird": "3.7.2", "bplist-creator": "0.1.1", "bplist-parser": "0.3.2", "form-data": "4.0.2", "get-stream": "6.0.1", "glob": "10.4.5", "jsftp": "2.1.3", "klaw": "4.1.0", "lockfile": "1.0.4", "lodash": "4.17.21", "log-symbols": "4.1.0", "moment": "2.30.1", "mv": "2.1.1", "ncp": "2.0.0", "pkg-dir": "5.0.0", "plist": "3.1.0", "pluralize": "8.0.0", "read-pkg": "5.2.0", "resolve-from": "5.0.0", "sanitize-filename": "1.6.3", "semver": "7.7.2", "shell-quote": "1.8.2", "source-map-support": "0.5.21", "supports-color": "8.1.1", "teen_process": "2.3.2", "type-fest": "4.41.0", "uuid": "11.1.0", "which": "4.0.0", "yauzl": "3.2.0"}, "optionalDependencies": {"sharp": "0.34.2"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "publishConfig": {"access": "public"}, "gitHead": "c8fe4412525f7e1fa237813cf83fe7d98f0125eb"}