{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAgBA,OAAO,IAAI,MAAM,gBAAgB,CAAC;AAClC,OAAO,EAAC,SAAS,EAAE,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAC7D,OAAO,EAAC,SAAS,EAAE,kBAAkB,EAAE,QAAQ,EAAE,kBAAkB,EAAC,MAAM,gBAAgB,CAAC;AAC3F,OAAO,EAAC,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AACrE,OAAO,EAAC,aAAa,EAAE,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAE/D,OAAO,EAAC,OAAO,EAAE,OAAO,EAAC,MAAM,eAAe,CAAC;AAC/C,OAAO,EAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AAEhE,OAAO,EAAC,UAAU,EAAE,SAAS,EAAC,MAAM,iBAAiB,CAAC;AAEtD,OAAO,EAAC,UAAU,EAAE,YAAY,EAAC,MAAM,kBAAkB,CAAC;AAC1D,OAAO,KAAK,EAAC,iBAAiB,EAAE,mBAAmB,EAAC,MAAM,kBAAkB,CAAC;AAC7E,OAAO,EAAC,UAAU,EAAC,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAC,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAChD,OAAO,EACL,eAAe,EACf,mBAAmB,EACnB,WAAW,EACX,WAAW,EACZ,MAAM,mBAAmB,CAAC;AAC3B,OAAO,KAAK,EACV,+BAA+B,EAC/B,kCAAkC,EAClC,2BAA2B,EAC3B,8BAA8B,EAC/B,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAC,mBAAmB,EAAC,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAC,mBAAmB,EAAC,MAAM,kBAAkB,CAAC;AACrD,OAAO,KAAK,EACV,YAAY,EACZ,MAAM,EACN,0BAA0B,EAC1B,6BAA6B,EAC7B,yBAAyB,EACzB,4BAA4B,EAC5B,yBAAyB,EACzB,4BAA4B,EAC5B,yBAAyB,EACzB,4BAA4B,EAC5B,6BAA6B,EAC7B,gCAAgC,EAChC,wBAAwB,EACxB,2BAA2B,EAC3B,uBAAuB,EACvB,0BAA0B,EAC1B,eAAe,EACf,mBAAmB,EACpB,MAAM,YAAY,CAAC;AAEpB,OAAO,EACL,IAAI,EAEJ,SAAS,EACT,aAAa,EACb,SAAS,EACT,QAAQ,EACR,kBAAkB,EAClB,kBAAkB,EAClB,SAAS,EACT,QAAQ,EACR,gBAAgB,EAChB,aAAa,EACb,YAAY,EACZ,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,SAAS,EAET,eAAe,EACf,mBAAmB,EACnB,WAAW,EACX,WAAW,EACX,UAAU,EACV,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,mBAAmB,EACnB,mBAAmB,EACnB,YAAY,EACb,CAAC;AAEF,YAAY,EACV,YAAY,EACZ,MAAM,EACN,0BAA0B,EAC1B,6BAA6B,EAC7B,yBAAyB,EACzB,4BAA4B,EAC5B,yBAAyB,EACzB,4BAA4B,EAC5B,yBAAyB,EACzB,4BAA4B,EAC5B,6BAA6B,EAC7B,gCAAgC,EAChC,wBAAwB,EACxB,2BAA2B,EAC3B,uBAAuB,EACvB,0BAA0B,EAC1B,eAAe,EACf,mBAAmB,EAEnB,iBAAiB,EACjB,mBAAmB,EAEnB,+BAA+B,EAC/B,kCAAkC,EAClC,2BAA2B,EAC3B,8BAA8B,EAC/B,CAAC"}