/*istanbul ignore start*/
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.hasOnlyUnixLineEndings = hasOnlyUnixLineEndings;
exports.hasOnlyWinLineEndings = hasOnlyWinLineEndings;
exports.longestCommonPrefix = longestCommonPrefix;
exports.longestCommonSuffix = longestCommonSuffix;
exports.maximumOverlap = maximumOverlap;
exports.removePrefix = removePrefix;
exports.removeSuffix = removeSuffix;
exports.replacePrefix = replacePrefix;
exports.replaceSuffix = replaceSuffix;
/*istanbul ignore end*/
function longestCommonPrefix(str1, str2) {
  var i;
  for (i = 0; i < str1.length && i < str2.length; i++) {
    if (str1[i] != str2[i]) {
      return str1.slice(0, i);
    }
  }
  return str1.slice(0, i);
}
function longestCommonSuffix(str1, str2) {
  var i;

  // Unlike longestCommonPrefix, we need a special case to handle all scenarios
  // where we return the empty string since str1.slice(-0) will return the
  // entire string.
  if (!str1 || !str2 || str1[str1.length - 1] != str2[str2.length - 1]) {
    return '';
  }
  for (i = 0; i < str1.length && i < str2.length; i++) {
    if (str1[str1.length - (i + 1)] != str2[str2.length - (i + 1)]) {
      return str1.slice(-i);
    }
  }
  return str1.slice(-i);
}
function replacePrefix(string, oldPrefix, newPrefix) {
  if (string.slice(0, oldPrefix.length) != oldPrefix) {
    throw Error(
    /*istanbul ignore start*/
    "string ".concat(
    /*istanbul ignore end*/
    JSON.stringify(string), " doesn't start with prefix ").concat(JSON.stringify(oldPrefix), "; this is a bug"));
  }
  return newPrefix + string.slice(oldPrefix.length);
}
function replaceSuffix(string, oldSuffix, newSuffix) {
  if (!oldSuffix) {
    return string + newSuffix;
  }
  if (string.slice(-oldSuffix.length) != oldSuffix) {
    throw Error(
    /*istanbul ignore start*/
    "string ".concat(
    /*istanbul ignore end*/
    JSON.stringify(string), " doesn't end with suffix ").concat(JSON.stringify(oldSuffix), "; this is a bug"));
  }
  return string.slice(0, -oldSuffix.length) + newSuffix;
}
function removePrefix(string, oldPrefix) {
  return replacePrefix(string, oldPrefix, '');
}
function removeSuffix(string, oldSuffix) {
  return replaceSuffix(string, oldSuffix, '');
}
function maximumOverlap(string1, string2) {
  return string2.slice(0, overlapCount(string1, string2));
}

// Nicked from https://stackoverflow.com/a/60422853/1709587
function overlapCount(a, b) {
  // Deal with cases where the strings differ in length
  var startA = 0;
  if (a.length > b.length) {
    startA = a.length - b.length;
  }
  var endB = b.length;
  if (a.length < b.length) {
    endB = a.length;
  }
  // Create a back-reference for each index
  //   that should be followed in case of a mismatch.
  //   We only need B to make these references:
  var map = Array(endB);
  var k = 0; // Index that lags behind j
  map[0] = 0;
  for (var j = 1; j < endB; j++) {
    if (b[j] == b[k]) {
      map[j] = map[k]; // skip over the same character (optional optimisation)
    } else {
      map[j] = k;
    }
    while (k > 0 && b[j] != b[k]) {
      k = map[k];
    }
    if (b[j] == b[k]) {
      k++;
    }
  }
  // Phase 2: use these references while iterating over A
  k = 0;
  for (var i = startA; i < a.length; i++) {
    while (k > 0 && a[i] != b[k]) {
      k = map[k];
    }
    if (a[i] == b[k]) {
      k++;
    }
  }
  return k;
}

/**
 * Returns true if the string consistently uses Windows line endings.
 */
function hasOnlyWinLineEndings(string) {
  return string.includes('\r\n') && !string.startsWith('\n') && !string.match(/[^\r]\n/);
}

/**
 * Returns true if the string consistently uses Unix line endings.
 */
function hasOnlyUnixLineEndings(string) {
  return !string.includes('\r\n') && string.includes('\n');
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJsb25nZXN0Q29tbW9uUHJlZml4Iiwic3RyMSIsInN0cjIiLCJpIiwibGVuZ3RoIiwic2xpY2UiLCJsb25nZXN0Q29tbW9uU3VmZml4IiwicmVwbGFjZVByZWZpeCIsInN0cmluZyIsIm9sZFByZWZpeCIsIm5ld1ByZWZpeCIsIkVycm9yIiwiY29uY2F0IiwiSlNPTiIsInN0cmluZ2lmeSIsInJlcGxhY2VTdWZmaXgiLCJvbGRTdWZmaXgiLCJuZXdTdWZmaXgiLCJyZW1vdmVQcmVmaXgiLCJyZW1vdmVTdWZmaXgiLCJtYXhpbXVtT3ZlcmxhcCIsInN0cmluZzEiLCJzdHJpbmcyIiwib3ZlcmxhcENvdW50IiwiYSIsImIiLCJzdGFydEEiLCJlbmRCIiwibWFwIiwiQXJyYXkiLCJrIiwiaiIsImhhc09ubHlXaW5MaW5lRW5kaW5ncyIsImluY2x1ZGVzIiwic3RhcnRzV2l0aCIsIm1hdGNoIiwiaGFzT25seVVuaXhMaW5lRW5kaW5ncyJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy91dGlsL3N0cmluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gbG9uZ2VzdENvbW1vblByZWZpeChzdHIxLCBzdHIyKSB7XG4gIGxldCBpO1xuICBmb3IgKGkgPSAwOyBpIDwgc3RyMS5sZW5ndGggJiYgaSA8IHN0cjIubGVuZ3RoOyBpKyspIHtcbiAgICBpZiAoc3RyMVtpXSAhPSBzdHIyW2ldKSB7XG4gICAgICByZXR1cm4gc3RyMS5zbGljZSgwLCBpKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHN0cjEuc2xpY2UoMCwgaSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBsb25nZXN0Q29tbW9uU3VmZml4KHN0cjEsIHN0cjIpIHtcbiAgbGV0IGk7XG5cbiAgLy8gVW5saWtlIGxvbmdlc3RDb21tb25QcmVmaXgsIHdlIG5lZWQgYSBzcGVjaWFsIGNhc2UgdG8gaGFuZGxlIGFsbCBzY2VuYXJpb3NcbiAgLy8gd2hlcmUgd2UgcmV0dXJuIHRoZSBlbXB0eSBzdHJpbmcgc2luY2Ugc3RyMS5zbGljZSgtMCkgd2lsbCByZXR1cm4gdGhlXG4gIC8vIGVudGlyZSBzdHJpbmcuXG4gIGlmICghc3RyMSB8fCAhc3RyMiB8fCBzdHIxW3N0cjEubGVuZ3RoIC0gMV0gIT0gc3RyMltzdHIyLmxlbmd0aCAtIDFdKSB7XG4gICAgcmV0dXJuICcnO1xuICB9XG5cbiAgZm9yIChpID0gMDsgaSA8IHN0cjEubGVuZ3RoICYmIGkgPCBzdHIyLmxlbmd0aDsgaSsrKSB7XG4gICAgaWYgKHN0cjFbc3RyMS5sZW5ndGggLSAoaSArIDEpXSAhPSBzdHIyW3N0cjIubGVuZ3RoIC0gKGkgKyAxKV0pIHtcbiAgICAgIHJldHVybiBzdHIxLnNsaWNlKC1pKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHN0cjEuc2xpY2UoLWkpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gcmVwbGFjZVByZWZpeChzdHJpbmcsIG9sZFByZWZpeCwgbmV3UHJlZml4KSB7XG4gIGlmIChzdHJpbmcuc2xpY2UoMCwgb2xkUHJlZml4Lmxlbmd0aCkgIT0gb2xkUHJlZml4KSB7XG4gICAgdGhyb3cgRXJyb3IoYHN0cmluZyAke0pTT04uc3RyaW5naWZ5KHN0cmluZyl9IGRvZXNuJ3Qgc3RhcnQgd2l0aCBwcmVmaXggJHtKU09OLnN0cmluZ2lmeShvbGRQcmVmaXgpfTsgdGhpcyBpcyBhIGJ1Z2ApO1xuICB9XG4gIHJldHVybiBuZXdQcmVmaXggKyBzdHJpbmcuc2xpY2Uob2xkUHJlZml4Lmxlbmd0aCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiByZXBsYWNlU3VmZml4KHN0cmluZywgb2xkU3VmZml4LCBuZXdTdWZmaXgpIHtcbiAgaWYgKCFvbGRTdWZmaXgpIHtcbiAgICByZXR1cm4gc3RyaW5nICsgbmV3U3VmZml4O1xuICB9XG5cbiAgaWYgKHN0cmluZy5zbGljZSgtb2xkU3VmZml4Lmxlbmd0aCkgIT0gb2xkU3VmZml4KSB7XG4gICAgdGhyb3cgRXJyb3IoYHN0cmluZyAke0pTT04uc3RyaW5naWZ5KHN0cmluZyl9IGRvZXNuJ3QgZW5kIHdpdGggc3VmZml4ICR7SlNPTi5zdHJpbmdpZnkob2xkU3VmZml4KX07IHRoaXMgaXMgYSBidWdgKTtcbiAgfVxuICByZXR1cm4gc3RyaW5nLnNsaWNlKDAsIC1vbGRTdWZmaXgubGVuZ3RoKSArIG5ld1N1ZmZpeDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHJlbW92ZVByZWZpeChzdHJpbmcsIG9sZFByZWZpeCkge1xuICByZXR1cm4gcmVwbGFjZVByZWZpeChzdHJpbmcsIG9sZFByZWZpeCwgJycpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gcmVtb3ZlU3VmZml4KHN0cmluZywgb2xkU3VmZml4KSB7XG4gIHJldHVybiByZXBsYWNlU3VmZml4KHN0cmluZywgb2xkU3VmZml4LCAnJyk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBtYXhpbXVtT3ZlcmxhcChzdHJpbmcxLCBzdHJpbmcyKSB7XG4gIHJldHVybiBzdHJpbmcyLnNsaWNlKDAsIG92ZXJsYXBDb3VudChzdHJpbmcxLCBzdHJpbmcyKSk7XG59XG5cbi8vIE5pY2tlZCBmcm9tIGh0dHBzOi8vc3RhY2tvdmVyZmxvdy5jb20vYS82MDQyMjg1My8xNzA5NTg3XG5mdW5jdGlvbiBvdmVybGFwQ291bnQoYSwgYikge1xuICAvLyBEZWFsIHdpdGggY2FzZXMgd2hlcmUgdGhlIHN0cmluZ3MgZGlmZmVyIGluIGxlbmd0aFxuICBsZXQgc3RhcnRBID0gMDtcbiAgaWYgKGEubGVuZ3RoID4gYi5sZW5ndGgpIHsgc3RhcnRBID0gYS5sZW5ndGggLSBiLmxlbmd0aDsgfVxuICBsZXQgZW5kQiA9IGIubGVuZ3RoO1xuICBpZiAoYS5sZW5ndGggPCBiLmxlbmd0aCkgeyBlbmRCID0gYS5sZW5ndGg7IH1cbiAgLy8gQ3JlYXRlIGEgYmFjay1yZWZlcmVuY2UgZm9yIGVhY2ggaW5kZXhcbiAgLy8gICB0aGF0IHNob3VsZCBiZSBmb2xsb3dlZCBpbiBjYXNlIG9mIGEgbWlzbWF0Y2guXG4gIC8vICAgV2Ugb25seSBuZWVkIEIgdG8gbWFrZSB0aGVzZSByZWZlcmVuY2VzOlxuICBsZXQgbWFwID0gQXJyYXkoZW5kQik7XG4gIGxldCBrID0gMDsgLy8gSW5kZXggdGhhdCBsYWdzIGJlaGluZCBqXG4gIG1hcFswXSA9IDA7XG4gIGZvciAobGV0IGogPSAxOyBqIDwgZW5kQjsgaisrKSB7XG4gICAgICBpZiAoYltqXSA9PSBiW2tdKSB7XG4gICAgICAgICAgbWFwW2pdID0gbWFwW2tdOyAvLyBza2lwIG92ZXIgdGhlIHNhbWUgY2hhcmFjdGVyIChvcHRpb25hbCBvcHRpbWlzYXRpb24pXG4gICAgICB9IGVsc2Uge1xuICAgICAgICAgIG1hcFtqXSA9IGs7XG4gICAgICB9XG4gICAgICB3aGlsZSAoayA+IDAgJiYgYltqXSAhPSBiW2tdKSB7IGsgPSBtYXBba107IH1cbiAgICAgIGlmIChiW2pdID09IGJba10pIHsgaysrOyB9XG4gIH1cbiAgLy8gUGhhc2UgMjogdXNlIHRoZXNlIHJlZmVyZW5jZXMgd2hpbGUgaXRlcmF0aW5nIG92ZXIgQVxuICBrID0gMDtcbiAgZm9yIChsZXQgaSA9IHN0YXJ0QTsgaSA8IGEubGVuZ3RoOyBpKyspIHtcbiAgICAgIHdoaWxlIChrID4gMCAmJiBhW2ldICE9IGJba10pIHsgayA9IG1hcFtrXTsgfVxuICAgICAgaWYgKGFbaV0gPT0gYltrXSkgeyBrKys7IH1cbiAgfVxuICByZXR1cm4gaztcbn1cblxuXG4vKipcbiAqIFJldHVybnMgdHJ1ZSBpZiB0aGUgc3RyaW5nIGNvbnNpc3RlbnRseSB1c2VzIFdpbmRvd3MgbGluZSBlbmRpbmdzLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFzT25seVdpbkxpbmVFbmRpbmdzKHN0cmluZykge1xuICByZXR1cm4gc3RyaW5nLmluY2x1ZGVzKCdcXHJcXG4nKSAmJiAhc3RyaW5nLnN0YXJ0c1dpdGgoJ1xcbicpICYmICFzdHJpbmcubWF0Y2goL1teXFxyXVxcbi8pO1xufVxuXG4vKipcbiAqIFJldHVybnMgdHJ1ZSBpZiB0aGUgc3RyaW5nIGNvbnNpc3RlbnRseSB1c2VzIFVuaXggbGluZSBlbmRpbmdzLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFzT25seVVuaXhMaW5lRW5kaW5ncyhzdHJpbmcpIHtcbiAgcmV0dXJuICFzdHJpbmcuaW5jbHVkZXMoJ1xcclxcbicpICYmIHN0cmluZy5pbmNsdWRlcygnXFxuJyk7XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBTyxTQUFTQSxtQkFBbUJBLENBQUNDLElBQUksRUFBRUMsSUFBSSxFQUFFO0VBQzlDLElBQUlDLENBQUM7RUFDTCxLQUFLQSxDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLEdBQUdGLElBQUksQ0FBQ0csTUFBTSxJQUFJRCxDQUFDLEdBQUdELElBQUksQ0FBQ0UsTUFBTSxFQUFFRCxDQUFDLEVBQUUsRUFBRTtJQUNuRCxJQUFJRixJQUFJLENBQUNFLENBQUMsQ0FBQyxJQUFJRCxJQUFJLENBQUNDLENBQUMsQ0FBQyxFQUFFO01BQ3RCLE9BQU9GLElBQUksQ0FBQ0ksS0FBSyxDQUFDLENBQUMsRUFBRUYsQ0FBQyxDQUFDO0lBQ3pCO0VBQ0Y7RUFDQSxPQUFPRixJQUFJLENBQUNJLEtBQUssQ0FBQyxDQUFDLEVBQUVGLENBQUMsQ0FBQztBQUN6QjtBQUVPLFNBQVNHLG1CQUFtQkEsQ0FBQ0wsSUFBSSxFQUFFQyxJQUFJLEVBQUU7RUFDOUMsSUFBSUMsQ0FBQzs7RUFFTDtFQUNBO0VBQ0E7RUFDQSxJQUFJLENBQUNGLElBQUksSUFBSSxDQUFDQyxJQUFJLElBQUlELElBQUksQ0FBQ0EsSUFBSSxDQUFDRyxNQUFNLEdBQUcsQ0FBQyxDQUFDLElBQUlGLElBQUksQ0FBQ0EsSUFBSSxDQUFDRSxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUU7SUFDcEUsT0FBTyxFQUFFO0VBQ1g7RUFFQSxLQUFLRCxDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLEdBQUdGLElBQUksQ0FBQ0csTUFBTSxJQUFJRCxDQUFDLEdBQUdELElBQUksQ0FBQ0UsTUFBTSxFQUFFRCxDQUFDLEVBQUUsRUFBRTtJQUNuRCxJQUFJRixJQUFJLENBQUNBLElBQUksQ0FBQ0csTUFBTSxJQUFJRCxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsSUFBSUQsSUFBSSxDQUFDQSxJQUFJLENBQUNFLE1BQU0sSUFBSUQsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUU7TUFDOUQsT0FBT0YsSUFBSSxDQUFDSSxLQUFLLENBQUMsQ0FBQ0YsQ0FBQyxDQUFDO0lBQ3ZCO0VBQ0Y7RUFDQSxPQUFPRixJQUFJLENBQUNJLEtBQUssQ0FBQyxDQUFDRixDQUFDLENBQUM7QUFDdkI7QUFFTyxTQUFTSSxhQUFhQSxDQUFDQyxNQUFNLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0VBQzFELElBQUlGLE1BQU0sQ0FBQ0gsS0FBSyxDQUFDLENBQUMsRUFBRUksU0FBUyxDQUFDTCxNQUFNLENBQUMsSUFBSUssU0FBUyxFQUFFO0lBQ2xELE1BQU1FLEtBQUs7SUFBQTtJQUFBLFVBQUFDLE1BQUE7SUFBQTtJQUFXQyxJQUFJLENBQUNDLFNBQVMsQ0FBQ04sTUFBTSxDQUFDLGlDQUFBSSxNQUFBLENBQThCQyxJQUFJLENBQUNDLFNBQVMsQ0FBQ0wsU0FBUyxDQUFDLG9CQUFpQixDQUFDO0VBQ3ZIO0VBQ0EsT0FBT0MsU0FBUyxHQUFHRixNQUFNLENBQUNILEtBQUssQ0FBQ0ksU0FBUyxDQUFDTCxNQUFNLENBQUM7QUFDbkQ7QUFFTyxTQUFTVyxhQUFhQSxDQUFDUCxNQUFNLEVBQUVRLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0VBQzFELElBQUksQ0FBQ0QsU0FBUyxFQUFFO0lBQ2QsT0FBT1IsTUFBTSxHQUFHUyxTQUFTO0VBQzNCO0VBRUEsSUFBSVQsTUFBTSxDQUFDSCxLQUFLLENBQUMsQ0FBQ1csU0FBUyxDQUFDWixNQUFNLENBQUMsSUFBSVksU0FBUyxFQUFFO0lBQ2hELE1BQU1MLEtBQUs7SUFBQTtJQUFBLFVBQUFDLE1BQUE7SUFBQTtJQUFXQyxJQUFJLENBQUNDLFNBQVMsQ0FBQ04sTUFBTSxDQUFDLCtCQUFBSSxNQUFBLENBQTRCQyxJQUFJLENBQUNDLFNBQVMsQ0FBQ0UsU0FBUyxDQUFDLG9CQUFpQixDQUFDO0VBQ3JIO0VBQ0EsT0FBT1IsTUFBTSxDQUFDSCxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUNXLFNBQVMsQ0FBQ1osTUFBTSxDQUFDLEdBQUdhLFNBQVM7QUFDdkQ7QUFFTyxTQUFTQyxZQUFZQSxDQUFDVixNQUFNLEVBQUVDLFNBQVMsRUFBRTtFQUM5QyxPQUFPRixhQUFhLENBQUNDLE1BQU0sRUFBRUMsU0FBUyxFQUFFLEVBQUUsQ0FBQztBQUM3QztBQUVPLFNBQVNVLFlBQVlBLENBQUNYLE1BQU0sRUFBRVEsU0FBUyxFQUFFO0VBQzlDLE9BQU9ELGFBQWEsQ0FBQ1AsTUFBTSxFQUFFUSxTQUFTLEVBQUUsRUFBRSxDQUFDO0FBQzdDO0FBRU8sU0FBU0ksY0FBY0EsQ0FBQ0MsT0FBTyxFQUFFQyxPQUFPLEVBQUU7RUFDL0MsT0FBT0EsT0FBTyxDQUFDakIsS0FBSyxDQUFDLENBQUMsRUFBRWtCLFlBQVksQ0FBQ0YsT0FBTyxFQUFFQyxPQUFPLENBQUMsQ0FBQztBQUN6RDs7QUFFQTtBQUNBLFNBQVNDLFlBQVlBLENBQUNDLENBQUMsRUFBRUMsQ0FBQyxFQUFFO0VBQzFCO0VBQ0EsSUFBSUMsTUFBTSxHQUFHLENBQUM7RUFDZCxJQUFJRixDQUFDLENBQUNwQixNQUFNLEdBQUdxQixDQUFDLENBQUNyQixNQUFNLEVBQUU7SUFBRXNCLE1BQU0sR0FBR0YsQ0FBQyxDQUFDcEIsTUFBTSxHQUFHcUIsQ0FBQyxDQUFDckIsTUFBTTtFQUFFO0VBQ3pELElBQUl1QixJQUFJLEdBQUdGLENBQUMsQ0FBQ3JCLE1BQU07RUFDbkIsSUFBSW9CLENBQUMsQ0FBQ3BCLE1BQU0sR0FBR3FCLENBQUMsQ0FBQ3JCLE1BQU0sRUFBRTtJQUFFdUIsSUFBSSxHQUFHSCxDQUFDLENBQUNwQixNQUFNO0VBQUU7RUFDNUM7RUFDQTtFQUNBO0VBQ0EsSUFBSXdCLEdBQUcsR0FBR0MsS0FBSyxDQUFDRixJQUFJLENBQUM7RUFDckIsSUFBSUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO0VBQ1hGLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDO0VBQ1YsS0FBSyxJQUFJRyxDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLEdBQUdKLElBQUksRUFBRUksQ0FBQyxFQUFFLEVBQUU7SUFDM0IsSUFBSU4sQ0FBQyxDQUFDTSxDQUFDLENBQUMsSUFBSU4sQ0FBQyxDQUFDSyxDQUFDLENBQUMsRUFBRTtNQUNkRixHQUFHLENBQUNHLENBQUMsQ0FBQyxHQUFHSCxHQUFHLENBQUNFLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFDckIsQ0FBQyxNQUFNO01BQ0hGLEdBQUcsQ0FBQ0csQ0FBQyxDQUFDLEdBQUdELENBQUM7SUFDZDtJQUNBLE9BQU9BLENBQUMsR0FBRyxDQUFDLElBQUlMLENBQUMsQ0FBQ00sQ0FBQyxDQUFDLElBQUlOLENBQUMsQ0FBQ0ssQ0FBQyxDQUFDLEVBQUU7TUFBRUEsQ0FBQyxHQUFHRixHQUFHLENBQUNFLENBQUMsQ0FBQztJQUFFO0lBQzVDLElBQUlMLENBQUMsQ0FBQ00sQ0FBQyxDQUFDLElBQUlOLENBQUMsQ0FBQ0ssQ0FBQyxDQUFDLEVBQUU7TUFBRUEsQ0FBQyxFQUFFO0lBQUU7RUFDN0I7RUFDQTtFQUNBQSxDQUFDLEdBQUcsQ0FBQztFQUNMLEtBQUssSUFBSTNCLENBQUMsR0FBR3VCLE1BQU0sRUFBRXZCLENBQUMsR0FBR3FCLENBQUMsQ0FBQ3BCLE1BQU0sRUFBRUQsQ0FBQyxFQUFFLEVBQUU7SUFDcEMsT0FBTzJCLENBQUMsR0FBRyxDQUFDLElBQUlOLENBQUMsQ0FBQ3JCLENBQUMsQ0FBQyxJQUFJc0IsQ0FBQyxDQUFDSyxDQUFDLENBQUMsRUFBRTtNQUFFQSxDQUFDLEdBQUdGLEdBQUcsQ0FBQ0UsQ0FBQyxDQUFDO0lBQUU7SUFDNUMsSUFBSU4sQ0FBQyxDQUFDckIsQ0FBQyxDQUFDLElBQUlzQixDQUFDLENBQUNLLENBQUMsQ0FBQyxFQUFFO01BQUVBLENBQUMsRUFBRTtJQUFFO0VBQzdCO0VBQ0EsT0FBT0EsQ0FBQztBQUNWOztBQUdBO0FBQ0E7QUFDQTtBQUNPLFNBQVNFLHFCQUFxQkEsQ0FBQ3hCLE1BQU0sRUFBRTtFQUM1QyxPQUFPQSxNQUFNLENBQUN5QixRQUFRLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQ3pCLE1BQU0sQ0FBQzBCLFVBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDMUIsTUFBTSxDQUFDMkIsS0FBSyxDQUFDLFNBQVMsQ0FBQztBQUN4Rjs7QUFFQTtBQUNBO0FBQ0E7QUFDTyxTQUFTQyxzQkFBc0JBLENBQUM1QixNQUFNLEVBQUU7RUFDN0MsT0FBTyxDQUFDQSxNQUFNLENBQUN5QixRQUFRLENBQUMsTUFBTSxDQUFDLElBQUl6QixNQUFNLENBQUN5QixRQUFRLENBQUMsSUFBSSxDQUFDO0FBQzFEIiwiaWdub3JlTGlzdCI6W119
