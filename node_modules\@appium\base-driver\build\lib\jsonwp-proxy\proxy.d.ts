export class JW<PERSON>roxy {
    /**
     * @param {import('@appium/types').ProxyOptions} [opts={}]
     */
    constructor(opts?: import("@appium/types").ProxyOptions);
    /** @type {string} */
    scheme: string;
    /** @type {string} */
    server: string;
    /** @type {number} */
    port: number;
    /** @type {string} */
    base: string;
    /** @type {string} */
    reqBasePath: string;
    /** @type {string?} */
    sessionId: string | null;
    /** @type {number} */
    timeout: number;
    /** @type {Protocol | null | undefined} */
    _downstreamProtocol: Protocol | null | undefined;
    /** @type {ProxyRequest[]} */
    _activeRequests: ProxyRequest[];
    httpAgent: http.Agent;
    httpsAgent: https.Agent;
    protocolConverter: ProtocolConverter;
    _log: import("@appium/types").AppiumLogger | undefined;
    get log(): import("@appium/types").AppiumLogger;
    /**
     * Performs requests to the downstream server
     *
     * @private - Do not call this method directly,
     * it uses client-specific arguments and responses!
     *
     * @param {import('axios').RawAxiosRequestConfig} requestConfig
     * @returns {Promise<import('axios').AxiosResponse>}
     */
    private request;
    /**
     * @returns {number}
     */
    getActiveRequestsCount(): number;
    cancelActiveRequests(): void;
    /**
     * @param {Protocol | null | undefined} value
     */
    set downstreamProtocol(value: Protocol | null | undefined);
    get downstreamProtocol(): Protocol | null | undefined;
    /**
     *
     * @param {string} url
     * @param {string} [method]
     * @returns {string}
     */
    getUrlForProxy(url: string, method?: string): string;
    /**
     *
     * @param {string} url
     * @param {string} method
     * @param {import('@appium/types').HTTPBody} [body=null]
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    proxy(url: string, method: string, body?: import("@appium/types").HTTPBody): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
    /**
     *
     * @param {Record<string, any>} resObj
     * @returns {Protocol | undefined}
     */
    getProtocolFromResBody(resObj: Record<string, any>): Protocol | undefined;
    /**
     * @deprecated This method is not used anymore and will be removed
     *
     * @param {string} url
     * @param {import('@appium/types').HTTPMethod} method
     * @returns {string|undefined}
     */
    requestToCommandName(url: string, method: import("@appium/types").HTTPMethod): string | undefined;
    /**
     *
     * @param {string} url
     * @param {import('@appium/types').HTTPMethod} method
     * @param {import('@appium/types').HTTPBody} [body=null]
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    proxyCommand(url: string, method: import("@appium/types").HTTPMethod, body?: import("@appium/types").HTTPBody): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
    /**
     *
     * @param {string} url
     * @param {import('@appium/types').HTTPMethod} method
     * @param {import('@appium/types').HTTPBody} [body=null]
     * @returns {Promise<import('@appium/types').HTTPBody>}
     */
    command(url: string, method: import("@appium/types").HTTPMethod, body?: import("@appium/types").HTTPBody): Promise<import("@appium/types").HTTPBody>;
    /**
     *
     * @param {string} url
     * @returns {string | null}
     */
    getSessionIdFromUrl(url: string): string | null;
    /**
     *
     * @param {import('express').Request} req
     * @param {import('express').Response} res
     */
    proxyReqRes(req: import("express").Request, res: import("express").Response): Promise<void>;
    /**
     *
     * @param {string} url
     * @returns {ParsedUrl}
     */
    _parseUrl(url: string): ParsedUrl;
    /**
     *
     * @param {ParsedUrl} parsedUrl
     * @returns {string}
     */
    _toNormalizedPathname(parsedUrl: ParsedUrl): string;
}
export default JWProxy;
export type ProxyError = Error & {
    response: {
        data: import("type-fest").JsonObject;
        status: import("http-status-codes").StatusCodes;
    };
};
export type ParsedUrl = nodeUrl.UrlWithStringQuery;
export type Protocol = (typeof PROTOCOLS)[keyof typeof PROTOCOLS];
import { ProxyRequest } from './proxy-request';
import http from 'http';
import https from 'https';
import ProtocolConverter from './protocol-converter';
import nodeUrl from 'node:url';
import { PROTOCOLS } from '../constants';
//# sourceMappingURL=proxy.d.ts.map