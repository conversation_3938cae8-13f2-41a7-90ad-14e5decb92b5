<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="prettify.css">
    <link rel="stylesheet" href="base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">All files</span></h1>
    <h2>
        Statements: <span class="metric">98.01% <small>(148 / 151)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">92.94% <small>(79 / 85)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">100% <small>(20 / 20)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">99.31% <small>(143 / 144)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric"><span class="ignore-none">none</span></span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"></div>
</div>
<div class="body">
<div class="coverage-summary">
<table>
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="lib/"><a href="lib/index.html">lib/</a></td>
	<td data-value="98.01" class="pic high"><span class="cover-fill" style="width: 98px;"></span><span class="cover-empty" style="width:2px;"></span></td>
	<td data-value="98.01" class="pct high">98.01%</td>
	<td data-value="151" class="abs high">(148&nbsp;/&nbsp;151)</td>
	<td data-value="92.94" class="pct high">92.94%</td>
	<td data-value="85" class="abs high">(79&nbsp;/&nbsp;85)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">(20&nbsp;/&nbsp;20)</td>
	<td data-value="99.31" class="pct high">99.31%</td>
	<td data-value="144" class="abs high">(143&nbsp;/&nbsp;144)</td>
	</tr>

</tbody>
</table>
</div>
</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Wed Jul 01 2015 04:16:19 GMT+0000 (UTC)</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
