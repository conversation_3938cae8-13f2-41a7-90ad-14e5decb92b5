<%- answers.isUsingTypeScript || answers.esmSupport
    ? "import { Given, When, Then } from '@wdio/cucumber-framework';"
    : "const { Given, When, Then } = require('@wdio/cucumber-framework');" %>
<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import { expect, $ } from '@wdio/globals'`
    : `const { expect, $ } = require('@wdio/globals')` %>
<%
/**
 * step definition with page objects
 */
if (answers.usePageObjects) { %>
<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import LoginPage from '${answers.relativePath}/login.page${answers.esmSupport ? '.js' : ''}';`
    : `const LoginPage = require('${answers.relativePath}/login.page');` %>
<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import SecurePage from '${answers.relativePath}/secure.page${answers.esmSupport ? '.js' : ''}';`
    : `const SecurePage = require('${answers.relativePath}/secure.page');` %>

const pages = {
    login: LoginPage
}

Given(/^I am on the (\w+) page$/, async (page) => {
    await pages[page].open()
});

When(/^I login with (\w+) and (.+)$/, async (username, password) => {
    await LoginPage.login(username, password)
});

Then(/^I should see a flash message saying (.*)$/, async (message) => {
    await expect(SecurePage.flashAlert).toBeExisting();
    await expect(SecurePage.flashAlert).toHaveText(expect.stringContaining(message));
});
<% } else {

/**
 * step definition without page objects
 */
%>
Given(/^I am on the (\w+) page$/, async (page) => {
    await browser.url(`https://the-internet.herokuapp.com/${page}`);
});

When(/^I login with (\w+) and (.+)$/, async (username, password) => {
    await $('#username').setValue(username);
    await $('#password').setValue(password);
    await $('button[type="submit"]').click();
});

Then(/^I should see a flash message saying (.*)$/, async (message) => {
    await expect($('#flash')).toBeExisting();
    await expect($('#flash')).toHaveText(expect.stringContaining(message));
});
<% } %>
