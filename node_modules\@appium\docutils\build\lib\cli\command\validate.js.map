{"version": 3, "file": "validate.js", "sourceRoot": "", "sources": ["../../../../lib/cli/command/validate.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAEH,6CAAqC;AAErC,uCAA0C;AAC1C,6CAAiE;AACjE,yCAAuC;AACvC,oCAA2C;AAE3C,MAAM,GAAG,GAAG,IAAA,kBAAS,EAAC,UAAU,CAAC,CAAC;AAElC,IAAK,oBAGJ;AAHD,WAAK,oBAAoB;IACvB,yDAAiC,CAAA;IACjC,+CAAuB,CAAA;AACzB,CAAC,EAHI,oBAAoB,KAApB,oBAAoB,QAGxB;AAED,MAAM,IAAI,GAAG;IACX,MAAM,EAAE;QACN,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,6BAA6B;QAC1C,KAAK,EAAE,oBAAoB,CAAC,QAAQ;QACpC,IAAI,EAAE,SAAS;KAChB;IACD,YAAY,EAAE;QACZ,kBAAkB,EAAE,cAAc;QAClC,WAAW,EAAE,oBAAoB;QACjC,KAAK,EAAE,oBAAoB,CAAC,KAAK;QACjC,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;KACf;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,sBAAsB;QAC1C,WAAW,EAAE,wBAAwB;QACrC,KAAK,EAAE,oBAAoB,CAAC,KAAK;QACjC,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;KACf;IACD,MAAM,EAAE;QACN,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,+BAA+B;QAC5C,KAAK,EAAE,oBAAoB,CAAC,QAAQ;QACpC,IAAI,EAAE,SAAS;KAChB;IACD,aAAa,EAAE;QACb,kBAAkB,EAAE,sBAAsB;QAC1C,WAAW,EAAE,4BAA4B;QACzC,KAAK,EAAE,oBAAoB,CAAC,KAAK;QACjC,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;KACf;IACD,eAAe,EAAE;QACf,kBAAkB,EAAE,iBAAiB;QACrC,QAAQ,EAAE,uBAAuB;QACjC,KAAK,EAAE,oBAAoB,CAAC,KAAK;QACjC,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;KACf;IACD,UAAU,EAAE;QACV,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,iCAAiC;QAC9C,KAAK,EAAE,oBAAoB,CAAC,QAAQ;QACpC,IAAI,EAAE,SAAS;KAChB;CACyC,CAAC;AAI7C,kBAAe;IACb,OAAO,EAAE,UAAU;IACnB,QAAQ,EAAE,sBAAsB;IAChC,OAAO,CAAC,KAAK;QACX,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACrD,OAAO,qGAAqG,CAAC;YAC/G,CAAC;YACD,OAAO,IAAA,yBAAiB,EAAC,IAAI,EAAE,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC;IACD,KAAK,CAAC,OAAO,CAAC,IAAI;QAChB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,SAAS,GAAG,IAAI,4BAAiB,CAAC,IAAI,CAAC;aAC1C,IAAI,CAAC,4BAAiB,CAAC,KAAK,EAAE,CAAC,KAAuB,EAAE,EAAE;YACzD,GAAG,CAAC,IAAI,CAAC,eAAe,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9C,CAAC,CAAC;aACD,IAAI,CAAC,4BAAiB,CAAC,GAAG,EAAE,CAAC,QAAgB,EAAE,EAAE;YAChD,UAAU,GAAG,QAAQ,CAAC;QACxB,CAAC,CAAC;aACD,EAAE,CAAC,4BAAiB,CAAC,OAAO,EAAE,CAAC,GAAkB,EAAE,EAAE;YACpD,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC,CAAC;aACD,EAAE,CAAC,4BAAiB,CAAC,OAAO,EAAE,CAAC,GAAW,EAAE,EAAE;YAC7C,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;QAEL,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;QAE3B,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,IAAI,qBAAa,CACrB,0BAA0B,UAAU,IAAI,cAAI,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAC9E,CAAC;QACJ,CAAC;IACH,CAAC;CACwC,CAAC"}