{"name": "@wdio/runner", "version": "9.17.0", "description": "A WebdriverIO service that runs tests in arbitrary environments", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/webdriverio/webdriverio/tree/main/packages/wdio-runner", "license": "MIT", "engines": {"node": ">=18.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/webdriverio/webdriverio.git", "directory": "packages/wdio-runner"}, "keywords": ["webdriver", "wdio", "wdio-reporter"], "bugs": {"url": "https://github.com/webdriverio/webdriverio/issues"}, "type": "module", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.js"}}, "typeScriptVersion": "3.8.3", "dependencies": {"@types/node": "^20.11.28", "@wdio/config": "9.17.0", "@wdio/dot-reporter": "9.17.0", "@wdio/globals": "9.17.0", "@wdio/logger": "9.16.2", "@wdio/types": "9.16.2", "@wdio/utils": "9.17.0", "deepmerge-ts": "^7.0.3", "webdriver": "9.17.0", "webdriverio": "9.17.0"}, "peerDependencies": {"expect-webdriverio": "^5.3.4", "webdriverio": "^9.0.0"}, "peerDependenciesMeta": {"expect-webdriverio": {"optional": false}, "webdriverio": {"optional": false}}, "publishConfig": {"access": "public"}, "gitHead": "796df04f0f5c744d5ef4bafd3759995fa4829d6f"}