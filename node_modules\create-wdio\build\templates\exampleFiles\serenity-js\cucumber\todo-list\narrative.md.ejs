Serenity/JS Todo List Example

Narrative:

This is a simple example of a Serenity/JS project, interacting with a [TodoMVC](http://todomvc.com/) application
available at [todo-app.serenity-js.org/](https://todo-app.serenity-js.org/).

This note is called _"the narrative"_. It can be used to provide the context around the business capability of your
product ("Reporting Results" in this case) and its features that help to enable this capability.

**Please note:** While [Cucumber](https://github.com/cucumber/cucumber-js) allows you to capture a description
of each feature in the `.feature` file, [Serenity/JS](https://serenityjs.org) allows us to group those `.feature`
files in directories corresponding to "epics", "themes" or "business capabilities" of your system and provide
each one of those with additional context using this `narrative.txt` file.

**By the way:** Did you notice that you can use **[markdown syntax](https://www.markdownguide.org/)** to better express
your thoughts?
