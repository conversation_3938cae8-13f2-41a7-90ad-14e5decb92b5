body, html {
    margin:0; padding: 0;
}
body {
    font-family: Helvetica Neue, Helvetica,Arial;
    font-size: 10pt;
}
div.header, div.footer {
    background: #eee;
    padding: 1em;
}
div.header {
    z-index: 100;
    position: fixed;
    top: 0;
    border-bottom: 1px solid #666;
    width: 100%;
}
div.footer {
    border-top: 1px solid #666;
}
div.body {
    margin-top: 10em;
}
div.meta {
    font-size: 90%;
    text-align: center;
}
h1, h2, h3 {
    font-weight: normal;
}
h1 {
    font-size: 12pt;
}
h2 {
    font-size: 10pt;
}
pre {
    font-family: Consolas, Menlo, Monaco, monospace;
    margin: 0;
    padding: 0;
    line-height: 14px;
    font-size: 14px;
    -moz-tab-size: 2;
    -o-tab-size:  2;
    tab-size: 2;
}

div.path { font-size: 110%; }
div.path a:link, div.path a:visited { color: #000; }
table.coverage { border-collapse: collapse; margin:0; padding: 0 }

table.coverage td {
    margin: 0;
    padding: 0;
    color: #111;
    vertical-align: top;
}
table.coverage td.line-count {
    width: 50px;
    text-align: right;
    padding-right: 5px;
}
table.coverage td.line-coverage {
    color: #777 !important;
    text-align: right;
    border-left: 1px solid #666;
    border-right: 1px solid #666;
}

table.coverage td.text {
}

table.coverage td span.cline-any {
    display: inline-block;
    padding: 0 5px;
    width: 40px;
}
table.coverage td span.cline-neutral {
    background: #eee;
}
table.coverage td span.cline-yes {
    background: #b5d592;
    color: #999;
}
table.coverage td span.cline-no {
    background: #fc8c84;
}

.cstat-yes { color: #111; }
.cstat-no { background: #fc8c84; color: #111; }
.fstat-no { background: #ffc520; color: #111 !important; }
.cbranch-no { background:  yellow !important; color: #111; }

.cstat-skip { background: #ddd; color: #111; }
.fstat-skip { background: #ddd; color: #111 !important; }
.cbranch-skip { background: #ddd !important; color: #111; }

.missing-if-branch {
    display: inline-block;
    margin-right: 10px;
    position: relative;
    padding: 0 4px;
    background: black;
    color: yellow;
}

.skip-if-branch {
    display: none;
    margin-right: 10px;
    position: relative;
    padding: 0 4px;
    background: #ccc;
    color: white;
}

.missing-if-branch .typ, .skip-if-branch .typ {
    color: inherit !important;
}

.entity, .metric { font-weight: bold; }
.metric { display: inline-block; border: 1px solid #333; padding: 0.3em; background: white; }
.metric small { font-size: 80%; font-weight: normal; color: #666; }

div.coverage-summary table { border-collapse: collapse; margin: 3em; font-size: 110%; }
div.coverage-summary td, div.coverage-summary table  th { margin: 0; padding: 0.25em 1em; border-top: 1px solid #666; border-bottom: 1px solid #666; }
div.coverage-summary th { text-align: left; border: 1px solid #666; background: #eee; font-weight: normal; }
div.coverage-summary th.file { border-right: none !important; }
div.coverage-summary th.pic { border-left: none !important; text-align: right; }
div.coverage-summary th.pct { border-right: none !important; }
div.coverage-summary th.abs { border-left: none !important; text-align: right; }
div.coverage-summary td.pct { text-align: right; border-left: 1px solid #666; }
div.coverage-summary td.abs { text-align: right; font-size: 90%; color: #444; border-right: 1px solid #666; }
div.coverage-summary td.file { border-left: 1px solid #666; white-space: nowrap;  }
div.coverage-summary td.pic { min-width: 120px !important;  }
div.coverage-summary a:link { text-decoration: none; color: #000; }
div.coverage-summary a:visited { text-decoration: none; color: #777; }
div.coverage-summary a:hover { text-decoration: underline; }
div.coverage-summary tfoot td { border-top: 1px solid #666; }

div.coverage-summary .sorter {
    height: 10px;
    width: 7px;
    display: inline-block;
    margin-left: 0.5em;
    background: url(sort-arrow-sprite.png) no-repeat scroll 0 0 transparent;
}
div.coverage-summary .sorted .sorter {
    background-position: 0 -20px;
}
div.coverage-summary .sorted-desc .sorter {
    background-position: 0 -10px;
}

.high { background: #b5d592 !important; }
.medium { background: #ffe87c !important; }
.low { background: #fc8c84 !important; }

span.cover-fill, span.cover-empty {
    display:inline-block;
    border:1px solid #444;
    background: white;
    height: 12px;
}
span.cover-fill {
    background: #ccc;
    border-right: 1px solid #444;
}
span.cover-empty {
    background: white;
    border-left: none;
}
span.cover-full {
    border-right: none !important;
}
pre.prettyprint {
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}
.com { color: #999 !important; }
.ignore-none { color: #999; font-weight: normal; }
