<%  if (answers.framework === 'jasmine') { %>
    // Options to be passed to <PERSON>.
    jasmineOpts: {
        // Jasmine default timeout
        defaultTimeoutInterval: 60000,
        //
        // The Jasmine framework allows interception of each assertion in order to log the state of the application
        // or website depending on the result. For example, it is pretty handy to take a screenshot every time
        // an assertion fails.
        expectationResultHandler: function(passed, assertion) {
            // do something
        }
    },
<% } else if (answers.serenityAdapter === 'jasmine') { %>
    // Jasmine configuration, see:
    //  https://serenity-js.org/api/jasmine-adapter/interface/JasmineConfig
    jasmineOpts: {
        defaultTimeoutInterval: 60000,
    },
<% } %>