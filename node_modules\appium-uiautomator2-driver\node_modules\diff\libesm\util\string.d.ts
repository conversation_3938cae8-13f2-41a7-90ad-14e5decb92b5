export declare function longestCommonPrefix(str1: string, str2: string): string;
export declare function longestCommonSuffix(str1: string, str2: string): string;
export declare function replacePrefix(string: string, oldPrefix: string, newPrefix: string): string;
export declare function replaceSuffix(string: string, oldSuffix: string, newSuffix: string): string;
export declare function removePrefix(string: string, oldPrefix: string): string;
export declare function removeSuffix(string: string, oldSuffix: string): string;
export declare function maximumOverlap(string1: string, string2: string): string;
/**
 * Returns true if the string consistently uses Windows line endings.
 */
export declare function hasOnlyWinLineEndings(string: string): boolean;
/**
 * Returns true if the string consistently uses Unix line endings.
 */
export declare function hasOnlyUnixLineEndings(string: string): boolean;
export declare function trailingWs(string: string): string;
export declare function leadingWs(string: string): string;
//# sourceMappingURL=string.d.ts.map