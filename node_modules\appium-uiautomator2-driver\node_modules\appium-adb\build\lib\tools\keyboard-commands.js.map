{"version": 3, "file": "keyboard-commands.js", "sourceRoot": "", "sources": ["../../../lib/tools/keyboard-commands.js"], "names": [], "mappings": ";;;;;AAkBA,oCAmBC;AAQD,sDAYC;AAQD,4BAIC;AASD,sCAOC;AAQD,kCAOC;AAQD,8BAEC;AAQD,gCAEC;AAQD,wBAEC;AAQD,gCAWC;AAWD,8BAoBC;AAWD,0CAiBC;AAhND,4CAAmC;AACnC,oDAAuB;AACvB,uCAA4C;AAC5C,wDAAyB;AAEzB,MAAM,WAAW,GAAG,GAAG,CAAC;AACxB,MAAM,YAAY,GAAG,CAAC,CAAC;AAEvB;;;;;;;;;GASG;AACI,KAAK,UAAU,YAAY,CAAE,SAAS,GAAG,IAAI;IAClD,IAAI,EAAC,eAAe,EAAE,gBAAgB,EAAC,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC7E,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,eAAG,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACrD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,2CAA2C;IAC3C,KAAK,MAAM,OAAO,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,EAAE,CAAC;QAClD,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,2BAAgB,EAAC,KAAK,IAAI,EAAE;gBACvC,CAAC,EAAC,eAAe,EAAC,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;gBACzD,OAAO,CAAC,eAAe,CAAC;YAC1B,CAAC,EAAE,EAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAC,CAAC,CAAC;QAC3C,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,qBAAqB;IACzC,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC;QAC7D,MAAM,eAAe,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzD,MAAM,mBAAmB,GAAG,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnE,OAAO;YACL,eAAe,EAAE,CAAC,CAAC,CAAC,eAAe,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;YACrE,gBAAgB,EAAE,CAAC,CAAC,CAAC,mBAAmB,IAAI,mBAAmB,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;SAC/E,CAAC;IACJ,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9E,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,QAAQ,CAAE,OAAO;IACrC,0BAA0B;IAC1B,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;IACxC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;AACrD,CAAC;AAGD;;;;;GAKG;AACI,KAAK,UAAU,aAAa;IACjC,IAAI,CAAC;QACH,OAAO,oBAAoB,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,IAAI,KAAK,CAAC,kDAAkD,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IACnF,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,WAAW;IAC/B,IAAI,CAAC;QACH,OAAO,oBAAoB,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,IAAI,KAAK,CAAC,gDAAgD,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IACjF,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,SAAS,CAAE,KAAK;IACpC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7C,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,UAAU,CAAE,KAAK;IACrC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,MAAM,CAAE,KAAK;IACjC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,UAAU;IAC9B,IAAI,CAAC;QACH,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,sBAAsB,CAAC,CAAC;QACrE,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,IAAI,KAAK,CAAC,8CAA8C,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IAC/E,CAAC;AACH,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,SAAS,CAAE,IAAI;IACnC,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;QAChB,OAAO;IACT,CAAC;IAED,MAAM,WAAW,GAAG,GAAG,IAAI,EAAE,CAAC;IAC9B,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1E,IAAI,IAAI,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;IAC1C,+GAA+G;IAC/G,MAAM,qBAAqB,GAAG,mBAAmB,CAAC;IAClD,IAAI,WAAW,KAAK,WAAW,IAAI,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;QAC3E,IAAI,gBAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF,CAAC;QACJ,CAAC;QACD,MAAM,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAChD,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;IAC/C,CAAC;IACD,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,eAAe,CAAE,GAAG,EAAE,EAAE;IAC5C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC5C,IAAI,WAAW,KAAK,GAAG,EAAE,CAAC;QACxB,eAAG,CAAC,KAAK,CAAC,oCAAoC,GAAG,iCAAiC,CAAC,CAAC;IACtF,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,gDAAgD;QAChD,MAAM,kBAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IACD,IAAI,CAAC;QACH,OAAO,MAAM,EAAE,EAAE,CAAC;IACpB,CAAC;YAAS,CAAC;QACT,IAAI,WAAW,IAAI,WAAW,KAAK,GAAG,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;AACH,CAAC;AAED,2BAA2B;AAE3B;;;GAGG;AACH,SAAS,oBAAoB,CAAE,MAAM;IACnC,uBAAuB;IACvB,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QACtC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACvC,yDAAyD;YACzD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,aAAa"}