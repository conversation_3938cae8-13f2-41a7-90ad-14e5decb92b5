"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalFfmpegCheck = exports.optionalGstreamerCheck = exports.optionalBundletoolCheck = void 0;
const appium_android_driver_1 = require("appium-android-driver");
exports.optionalBundletoolCheck = appium_android_driver_1.doctor.optionalBundletoolCheck;
exports.optionalGstreamerCheck = appium_android_driver_1.doctor.optionalGstreamerCheck;
exports.optionalFfmpegCheck = appium_android_driver_1.doctor.optionalFfmpegCheck;
//# sourceMappingURL=optional-checks.js.map