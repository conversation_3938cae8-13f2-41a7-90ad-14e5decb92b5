/**
 * @typedef {Object} SettingsAppOpts
 * @property {import('appium-adb').ADB} adb
 */
export class SettingsApp {
    /**
     * @param {SettingsAppOpts} opts
     */
    constructor(opts: SettingsAppOpts);
    /** @type {import('appium-adb').ADB} */
    adb: import("appium-adb").ADB;
    /** @type {import('@appium/logger').Logger} */
    log: import("@appium/logger").Logger;
    /**
     * @typedef {Object} SettingsAppStartupOptions
     * @property {number} [timeout=5000] The maximum number of milliseconds
     * to wait until the app has started
     * @property {boolean} [shouldRestoreCurrentApp=false] Whether to restore
     * the activity which was the current one before Settings startup
     * @property {boolean} [forceRestart=false] Whether to forcefully restart
     * the Settings app if it is already running
     */
    /**
     * Ensures that Appium Settings helper application is running
     * and starts it if necessary
     *
     * @param {SettingsAppStartupOptions} [opts={}]
     * @throws {Error} If Appium Settings has failed to start
     * @returns {Promise<SettingsApp>} self instance for chaining
     */
    requireRunning(opts?: {
        /**
         * The maximum number of milliseconds
         * to wait until the app has started
         */
        timeout?: number | undefined;
        /**
         * Whether to restore
         * the activity which was the current one before Settings startup
         */
        shouldRestoreCurrentApp?: boolean | undefined;
        /**
         * Whether to forcefully restart
         * the Settings app if it is already running
         */
        forceRestart?: boolean | undefined;
    }): Promise<SettingsApp>;
    /**
     * If the io.appium.settings package has running foreground service.
     * It returns the io.appium.settings's process existence for api level 25 and lower
     * becase the concept of foreground services has only been introduced since API 26
     *
     * @throws {Error} If the method gets an error in the adb shell execution.
     * @returns {Promise<boolean>} Return true if the device Settings app has a servicve running in foreground.
     */
    isRunningInForeground(): Promise<boolean>;
    /**
     * Performs broadcast and verifies the result of it
     *
     * @param {string[]} args Arguments passed to the `am broadcast` comand
     * @param {string} action The exception message in case of broadcast failure
     * @param {boolean} [requireRunningApp=true] Whether to run a check for a running Appium Settings app
     * @returns {Promise<string>}
     */
    checkBroadcast(args: string[], action: string, requireRunningApp?: boolean): Promise<string>;
    /**
     * Parses the output in JSON format retrieved from
     * the corresponding Appium Settings broadcast calls
     *
     * @param {string} output The actual command output
     * @param {string} entityName The name of the entity which is
     * going to be parsed
     * @returns {Object} The parsed JSON object
     * @throws {Error} If the output cannot be parsed
     * as a valid JSON
     */
    _parseJsonData(output: string, entityName: string): any;
    setAnimationState: typeof setAnimationState;
    setBluetoothState: typeof setBluetoothState;
    unpairAllBluetoothDevices: typeof unpairAllBluetoothDevices;
    getClipboard: typeof getClipboard;
    setGeoLocation: typeof setGeoLocation;
    getGeoLocation: typeof getGeoLocation;
    refreshGeoLocationCache: typeof refreshGeoLocationCache;
    listSupportedLocales: typeof listSupportedLocales;
    setDeviceLocale: typeof setDeviceLocale;
    scanMedia: typeof scanMedia;
    setDataState: typeof setDataState;
    setWifiState: typeof setWifiState;
    getNotifications: typeof getNotifications;
    adjustNotificationsPermissions: typeof adjustNotificationsPermissions;
    getSmsList: typeof getSmsList;
    performEditorAction: typeof performEditorAction;
    typeUnicode: typeof typeUnicode;
    makeMediaProjectionRecorder: typeof makeMediaProjectionRecorder;
    adjustMediaProjectionServicePermissions: typeof adjustMediaProjectionServicePermissions;
}
export type SettingsAppOpts = {
    adb: import("appium-adb").ADB;
};
import { setAnimationState } from './commands/animation';
import { setBluetoothState } from './commands/bluetooth';
import { unpairAllBluetoothDevices } from './commands/bluetooth';
import { getClipboard } from './commands/clipboard';
import { setGeoLocation } from './commands/geolocation';
import { getGeoLocation } from './commands/geolocation';
import { refreshGeoLocationCache } from './commands/geolocation';
import { listSupportedLocales } from './commands/locale';
import { setDeviceLocale } from './commands/locale';
import { scanMedia } from './commands/media';
import { setDataState } from './commands/network';
import { setWifiState } from './commands/network';
import { getNotifications } from './commands/notifications';
import { adjustNotificationsPermissions } from './commands/notifications';
import { getSmsList } from './commands/sms';
import { performEditorAction } from './commands/typing';
import { typeUnicode } from './commands/typing';
import { makeMediaProjectionRecorder } from './commands/media-projection';
import { adjustMediaProjectionServicePermissions } from './commands/media-projection';
//# sourceMappingURL=client.d.ts.map