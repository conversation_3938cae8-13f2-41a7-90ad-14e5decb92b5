/**
 * A shortcut for a successful required doctor check
 *
 * @param {string} message
 * @returns {<PERSON><PERSON><PERSON><PERSON>R<PERSON>ult}
 */
export function ok(message: string): <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
/**
 * A shortcut for an unsuccessful required doctor check
 *
 * @param {string} message
 * @returns {<PERSON><PERSON><PERSON><PERSON>R<PERSON>ult}
 */
export function nok(message: string): <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
/**
 * A shortcut for a successful optional doctor check
 *
 * @param {string} message
 * @returns {DoctorCheckResult}
 */
export function okOptional(message: string): <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
/**
 * A shortcut for an unsuccessful optional doctor check
 *
 * @param {string} message
 * @returns {DoctorCheckResult}
 */
export function nokOptional(message: string): <PERSON><PERSON><PERSON><PERSON>R<PERSON>ult;
/**
 * Throw this exception in the fix() method
 * of your doctor check to skip the actual fix if hasAutofix() is true
 */
export class FixSkippedError extends Error {
}
export type DoctorCheckResult = import("@appium/types").DoctorCheckResult;
//# sourceMappingURL=doctor.d.ts.map