{"name": "bare-events", "version": "2.5.4", "description": "Event emitters for JavaScript", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./errors": "./lib/errors.js"}, "files": ["index.js", "index.d.ts", "lib"], "scripts": {"test": "npm run lint && npm run test:bare && npm run test:node", "test:bare": "bare test.js", "test:node": "node test.js", "lint": "prettier . --check"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-events.git"}, "author": "Holepunch", "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-events/issues"}, "homepage": "https://github.com/holepunchto/bare-events#readme", "devDependencies": {"brittle": "^3.3.2", "prettier": "^3.4.2", "prettier-config-standard": "^7.0.0"}}