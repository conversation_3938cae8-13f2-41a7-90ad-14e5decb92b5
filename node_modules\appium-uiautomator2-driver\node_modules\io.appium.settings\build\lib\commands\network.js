"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setWifiState = setWifiState;
exports.setDataState = setDataState;
const constants_js_1 = require("../constants.js");
/**
 * Change the state of WiFi on the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @param {boolean} on - True to enable and false to disable it.
 * @param {boolean} [isEmulator=false] - Set it to true if the device under test
 *                                       is an emulator rather than a real device.
 */
async function setWifiState(on, isEmulator = false) {
    if (isEmulator) {
        // The svc command does not require to be root since API 26
        await this.adb.shell(['svc', 'wifi', on ? 'enable' : 'disable'], {
            privileged: await this.adb.getApiLevel() < 26,
        });
        return;
    }
    if (await this.adb.getApiLevel() < 30) {
        // Android below API 30 does not have a dedicated adb command
        // to manipulate wifi connection state, so try to do it via Settings app
        // as a workaround
        await this.checkBroadcast([
            '-a', constants_js_1.WIFI_CONNECTION_SETTING_ACTION,
            '-n', constants_js_1.WIFI_CONNECTION_SETTING_RECEIVER,
            '--es', 'setstatus', on ? 'enable' : 'disable'
        ], `${on ? 'enable' : 'disable'} WiFi`);
        return;
    }
    await this.adb.shell(['cmd', '-w', 'wifi', 'set-wifi-enabled', on ? 'enabled' : 'disabled']);
}
/**
 * Change the state of Data transfer on the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @param {boolean} on - True to enable and false to disable it.
 * @param {boolean} [isEmulator=false] - Set it to true if the device under test
 *                                       is an emulator rather than a real device.
 */
async function setDataState(on, isEmulator = false) {
    if (isEmulator) {
        // The svc command does not require to be root since API 26
        await this.adb.shell(['svc', 'data', on ? 'enable' : 'disable'], {
            privileged: await this.adb.getApiLevel() < 26,
        });
        return;
    }
    if (await this.adb.getApiLevel() < 30) {
        await this.checkBroadcast([
            '-a', constants_js_1.DATA_CONNECTION_SETTING_ACTION,
            '-n', constants_js_1.DATA_CONNECTION_SETTING_RECEIVER,
            '--es', 'setstatus', on ? 'enable' : 'disable'
        ], `${on ? 'enable' : 'disable'} data`);
        return;
    }
    await this.adb.shell(['cmd', 'phone', 'data', on ? 'enable' : 'disable']);
}
//# sourceMappingURL=network.js.map