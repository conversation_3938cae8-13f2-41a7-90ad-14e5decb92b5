{"name": "taller", "type": "module", "scripts": {"test": "wdio run wdio.conf.ts", "test:android": "wdio run wdio.conf.ts", "test:challenge": "wdio run wdio.conf.ts --spec ./test/specs/wikipedia.test.ts", "appium": "appium server", "appium:debug": "appium server --log-level debug", "setup": "npm install && mkdir -p screenshots logs", "clean": "rimraf screenshots/* logs/*"}, "devDependencies": {"@types/node": "^20.0.0", "@wdio/appium-service": "^9.17.0", "@wdio/cli": "^9.17.0", "@wdio/globals": "^9.17.0", "@wdio/local-runner": "^9.17.0", "@wdio/mocha-framework": "^9.17.0", "@wdio/spec-reporter": "^9.17.0", "appium": "^2.19.0", "appium-uiautomator2-driver": "^4.2.5", "ts-node": "^10.9.0", "typescript": "^5.0.0"}}