{"name": "is-number-like", "version": "1.0.8", "description": "Checks whether provided parameter looks like a number", "main": "lib/index.js", "scripts": {"test": "NODE_ENV=test node test | tap-difflet && standard", "cover": "istanbul cover --report none --print detail test/index.js", "view-cover": "istanbul report html && open ./coverage/index.html", "watch": "nodemon test | tap-difflet", "travis": "npm run cover -s && istanbul report lcov && ((cat coverage/lcov.info | coveralls) || exit 0)"}, "repository": {"type": "git", "url": "git+https://github.com/vigour-io/is-number-like.git"}, "keywords": ["is-number", "typeof", "number-like"], "author": "Vigour.io <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/vigour-io/is-number-like/issues"}, "homepage": "https://github.com/vigour-io/is-number-like#readme", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "dependencies": {"lodash.isfinite": "^3.3.2"}, "engines": {}, "devDependencies": {"ducktape": "^1.0.0", "coveralls": "^2.11.9", "nodemon": "^1.9.1", "pre-commit": "^1.1.3", "istanbul": "^0.4.4", "standard": "^8.1.0", "tape": "4.4.0", "tap-difflet": "0.6.0"}, "browserify": {"transform": ["bubleify"]}}