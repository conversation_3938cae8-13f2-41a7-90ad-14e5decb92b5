"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyRequest = void 0;
const axios_1 = __importDefault(require("axios"));
const bluebird_1 = require("bluebird");
const node_events_1 = __importDefault(require("node:events"));
const CANCEL_EVENT = 'cancel';
const FINISH_EVENT = 'finish';
class ProxyRequest {
    constructor(requestConfig) {
        this._requestConfig = requestConfig;
        this._ee = new node_events_1.default();
        this._resultPromise = null;
    }
    async execute() {
        if (this._resultPromise) {
            return await this._resultPromise;
        }
        try {
            this._resultPromise = Promise.race([
                this._makeRacingTimer(),
                this._makeRequest(),
            ]);
            return await this._resultPromise;
        }
        finally {
            this._ee.emit(FINISH_EVENT);
            this._ee.removeAllListeners();
        }
    }
    cancel() {
        this._ee.emit(CANCEL_EVENT);
    }
    async _makeRequest() {
        return await (0, axios_1.default)(this._requestConfig);
    }
    async _makeRacingTimer() {
        return await new Promise((resolve, reject) => {
            this._ee.once(FINISH_EVENT, resolve);
            this._ee.once(CANCEL_EVENT, () => reject(new bluebird_1.CancellationError('The request has been cancelled')));
        });
    }
}
exports.ProxyRequest = ProxyRequest;
//# sourceMappingURL=proxy-request.js.map