/**
 * @this {AndroidUiautomator2Driver}
 * @param {string|number} keycode
 * @param {number} [metastate]
 * @param {number} [flags]
 * @returns {Promise<void>}
 */
export function pressKeyCode(this: import("../driver").AndroidUiautomator2Driver, keycode: string | number, metastate?: number, flags?: number): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string|number} keycode
 * @param {number} metastate
 * @param {number} [flags]
 * @returns {Promise<void>}
 */
export function longPressKeyCode(this: import("../driver").AndroidUiautomator2Driver, keycode: string | number, metastate: number, flags?: number): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {number} keycode A valid Android key code. See https://developer.android.com/reference/android/view/KeyEvent
 * for the list of available key codes.
 * @param {number} [metastate] An integer in which each bit set to 1 represents a pressed meta key. See
 * https://developer.android.com/reference/android/view/KeyEvent for more details.
 * @param {string} [flags] Flags for the particular key event. See
 * https://developer.android.com/reference/android/view/KeyEvent for more details.
 * @param {boolean} [isLongPress=false] Whether to emulate long key press
 * @returns {Promise<void>}
 */
export function mobilePressKey(this: import("../driver").AndroidUiautomator2Driver, keycode: number, metastate?: number, flags?: string, isLongPress?: boolean): Promise<void>;
/**
 * Types the given Unicode string.
 * It is expected that the focus is already put
 * to the destination input field before this method is called.
 *
 * @this {AndroidUiautomator2Driver}
 * @param {string | number | boolean} text The text to type. Can be a string, number or boolean.
 * @returns {Promise<boolean>} `true` if the input text has been successfully sent to adb
 * @throws {Error} if `text` property has not been provided
 */
export function mobileType(this: import("../driver").AndroidUiautomator2Driver, text: string | number | boolean): Promise<boolean>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {import('appium-android-driver').SendKeysOpts} params
 * @returns {Promise<void>}
 */
export function doSendKeys(this: import("../driver").AndroidUiautomator2Driver, params: import("appium-android-driver").SendKeysOpts): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string|number} keycode
 * @param {number} [metastate]
 * @returns {Promise<void>}
 */
export function keyevent(this: import("../driver").AndroidUiautomator2Driver, keycode: string | number, metastate?: number): Promise<void>;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
//# sourceMappingURL=keyboard.d.ts.map