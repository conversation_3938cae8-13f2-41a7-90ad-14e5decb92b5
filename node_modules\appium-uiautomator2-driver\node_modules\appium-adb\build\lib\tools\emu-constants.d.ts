export declare const POWER_AC_STATES: {
    readonly POWER_AC_ON: "on";
    readonly POWER_AC_OFF: "off";
};
export declare const GSM_CALL_ACTIONS: {
    readonly GSM_CALL: "call";
    readonly GSM_ACCEPT: "accept";
    readonly GSM_CANCEL: "cancel";
    readonly GSM_HOLD: "hold";
};
export declare const GSM_VOICE_STATES: {
    readonly GSM_VOICE_UNREGISTERED: "unregistered";
    readonly GSM_VOICE_HOME: "home";
    readonly GSM_VOICE_ROAMING: "roaming";
    readonly GSM_VOICE_SEARCHING: "searching";
    readonly GSM_VOICE_DENIED: "denied";
    readonly GSM_VOICE_OFF: "off";
    readonly GSM_VOICE_ON: "on";
};
export declare const GSM_SIGNAL_STRENGTHS: readonly [0, 1, 2, 3, 4];
export declare const NETWORK_SPEED: {
    readonly GSM: "gsm";
    readonly SCSD: "scsd";
    readonly GPRS: "gprs";
    readonly EDGE: "edge";
    readonly UMTS: "umts";
    readonly HSDPA: "hsdpa";
    readonly LTE: "lte";
    readonly EVDO: "evdo";
    readonly FULL: "full";
};
export declare const SENSORS: {
    readonly ACCELERATION: "acceleration";
    readonly GYROSCOPE: "gyroscope";
    readonly MAGNETIC_FIELD: "magnetic-field";
    readonly ORIENTATION: "orientation";
    readonly TEMPERATURE: "temperature";
    readonly PROXIMITY: "proximity";
    readonly LIGHT: "light";
    readonly PRESSURE: "pressure";
    readonly HUMIDITY: "humidity";
    readonly MAGNETIC_FIELD_UNCALIBRATED: "magnetic-field-uncalibrated";
    readonly GYROSCOPE_UNCALIBRATED: "gyroscope-uncalibrated";
    readonly HINGE_ANGLE0: "hinge-angle0";
    readonly HINGE_ANGLE1: "hinge-angle1";
    readonly HINGE_ANGLE2: "hinge-angle2";
    readonly HEART_RATE: "heart-rate";
    readonly RGBC_LIGHT: "rgbc-light";
};
//# sourceMappingURL=emu-constants.d.ts.map