{"name": "appium", "version": "2.19.0", "description": "Automation for Apps.", "keywords": ["automation", "javascript", "selenium", "webdriver", "ios", "android", "firefoxos", "testing"], "homepage": "https://appium.io", "bugs": {"url": "https://github.com/appium/appium/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/appium.git", "directory": "packages/appium"}, "license": "Apache-2.0", "author": "https://github.com/appium", "types": "./build/lib/main.d.ts", "bin": {"appium": "index.js"}, "directories": {"lib": "./lib"}, "files": ["lib", "build", "index.js", "driver.*", "support.*", "plugin.*", "scripts/autoinstall-extensions.js", "types", "tsconfig.json", "!build/tsconfig.tsbuildinfo"], "scripts": {"build:docs": "node docs/scripts/build-docs.js", "build:docs:preview": "cross-env APPIUM_DOCS_PREVIEW=1 npm run build:docs", "build:docs:cli": "node docs/scripts/gen-cli-args-docs.js", "build:docs:reference": "node docs/scripts/build-reference.js", "dev:docs": "npm run dev:docs:en", "dev:docs:en": "appium-docs build --serve --mkdocs-yml ./docs/mkdocs-en.yml", "dev:docs:ja": "appium-docs build --serve --mkdocs-yml ./docs/mkdocs-ja.yml", "dev:docs:zh": "appium-docs build --serve --mkdocs-yml ./docs/mkdocs-zh.yml", "install-docs-deps": "appium-docs init --no-mkdocs", "postinstall": "node ./scripts/autoinstall-extensions.js", "publish:docs": "cross-env APPIUM_DOCS_PUBLISH=1 npm run build:docs", "test": "npm run test:unit", "test:e2e": "mocha --timeout 1m --slow 30s \"./test/e2e/**/*.spec.js\"", "test:smoke": "cross-env APPIUM_HOME=./local_appium_home node ./index.js driver install uiautomator2 && cross-env APPIUM_HOME=./local_appium_home node ./index.js driver list", "test:unit": "mocha \"./test/unit/**/*.spec.js\""}, "dependencies": {"@appium/base-driver": "^9.18.0", "@appium/base-plugin": "^2.3.7", "@appium/docutils": "^1.1.1", "@appium/logger": "^1.7.1", "@appium/schema": "^0.8.1", "@appium/support": "^6.1.1", "@appium/types": "^0.26.0", "@sidvind/better-ajv-errors": "3.0.1", "ajv": "8.17.1", "ajv-formats": "3.0.1", "argparse": "2.0.1", "async-lock": "1.4.1", "asyncbox": "3.0.0", "axios": "1.9.0", "bluebird": "3.7.2", "lilconfig": "3.1.3", "lodash": "4.17.21", "lru-cache": "10.4.3", "ora": "5.4.1", "package-changed": "3.0.0", "resolve-from": "5.0.0", "semver": "7.7.2", "source-map-support": "0.5.21", "teen_process": "2.3.2", "type-fest": "4.41.0", "winston": "3.17.0", "wrap-ansi": "7.0.0", "ws": "8.18.2", "yaml": "2.8.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "publishConfig": {"access": "public"}, "gitHead": "c8fe4412525f7e1fa237813cf83fe7d98f0125eb"}