{"name": "@jest/types", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-types"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/pattern": "30.0.1", "@jest/schemas": "30.0.1", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-reports": "^3.0.4", "@types/node": "*", "@types/yargs": "^17.0.33", "chalk": "^4.1.2"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7"}