{"version": 3, "file": "args.js", "sourceRoot": "", "sources": ["../../../lib/cli/args.js"], "names": [], "mappings": ";;;;;;AAyTQ,sCAAa;AAzTrB,oDAAuB;AACvB,4CASsB;AACtB,oEAA4D;AAC5D,iDAAgD;AAChD,MAAM,cAAc,GAAG,UAAU,CAAC;AAClC,MAAM,cAAc,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,MAAM,mBAAmB,GAAG,CAAC,GAAG,gCAAa,CAAC,CAAC;AAE/C,iCAAiC;AACjC,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC,uBAAW,EAAE,uBAAW,CAAC,CAAC,CAAC;AAE5D,qEAAqE;AACrE,kCAAkC;AAClC,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC;IAClC;QACE,CAAC,QAAQ,CAAC;QACV;YACE,QAAQ,EAAE,KAAK;YACf,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,8BAA8B;YACpC,IAAI,EAAE,MAAM;SACb;KACF;CACF,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,gBAAgB,GAAG,gBAAC,CAAC,OAAO,CAAC,SAAS,gBAAgB;IAC1D,MAAM,aAAa,GAAG,EAAE,CAAC;IACzB,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;QACnC,aAAa,CAAC,IAAI,CAAC,GAAG;YACpB,CAAC,+BAAmB,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC;YACzC,CAAC,kCAAsB,CAAC,EAAE,eAAe,CAAC,IAAI,CAAC;YAC/C,CAAC,oCAAwB,CAAC,EAAE,iBAAiB,CAAC,IAAI,CAAC;YACnD,CAAC,iCAAqB,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC;YAC7C,CAAC,8BAAkB,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC;YACvC,CAAC,iCAAqB,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC;SAC9C,CAAC;IACJ,CAAC;IACD,OAAO,+GAA+G,CAAC,CACrH,aAAa,CACd,CAAC;AACJ,CAAC,CAAC,CAAC;AA+PoB,4CAAgB;AA7PvC;;;;GAIG;AACH,SAAS,YAAY,CAAC,IAAI;IACxB,OAAO,IAAI,GAAG,CAAC;QACb,GAAG,mBAAmB;QACtB;YACE,CAAC,aAAa,CAAC;YACf;gBACE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,YAAY;gBACpB,IAAI,EAAE,uBAAuB,IAAI,GAAG;gBACpC,IAAI,EAAE,eAAe;aACtB;SACF;QACD;YACE,CAAC,WAAW,CAAC;YACb;gBACE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,YAAY;gBACpB,IAAI,EAAE,oCAAoC,IAAI,UAAU;gBACxD,IAAI,EAAE,aAAa;aACpB;SACF;QACD;YACE,CAAC,WAAW,CAAC;YACb;gBACE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,YAAY;gBACpB,IAAI,EAAE,oCAAoC,IAAI,EAAE;gBAChD,IAAI,EAAE,SAAS;aAChB;SACF;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,IAAI;IAC3B,OAAO,IAAI,GAAG,CAAC;QACb,GAAG,mBAAmB;QACtB;YACE,CAAC,IAAI,CAAC;YACN;gBACE,IAAI,EAAE,KAAK;gBACX,IAAI,EACF,eAAe,IAAI,4BAA4B;oBAC/C,CAAC,IAAI,KAAK,uBAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC;aAC3D;SACF;QACD;YACE,CAAC,UAAU,CAAC;YACZ;gBACE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EACF,yBAAyB,IAAI,yCAAyC;oBACtE,GAAG,IAAI,uBAAuB,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAChE,IAAI,EAAE,aAAa;aACpB;SACF;QACD;YACE,CAAC,WAAW,CAAC;YACb;gBACE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,KAAK;gBACX,IAAI,EACF,mCAAmC,IAAI,IAAI;oBAC3C,mDAAmD;gBACrD,IAAI,EAAE,aAAa;aACpB;SACF;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,IAAI;IAC7B,OAAO,IAAI,GAAG,CAAC;QACb,GAAG,mBAAmB;QACtB;YACE,CAAC,IAAI,CAAC;YACN;gBACE,IAAI,EAAE,KAAK;gBACX,IAAI,EACF,eAAe,IAAI,8BAA8B;oBACjD,CAAC,IAAI,KAAK,uBAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC;aAC3D;SACF;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,cAAc,CAAC,IAAI;IAC1B,OAAO,IAAI,GAAG,CAAC;QACb,GAAG,mBAAmB;QACtB;YACE,CAAC,IAAI,CAAC;YACN;gBACE,IAAI,EAAE,KAAK;gBACX,IAAI,EACF,eAAe,IAAI,0CAA0C;oBAC7D,CAAC,IAAI,KAAK,uBAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC;aAC3D;SACF;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,cAAc,CAAC,IAAI;IAC1B,OAAO,IAAI,GAAG,CAAC;QACb,GAAG,mBAAmB;QACtB;YACE,CAAC,IAAI,CAAC;YACN;gBACE,IAAI,EAAE,KAAK;gBACX,IAAI,EACF,eAAe,IAAI,sDAAsD,IAAI,KAAK;oBAClF,oBAAoB,IAAI,yBAAyB,IAAI,gCAAgC;oBACrF,eAAe;oBACf,CAAC,IAAI,KAAK,uBAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC;aAC3D;SACF;QACD;YACE,CAAC,UAAU,CAAC;YACZ;gBACE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,YAAY;gBACpB,IAAI,EACF,+EAA+E;aAClF;SACF;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,WAAW,CAAC,IAAI;IACvB,OAAO,IAAI,GAAG,CAAC;QACb,GAAG,mBAAmB;QACtB;YACE,CAAC,IAAI,CAAC;YACN;gBACE,IAAI,EAAE,KAAK;gBACX,IAAI,EACF,eAAe,IAAI,sCAAsC;oBACzD,CAAC,IAAI,KAAK,uBAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC;aAC3D;SACF;QACD;YACE,CAAC,YAAY,CAAC;YACd;gBACE,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,GAAG;gBACV,IAAI,EAAE,KAAK;gBACX,IAAI,EACF,eAAe,IAAI,iDAAiD;oBACpE,iCAAiC,IAAI,GAAG;aAC3C;SACF;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,aAAa;IACpB,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,IAAA,uBAAY,GAAE,EAAE,GAAG,4BAA4B,CAAC,CAAC,CAAC;AACvE,CAAC;AAED;;;GAGG;AACH,MAAM,4BAA4B,GAAG,IAAI,GAAG,CAAC;IAC3C;QACE,CAAC,SAAS,CAAC;QACX;YACE,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,iBAAiB;YACvB,MAAM,EAAE,aAAa;YACrB,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,OAAO;SACd;KACF;IACD;QACE,CAAC,mBAAmB,CAAC;QACrB;YACE,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE,aAAa;YACrB,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,2CAA2C;SAClD;KACF;IACD;QACE,CAAC,mBAAmB,CAAC;QACrB;YACE,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE,aAAa;YACrB,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,8DAA8D;SACrE;KACF;IACD;QACE,CAAC,eAAe,CAAC;QACjB;YACE,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,aAAa;YACrB,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,gDAAgD;SACvD;KACF;IACD;QACE,CAAC,UAAU,CAAC;QACZ;YACE,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,KAAK;YACX,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,4CAA4C;SACnD;KACF;CACF,CAAC,CAAC;AAIH;;GAEG;AAEH;;;GAGG"}