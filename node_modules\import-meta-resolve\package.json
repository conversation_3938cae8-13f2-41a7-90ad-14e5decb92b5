{"name": "import-meta-resolve", "version": "4.1.0", "description": "Resolve things like Node.js — ponyfill for `import.meta.resolve`", "license": "MIT", "keywords": ["resolve", "node", "esm", "module"], "repository": "wooorm/import-meta-resolve", "bugs": "https://github.com/wooorm/import-meta-resolve/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["lib/", "index.d.ts", "index.js"], "devDependencies": {"@types/node": "^20.0.0", "@types/semver": "^7.0.0", "c8": "^9.0.0", "f-ck": "^2.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "semver": "^7.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.58.0"}, "scripts": {"prepack": "npm run generate && npm run build && npm run format", "generate": "node --conditions development script.js", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --experimental-import-meta-resolve test/baseline.js && node --experimental-import-meta-resolve test/baseline-async.js && node test/index.js", "test-coverage": "c8 --check-coverage --branches 75 --functions 75 --lines 75 --statements 75 --reporter lcov npm run test-api", "test": "npm run generate && npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "rules": {"complexity": "off", "max-depth": "off", "max-params": "off", "no-constant-condition": "off", "no-new": "off", "prefer-arrow-callback": "off", "unicorn/prefer-at": "off", "unicorn/prefer-string-replace-all": "off"}, "ignore": ["test/node_modules/"]}, "remarkConfig": {"plugins": ["preset-wooorm", ["remark-lint-maximum-heading-length", false]]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true, "ignoreFiles": ["lib/errors.d.ts"]}}