{"version": 3, "file": "android-manifest.js", "sourceRoot": "", "sources": ["../../../lib/tools/android-manifest.js"], "names": [], "mappings": ";;;;;AAoBA,oFAcC;AAWD,oEAeC;AAWD,4DAMC;AAYD,0CAmDC;AAgBD,wCAsCC;AASD,8EAQC;AAUD,8DAkCC;AA/PD,oDAAuB;AACvB,+CAAoC;AACpC,4CAAmC;AACnC,8CAIuB;AACvB,6CAAyD;AACzD,gDAAwB;AAExB;;;;;;;;GAQG;AACI,KAAK,UAAU,oCAAoC,CAAE,OAAO;IACjE,IAAI,OAAO,CAAC,QAAQ,CAAC,2BAAc,CAAC,EAAE,CAAC;QACrC,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,EACJ,IAAI,EAAE,UAAU,EAChB,kBAAkB,EAAE,EAClB,IAAI,EAAE,WAAW,GAClB,GACF,GAAG,MAAM,gCAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;IAClD,eAAG,CAAC,IAAI,CAAC,kBAAkB,UAAU,GAAG,CAAC,CAAC;IAC1C,eAAG,CAAC,IAAI,CAAC,wBAAwB,WAAW,GAAG,CAAC,CAAC;IACjD,OAAO,EAAC,UAAU,EAAE,WAAW,EAAC,CAAC;AACnC,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,4BAA4B,CAAE,OAAO;IACzD,eAAG,CAAC,KAAK,CAAC,qCAAqC,OAAO,GAAG,CAAC,CAAC;IAC3D,MAAM,eAAe,GAAG,OAAO,CAAC;IAChC,IAAI,OAAO,CAAC,QAAQ,CAAC,2BAAc,CAAC,EAAE,CAAC;QACrC,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,EAAC,gBAAgB,EAAC,GAAG,MAAM,gCAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;IACzE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,uCAAuC,eAAe,UAAU;YAChE,iCAAiC,CAClC,CAAC;IACJ,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,wBAAwB,CAAE,GAAG,EAAE,SAAS,GAAG,IAAI;IACnE,MAAM,MAAM,GAAG,SAAS,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1E,MAAM,qBAAqB,GAAG,IAAI,MAAM,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/E,OAAO,qBAAqB,IAAI,qBAAqB,CAAC,MAAM,IAAI,CAAC;QAC/D,CAAC,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AAED;;;;;;;;;GASG;AACI,KAAK,UAAU,eAAe,CAAE,QAAQ,EAAE,eAAe,EAAE,aAAa;IAC7E,MAAM,EAAC,QAAQ,EAAE,YAAY,EAAC,GAAG,MAAM,yBAAyB,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACvG,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,qFAAqF,CAAC,CAAC;IACzG,CAAC;IACD,MAAM,UAAU,GAAG,GAAG,QAAQ,MAAM,CAAC;IACrC,MAAM,cAAc,GAAG,cAAI,CAAC,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;IACjE,IAAI,MAAM,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;QAChC,MAAM,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IACD,IAAI,CAAC;QACH,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACvB,0DAA0D;QAC1D,MAAM,IAAI,GAAG;YACX,MAAM;YACN,IAAI,EAAE,UAAU;YAChB,YAAY,EAAE,QAAQ;YACtB,2BAA2B,EAAE,eAAe;YAC5C,yCAAyC,EAAE,aAAa;YACxD,IAAI,EAAE,cAAc;YACpB,IAAI;SACL,CAAC;QACF,eAAG,CAAC,KAAK,CAAC,iCAAiC,cAAI,CAAC,KAAK,CAAC;YACpD,EAAC,6CAA8C,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;YACrE,GAAG,IAAI;SACR,CAAC,GAAG,CAAC,CAAC;QACP,MAAM,IAAA,mBAAI,EAAC,EAAC,6CAA8C,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC1F,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,eAAG,CAAC,KAAK,CAAC,+DAA+D;YACvE,mBAAmB,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9C,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,GAAG;YACX,SAAS;YACT,IAAI,EAAE,QAAQ;YACd,2BAA2B,EAAE,eAAe;YAC5C,yCAAyC,EAAE,aAAa;YACxD,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,UAAU;YAChB,IAAI;SACL,CAAC;QACF,eAAG,CAAC,KAAK,CAAC,iCAAiC,cAAI,CAAC,KAAK,CAAC;YACpD,EAAC,6CAA8C,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;YACpE,GAAG,IAAI;SACR,CAAC,GAAG,CAAC,CAAC;QACP,IAAI,CAAC;YACH,MAAM,IAAA,mBAAI,EAAC,EAAC,6CAA8C,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACzF,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,gDAAgD,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IACD,eAAG,CAAC,KAAK,CAAC,6BAA6B,UAAU,GAAG,CAAC,CAAC;AACxD,CAAC;AAED;;;;;;;;;;;;;GAaG;AACI,KAAK,UAAU,cAAc,CAAE,QAAQ,EAAE,MAAM,EAAE,MAAM;IAC5D,eAAG,CAAC,KAAK,CAAC,uBAAuB,QAAQ,YAAY,MAAM,YAAY,MAAM,GAAG,CAAC,CAAC;IAClF,MAAM,aAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,IAAA,sBAAS,EAAC,GAAG,QAAQ,MAAM,CAAC,CAAC;IACnC,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC7C,IAAI,CAAC;QACH,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,YAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,eAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,IAAA,mBAAI,EAAC,EAAC,6CAA8C,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;gBAC/E,QAAQ,EAAE,MAAM,EAAE,YAAY;aAC/B,CAAC,CAAC;QACL,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;QACV,MAAM,IAAA,mBAAI,EAAC,EAAC,6CAA8C,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;YAC/E,KAAK,EAAE,MAAM,EAAE,YAAY;SAC5B,EAAE,EAAC,GAAG,EAAE,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC;IACpC,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,eAAG,CAAC,KAAK,CAAC,wDAAwD;YAChE,mBAAmB,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,OAAO,EAAE,CAAC;QACxC,IAAI,CAAC;YACH,6DAA6D;YAC7D,2DAA2D;YAC3D,iCAAiC;YACjC,eAAG,CAAC,KAAK,CAAC,iCAAiC,MAAM,GAAG,CAAC,CAAC;YACtD,MAAM,aAAG,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACxC,eAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC7B,MAAM,YAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,cAAI,CAAC,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;YAC3D,eAAG,CAAC,KAAK,CAAC,sCAAsC,MAAM,GAAG,CAAC,CAAC;YAC3D,MAAM,aAAG,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC1B,GAAG,EAAE,OAAO;aACb,CAAC,CAAC;QACL,CAAC;gBAAS,CAAC;YACT,MAAM,YAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IACD,eAAG,CAAC,KAAK,CAAC,4BAA4B,MAAM,gBAAgB,CAAC,CAAC;AAChE,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,iCAAiC,CAAE,OAAO;IAC9D,eAAG,CAAC,KAAK,CAAC,gBAAgB,OAAO,uDAAuD,CAAC,CAAC;IAC1F,IAAI,OAAO,CAAC,QAAQ,CAAC,2BAAc,CAAC,EAAE,CAAC;QACrC,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,EAAC,eAAe,EAAC,GAAG,MAAM,gCAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;IACxE,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,6BAA6B,CAAC,CAAC;AACtG,CAAC;AAED,4BAA4B;AAE5B;;;;;GAKG;AACI,KAAK,UAAU,yBAAyB,CAAE,OAAO;IACtD,MAAM,UAAU,GAAG,MAAM,YAAE,CAAC,IAAI,CAAC,cAAc,EAAE;QAC/C,GAAG,EAAE,cAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC;QACvC,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;IACH,6DAA6D;IAC7D,MAAM,gBAAgB,GAAG,EAAE,CAAC;IAC5B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,MAAM,YAAY,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC3D,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,+BAA+B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,eAAG,CAAC,IAAI,CAAC,qCAAqC,SAAS,gBAAgB,QAAQ,GAAG,CAAC,CAAC;YACpF,SAAS;QACX,CAAC;QACD,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG;YACzC,QAAQ;YACR,YAAY;SACb,CAAC;IACJ,CAAC;IACD,IAAI,gBAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAChC,eAAG,CAAC,IAAI,CAAC,mCAAmC,cAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK;YACjF,yCAAyC,CAAC,CAAC;QAC7C,OAAO;YACL,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;SACnB,CAAC;IACJ,CAAC;IAED,MAAM,gBAAgB,GAAG,gBAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IACtE,MAAM,MAAM,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IAClD,eAAG,CAAC,KAAK,CAAC,2CAA2C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC/E,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,aAAa"}