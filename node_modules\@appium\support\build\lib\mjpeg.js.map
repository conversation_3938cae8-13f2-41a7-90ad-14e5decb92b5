{"version": 3, "file": "mjpeg.js", "sourceRoot": "", "sources": ["../../lib/mjpeg.js"], "names": [], "mappings": ";;;;;;AAAA,oDAAuB;AACvB,sDAA2B;AAC3B,wDAAyB;AACzB,6CAA0C;AAC1C,mCAAgC;AAChC,iCAAsC;AACtC,kDAA0B;AAE1B,+CAA+C;AAC/C,IAAI,aAAa,GAAG,IAAI,CAAC;AAEzB;;GAEG;AACH,KAAK,UAAU,iBAAiB;IAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,IAAI,CAAC;YACH,aAAa,GAAG,MAAM,IAAA,qBAAc,EAAC,gBAAgB,CAAC,CAAC;QACzD,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IACD,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CACb,qEAAqE;YACnE,uEAAuE,CAC1E,CAAC;IACJ,CAAC;AACH,CAAC;AAED,2DAA2D;AAC3D,MAAM,uBAAuB,GAAG,KAAK,CAAC;AAEtC,+DAA+D;AAC/D,MAAM,WAAY,SAAQ,iBAAQ;IAMhC;;;;;;OAMG;IACH,YAAY,QAAQ,EAAE,YAAY,GAAG,gBAAC,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE;QACvD,KAAK,CAAC,OAAO,CAAC,CAAC;QAbjB;;WAEG;QACH,gBAAW,GAAG,CAAC,CAAC;QAYd,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;QACpB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACH,IAAI,eAAe;QACjB,MAAM,SAAS,GAAG,qBAAqB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,OAAO,CAAC,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;YAC7D,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC9B,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,YAAY;QAChB,MAAM,SAAS,GAAG,qBAAqB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,gBAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,yBAAY,GAAE,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC1D,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,kBAAkB;QACtB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,uBAAuB;QACjD,mCAAmC;QACnC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,MAAM,iBAAiB,EAAE,CAAC;QAE1B,IAAI,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,cAAc,GAAG,CACpB,MAAM,IAAA,eAAK,EAAC;gBACV,GAAG;gBACH,YAAY,EAAE,QAAQ;gBACtB,OAAO,EAAE,aAAa;aACvB,CAAC,CACH,CAAC,IAAI,CAAC;QACT,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CACb,yCAAyC,GAAG,IAAI;gBAChD,mBAAmB,gBAAC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAC1G,CAAC;QACJ,CAAC;QAED,MAAM,KAAK,GAAG,CAAC,oBAAoB,CAAC,GAAG,EAAE,EAAE;YACzC,sEAAsE;YACtE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAEtB,gBAAG,CAAC,KAAK,CAAC,yCAAyC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC9B,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC;QACF,MAAM,OAAO,GAAG,GAAG,EAAE;YACnB,gBAAG,CAAC,KAAK,CAAC,qCAAqC,GAAG,kBAAkB,CAAC,CAAC;YACtE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC,CAAC;QACF,sEAAsE;QACtE,+CAA+C;QAC/C,MAAM,YAAY,GAAG,IAAI,kBAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACtC,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC;YAChC,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC;QAClC,CAAC,CAAC;YACA,uEAAuE;YACvE,iBAAiB;aAChB,OAAO,CACN,aAAa,EACb,UAAU,aAAa,+CAA+C,CACvE,CAAC;QAEJ,IAAI,CAAC,cAAc;aAChB,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;aACtB,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,qCAAqC;aACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,+CAA+C;aACnE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;QAElD,MAAM,YAAY,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,IAAI;QACF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACnC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAEO,kCAAW"}