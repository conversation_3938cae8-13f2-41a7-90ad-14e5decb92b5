"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RECORDING_ACTION_STOP = exports.RECORDING_ACTION_START = exports.RECORDING_ACTIVITY_NAME = exports.RECORDING_SERVICE_NAME = exports.NOTIFICATIONS_RETRIEVAL_ACTION = exports.SETTING_NOTIFICATIONS_LISTENER_SERVICE = exports.MEDIA_SCAN_ACTION = exports.MEDIA_SCAN_RECEIVER = exports.SMS_LIST_RETRIEVAL_ACTION = exports.SMS_LIST_RECEIVER = exports.LOCATION_RETRIEVAL_ACTION = exports.LOCATION_RECEIVER = exports.LOCATION_SERVICE = exports.LOCALES_LIST_SETTING_ACTION = exports.LOCALES_LIST_SETTING_RECEIVER = exports.LOCALE_SETTING_ACTION = exports.LOCALE_SETTING_RECEIVER = exports.ANIMATION_SETTING_ACTION = exports.ANIMATION_SETTING_RECEIVER = exports.BLUETOOTH_UNPAIR_ACTION = exports.BLUETOOTH_UNPAIR_RECEIVER = exports.BLUETOOTH_SETTING_ACTION = exports.BLUETOOTH_SETTING_RECEIVER = exports.DATA_CONNECTION_SETTING_ACTION = exports.DATA_CONNECTION_SETTING_RECEIVER = exports.WIFI_CONNECTION_SETTING_ACTION = exports.WIFI_CONNECTION_SETTING_RECEIVER = exports.EMPTY_IME = exports.UNICODE_IME = exports.APPIUM_IME = exports.CLIPBOARD_RETRIEVAL_ACTION = exports.CLIPBOARD_RECEIVER = exports.SETTINGS_HELPER_MAIN_ACTIVITY = exports.SETTINGS_HELPER_ID = void 0;
exports.SETTINGS_HELPER_ID = 'io.appium.settings';
exports.SETTINGS_HELPER_MAIN_ACTIVITY = '.Settings';
const RECEIVERS_PKG = `${exports.SETTINGS_HELPER_ID}/.receivers`;
exports.CLIPBOARD_RECEIVER = `${RECEIVERS_PKG}.ClipboardReceiver`;
exports.CLIPBOARD_RETRIEVAL_ACTION = `${exports.SETTINGS_HELPER_ID}.clipboard.get`;
exports.APPIUM_IME = `${exports.SETTINGS_HELPER_ID}/.AppiumIME`;
exports.UNICODE_IME = `${exports.SETTINGS_HELPER_ID}/.UnicodeIME`;
exports.EMPTY_IME = `${exports.SETTINGS_HELPER_ID}/.EmptyIME`;
exports.WIFI_CONNECTION_SETTING_RECEIVER = `${RECEIVERS_PKG}.WiFiConnectionSettingReceiver`;
exports.WIFI_CONNECTION_SETTING_ACTION = `${exports.SETTINGS_HELPER_ID}.wifi`;
exports.DATA_CONNECTION_SETTING_RECEIVER = `${RECEIVERS_PKG}.DataConnectionSettingReceiver`;
exports.DATA_CONNECTION_SETTING_ACTION = `${exports.SETTINGS_HELPER_ID}.data_connection`;
exports.BLUETOOTH_SETTING_RECEIVER = `${RECEIVERS_PKG}.BluetoothConnectionSettingReceiver`;
exports.BLUETOOTH_SETTING_ACTION = `${exports.SETTINGS_HELPER_ID}.bluetooth`;
exports.BLUETOOTH_UNPAIR_RECEIVER = `${RECEIVERS_PKG}.UnpairBluetoothDevicesReceiver`;
exports.BLUETOOTH_UNPAIR_ACTION = `${exports.SETTINGS_HELPER_ID}.unpair_bluetooth`;
exports.ANIMATION_SETTING_RECEIVER = `${RECEIVERS_PKG}.AnimationSettingReceiver`;
exports.ANIMATION_SETTING_ACTION = `${exports.SETTINGS_HELPER_ID}.animation`;
exports.LOCALE_SETTING_RECEIVER = `${RECEIVERS_PKG}.LocaleSettingReceiver`;
exports.LOCALE_SETTING_ACTION = `${exports.SETTINGS_HELPER_ID}.locale`;
exports.LOCALES_LIST_SETTING_RECEIVER = `${RECEIVERS_PKG}.LocalesReader`;
exports.LOCALES_LIST_SETTING_ACTION = `${exports.SETTINGS_HELPER_ID}.list_locales`;
exports.LOCATION_SERVICE = `${exports.SETTINGS_HELPER_ID}/.LocationService`;
exports.LOCATION_RECEIVER = `${RECEIVERS_PKG}.LocationInfoReceiver`;
exports.LOCATION_RETRIEVAL_ACTION = `${exports.SETTINGS_HELPER_ID}.location`;
exports.SMS_LIST_RECEIVER = `${RECEIVERS_PKG}.SmsReader`;
exports.SMS_LIST_RETRIEVAL_ACTION = `${exports.SETTINGS_HELPER_ID}.sms.read`;
exports.MEDIA_SCAN_RECEIVER = `${RECEIVERS_PKG}.MediaScannerReceiver`;
exports.MEDIA_SCAN_ACTION = `${exports.SETTINGS_HELPER_ID}.scan_media`;
exports.SETTING_NOTIFICATIONS_LISTENER_SERVICE = `${exports.SETTINGS_HELPER_ID}/.NLService`;
exports.NOTIFICATIONS_RETRIEVAL_ACTION = `${exports.SETTINGS_HELPER_ID}.notifications`;
exports.RECORDING_SERVICE_NAME = `${exports.SETTINGS_HELPER_ID}/.recorder.RecorderService`;
exports.RECORDING_ACTIVITY_NAME = `${exports.SETTINGS_HELPER_ID}/io.appium.settings.Settings`;
exports.RECORDING_ACTION_START = `${exports.SETTINGS_HELPER_ID}.recording.ACTION_START`;
exports.RECORDING_ACTION_STOP = `${exports.SETTINGS_HELPER_ID}.recording.ACTION_STOP`;
//# sourceMappingURL=constants.js.map