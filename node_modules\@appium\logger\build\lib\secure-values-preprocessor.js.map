{"version": 3, "file": "secure-values-preprocessor.js", "sourceRoot": "", "sources": ["../../lib/secure-values-preprocessor.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAAuB;AAQV,QAAA,uBAAuB,GAAG,YAAY,CAAC;AAEpD;;;;GAIG;AACH,SAAS,gBAAgB,CAAC,KAAa;IACrC,OAAO,SAAS,IAAI,KAAK,CAAC;AAC5B,CAAC;AAED,MAAa,wBAAwB;IAGnC;QACE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;IAED;;;OAGG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACH,SAAS,CAAC,IAAwB;QAChC,IAAI,OAA2B,CAAC;QAChC,IAAI,QAAQ,GAAG,+BAAuB,CAAC;QACvC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QAClB,IAAI,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC5E,CAAC;YACD,OAAO,GAAG,MAAM,gBAAC,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;QAC5C,CAAC;aAAM,IAAI,gBAAC,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3D,MAAM,IAAI,KAAK,CACb,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,6DAA6D,CACrF,CAAC;gBACJ,CAAC;gBACD,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YACzB,CAAC;iBAAM,IAAI,gBAAC,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrD,MAAM,IAAI,KAAK,CACb,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,0DAA0D,CAClF,CAAC;gBACJ,CAAC;gBACD,OAAO,GAAG,MAAM,gBAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YACjD,CAAC;YACD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CACb,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,wDAAwD,CAChF,CAAC;YACJ,CAAC;YAED,IAAI,gBAAC,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;gBACzB,oHAAoH;gBACpH,KAAK,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;oBAClD,IAAI,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC;wBACjC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnB,CAAC;gBACH,CAAC;gBACD,KAAK,GAAG,gBAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;YAED,IAAI,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9B,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC3B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACrF,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5C,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,SAAS,CAAC,OAA6C;QAC3D,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAA2B,EAAE,CAAC;QAC5C,KAAK,MAAM,MAAM,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YAChE,IAAI,gBAAC,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC5B,QAAQ,CAAC,IAAI,CAAC,MAAmB,CAAC,CAAC;YACrC,CAAC;iBAAM,IAAI,gBAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9B,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,sCAAsC,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,MAAM,CAAC,IAAI,CAAE,CAAW,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,UAAU,CAAC,GAAW;QACpB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACzD,OAAO,GAAG,CAAC;QACb,CAAC;QAED,IAAI,MAAM,GAAG,GAAG,CAAC;QACjB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,IAAI,+BAAuB,CAAC,CAAC;QAClF,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAjID,4DAiIC"}