language: node_js

matrix:
  include:
    - node_js: '0.10'
      env: NO_COVERAGE=1
    - node_js: '0.11'
      env: NO_COVERAGE=1
    - node_js: '0.12'

deploy:
  provider: npm
  email: <EMAIL>
  api_key:
    secure: GIkpHAQZJH8xTq1oRG66f+J0j4PMxEBoDzIiOyREBypGslOy6NbWxP0Q8rgD6wfzBRMixSctqeiKyTkiC+92BGPLL7k1eyeN9ycXjYHuDVJrRyYAMjg1Xh72ekFPZnycgUngMTwWX/FtQShkvaDoG+1S0W+qdC+cRz+9Dh7+XEM=
  on:
    tags: true
    all_branches: true
    node_js: 0.12

addons:
  code_climate:
    repo_token:
      secure: "qoQoeJZrjiE7RmcGIZNmR2tO3/oP1NqlxhYkj1TYbMVOYmK4zsOdeVjhllkETZaGejKcw1uXEQx7caSmpZQ6lw5V5JXmyyTzo8xfAbanP9Wf4WXw5uSOaBDYR/DR2B9VfkHT7spPVwdoX09sgb+oTIy4IgBUivucm6IGmiw7PuY="

after_script:
  - npm install -g codeclimate-test-reporter
  - cat coverage/lcov.info | codeclimate
