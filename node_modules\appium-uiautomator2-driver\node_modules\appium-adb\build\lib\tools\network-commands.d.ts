/**
 * Get TCP port forwarding with adb on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string[]>} The output of the corresponding adb command.
 * An array contains each forwarding line of output
 */
export function getForwardList(this: import("../adb.js").ADB): Promise<string[]>;
/**
 * Setup TCP port forwarding with adb on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} systemPort - The number of the local system port.
 * @param {string|number} devicePort - The number of the remote device port.
 */
export function forwardPort(this: import("../adb.js").ADB, systemPort: string | number, devicePort: string | number): Promise<void>;
/**
 * Remove TCP port forwarding with adb on the device under test. The forwarding
 * for the given port should be setup with {@link #forwardPort} first.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} systemPort - The number of the local system port
 *                                     to remove forwarding on.
 */
export function removePortForward(this: import("../adb.js").ADB, systemPort: string | number): Promise<void>;
/**
 * Get TCP port forwarding with adb on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string[]>} The output of the corresponding adb command.
 * An array contains each forwarding line of output
 */
export function getReverseList(this: import("../adb.js").ADB): Promise<string[]>;
/**
 * Setup TCP port forwarding with adb on the device under test.
 * Only available for API 21+.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} devicePort - The number of the remote device port.
 * @param {string|number} systemPort - The number of the local system port.
 */
export function reversePort(this: import("../adb.js").ADB, devicePort: string | number, systemPort: string | number): Promise<void>;
/**
 * Remove TCP port forwarding with adb on the device under test. The forwarding
 * for the given port should be setup with {@link #forwardPort} first.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} devicePort - The number of the remote device port
 *                                     to remove forwarding on.
 */
export function removePortReverse(this: import("../adb.js").ADB, devicePort: string | number): Promise<void>;
/**
 * Setup TCP port forwarding with adb on the device under test. The difference
 * between {@link #forwardPort} is that this method does setup for an abstract
 * local port.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} systemPort - The number of the local system port.
 * @param {string|number} devicePort - The number of the remote device port.
 */
export function forwardAbstractPort(this: import("../adb.js").ADB, systemPort: string | number, devicePort: string | number): Promise<void>;
/**
 * Execute ping shell command on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if the command output contains 'ping' substring.
 * @throws {Error} If there was an error while executing 'ping' command on the
 *                 device under test.
 */
export function ping(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Returns the list of TCP port states of the given family.
 * Could be empty if no ports are opened.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').PortFamily} [family='4']
 * @returns {Promise<import('./types').PortInfo[]>}
 */
export function listPorts(this: import("../adb.js").ADB, family?: import("./types").PortFamily): Promise<import("./types").PortInfo[]>;
//# sourceMappingURL=network-commands.d.ts.map