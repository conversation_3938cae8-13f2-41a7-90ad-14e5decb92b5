{"version": 3, "file": "protocol-converter.js", "sourceRoot": "", "sources": ["../../../lib/jsonwp-proxy/protocol-converter.js"], "names": [], "mappings": ";;;;;;AAAA,oDAAuB;AACvB,6CAA6C;AAC7C,mDAAoD;AACpD,4CAA6E;AAEhE,QAAA,sBAAsB,GAAG;IACpC;QACE,YAAY,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;QACzC,eAAe,EAAE,CAAC,GAAG,EAAE,EAAE,CACvB,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC;QACnF,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,CACpB,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC;KACzF;IACD;QACE,YAAY,EAAE,CAAC,sBAAsB,CAAC;QACtC,eAAe,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,iCAAiC,EAAE,gBAAgB,CAAC;QAC1F,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE,wBAAwB,CAAC;KACtF;IACD;QACE,YAAY,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;QACrD,eAAe,CAAC,GAAG;YACjB,OAAO,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC5B,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,gBAAgB,CAAC;gBAC5C,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE,kBAAkB,CAAC,CAAC;QAC/D,CAAC;QACD,YAAY,CAAC,GAAG;YACd,OAAO,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gBACnC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,kBAAkB,EAAE,SAAS,CAAC;gBAC5C,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;QAC1D,CAAC;KACF;IACD;QACE,YAAY,EAAE,CAAC,aAAa,CAAC;QAC7B,eAAe,EAAE,CAAC,MAAM,EAAE,EAAE;YAC1B,MAAM,gBAAgB,GAAG,uCAAuC,CAAC;YACjE,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,CAAC;YAC/E,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,YAAY,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,EAAE,wEAAwE;KACjH;CACF,CAAC;AACF,MAAM,EAAC,OAAO,EAAE,GAAG,EAAC,GAAG,qBAAS,CAAC;AACjC,MAAM,WAAW,GAAG,gBAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;AAE3D,MAAM,iBAAiB;IACrB;;;;OAIG;IACH,YAAY,SAAS,EAAE,GAAG,GAAG,IAAI;QAC/B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC;IAClC,CAAC;IAED,IAAI,kBAAkB,CAAC,KAAK;QAC1B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;;;;;;OAOG;IACH,wBAAwB,CAAC,IAAI;QAC3B,IAAI,IAAI,CAAC,kBAAkB,KAAK,GAAG,IAAI,gBAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,gBAAC,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;YAChF,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,OAAO;gBACL;oBACE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;iBAChC;aACF,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,IAAI,CAAC,CAAC,gBAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAC,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC;YACxF,MAAM,YAAY,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,OAAO,CACL,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC;gBACb,wEAAwE;iBACvE,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;iBACzD,GAAG,CAAC,UAAU,IAAI;gBACjB,OAAO;oBACL,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC3B,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;iBACZ,CAAC;YACJ,CAAC,CAAC,CACL,CAAC;QACJ,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI;QACtC,IAAI,QAAQ,EAAE,OAAO,CAAC;QAEtB,MAAM,qBAAqB,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,wDAAwD,IAAI,CAAC,SAAS,CACpE,qBAAqB,CACtB,EAAE,CACJ,CAAC;QACF,KAAK,MAAM,UAAU,IAAI,qBAAqB,EAAE,CAAC;YAC/C,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YAEpE,0EAA0E;YAC1E,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE,CAAC;gBACxC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC7B,CAAC;YAED,kDAAkD;YAClD,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;gBAC/B,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC7B,CAAC;YAED,mDAAmD;QACrD,CAAC;QACD,OAAO,CAAC,oDAAoD,CAAA,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;IACnF,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI;QACpC,MAAM,OAAO,GAAG,cAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,gBAAC,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7B,IAAI,IAAI,CAAC,kBAAkB,KAAK,GAAG,IAAI,gBAAC,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAC,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC3F,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,wBAAwB,CAAA,mDAAoD,CAAC,OAAO,CAAC,CAAC,IAAI,+BAA+B,CAC1H,CAAC;gBACF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE;oBACvC,IAAG,mDAAoD,CAAC,OAAO,CAAC;oBAChE,MAAM,EAAE,mDAAmD,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI;iBAC3E,CAAC,CAAC;YACL,CAAC;YACD,IACE,IAAI,CAAC,kBAAkB,KAAK,OAAO;gBACnC,gBAAC,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;gBACxB,CAAC,gBAAC,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,EACvB,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,0BAA0B,CAAA,mDAAoD,CAAC,OAAO,CAAC,CAAC,MAAM,gCAAgC,CAC/H,CAAC;gBACF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE;oBACvC,IAAG,mDAAoD,CAAC,OAAO,CAAC;oBAChE,IAAI,EAAE,mDAAmD,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;iBAC3E,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI;QACnC,MAAM,OAAO,GAAG,cAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,gBAAC,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC9F,IAAI,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,OAAO,CAAC;YAC5B,IAAI,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjD,KAAK,GAAG,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnE,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,0BAA0B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,6BAA6B,CAC7E,CAAC;YACJ,CAAC;iBAAM,IAAI,CAAC,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,cAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxD,IAAI,GAAG,gBAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1E,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC7F,CAAC;YACD,OAAO,MAAM,IAAI,CAAC,SAAS,CACzB,GAAG,EACH,MAAM,EACN,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE;gBACzB,IAAI;gBACJ,KAAK;aACN,CAAC,CACH,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI;QACnC,MAAM,OAAO,GAAG,cAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,gBAAC,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,gBAAC,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;YACxD,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE;gBAChC,GAAG,OAAO;gBACV,EAAE,EAAE,IAAA,uBAAa,EAAC,OAAO,CAAC,EAAE,EAAE,+BAAmB,EAAE,2BAAe,CAAC;aACpE,CAAC;YACJ,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI;QACzC,MAAM,OAAO,GAAG,cAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,gBAAC,CAAC,aAAa,CAAC,OAAO,CAAC;YAC7B,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,CAClB,GAAG,EACH,MAAM,EACN,IAAA,uBAAa,EAAC,OAAO,EAAE,+BAAmB,EAAE,2BAAe,CAAC,CAC7D;YACH,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM;QACnC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,eAAe,CAAC,WAAW,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI;QAClD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QACjD,CAAC;QAED,oCAAoC;QACpC,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YACxD,KAAK,WAAW;gBACd,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YACtD,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YACrD,KAAK,gBAAgB;gBACnB,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YAC3D,KAAK,gBAAgB;gBACnB,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACrD,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YACrD;gBACE,MAAM;QACV,CAAC;QAED,qCAAqC;QACrC,KAAK,MAAM,EAAC,YAAY,EAAE,eAAe,EAAE,YAAY,EAAC,IAAI,8BAAsB,EAAE,CAAC;YACnF,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACxC,SAAS;YACX,CAAC;YAED,MAAM,YAAY,GAChB,IAAI,CAAC,kBAAkB,KAAK,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACjF,IAAI,YAAY,KAAK,GAAG,EAAE,CAAC;gBACzB,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,iDAAiD,GAAG,IAAI;oBACtD,OAAO,IAAI,CAAC,kBAAkB,WAAW,CAC5C,CAAC;gBACF,MAAM;YACR,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,IAAI,CACX,6BAA6B,GAAG,SAAS,YAAY,IAAI;gBACvD,OAAO,IAAI,CAAC,kBAAkB,WAAW,CAC5C,CAAC;YACF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1D,CAAC;QAED,qCAAqC;QACrC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;CACF;AAED,kBAAe,iBAAiB,CAAC;AAEjC;;GAEG"}