"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const lodash_1 = __importDefault(require("lodash"));
const protocol_1 = require("../../protocol");
const mixin_1 = require("./mixin");
const fastest_levenshtein_1 = require("fastest-levenshtein");
const ExecuteCommands = {
    async executeMethod(script, protoArgs) {
        const Driver = this.constructor;
        const commandMetadata = { ...Driver.executeMethodMap?.[script] };
        if (!commandMetadata.command) {
            const availableScripts = lodash_1.default.keys(Driver.executeMethodMap);
            if (lodash_1.default.isEmpty(availableScripts)) {
                throw new protocol_1.errors.UnsupportedOperationError(`Unsupported execute method '${script}'. ` +
                    `Make sure the installed ${Driver.name} is up-to-date. ` +
                    `The current driver version does not define any execute methods.`);
            }
            const matchesMap = availableScripts
                .map((name) => [(0, fastest_levenshtein_1.distance)(script, name), name])
                .reduce((acc, [key, value]) => {
                if (key in acc) {
                    acc[key].push(value);
                }
                else {
                    acc[key] = [value];
                }
                return acc;
            }, {});
            const sortedMatches = lodash_1.default.flatten(lodash_1.default.keys(matchesMap)
                .sort((a, b) => parseInt(a, 10) - parseInt(b, 10))
                .map((x) => matchesMap[x]));
            throw new protocol_1.errors.UnsupportedOperationError(`Unsupported execute method '${script}', did you mean '${sortedMatches[0]}'? ` +
                `Make sure the installed ${Driver.name} is up-to-date. ` +
                `Execute methods available in the current driver version are: ` +
                sortedMatches.join(', '));
        }
        const args = (0, protocol_1.validateExecuteMethodParams)(protoArgs, commandMetadata.params);
        const command = this[commandMetadata.command];
        return await command.call(this, ...args);
    },
};
(0, mixin_1.mixin)(ExecuteCommands);
//# sourceMappingURL=execute.js.map