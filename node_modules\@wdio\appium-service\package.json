{"name": "@wdio/appium-service", "version": "9.17.0", "description": "A WebdriverIO service to start & stop Appium Server", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/webdriverio/webdriverio/tree/main/packages/wdio-appium-service", "license": "MIT", "engines": {"node": ">=18.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/webdriverio/webdriverio.git", "directory": "packages/wdio-appium-service"}, "keywords": ["webdriver", "webdriverio", "wdio", "wdio-service", "appium", "server", "tests"], "bugs": {"url": "https://github.com/webdriverio/webdriverio/issues"}, "type": "module", "types": "./build/index.d.ts", "exports": {".": {"import": "./build/index.js", "types": "./build/index.d.ts"}}, "typeScriptVersion": "3.8.3", "dependencies": {"@wdio/config": "9.17.0", "@wdio/logger": "9.16.2", "@wdio/types": "9.16.2", "@wdio/utils": "9.17.0", "change-case": "^5.4.3", "get-port": "^7.0.0", "import-meta-resolve": "^4.0.0", "tree-kill": "^1.2.2", "webdriverio": "9.17.0"}, "publishConfig": {"access": "public"}, "gitHead": "796df04f0f5c744d5ef4bafd3759995fa4829d6f"}