{"name": "appium-adb", "version": "12.12.6", "description": "Android Debug Bridge interface", "main": "./build/index.js", "scripts": {"build": "tsc -b", "clean": "npm run build -- --clean", "dev": "npm run build -- --watch", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "prepare": "npm run rebuild", "rebuild": "npm run clean && npm run build", "format": "prettier -w ./lib", "test": "mocha --exit --timeout 1m \"./test/unit/**/*-specs.js\"", "e2e-test": "mocha --exit --timeout 5m \"./test/functional/**/*-specs.js\""}, "repository": {"type": "git", "url": "https://github.com/appium/appium-adb.git"}, "author": "Appium Contributors", "license": "Apache-2.0", "bugs": {"url": "https://github.com/appium/appium-adb/issues"}, "engines": {"node": ">=14", "npm": ">=8"}, "bin": {}, "directories": {"lib": "lib"}, "prettier": {"bracketSpacing": false, "printWidth": 100, "singleQuote": true}, "files": ["index.ts", "keys", "lib", "build/index.*", "build/lib", "CHANGELOG.md"], "homepage": "https://github.com/appium/appium-adb", "dependencies": {"@appium/support": "^6.0.0", "async-lock": "^1.0.0", "asyncbox": "^3.0.0", "bluebird": "^3.4.7", "ini": "^5.0.0", "lodash": "^4.0.0", "lru-cache": "^10.0.0", "semver": "^7.0.0", "source-map-support": "^0.x", "teen_process": "^2.2.0"}, "devDependencies": {"@appium/eslint-config-appium-ts": "^1.x", "@appium/test-support": "^3.0.1", "@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@types/async-lock": "^1.4.0", "@types/bluebird": "^3.5.38", "@types/ini": "^4.1.0", "@types/lodash": "^4.14.195", "@types/mocha": "^10.0.1", "@types/node": "^24.0.0", "@types/semver": "^7.5.0", "@types/source-map-support": "^0.5.6", "@types/teen_process": "^2.0.0", "chai": "^5.1.1", "chai-as-promised": "^8.0.0", "conventional-changelog-conventionalcommits": "^9.0.0", "mocha": "^11.0.1", "prettier": "^3.0.0", "rimraf": "^5.0.0", "semantic-release": "^24.0.0", "ts-node": "^10.9.1", "typescript": "^5.4.2"}, "types": "./build/index.d.ts"}