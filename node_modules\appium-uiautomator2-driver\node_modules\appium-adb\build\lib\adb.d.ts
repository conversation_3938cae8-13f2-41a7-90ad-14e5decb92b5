import _ from 'lodash';
import type { ADBOptions, ADBExecutable } from './types';
import type { Logcat } from './logcat';
import type { LogcatOpts, StringRecord } from './tools/types';
import type { LRUCache } from 'lru-cache';
import * as generalCommands from './tools/general-commands';
import * as manifestCommands from './tools/android-manifest';
import * as systemCommands from './tools/system-calls';
import * as signingCommands from './tools/apk-signing';
import * as apkUtilCommands from './tools/apk-utils';
import * as apksUtilCommands from './tools/apks-utils';
import * as aabUtilCommands from './tools/aab-utils';
import * as emuCommands from './tools/emulator-commands';
import * as lockManagementCommands from './tools/lockmgmt';
import * as keyboardCommands from './tools/keyboard-commands';
import * as deviceSettingsCommands from './tools/device-settings';
import * as fsCommands from './tools/fs-commands';
import * as appCommands from './tools/app-commands';
import * as networkCommands from './tools/network-commands';
import * as logcatCommands from './tools/logcat-commands';
export declare const DEFAULT_ADB_PORT = 5037;
export declare const DEFAULT_OPTS: {
    readonly sdkRoot: string | undefined;
    readonly executable: {
        readonly path: "adb";
        readonly defaultArgs: readonly [];
    };
    readonly tmpDir: string;
    readonly binaries: {};
    readonly adbPort: 5037;
    readonly adbExecTimeout: 20000;
    readonly remoteAppsCacheLimit: 10;
    readonly allowOfflineDevices: false;
    readonly allowDelayAdb: true;
};
export declare class ADB implements ADBOptions {
    adbHost?: string;
    adbPort?: number;
    _apiLevel: number | undefined;
    _logcatStartupParams: LogcatOpts | undefined;
    _doesPsSupportAOption: boolean | undefined;
    _isPgrepAvailable: boolean | undefined;
    _canPgrepUseFullCmdLineSearch: boolean | undefined;
    _isPidofAvailable: boolean | undefined;
    _memoizedFeatures: (() => Promise<string>) | undefined;
    _areExtendedLsOptionsSupported: boolean | undefined;
    remoteAppsCache: LRUCache<string, string> | undefined;
    _isLockManagementSupported: boolean | undefined;
    sdkRoot?: string;
    udid?: string;
    appDeviceReadyTimeout?: number;
    useKeystore?: boolean;
    keystorePath?: string;
    keystorePassword?: string;
    keyAlias?: string;
    keyPassword?: string;
    executable: ADBExecutable;
    tmpDir?: string;
    curDeviceId?: string;
    emulatorPort?: number;
    logcat?: Logcat;
    binaries?: StringRecord;
    suppressKillServer?: boolean;
    adbExecTimeout?: number;
    remoteAppsCacheLimit?: number;
    buildToolsVersion?: string;
    allowOfflineDevices?: boolean;
    allowDelayAdb?: boolean;
    remoteAdbHost?: string;
    remoteAdbPort?: number;
    clearDeviceLogsOnStart?: boolean;
    constructor(opts?: ADBOptions);
    /**
     * Create a new instance of `ADB` that inherits configuration from this `ADB` instance.
     * This avoids the need to call `ADB.createADB()` multiple times.
     * @param opts - Additional options mapping to pass to the `ADB` constructor.
     * @returns The resulting class instance.
     */
    clone(opts?: ADBOptions): ADB;
    static createADB(opts?: ADBOptions): Promise<ADB>;
    getAdbWithCorrectAdbPath: typeof generalCommands.getAdbWithCorrectAdbPath;
    initAapt: typeof generalCommands.initAapt;
    initAapt2: typeof generalCommands.initAapt2;
    initZipAlign: typeof generalCommands.initZipAlign;
    initBundletool: typeof generalCommands.initBundletool;
    getApiLevel: typeof generalCommands.getApiLevel;
    isDeviceConnected: typeof generalCommands.isDeviceConnected;
    clearTextField: typeof generalCommands.clearTextField;
    back: typeof generalCommands.back;
    goToHome: typeof generalCommands.goToHome;
    getAdbPath: typeof generalCommands.getAdbPath;
    restart: typeof generalCommands.restart;
    bugreport: typeof generalCommands.bugreport;
    screenrecord: typeof generalCommands.screenrecord;
    listFeatures: typeof generalCommands.listFeatures;
    isStreamedInstallSupported: typeof generalCommands.isStreamedInstallSupported;
    isIncrementalInstallSupported: typeof generalCommands.isIncrementalInstallSupported;
    takeScreenshot: typeof generalCommands.takeScreenshot;
    startLogcat: typeof logcatCommands.startLogcat;
    stopLogcat: typeof logcatCommands.stopLogcat;
    getLogcatLogs: typeof logcatCommands.getLogcatLogs;
    setLogcatListener: typeof logcatCommands.setLogcatListener;
    removeLogcatListener: typeof logcatCommands.removeLogcatListener;
    getForwardList: typeof networkCommands.getForwardList;
    forwardPort: typeof networkCommands.forwardPort;
    listPorts: typeof networkCommands.listPorts;
    ping: typeof networkCommands.ping;
    forwardAbstractPort: typeof networkCommands.forwardAbstractPort;
    removePortReverse: typeof networkCommands.removePortReverse;
    reversePort: typeof networkCommands.reversePort;
    getReverseList: typeof networkCommands.getReverseList;
    removePortForward: typeof networkCommands.removePortForward;
    executeApksigner: typeof signingCommands.executeApksigner;
    signWithDefaultCert: typeof signingCommands.signWithDefaultCert;
    signWithCustomCert: typeof signingCommands.signWithCustomCert;
    sign: typeof signingCommands.sign;
    zipAlignApk: typeof signingCommands.zipAlignApk;
    checkApkCert: typeof signingCommands.checkApkCert;
    getKeystoreHash: typeof signingCommands.getKeystoreHash;
    grantAllPermissions: typeof appCommands.grantAllPermissions;
    grantPermissions: typeof appCommands.grantPermissions;
    grantPermission: typeof appCommands.grantPermission;
    revokePermission: typeof appCommands.revokePermission;
    getGrantedPermissions: typeof appCommands.getGrantedPermissions;
    getDeniedPermissions: typeof appCommands.getDeniedPermissions;
    getReqPermissions: typeof appCommands.getReqPermissions;
    stopAndClear: typeof appCommands.stopAndClear;
    isValidClass: typeof appCommands.isValidClass;
    resolveLaunchableActivity: typeof appCommands.resolveLaunchableActivity;
    forceStop: typeof appCommands.forceStop;
    killPackage: typeof appCommands.killPackage;
    clear: typeof appCommands.clear;
    listProcessStatus: typeof appCommands.listProcessStatus;
    getNameByPid: typeof appCommands.getNameByPid;
    getPIDsByName: typeof appCommands.getPIDsByName;
    killProcessesByName: typeof appCommands.killProcessesByName;
    killProcessByPID: typeof appCommands.killProcessByPID;
    broadcastProcessEnd: typeof appCommands.broadcastProcessEnd;
    broadcast: typeof appCommands.broadcast;
    processExists: typeof appCommands.processExists;
    readonly APP_INSTALL_STATE: StringRecord<import("./tools/types").InstallState>;
    isAppInstalled: typeof appCommands.isAppInstalled;
    startUri: typeof appCommands.startUri;
    startApp: typeof appCommands.startApp;
    dumpWindows: typeof appCommands.dumpWindows;
    getFocusedPackageAndActivity: typeof appCommands.getFocusedPackageAndActivity;
    waitForActivityOrNot: typeof appCommands.waitForActivityOrNot;
    waitForActivity: typeof appCommands.waitForActivity;
    waitForNotActivity: typeof appCommands.waitForNotActivity;
    getPackageInfo: typeof appCommands.getPackageInfo;
    pullApk: typeof appCommands.pullApk;
    activateApp: typeof appCommands.activateApp;
    uninstallApk: typeof apkUtilCommands.uninstallApk;
    installFromDevicePath: typeof apkUtilCommands.installFromDevicePath;
    cacheApk: typeof apkUtilCommands.cacheApk;
    install: typeof apkUtilCommands.install;
    installOrUpgrade: typeof apkUtilCommands.installOrUpgrade;
    extractStringsFromApk: typeof apkUtilCommands.extractStringsFromApk;
    getApkInfo: typeof apkUtilCommands.getApkInfo;
    getApplicationInstallState: typeof apkUtilCommands.getApplicationInstallState;
    hideKeyboard: typeof keyboardCommands.hideKeyboard;
    isSoftKeyboardPresent: typeof keyboardCommands.isSoftKeyboardPresent;
    keyevent: typeof keyboardCommands.keyevent;
    availableIMEs: typeof keyboardCommands.availableIMEs;
    enabledIMEs: typeof keyboardCommands.enabledIMEs;
    enableIME: typeof keyboardCommands.enableIME;
    disableIME: typeof keyboardCommands.disableIME;
    setIME: typeof keyboardCommands.setIME;
    defaultIME: typeof keyboardCommands.defaultIME;
    inputText: typeof keyboardCommands.inputText;
    runInImeContext: typeof keyboardCommands.runInImeContext;
    lock: typeof lockManagementCommands.lock;
    isLockManagementSupported: typeof lockManagementCommands.isLockManagementSupported;
    verifyLockCredential: typeof lockManagementCommands.verifyLockCredential;
    clearLockCredential: typeof lockManagementCommands.clearLockCredential;
    isLockEnabled: typeof lockManagementCommands.isLockEnabled;
    setLockCredential: typeof lockManagementCommands.setLockCredential;
    isScreenLocked: typeof lockManagementCommands.isScreenLocked;
    dismissKeyguard: typeof lockManagementCommands.dismissKeyguard;
    cycleWakeUp: typeof lockManagementCommands.cycleWakeUp;
    getSdkBinaryPath: typeof systemCommands.getSdkBinaryPath;
    getBinaryNameForOS: ((binaryName: string) => string) & _.MemoizedFunction;
    getBinaryFromSdkRoot: typeof systemCommands.getBinaryFromSdkRoot;
    getBinaryFromPath: typeof systemCommands.getBinaryFromPath;
    getConnectedDevices: typeof systemCommands.getConnectedDevices;
    getDevicesWithRetry: typeof systemCommands.getDevicesWithRetry;
    reconnect: typeof systemCommands.reconnect;
    restartAdb: typeof systemCommands.restartAdb;
    killServer: typeof systemCommands.killServer;
    resetTelnetAuthToken: (() => Promise<boolean>) & _.MemoizedFunction;
    adbExecEmu: typeof systemCommands.adbExecEmu;
    EXEC_OUTPUT_FORMAT: {
        STDOUT: "stdout";
        FULL: "full";
    };
    adbExec: typeof systemCommands.adbExec;
    shell: typeof systemCommands.shell;
    shellChunks: typeof systemCommands.shellChunks;
    createSubProcess: typeof systemCommands.createSubProcess;
    getAdbServerPort: typeof systemCommands.getAdbServerPort;
    getEmulatorPort: typeof systemCommands.getEmulatorPort;
    getPortFromEmulatorString: typeof systemCommands.getPortFromEmulatorString;
    getConnectedEmulators: typeof systemCommands.getConnectedEmulators;
    setEmulatorPort: typeof systemCommands.setEmulatorPort;
    setDeviceId: typeof systemCommands.setDeviceId;
    setDevice: typeof systemCommands.setDevice;
    getRunningAVD: typeof systemCommands.getRunningAVD;
    getRunningAVDWithRetry: typeof systemCommands.getRunningAVDWithRetry;
    killAllEmulators: typeof systemCommands.killAllEmulators;
    killEmulator: typeof systemCommands.killEmulator;
    launchAVD: typeof systemCommands.launchAVD;
    getVersion: (() => Promise<{
        binary: {
            version: import("semver").SemVer | null;
            build: number;
        };
        bridge: {
            version: import("semver").SemVer | null;
        };
    }>) & _.MemoizedFunction;
    waitForEmulatorReady: typeof systemCommands.waitForEmulatorReady;
    waitForDevice: typeof systemCommands.waitForDevice;
    reboot: typeof systemCommands.reboot;
    changeUserPrivileges: typeof systemCommands.changeUserPrivileges;
    root: typeof systemCommands.root;
    unroot: typeof systemCommands.unroot;
    isRoot: typeof systemCommands.isRoot;
    installMitmCertificate: typeof systemCommands.installMitmCertificate;
    isMitmCertificateInstalled: typeof systemCommands.isMitmCertificateInstalled;
    execBundletool: typeof apksUtilCommands.execBundletool;
    getDeviceSpec: typeof apksUtilCommands.getDeviceSpec;
    installMultipleApks: typeof apksUtilCommands.installMultipleApks;
    installApks: typeof apksUtilCommands.installApks;
    extractBaseApk: typeof apksUtilCommands.extractBaseApk;
    extractLanguageApk: typeof apksUtilCommands.extractLanguageApk;
    isTestPackageOnlyError: typeof apksUtilCommands.isTestPackageOnlyError;
    packageAndLaunchActivityFromManifest: typeof manifestCommands.packageAndLaunchActivityFromManifest;
    targetSdkVersionFromManifest: typeof manifestCommands.targetSdkVersionFromManifest;
    targetSdkVersionUsingPKG: typeof manifestCommands.targetSdkVersionUsingPKG;
    compileManifest: typeof manifestCommands.compileManifest;
    insertManifest: typeof manifestCommands.insertManifest;
    hasInternetPermissionFromManifest: typeof manifestCommands.hasInternetPermissionFromManifest;
    extractUniversalApk: typeof aabUtilCommands.extractUniversalApk;
    isEmulatorConnected: typeof emuCommands.isEmulatorConnected;
    verifyEmulatorConnected: typeof emuCommands.verifyEmulatorConnected;
    fingerprint: typeof emuCommands.fingerprint;
    rotate: typeof emuCommands.rotate;
    powerAC: typeof emuCommands.powerAC;
    sensorSet: typeof emuCommands.sensorSet;
    powerCapacity: typeof emuCommands.powerCapacity;
    powerOFF: typeof emuCommands.powerOFF;
    sendSMS: typeof emuCommands.sendSMS;
    gsmCall: typeof emuCommands.gsmCall;
    gsmSignal: typeof emuCommands.gsmSignal;
    gsmVoice: typeof emuCommands.gsmVoice;
    networkSpeed: typeof emuCommands.networkSpeed;
    sendTelnetCommand: typeof emuCommands.sendTelnetCommand;
    execEmuConsoleCommand: typeof emuCommands.execEmuConsoleCommand;
    getEmuVersionInfo: typeof emuCommands.getEmuVersionInfo;
    getEmuImageProperties: typeof emuCommands.getEmuImageProperties;
    checkAvdExist: typeof emuCommands.checkAvdExist;
    readonly POWER_AC_STATES: {
        readonly POWER_AC_ON: "on";
        readonly POWER_AC_OFF: "off";
    };
    readonly GSM_CALL_ACTIONS: {
        readonly GSM_CALL: "call";
        readonly GSM_ACCEPT: "accept";
        readonly GSM_CANCEL: "cancel";
        readonly GSM_HOLD: "hold";
    };
    readonly GSM_VOICE_STATES: {
        readonly GSM_VOICE_UNREGISTERED: "unregistered";
        readonly GSM_VOICE_HOME: "home";
        readonly GSM_VOICE_ROAMING: "roaming";
        readonly GSM_VOICE_SEARCHING: "searching";
        readonly GSM_VOICE_DENIED: "denied";
        readonly GSM_VOICE_OFF: "off";
        readonly GSM_VOICE_ON: "on";
    };
    readonly GSM_SIGNAL_STRENGTHS: readonly [0, 1, 2, 3, 4];
    readonly NETWORK_SPEED: {
        readonly GSM: "gsm";
        readonly SCSD: "scsd";
        readonly GPRS: "gprs";
        readonly EDGE: "edge";
        readonly UMTS: "umts";
        readonly HSDPA: "hsdpa";
        readonly LTE: "lte";
        readonly EVDO: "evdo";
        readonly FULL: "full";
    };
    readonly SENSORS: {
        readonly ACCELERATION: "acceleration";
        readonly GYROSCOPE: "gyroscope";
        readonly MAGNETIC_FIELD: "magnetic-field";
        readonly ORIENTATION: "orientation";
        readonly TEMPERATURE: "temperature";
        readonly PROXIMITY: "proximity";
        readonly LIGHT: "light";
        readonly PRESSURE: "pressure";
        readonly HUMIDITY: "humidity";
        readonly MAGNETIC_FIELD_UNCALIBRATED: "magnetic-field-uncalibrated";
        readonly GYROSCOPE_UNCALIBRATED: "gyroscope-uncalibrated";
        readonly HINGE_ANGLE0: "hinge-angle0";
        readonly HINGE_ANGLE1: "hinge-angle1";
        readonly HINGE_ANGLE2: "hinge-angle2";
        readonly HEART_RATE: "heart-rate";
        readonly RGBC_LIGHT: "rgbc-light";
    };
    fileExists: typeof fsCommands.fileExists;
    ls: typeof fsCommands.ls;
    fileSize: typeof fsCommands.fileSize;
    rimraf: typeof fsCommands.rimraf;
    push: typeof fsCommands.push;
    pull: typeof fsCommands.pull;
    mkdir: typeof fsCommands.mkdir;
    getDeviceProperty: typeof deviceSettingsCommands.getDeviceProperty;
    setDeviceProperty: typeof deviceSettingsCommands.setDeviceProperty;
    getDeviceSysLanguage: typeof deviceSettingsCommands.getDeviceSysLanguage;
    getDeviceSysCountry: typeof deviceSettingsCommands.getDeviceSysCountry;
    getDeviceSysLocale: typeof deviceSettingsCommands.getDeviceSysLocale;
    getDeviceProductLanguage: typeof deviceSettingsCommands.getDeviceProductLanguage;
    getDeviceProductCountry: typeof deviceSettingsCommands.getDeviceProductCountry;
    getDeviceProductLocale: typeof deviceSettingsCommands.getDeviceProductLocale;
    getModel: typeof deviceSettingsCommands.getModel;
    getManufacturer: typeof deviceSettingsCommands.getManufacturer;
    getScreenSize: typeof deviceSettingsCommands.getScreenSize;
    getScreenDensity: typeof deviceSettingsCommands.getScreenDensity;
    setHttpProxy: typeof deviceSettingsCommands.setHttpProxy;
    deleteHttpProxy: typeof deviceSettingsCommands.deleteHttpProxy;
    setSetting: typeof deviceSettingsCommands.setSetting;
    getSetting: typeof deviceSettingsCommands.getSetting;
    getTimeZone: typeof deviceSettingsCommands.getTimeZone;
    getPlatformVersion: typeof deviceSettingsCommands.getPlatformVersion;
    getLocationProviders: typeof deviceSettingsCommands.getLocationProviders;
    toggleGPSLocationProvider: typeof deviceSettingsCommands.toggleGPSLocationProvider;
    setHiddenApiPolicy: typeof deviceSettingsCommands.setHiddenApiPolicy;
    setDefaultHiddenApiPolicy: typeof deviceSettingsCommands.setDefaultHiddenApiPolicy;
    getDeviceLanguage: typeof deviceSettingsCommands.getDeviceLanguage;
    getDeviceCountry: typeof deviceSettingsCommands.getDeviceCountry;
    getDeviceLocale: typeof deviceSettingsCommands.getDeviceLocale;
    ensureCurrentLocale: typeof deviceSettingsCommands.ensureCurrentLocale;
    setWifiState: typeof deviceSettingsCommands.setWifiState;
    setDataState: typeof deviceSettingsCommands.setDataState;
    getDeviceIdleWhitelist: typeof deviceSettingsCommands.getDeviceIdleWhitelist;
    addToDeviceIdleWhitelist: typeof deviceSettingsCommands.addToDeviceIdleWhitelist;
    isAirplaneModeOn: typeof deviceSettingsCommands.isAirplaneModeOn;
    setAirplaneMode: typeof deviceSettingsCommands.setAirplaneMode;
    setBluetoothOn: typeof deviceSettingsCommands.setBluetoothOn;
    setNfcOn: typeof deviceSettingsCommands.setNfcOn;
    broadcastAirplaneMode: typeof deviceSettingsCommands.broadcastAirplaneMode;
    isWifiOn: typeof deviceSettingsCommands.isWifiOn;
    isDataOn: typeof deviceSettingsCommands.isDataOn;
    isAnimationOn: typeof deviceSettingsCommands.isAnimationOn;
    setAnimationScale: typeof deviceSettingsCommands.setAnimationScale;
    getScreenOrientation: typeof deviceSettingsCommands.getScreenOrientation;
}
//# sourceMappingURL=adb.d.ts.map