{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,+BAA0E;AAC1E,uDAAuD;AACvD,+BAA8B;AAC9B,2CAAsC;AACtC,6BAA4B;AAC5B,6BAAmC;AAEnC,mCAAuH;AAEvH,2BAA2B;AAC3B,MAAM,aAAa,SAAG,sBAAiB,aAAjB,sBAAiB,cAAjB,sBAAiB,GAAI,8BAAyB,mCAAI,OAAO,CAAC,gBAAgB,CAA0B,CAAA;AAE1H,+BAAmE;AAA1D,kGAAA,UAAU,OAAA;AAEnB;;;GAGG;AACH,MAAM,8BAA8B,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAA;AAE9F,uEAAuE;AACvE,IAAI,4BAAsE,CAAA;AAE1E;;;GAGG;AACH,SAAS,wBAAwB,CAAE,QAAgB;IACjD,IAAI,CAAC,8BAA8B;QAAE,OAAM;IAC3C,IAAI,CAAC,4BAA4B;QAAE,4BAA4B,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC,4BAA4B,CAAA;IAC3I,4BAA6B,CAAC,QAAQ,CAAC,CAAA;AACzC,CAAC;AAED;;GAEG;AACU,QAAA,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAA;AAaxE;;GAEG;AACU,QAAA,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAA;AAE9D;;;GAGG;AACH,SAAS,EAAE,CAAE,KAAyB;;IACpC,aAAO,QAAQ,CAAC,KAAK,CAAC,mCAAI,SAAS,CAAA;AACrC,CAAC;AAED;;GAEG;AACH,MAAM,WAAW,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;AACjD,gBAAgB;AACH,QAAA,KAAK,GAAG,WAAW,CAAC,CAAC;IAChC,CAAC,GAAG,IAAS,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAC/E,CAAC,CAAC,GAAG,EAAE,CAAC,SAAS,CAAA;AACnB,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC;IAC3B,CAAO,GAAW,EAAE,EAAiB,EAAE,EAAE;QACvC,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,OAAO,CAAC,CAAI,EAAE,EAAE;YACd,aAAK,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;YAClB,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;QACd,CAAC,CAAA;IACH,CAAC,CAAC,CAAC;IACH,CAAO,CAAS,EAAE,EAAiB,EAAE,EAAE,CAAC,EAAE,CAAA;AAoC5C;;GAEG;AACU,QAAA,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAA;AA+IzD;;GAEG;AACH,SAAS,MAAM,CAAoB,YAAe,EAAE,GAAG,OAAiB;IACtE,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACrC,MAAM,KAAK,GAAI,MAAc,CAAC,GAAG,CAAC,CAAA;YAClC,IAAI,KAAK,KAAK,SAAS;gBAAG,YAAoB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;SAC5D;KACF;IACD,OAAO,YAAY,CAAA;AACrB,CAAC;AAUD;;;GAGG;AACU,QAAA,QAAQ,GAAoB;IACvC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;IAC5B,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;IAClC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;IACpC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;IACpC,MAAM,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;IACtC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB;IACtC,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC;IAC5D,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;IACzC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;IACpC,WAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;IACjD,UAAU,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;IAC/C,YAAY,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;IACpD,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;IAChE,aAAa,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;IACrD,SAAS,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAC7C,YAAY,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;IACnD,QAAQ,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;IAC3C,qBAAqB,EAAE,KAAK;CAC7B,CAAA;AAED;;GAEG;AACH,MAAM,wBAAwB,GAAG;IAC/B,SAAS,EAAE,IAAI;IACf,eAAe,EAAE,KAAK;IACtB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,KAAK;IAClB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,UAAU;CACnB,CAAA;AAED;;GAEG;AACH,SAAgB,KAAK,CAAE,KAAyB;IAC9C,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;AACtE,CAAC;AAFD,sBAEC;AAED;;GAEG;AACH,SAAgB,KAAK,CAAE,KAAyB;IAC9C,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;AAClE,CAAC;AAFD,sBAEC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAE,KAAa;IAC7C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;AAClC,CAAC;AAFD,4CAEC;AAED;;GAEG;AACH,MAAa,OAAQ,SAAQ,sBAAS;IAGpC,YAAoB,cAAsB,EAAS,eAAyB;QAC1E,KAAK,CAAC,oCAAoC,cAAc,EAAE,CAAC,CAAA;QADzC,mBAAc,GAAd,cAAc,CAAQ;QAAS,oBAAe,GAAf,eAAe,CAAU;QAF5E,SAAI,GAAG,SAAS,CAAA;IAIhB,CAAC;IAED;;OAEG;IACH,CAAC,sBAAc,CAAC;QACd,OAAO,IAAI,CAAC,cAAc,CAAA;IAC5B,CAAC;CACF;AAbD,0BAaC;AAsBD;;GAEG;AACH,SAAS,YAAY,CAAK,EAAsB;IAC9C,MAAM,KAAK,GAAG,IAAI,GAAG,EAAa,CAAA;IAElC,OAAO,CAAC,GAAW,EAAK,EAAE;QACxB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACnB,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;SACxB;QAED,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,CAAE,CAAA;IACxB,CAAC,CAAA;AACH,CAAC;AAED,gBAAgB;AAChB,SAAgB,aAAa,CAAE,MAA6B;IAC1D,MAAM,YAAY,GAAG,CAAC,KAAK,CAAC,CAAA;IAC5B,MAAM,YAAY,GAAG,EAAE,CAAA;IAEvB,iEAAiE;IACjE,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG;QAAE,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACjD,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO;QAAE,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACpD,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO;QAAE,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAC3E,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,CAAA;AACvC,CAAC;AATD,sCASC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAE,OAAwB,EAAE;IAClD,MAAM,iBAAiB,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA,CAAC,sBAAsB;IAC1E,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAC5B,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACpE,MAAM,UAAU,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,YAAY,CAAC,CAAA;IAErD,uCAAuC;IACvC,OAAO,CAAC,yBAAiB,CAAC,GAAG,OAAO,CAAA;IAEpC,2BAA2B;IAC3B,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAGvF;IAAC,eAAc,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;IAEzD,OAAO,OAAO,CAAA;AAChB,CAAC;AAhBD,4BAgBC;AAED;;GAEG;AACH,SAAgB,MAAM,CAAE,aAA4B,EAAE;;IACpD,MAAM,GAAG,SAAG,UAAU,CAAC,GAAG,mCAAI,gBAAQ,CAAC,GAAG,CAAA;IAC1C,MAAM,YAAY,SAAG,UAAU,CAAC,QAAQ,mCAAI,gBAAQ,CAAC,QAAQ,CAAA;IAC7D,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,cAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAA;IAE9C;;;OAGG;IACH,SAAS,YAAY,CAAE,IAAwB;QAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,YAAY,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC,CAAA;QACnF,MAAM,EAAE,GAAe,OAAO,CAAC,QAAQ,CAAC,CAAA;QACxC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAA;IACzB,CAAC;IAED,mDAAmD;IACnD,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,CAAA;IAEjD,sEAAsE;IACtE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,EAAE,EAAE,UAAU,CAAC,CAAA;IAC5E,MAAM,OAAO,GAAG,MAAM,CAAkB,EAAE,EAAE,gBAAQ,EAAE,eAAe,IAAI,EAAE,EAAE,UAAU,CAAC,CAAA;IACxF,OAAO,CAAC,OAAO,GAAG;QAChB,GAAG,eAAe,CAAC,OAAO,IAAI,EAAE;QAChC,GAAG,UAAU,CAAC,OAAO,IAAI,EAAE;KAC5B,CAAA;IAED,wEAAwE;IACxE,IAAI,OAAO,CAAC,QAAQ,KAAK,YAAY,EAAE;QACrC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;KACpD;IAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAA;IACpD,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,GAAG,CAAC,UAAU,CAAA;IAC1D,oFAAoF;IACpF,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,KAAK,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,IAAI,CAAA;IAClF,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,SAAS,CAAA;IACtD,MAAM,iBAAiB,GAAG;QACxB,IAAI;QACJ,KAAK;QACL,KAAK;QACL,GAAG,CAAC,OAAO,CAAC,iBAAiB,IAAI,EAAE,CAAC;KACrC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAEb,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;IAChF,MAAM,WAAW,GAAG,IAAI,GAAG,EAEvB,CAAA;IAEJ,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAe,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAA;IAC5F,MAAM,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAC1D,OAAO,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,CAC3C,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAE9B,MAAM,cAAc,GAA8B;QAChD,UAAU,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO;QAChC,mBAAmB,EAAE,GAAG,EAAE,CAAC,GAAG;QAC9B,oBAAoB,EAAE,EAAE,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE;KACvF,CAAA;IAED,yDAAyD;IACzD,gBAAgB,CAAC,OAAO,CAAC;QACvB,WAAW,EAAE,MAAM;QACnB,YAAY,CAAE,SAAiB;;YAC7B,IAAI,IAAI,GAAG,SAAS,CAAA;YACpB,4CAA4C;YAC5C,uDAAuD;YACvD,uEAAuE;YACvE,IAAI,OAAO,CAAC,qBAAqB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;gBAC/D,IAAI;oBACF,IAAI,GAAG,mBAAa,CAAC,IAAI,CAAC,CAAA;iBAC3B;gBAAC,OAAO,CAAC,EAAE,EAAC,mBAAmB,EAAC;aAClC;YACD,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAA;YAC7B,OAAO,OAAA,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,0CAAE,OAAO,KAAI,EAAE,CAAA;QAC7C,CAAC;KACF,CAAC,CAAA;IAEF,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM;QAC9D,CAAC,CAAC,CAAC,EAAE,CAAC,oCAAoC,IAAI,EAAE,CAAC,iBAAiB,CAAC;QACnE,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAA;IAExB,SAAS,aAAa,CAAE,WAA0C;QAChE,MAAM,cAAc,GAAG,iBAAiB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAA;QACrE,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;QACpD,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE,eAAe,CAAC,CAAA;IACrD,CAAC;IAED,SAAS,aAAa,CAAE,oBAAsC;QAC5D,MAAM,KAAK,GAAG,aAAa,CAAC,oBAAoB,CAAC,CAAA;QACjD,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,mDAAmD;YACnD,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;SAC1C;aAAM;YACL,mCAAmC;YACnC,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAED,mCAAmC;IACnC,IAAI,oBAAoB,CAAC,MAAM;QAAE,aAAa,CAAC,oBAAoB,CAAC,CAAA;IAEpE;;OAEG;IACH,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;IAExB;;OAEG;IACH,IAAI,SAA2D,CAAA;IAC/D,IAAI,WAA8E,CAAA;IAElF,MAAM,oBAAoB,GAAI,EAA4B,CAAC,0BAA0B,CAAC,EAAE,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;IAEvH,+FAA+F;IAC/F,SAAS,uBAAuB,CAAE,WAAqC;QACrE,MAAM,qBAAqB,GAAG,EAAE,CAAC,2BAA2B,CAAC,GAAG,EAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAA;QACvG,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAU,CAAA;QAChD,gFAAgF;QAChF,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAA;QAEzC,4FAA4F;QAC5F,wHAAwH;QACxH,sIAAsI;QACtI,MAAM,cAAc,GAAG,0CAA0C,CAAA;QACjE,SAAS,eAAe,CAAE,QAAgB;YACxC,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC1C,IAAI,IAAI;gBAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;YACxB,OAAO,EAAE,CAAA;QACX,CAAC;QAED,0EAA0E;QAC1E,SAAS,4BAA4B,CAAE,QAAgB;YACrD,eAAe,CAAC,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAA;QAChD,CAAC;QAED,SAAS,sBAAsB,CAAE,QAAgB;YAC/C,OAAO,eAAe,CAAC,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAA;QACvD,CAAC;QAED,SAAS,uBAAuB,CAAE,QAAgB;YAChD,OAAO,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC7C,CAAC;QAED;;WAEG;QACH,MAAM,mBAAmB,GAAG,CAAC,cAAuE,EAAE,EAAE;YACtG,MAAM,EAAE,gBAAgB,EAAE,GAAG,cAAc,CAAA;YAC3C,IAAI,gBAAgB,KAAK,SAAS;gBAAE,OAAM;YAC1C,qCAAqC;YACrC,4BAA4B;YAC5B,IACE,cAAc,CAAC,uBAAuB,IAAI,CACxC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACzE,uBAAuB,CAAC,gBAAgB,CAAC;gBACzC,sBAAsB,CAAC,gBAAgB,CAAC,CACzC,EACD;gBACA,cAAc,CAAC,uBAAuB,GAAG,KAAK,CAAA;aAC/C;YACD,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;gBAC3C,sBAAsB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;aAC7C;QACH,CAAC,CAAA;QACD;;;;WAIG;QACH,MAAM,kBAAkB,GAAkD,CAAC,WAAqB,EAAE,cAAsB,EAAE,WAAiC,EAAE,mBAA6D,EAAE,8BAAmD,EAAsC,EAAE;YACrT,OAAO,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBAClC,MAAM,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,UAAU,EAAE,cAAc,EAAE,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,qBAAqB,EAAE,mBAAmB,CAAC,CAAA;gBACpJ,IAAI,cAAc,EAAE;oBAClB,mBAAmB,CAAC,cAAc,CAAC,CAAA;iBACpC;gBACD,OAAO,cAAc,CAAA;YACvB,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,gFAAgF;QAChF,MAAM,mDAAmD,GAAmF,CAAC,UAAU,EAAE,cAAc,EAA2D,EAAE;YAClO,MAAM,GAAG,GAAG,EAAE,CAAC,0BAA0B,CAAC,UAAU,EAAE,cAAc,EAAE,qBAAqB,CAAC,CAAA;YAC5F,IAAI,GAAG,IAAI,GAAG,CAAC,cAAc,EAAE;gBAC7B,mBAAmB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;aACxC;YACD,OAAO,GAAG,CAAA;QACZ,CAAC,CAAA;QAED,MAAM,8BAA8B,GAA8D,CAAC,kBAA4B,EAAE,cAAsB,EAAE,mBAA6D,EAAE,OAA4B,EAAsD,EAAE;YAC1S,8EAA8E;YAC9E,OAAO,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;gBAChD,MAAM,EAAE,8BAA8B,EAAE,GAAG,EAAE,CAAC,6BAA6B,CAAC,iBAAiB,EAAE,cAAc,EAAE,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAA;gBAChK,IAAI,8BAA8B,EAAE;oBAClC,mBAAmB,CAAC,8BAA8B,CAAC,CAAA;iBACpD;gBACD,OAAO,8BAA8B,CAAA;YACvC,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,OAAO;YACL,kBAAkB;YAClB,mDAAmD;YACnD,8BAA8B;YAC9B,uBAAuB;YACvB,4BAA4B;SAC7B,CAAA;IACH,CAAC;IAED,+DAA+D;IAC/D,IAAI,CAAC,aAAa,EAAE;QAClB,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAA;QAC9C,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QAC/C,MAAM,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAA;QAElE,sEAAsE;QACtE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YACzB,IAAI,cAAc,GAAG,CAAC,CAAA;YACtB,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;YAEtF,MAAM,qBAAqB,GAAG,GAAG,EAAE;gBACjC,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE;oBACtC,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAA;oBACpC,OAAO,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;iBACnD;gBAED,OAAO,YAAY,CAAA;YACrB,CAAC,CAAA;YAED,8CAA8C;YAC9C,MAAM,WAAW,GAAiG;gBAChH,iBAAiB,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC/C,kBAAkB,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;gBACnD,gBAAgB,EAAE,CAAC,QAAgB,EAAE,EAAE;oBACrC,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;oBAC1C,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;gBAC1C,CAAC;gBACD,iBAAiB,CAAE,QAAgB;oBACjC,qEAAqE;oBACrE,IAAI,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;oBAEzC,8CAA8C;oBAC9C,IAAI,QAAQ,KAAK,SAAS,EAAE;wBAC1B,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAA;wBACnC,IAAI,QAAQ,KAAK,SAAS;4BAAE,OAAM;wBAElC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;wBAC7B,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;wBACpC,cAAc,EAAE,CAAA;qBACjB;oBAED,OAAO,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;gBAC/C,CAAC;gBACD,QAAQ,EAAE,cAAc;gBACxB,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa;gBACnC,cAAc,EAAE,YAAY,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAC9E,UAAU,EAAE,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAC3D,eAAe,EAAE,YAAY,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;gBACjF,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC1F,UAAU,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO;gBAChC,yBAAyB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,yBAAyB;gBACjE,mBAAmB,EAAE,GAAG,EAAE,CAAC,GAAG;gBAC9B,sBAAsB,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO;gBAC5C,qBAAqB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC;gBACrE,qBAAqB,EAAE,qBAAqB;aAC7C,CAAA;YACD,MAAM,EAAE,kBAAkB,EAAE,mDAAmD,EAAE,8BAA8B,EAAE,uBAAuB,EAAE,4BAA4B,EAAE,GAAG,uBAAuB,CAAC,WAAW,CAAC,CAAA;YAC/M,WAAW,CAAC,kBAAkB,GAAG,kBAAkB,CAAA;YACnD,WAAW,CAAC,mDAAmD,GAAG,mDAAmD,CAAA;YACrH,WAAW,CAAC,8BAA8B,GAAG,8BAA8B,CAAA;YAE3E,MAAM,QAAQ,GAAG,EAAE,CAAC,sBAAsB,CAAC,EAAE,CAAC,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAA;YACjF,MAAM,OAAO,GAAG,EAAE,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAA;YAE/D,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,QAAgB,EAAE,EAAE;gBAC/D,qFAAqF;gBACrF,wEAAwE;gBACxE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE;oBACtE,4BAA4B,CAAC,QAAQ,CAAC,CAAA;oBACtC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;oBAC3B,+DAA+D;oBAC/D,cAAc,EAAE,CAAA;iBACjB;gBAED,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBACvD,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBACnD,qDAAqD;gBACrD,IAAI,QAAQ,KAAK,gBAAgB,EAAE;oBACjC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,CAAC,CAAA;oBAC/C,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;oBACpC,mDAAmD;oBACnD,cAAc,EAAE,CAAA;iBACjB;YACH,CAAC,CAAA;YAED,IAAI,eAAe,GAA4B,SAAS,CAAA;YAExD,SAAS,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE;gBAC7C,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;gBAEjC,MAAM,aAAa,GAAG,OAAO,CAAC,UAAU,EAAE,CAAA;gBAC1C,IAAI,aAAa,KAAK,eAAe,EAAE;oBACrC,aAAK,CAAC,6DAA6D,QAAQ,EAAE,CAAC,CAAA;iBAC/E;gBAED,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBAE9C,iFAAiF;gBACjF,MAAM,WAAW,GAAG,OAAO,CAAC,sBAAsB,CAAC,QAAQ,CAAC;qBACzD,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAA;gBAEpD,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,EAAE,CAAA;gBAEzC,aAAK,CACH,6HAA6H,EAC7H,aAAa,KAAK,YAAY,CAC/B,CAAA;gBAED,eAAe,GAAG,YAAY,CAAA;gBAE9B,MAAM,cAAc,GAAG,iBAAiB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAA;gBACxE,IAAI,cAAc,CAAC,MAAM;oBAAE,aAAa,CAAC,cAAc,CAAC,CAAA;gBAExD,IAAI,MAAM,CAAC,WAAW,EAAE;oBACtB,MAAM,IAAI,SAAS,CAAC,GAAG,eAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAA;iBAChE;gBAED,+CAA+C;gBAC/C,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;oBACnC,MAAM,IAAI,SAAS,CACjB,2BAA2B,eAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI;wBACtD,kEAAkE;wBAClE,yEAAyE;wBACzE,6CAA6C,CAC9C,CAAA;iBACF;gBAED,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;YACjE,CAAC,CAAA;YAED,WAAW,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,QAAgB,EAAE,EAAE;gBACjE,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;gBAEjC,MAAM,IAAI,GAAG,OAAO,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;gBAC/D,MAAM,IAAI,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;gBACnE,MAAM,OAAO,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;gBAEvE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAA;YAC1B,CAAC,CAAA;SACF;aAAM;YACL,MAAM,GAAG,iDACJ,EAAE,CAAC,GAAG,GACN,cAAc,KACjB,QAAQ,EAAE,CAAC,QAAgB,EAAE,EAAE;oBAC7B,MAAM,aAAa,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;oBAChD,IAAI,aAAa,KAAK,SAAS;wBAAE,OAAO,aAAa,CAAA;oBACrD,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAA;oBACzC,IAAI,QAAQ;wBAAE,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;oBAClD,OAAO,QAAQ,CAAA;gBACjB,CAAC,EACD,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa,EACnC,cAAc,EAAE,YAAY,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,EAC9E,UAAU,EAAE,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,EAC3D,eAAe,EAAE,YAAY,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,EACjF,WAAW,EAAE,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EACrE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAC3F,CAAA;YAED,MAAM,IAAI,GAAqB,EAAE,CAAC,6BAA6B;gBAC7D,CAAC,CAAC,EAAE,CAAC,6BAA6B,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC;gBACvD,CAAC,iCACI,GAAG,KACN,aAAa,EAAE,CAAC,QAAQ,EAAE,eAAe,EAAE,EAAE;wBAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;wBACvC,IAAI,QAAQ,KAAK,SAAS;4BAAE,OAAM;wBAClC,OAAO,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAA;oBACjE,CAAC,EACD,qBAAqB,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,cAAO,CAAC,QAAQ,CAAC,CAAC,EAChE,qBAAqB,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,WAAI,CAAC,cAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAChH,yBAAyB,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,yBAAyB,GAC/D,CAAA;YACH,MAAM,EAAE,kBAAkB,EAAE,8BAA8B,EAAE,uBAAuB,EAAE,4BAA4B,EAAE,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAA;YACnJ,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAA;YAC5C,IAAI,CAAC,8BAA8B,GAAG,8BAA8B,CAAA;YAEpE,kEAAkE;YAClE,IAAI,cAAc,GAAG,EAAE,CAAC,wBAAwB;gBAC9C,CAAC,CAAC,EAAE,CAAC,wBAAwB,CAAC;oBAC5B,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;oBACpC,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,IAAI,EAAE,IAAI;oBACV,4BAA4B,EAAE,MAAM,CAAC,MAAM;oBAC3C,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;iBAC5C,CAAC;gBACF,CAAC,CAAC,EAAE,CAAC,8CAA8C,CACjD,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EACzB,MAAM,CAAC,OAAO,EACd,IAAI,EACJ,SAAS,EACT,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,iBAAiB,CACzB,CAAA;YAEH,sCAAsC;YACtC,MAAM,kBAAkB,GAAG,OAAO,YAAY,KAAK,UAAU;gBAC3D,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;gBAC3C,CAAC,CAAC,YAAY,CAAA;YAEhB,6CAA6C;YAC7C,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,QAAgB,EAAE,EAAE;gBAC/D,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBACnD,MAAM,eAAe,GAAG,gBAAgB,KAAK,QAAQ,CAAA;gBACrD,IAAI,eAAe,EAAE;oBACnB,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;iBACrC;gBAED,qEAAqE;gBACrE,IAAI,oBAAoB,GAAG,KAAK,CAAA;gBAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE;oBACtE,4BAA4B,CAAC,QAAQ,CAAC,CAAA;oBACtC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;oBAC3B,oBAAoB,GAAG,IAAI,CAAA;iBAC5B;gBAED,oCAAoC;gBACpC,IAAI,oBAAoB,IAAI,eAAe,EAAE;oBAC3C,cAAc,GAAG,EAAE,CAAC,8CAA8C,CAChE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EACzB,MAAM,CAAC,OAAO,EACd,IAAI,EACJ,cAAc,EACd,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,iBAAiB,CACzB,CAAA;iBACF;YACH,CAAC,CAAA;YAED,SAAS,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE;gBAC7C,MAAM,MAAM,GAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;gBAEzC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;gBAEjC,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBACzD,IAAI,CAAC,UAAU;oBAAE,MAAM,IAAI,SAAS,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAA;gBAExE,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAA;gBAC3C,MAAM,WAAW,GAAG,EAAE,CAAC,qBAAqB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;gBACjE,MAAM,cAAc,GAAG,iBAAiB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAA;gBACxE,IAAI,cAAc,CAAC,MAAM;oBAAE,aAAa,CAAC,cAAc,CAAC,CAAA;gBAExD,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,EAAE;oBAChF,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;wBACzB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;qBACjB;yBAAM;wBACL,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;qBACjB;oBAED,IAAI,OAAO,CAAC,IAAI;wBAAE,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAA;gBACjE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,CAAC,CAAA;gBAE5C,IAAI,MAAM,CAAC,WAAW,EAAE;oBACtB,MAAM,IAAI,SAAS,CAAC,GAAG,eAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAA;iBAChE;gBAED,+DAA+D;gBAC/D,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;oBACpB,IAAI,OAAO,CAAC,+BAA+B,CAAC,UAAU,CAAC,EAAE;wBACvD,MAAM,IAAI,SAAS,CAAC,iDAAiD,eAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAA;qBAChG;oBAED,MAAM,IAAI,SAAS,CACjB,2BAA2B,eAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI;wBACtD,kEAAkE;wBAClE,yEAAyE;wBACzE,6CAA6C,CAC9C,CAAA;iBACF;gBAED,OAAO,MAAM,CAAA;YACf,CAAC,CAAA;YAED,WAAW,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,QAAgB,EAAE,EAAE;gBACjE,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;gBAEjC,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBACzD,IAAI,CAAC,UAAU;oBAAE,MAAM,IAAI,SAAS,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAA;gBAExE,MAAM,IAAI,GAAG,kBAAkB,CAAC,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAA;gBACzD,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,CAAA;gBAC5D,MAAM,MAAM,GAAG,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;gBAEhD,IAAI,CAAC,MAAM;oBAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAA;gBAE7C,MAAM,IAAI,GAAG,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;gBAC5D,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAA;gBAElF,OAAO;oBACL,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;oBACnH,OAAO,EAAE,EAAE,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;iBACxF,CAAA;YACH,CAAC,CAAA;YAED,kDAAkD;YAClD,IAAI,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC9C,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACtB,sCAAsC;oBACrC,cAAc,CAAC,UAAU,EAAU,CAAC,aAAa,EAAE,CAAA;gBACtD,CAAC,CAAC,CAAA;aACH;SACF;KACF;SAAM;QACL,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE;YACtC,MAAM,IAAI,SAAS,CAAC,4DAA4D,CAAC,CAAA;SAClF;QAED,SAAS,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAgB,EAAE;YAC3D,MAAM,MAAM,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE;gBACtC,QAAQ;gBACR,eAAe,EAAE,MAAM,CAAC,OAAO;gBAC/B,iBAAiB,EAAE,IAAI;gBACvB,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAA;YAEF,MAAM,cAAc,GAAG,iBAAiB,CAAC,MAAM,CAAC,WAAW,IAAI,EAAE,EAAE,iBAAiB,CAAC,CAAA;YACrF,IAAI,cAAc,CAAC,MAAM;gBAAE,aAAa,CAAC,cAAc,CAAC,CAAA;YAExD,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,aAAuB,CAAC,CAAA;QAC5D,CAAC,CAAA;QAED,WAAW,GAAG,GAAG,EAAE;YACjB,MAAM,IAAI,SAAS,CAAC,uDAAuD,CAAC,CAAA;QAC9E,CAAC,CAAA;KACF;IAED,6CAA6C;IAC7C,SAAS,OAAO,CAAE,IAAY,EAAE,QAAgB,EAAE,UAAU,GAAG,CAAC;QAC9D,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAA;QACrD,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;QAC9D,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,kBAAkB,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;QAC/E,WAAW,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAA;QACxD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,MAAM,GAAG,IAAI,CAAA;IACjB,MAAM,OAAO,GAAG,CAAC,OAAiB,EAAE,EAAE,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAA;IAC5F,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAA;IACxC,MAAM,OAAO,GAAG,CAAC,QAAgB,EAAE,EAAE;QACnC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAA;QACxB,MAAM,GAAG,GAAG,cAAO,CAAC,QAAQ,CAAC,CAAA;QAC7B,IAAI,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAClF,MAAM,OAAO,GAAG,eAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;YACvC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,CAAA;SACnD;QACD,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;IAED,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAA;AACxE,CAAC;AA/iBD,wBA+iBC;AAED;;GAEG;AACH,SAAS,YAAY,CAAE,MAAgB;IACrC,OAAO,CAAC,OAAe,EAAE,EAAE;QACzB,MAAM,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAA;QAEtC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACvC,CAAC,CAAA;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,uBAAuB,CAAE,GAAW;IAC3C,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA,CAAC,sBAAsB;IAC1D,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA,CAAC,sBAAsB;IACrD,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA,CAAC,sBAAsB;AACtD,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CACzB,YAAwC,EACxC,UAAoB,EACpB,OAAgB,EAChB,iBAA2D;IAE3D,2BAA2B;IAC3B,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;QAC5B,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAA;KACnD;IAED,IAAI,YAAY,EAAE;QAChB,2BAA2B;QAC3B,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,UAAU,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAExF,KAAK,MAAM,GAAG,IAAI,mBAAmB;YAAE,uBAAuB,CAAC,GAAG,CAAC,CAAA;KACpE;AACH,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CACxB,GAAW,EACX,OAAgB,EAChB,eAAyD;IAEzD,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,eAAe,CAAA,CAAC,sBAAsB;IAE7E,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,UAAU,CAAM,EAAE,QAAQ;QAClD,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;QAEtD,IAAI,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE;YACzC,wBAAwB,CAAC,QAAQ,CAAC,CAAA;SACnC;QAED,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;QAE3B,CAAC,CAAC,QAAQ,GAAG,UAAU,IAAY,EAAE,QAAgB;YACnD,aAAK,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAA;YAElC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAA;QACvE,CAAC,CAAA;QAED,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;IACzB,CAAC,CAAA;AACH,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAE,EAAY,EAAE,MAA6B;IAC7D,sDAAsD;IACtD,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAA;IACzB,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAA;IAC7B,OAAO,MAAM,CAAC,OAAO,CAAC,SAAS,CAAA;IAC/B,OAAO,MAAM,CAAC,OAAO,CAAC,cAAc,CAAA;IACpC,OAAO,MAAM,CAAC,OAAO,CAAC,cAAc,CAAA;IACpC,OAAO,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAA;IAEzC,iDAAiD;IACjD,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;QACvC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,CAAA;KAC5C;IAED,qGAAqG;IACrG,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;QACvC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAA;KAC/C;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CACjB,GAAW,EACX,EAAY,EACZ,UAAyB;;IAOzB,IAAI,MAAM,GAAQ,EAAE,eAAe,EAAE,EAAE,EAAE,CAAA;IACzC,IAAI,QAAQ,GAAG,GAAG,CAAA;IAClB,IAAI,cAAc,GAAuB,SAAS,CAAA;IAElD,MAAM,EACJ,UAAU,GAAG,EAAE,CAAC,GAAG,CAAC,UAAU,EAC9B,QAAQ,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,EAC1B,WAAW,GAAG,gBAAQ,CAAC,WAAW,EAClC,OAAO,GAAG,gBAAQ,CAAC,OAAO,EAC3B,GAAG,UAAU,CAAA;IAEd,6CAA6C;IAC7C,IAAI,CAAC,WAAW,EAAE;QAChB,cAAc,GAAG,OAAO;YACtB,CAAC,CAAC,cAAO,CAAC,GAAG,EAAE,OAAO,CAAC;YACvB,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;QAEtC,IAAI,cAAc,EAAE;YAClB,MAAM,MAAM,GAAG,EAAE,CAAC,cAAc,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAA;YAE1D,sBAAsB;YACtB,IAAI,MAAM,CAAC,KAAK,EAAE;gBAChB,OAAO;oBACL,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;oBAC9D,OAAO,EAAE,EAAE;iBACZ,CAAA;aACF;YAED,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;YACtB,QAAQ,GAAG,cAAO,CAAC,cAAc,CAAC,CAAA;SACnC;KACF;IAED,mDAAmD;IACnD,MAAM,eAAe,GAAoB,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;IAE7E,gCAAgC;IAChC,MAAM,KAAK,eAAG,UAAU,CAAC,KAAK,mCAAI,eAAe,CAAC,KAAK,mCAAI,gBAAQ,CAAC,KAAK,CAAA;IACzE,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,CAAC,KAAK,GAAG,EAAE,CAAA;QACjB,MAAM,CAAC,OAAO,GAAG,EAAE,CAAA;KACpB;IAED,6DAA6D;IAC7D,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CACpC,EAAE,EACF,MAAM,CAAC,eAAe,EACtB,gBAAQ,CAAC,eAAe,EACxB,eAAe,CAAC,eAAe,EAC/B,UAAU,CAAC,eAAe,EAC1B,wBAAwB,CACzB,CAAA;IAED,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,0BAA0B,CAAC,MAAM,EAAE;QACtE,UAAU;QACV,QAAQ;QACR,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa;QACnC,yBAAyB,EAAE,EAAE,CAAC,GAAG,CAAC,yBAAyB;KAC5D,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC,CAAA;IAExC,IAAI,eAAe,CAAC,OAAO,EAAE;QAC3B,wEAAwE;QACxE,MAAM,uBAAuB,GAAG,aAAa,CAAC,cAAe,CAAC,CAAA;QAC9D,eAAe,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE;YACrE,OAAO,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC9C,CAAC,CAAC,CAAA;KACH;IAED,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,CAAA;AAC1D,CAAC;AAOD;;GAEG;AACH,SAAS,YAAY,CAAE,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,YAA0C;IACxH,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;IAC9F,MAAM,gBAAgB,GAAG,8CAA8C,SAAS,EAAE,CAAA;IAClF,MAAM,eAAe,GAAG,GAAG,eAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,cAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAA;IAEvH,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,GAAG,gBAAgB,CAAA;AACjE,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAE,aAAqB,EAAE,QAAgB;IAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;IAC3C,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAA;IACzB,SAAS,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC9B,OAAO,SAAS,CAAC,UAAU,CAAA;IAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;AAClC,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAE,WAAsC,EAAE,MAAgB;IAClF,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AAC/D,CAAC;AAED;;;;GAIG;AACH,SAAS,kBAAkB,CAAE,EAAc,EAAE,UAA0B,EAAE,QAAgB;IACvF,IAAI,OAAO,GAAa,UAAU,CAAA;IAElC,KAAK,EAAE,OAAO,IAAI,EAAE;QAClB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;YACnD,MAAM,KAAK,GAAG,KAAK,CAAC,YAAY,EAAE,CAAA;YAClC,IAAI,KAAK,GAAG,QAAQ;gBAAE,MAAK;YAE3B,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;YAC1B,IAAI,QAAQ,IAAI,GAAG,EAAE;gBACnB,OAAO,GAAG,KAAK,CAAA;gBACf,SAAS,KAAK,CAAA;aACf;SACF;QAED,OAAO,OAAO,CAAA;KACf;AACH,CAAC", "sourcesContent": ["import { relative, basename, extname, resolve, dirname, join } from 'path'\nimport sourceMapSupport = require('source-map-support')\nimport * as ynModule from 'yn'\nimport { BaseError } from 'make-error'\nimport * as util from 'util'\nimport { fileURLToPath } from 'url'\nimport type * as _ts from 'typescript'\nimport { Mo<PERSON><PERSON>, createRequire as nodeCreateRequire, createRequireFromPath as nodeCreateRequireFromPath } from 'module'\nimport type _createRequire from 'create-require'\n// tslint:disable-next-line\nconst createRequire = nodeCreateRequire ?? nodeCreateRequireFromPath ?? require('create-require') as typeof _createRequire\n\nexport { createRepl, CreateReplOptions, ReplService } from './repl'\n\n/**\n * Does this version of node obey the package.json \"type\" field\n * and throw ERR_REQUIRE_ESM when attempting to require() an ESM modules.\n */\nconst engineSupportsPackageTypeField = parseInt(process.versions.node.split('.')[0], 10) >= 12\n\n// Loaded conditionally so we don't need to support older node versions\nlet assertScriptCanLoadAsCJSImpl: ((filename: string) => void) | undefined\n\n/**\n * Assert that script can be loaded as CommonJS when we attempt to require it.\n * If it should be loaded as ESM, throw ERR_REQUIRE_ESM like node does.\n */\nfunction assertScriptCanLoadAsCJS (filename: string) {\n  if (!engineSupportsPackageTypeField) return\n  if (!assertScriptCanLoadAsCJSImpl) assertScriptCanLoadAsCJSImpl = require('../dist-raw/node-cjs-loader-utils').assertScriptCanLoadAsCJSImpl\n  assertScriptCanLoadAsCJSImpl!(filename)\n}\n\n/**\n * Registered `ts-node` instance information.\n */\nexport const REGISTER_INSTANCE = Symbol.for('ts-node.register.instance')\n\n/**\n * Expose `REGISTER_INSTANCE` information on node.js `process`.\n */\ndeclare global {\n  namespace NodeJS {\n    interface Process {\n      [REGISTER_INSTANCE]?: Service\n    }\n  }\n}\n\n/**\n * @internal\n */\nexport const INSPECT_CUSTOM = util.inspect.custom || 'inspect'\n\n/**\n * Wrapper around yn module that returns `undefined` instead of `null`.\n * This is implemented by yn v4, but we're staying on v3 to avoid v4's node 10 requirement.\n */\nfunction yn (value: string | undefined) {\n  return ynModule(value) ?? undefined\n}\n\n/**\n * Debugging `ts-node`.\n */\nconst shouldDebug = yn(process.env.TS_NODE_DEBUG)\n/** @internal */\nexport const debug = shouldDebug ?\n  (...args: any) => console.log(`[ts-node ${new Date().toISOString()}]`, ...args)\n  : () => undefined\nconst debugFn = shouldDebug ?\n  <T, U>(key: string, fn: (arg: T) => U) => {\n    let i = 0\n    return (x: T) => {\n      debug(key, x, ++i)\n      return fn(x)\n    }\n  } :\n  <T, U>(_: string, fn: (arg: T) => U) => fn\n\n/**\n * Common TypeScript interfaces between versions.\n */\nexport interface TSCommon {\n  version: typeof _ts.version\n  sys: typeof _ts.sys\n  ScriptSnapshot: typeof _ts.ScriptSnapshot\n  displayPartsToString: typeof _ts.displayPartsToString\n  createLanguageService: typeof _ts.createLanguageService\n  getDefaultLibFilePath: typeof _ts.getDefaultLibFilePath\n  getPreEmitDiagnostics: typeof _ts.getPreEmitDiagnostics\n  flattenDiagnosticMessageText: typeof _ts.flattenDiagnosticMessageText\n  transpileModule: typeof _ts.transpileModule\n  ModuleKind: typeof _ts.ModuleKind\n  ScriptTarget: typeof _ts.ScriptTarget\n  findConfigFile: typeof _ts.findConfigFile\n  readConfigFile: typeof _ts.readConfigFile\n  parseJsonConfigFileContent: typeof _ts.parseJsonConfigFileContent\n  formatDiagnostics: typeof _ts.formatDiagnostics\n  formatDiagnosticsWithColorAndContext: typeof _ts.formatDiagnosticsWithColorAndContext\n}\n\n/**\n * Compiler APIs we use that are marked internal and not included in TypeScript's public API declarations\n */\ninterface TSInternal {\n  // https://github.com/microsoft/TypeScript/blob/4a34294908bed6701dcba2456ca7ac5eafe0ddff/src/compiler/core.ts#L1906-L1909\n  createGetCanonicalFileName (useCaseSensitiveFileNames: boolean): TSInternal.GetCanonicalFileName\n}\nnamespace TSInternal {\n  // https://github.com/microsoft/TypeScript/blob/4a34294908bed6701dcba2456ca7ac5eafe0ddff/src/compiler/core.ts#L1906\n  export type GetCanonicalFileName = (fileName: string) => string\n}\n\n/**\n * Export the current version.\n */\nexport const VERSION = require('../package.json').version\n\n/**\n * Options for creating a new TypeScript compiler instance.\n */\nexport interface CreateOptions {\n  /**\n   * Specify working directory for config resolution.\n   *\n   * @default process.cwd()\n   */\n  dir?: string\n  /**\n   * Emit output files into `.ts-node` directory.\n   *\n   * @default false\n   */\n  emit?: boolean\n  /**\n   * Scope compiler to files within `cwd`.\n   *\n   * @default false\n   */\n  scope?: boolean\n  /**\n   * Use pretty diagnostic formatter.\n   *\n   * @default false\n   */\n  pretty?: boolean\n  /**\n   * Use TypeScript's faster `transpileModule`.\n   *\n   * @default false\n   */\n  transpileOnly?: boolean\n  /**\n   * **DEPRECATED** Specify type-check is enabled (e.g. `transpileOnly == false`).\n   *\n   * @default true\n   */\n  typeCheck?: boolean\n  /**\n   * Use TypeScript's compiler host API.\n   *\n   * @default false\n   */\n  compilerHost?: boolean\n  /**\n   * Logs TypeScript errors to stderr instead of throwing exceptions.\n   *\n   * @default false\n   */\n  logError?: boolean\n  /**\n   * Load files from `tsconfig.json` on startup.\n   *\n   * @default false\n   */\n  files?: boolean\n  /**\n   * Specify a custom TypeScript compiler.\n   *\n   * @default \"typescript\"\n   */\n  compiler?: string\n  /**\n   * Override the path patterns to skip compilation.\n   *\n   * @default /node_modules/\n   * @docsDefault \"/node_modules/\"\n   */\n  ignore?: string[]\n  /**\n   * Path to TypeScript JSON project file.\n   */\n  project?: string\n  /**\n   * Skip project config resolution and loading.\n   *\n   * @default false\n   */\n  skipProject?: boolean\n  /**\n   * Skip ignore check.\n   *\n   * @default false\n   */\n  skipIgnore?: boolean\n  /**\n   * JSON object to merge with compiler options.\n   *\n   * @allOf [{\"$ref\": \"https://schemastore.azurewebsites.net/schemas/json/tsconfig.json#definitions/compilerOptionsDefinition/properties/compilerOptions\"}]\n   */\n  compilerOptions?: object\n  /**\n   * Ignore TypeScript warnings by diagnostic code.\n   */\n  ignoreDiagnostics?: Array<number | string>\n  /**\n   * Modules to require, like node's `--require` flag.\n   *\n   * If specified in tsconfig.json, the modules will be resolved relative to the tsconfig.json file.\n   *\n   * If specified programmatically, each input string should be pre-resolved to an absolute path for\n   * best results.\n   */\n  require?: Array<string>\n  readFile?: (path: string) => string | undefined\n  fileExists?: (path: string) => boolean\n  transformers?: _ts.CustomTransformers | ((p: _ts.Program) => _ts.CustomTransformers)\n  /**\n   * True if require() hooks should interop with experimental ESM loader.\n   * Enabled explicitly via a flag since it is a breaking change.\n   * @internal\n   */\n  experimentalEsmLoader?: boolean\n}\n\n/**\n * Options for registering a TypeScript compiler instance globally.\n */\nexport interface RegisterOptions extends CreateOptions {\n  /**\n   * Re-order file extensions so that TypeScript imports are preferred.\n   *\n   * @default false\n   */\n  preferTsExts?: boolean\n}\n\n/**\n * Must be an interface to support `typescript-json-schema`.\n */\nexport interface TsConfigOptions extends Omit<RegisterOptions,\n  | 'transformers'\n  | 'readFile'\n  | 'fileExists'\n  | 'skipProject'\n  | 'project'\n  | 'dir'\n  > {}\n\n/**\n * Like `Object.assign`, but ignores `undefined` properties.\n */\nfunction assign<T extends object> (initialValue: T, ...sources: Array<T>): T {\n  for (const source of sources) {\n    for (const key of Object.keys(source)) {\n      const value = (source as any)[key]\n      if (value !== undefined) (initialValue as any)[key] = value\n    }\n  }\n  return initialValue\n}\n\n/**\n * Information retrieved from type info check.\n */\nexport interface TypeInfo {\n  name: string\n  comment: string\n}\n\n/**\n * Default register options, including values specified via environment\n * variables.\n */\nexport const DEFAULTS: RegisterOptions = {\n  dir: process.env.TS_NODE_DIR,\n  emit: yn(process.env.TS_NODE_EMIT),\n  scope: yn(process.env.TS_NODE_SCOPE),\n  files: yn(process.env.TS_NODE_FILES),\n  pretty: yn(process.env.TS_NODE_PRETTY),\n  compiler: process.env.TS_NODE_COMPILER,\n  compilerOptions: parse(process.env.TS_NODE_COMPILER_OPTIONS),\n  ignore: split(process.env.TS_NODE_IGNORE),\n  project: process.env.TS_NODE_PROJECT,\n  skipProject: yn(process.env.TS_NODE_SKIP_PROJECT),\n  skipIgnore: yn(process.env.TS_NODE_SKIP_IGNORE),\n  preferTsExts: yn(process.env.TS_NODE_PREFER_TS_EXTS),\n  ignoreDiagnostics: split(process.env.TS_NODE_IGNORE_DIAGNOSTICS),\n  transpileOnly: yn(process.env.TS_NODE_TRANSPILE_ONLY),\n  typeCheck: yn(process.env.TS_NODE_TYPE_CHECK),\n  compilerHost: yn(process.env.TS_NODE_COMPILER_HOST),\n  logError: yn(process.env.TS_NODE_LOG_ERROR),\n  experimentalEsmLoader: false\n}\n\n/**\n * TypeScript compiler option values required by `ts-node` which cannot be overridden.\n */\nconst TS_NODE_COMPILER_OPTIONS = {\n  sourceMap: true,\n  inlineSourceMap: false,\n  inlineSources: true,\n  declaration: false,\n  noEmit: false,\n  outDir: '.ts-node'\n}\n\n/**\n * Split a string array of values.\n */\nexport function split (value: string | undefined) {\n  return typeof value === 'string' ? value.split(/ *, */g) : undefined\n}\n\n/**\n * Parse a string as JSON.\n */\nexport function parse (value: string | undefined): object | undefined {\n  return typeof value === 'string' ? JSON.parse(value) : undefined\n}\n\n/**\n * Replace backslashes with forward slashes.\n */\nexport function normalizeSlashes (value: string): string {\n  return value.replace(/\\\\/g, '/')\n}\n\n/**\n * TypeScript diagnostics error.\n */\nexport class TSError extends BaseError {\n  name = 'TSError'\n\n  constructor (public diagnosticText: string, public diagnosticCodes: number[]) {\n    super(`⨯ Unable to compile TypeScript:\\n${diagnosticText}`)\n  }\n\n  /**\n   * @internal\n   */\n  [INSPECT_CUSTOM] () {\n    return this.diagnosticText\n  }\n}\n\n/**\n * Primary ts-node service, which wraps the TypeScript API and can compile TypeScript to JavaScript\n */\nexport interface Service {\n  ts: TSCommon\n  config: _ts.ParsedCommandLine\n  options: RegisterOptions\n  enabled (enabled?: boolean): boolean\n  ignored (fileName: string): boolean\n  compile (code: string, fileName: string, lineOffset?: number): string\n  getTypeInfo (code: string, fileName: string, position: number): TypeInfo\n}\n\n/**\n * Re-export of `Service` interface for backwards-compatibility\n * @deprecated use `Service` instead\n * @see Service\n */\nexport type Register = Service\n\n/**\n * Cached fs operation wrapper.\n */\nfunction cachedLookup<T> (fn: (arg: string) => T): (arg: string) => T {\n  const cache = new Map<string, T>()\n\n  return (arg: string): T => {\n    if (!cache.has(arg)) {\n      cache.set(arg, fn(arg))\n    }\n\n    return cache.get(arg)!\n  }\n}\n\n/** @internal */\nexport function getExtensions (config: _ts.ParsedCommandLine) {\n  const tsExtensions = ['.ts']\n  const jsExtensions = []\n\n  // Enable additional extensions when JSX or `allowJs` is enabled.\n  if (config.options.jsx) tsExtensions.push('.tsx')\n  if (config.options.allowJs) jsExtensions.push('.js')\n  if (config.options.jsx && config.options.allowJs) jsExtensions.push('.jsx')\n  return { tsExtensions, jsExtensions }\n}\n\n/**\n * Register TypeScript compiler instance onto node.js\n */\nexport function register (opts: RegisterOptions = {}): Service {\n  const originalJsHandler = require.extensions['.js'] // tslint:disable-line\n  const service = create(opts)\n  const { tsExtensions, jsExtensions } = getExtensions(service.config)\n  const extensions = [...tsExtensions, ...jsExtensions]\n\n  // Expose registered instance globally.\n  process[REGISTER_INSTANCE] = service\n\n  // Register the extensions.\n  registerExtensions(service.options.preferTsExts, extensions, service, originalJsHandler)\n\n  // Require specified modules before start-up.\n  ;(Module as any)._preloadModules(service.options.require)\n\n  return service\n}\n\n/**\n * Create TypeScript compiler instance.\n */\nexport function create (rawOptions: CreateOptions = {}): Service {\n  const dir = rawOptions.dir ?? DEFAULTS.dir\n  const compilerName = rawOptions.compiler ?? DEFAULTS.compiler\n  const cwd = dir ? resolve(dir) : process.cwd()\n\n  /**\n   * Load the typescript compiler. It is required to load the tsconfig but might\n   * be changed by the tsconfig, so we sometimes have to do this twice.\n   */\n  function loadCompiler (name: string | undefined) {\n    const compiler = require.resolve(name || 'typescript', { paths: [cwd, __dirname] })\n    const ts: typeof _ts = require(compiler)\n    return { compiler, ts }\n  }\n\n  // Compute minimum options to read the config file.\n  let { compiler, ts } = loadCompiler(compilerName)\n\n  // Read config file and merge new options between env and CLI options.\n  const { config, options: tsconfigOptions } = readConfig(cwd, ts, rawOptions)\n  const options = assign<RegisterOptions>({}, DEFAULTS, tsconfigOptions || {}, rawOptions)\n  options.require = [\n    ...tsconfigOptions.require || [],\n    ...rawOptions.require || []\n  ]\n\n  // If `compiler` option changed based on tsconfig, re-load the compiler.\n  if (options.compiler !== compilerName) {\n    ({ compiler, ts } = loadCompiler(options.compiler))\n  }\n\n  const readFile = options.readFile || ts.sys.readFile\n  const fileExists = options.fileExists || ts.sys.fileExists\n  // typeCheck can override transpileOnly, useful for CLI flag to override config file\n  const transpileOnly = options.transpileOnly === true && options.typeCheck !== true\n  const transformers = options.transformers || undefined\n  const ignoreDiagnostics = [\n    6059, // \"'rootDir' is expected to contain all source files.\"\n    18002, // \"The 'files' list in config file is empty.\"\n    18003, // \"No inputs were found in config file.\"\n    ...(options.ignoreDiagnostics || [])\n  ].map(Number)\n\n  const configDiagnosticList = filterDiagnostics(config.errors, ignoreDiagnostics)\n  const outputCache = new Map<string, {\n    content: string\n  }>()\n\n  const isScoped = options.scope ? (relname: string) => relname.charAt(0) !== '.' : () => true\n  const shouldIgnore = createIgnore(options.skipIgnore ? [] : (\n    options.ignore || ['(?:^|/)node_modules/']\n  ).map(str => new RegExp(str)))\n\n  const diagnosticHost: _ts.FormatDiagnosticsHost = {\n    getNewLine: () => ts.sys.newLine,\n    getCurrentDirectory: () => cwd,\n    getCanonicalFileName: ts.sys.useCaseSensitiveFileNames ? x => x : x => x.toLowerCase()\n  }\n\n  // Install source map support and read from memory cache.\n  sourceMapSupport.install({\n    environment: 'node',\n    retrieveFile (pathOrUrl: string) {\n      let path = pathOrUrl\n      // If it's a file URL, convert to local path\n      // Note: fileURLToPath does not exist on early node v10\n      // I could not find a way to handle non-URLs except to swallow an error\n      if (options.experimentalEsmLoader && path.startsWith('file://')) {\n        try {\n          path = fileURLToPath(path)\n        } catch (e) {/* swallow error */}\n      }\n      path = normalizeSlashes(path)\n      return outputCache.get(path)?.content || ''\n    }\n  })\n\n  const formatDiagnostics = process.stdout.isTTY || options.pretty\n    ? (ts.formatDiagnosticsWithColorAndContext || ts.formatDiagnostics)\n    : ts.formatDiagnostics\n\n  function createTSError (diagnostics: ReadonlyArray<_ts.Diagnostic>) {\n    const diagnosticText = formatDiagnostics(diagnostics, diagnosticHost)\n    const diagnosticCodes = diagnostics.map(x => x.code)\n    return new TSError(diagnosticText, diagnosticCodes)\n  }\n\n  function reportTSError (configDiagnosticList: _ts.Diagnostic[]) {\n    const error = createTSError(configDiagnosticList)\n    if (options.logError) {\n      // Print error in red color and continue execution.\n      console.error('\\x1b[31m%s\\x1b[0m', error)\n    } else {\n      // Throw error and exit the script.\n      throw error\n    }\n  }\n\n  // Render the configuration errors.\n  if (configDiagnosticList.length) reportTSError(configDiagnosticList)\n\n  /**\n   * Get the extension for a transpiled file.\n   */\n  const getExtension = config.options.jsx === ts.JsxEmit.Preserve ?\n    ((path: string) => /\\.[tj]sx$/.test(path) ? '.jsx' : '.js') :\n    ((_: string) => '.js')\n\n  /**\n   * Create the basic required function using transpile mode.\n   */\n  let getOutput: (code: string, fileName: string) => SourceOutput\n  let getTypeInfo: (_code: string, _fileName: string, _position: number) => TypeInfo\n\n  const getCanonicalFileName = (ts as unknown as TSInternal).createGetCanonicalFileName(ts.sys.useCaseSensitiveFileNames)\n\n  // In a factory because these are shared across both CompilerHost and LanguageService codepaths\n  function createResolverFunctions (serviceHost: _ts.ModuleResolutionHost) {\n    const moduleResolutionCache = ts.createModuleResolutionCache(cwd, getCanonicalFileName, config.options)\n    const knownInternalFilenames = new Set<string>()\n    /** \"Buckets\" (module directories) whose contents should be marked \"internal\" */\n    const internalBuckets = new Set<string>()\n\n    // Get bucket for a source filename.  Bucket is the containing `./node_modules/*/` directory\n    // For '/project/node_modules/foo/node_modules/bar/lib/index.js' bucket is '/project/node_modules/foo/node_modules/bar/'\n    // For '/project/node_modules/foo/node_modules/@scope/bar/lib/index.js' bucket is '/project/node_modules/foo/node_modules/@scope/bar/'\n    const moduleBucketRe = /.*\\/node_modules\\/(?:@[^\\/]+\\/)?[^\\/]+\\//\n    function getModuleBucket (filename: string) {\n      const find = moduleBucketRe.exec(filename)\n      if (find) return find[0]\n      return ''\n    }\n\n    // Mark that this file and all siblings in its bucket should be \"internal\"\n    function markBucketOfFilenameInternal (filename: string) {\n      internalBuckets.add(getModuleBucket(filename))\n    }\n\n    function isFileInInternalBucket (filename: string) {\n      return internalBuckets.has(getModuleBucket(filename))\n    }\n\n    function isFileKnownToBeInternal (filename: string) {\n      return knownInternalFilenames.has(filename)\n    }\n\n    /**\n     * If we need to emit JS for a file, force TS to consider it non-external\n     */\n    const fixupResolvedModule = (resolvedModule: _ts.ResolvedModule | _ts.ResolvedTypeReferenceDirective) => {\n      const { resolvedFileName } = resolvedModule\n      if (resolvedFileName === undefined) return\n      // .ts is always switched to internal\n      // .js is switched on-demand\n      if (\n        resolvedModule.isExternalLibraryImport && (\n          (resolvedFileName.endsWith('.ts') && !resolvedFileName.endsWith('.d.ts')) ||\n          isFileKnownToBeInternal(resolvedFileName) ||\n          isFileInInternalBucket(resolvedFileName)\n        )\n      ) {\n        resolvedModule.isExternalLibraryImport = false\n      }\n      if (!resolvedModule.isExternalLibraryImport) {\n        knownInternalFilenames.add(resolvedFileName)\n      }\n    }\n    /*\n     * NOTE:\n     * Older ts versions do not pass `redirectedReference` nor `options`.\n     * We must pass `redirectedReference` to newer ts versions, but cannot rely on `options`, hence the weird argument name\n     */\n    const resolveModuleNames: _ts.LanguageServiceHost['resolveModuleNames'] = (moduleNames: string[], containingFile: string, reusedNames: string[] | undefined, redirectedReference: _ts.ResolvedProjectReference | undefined, optionsOnlyWithNewerTsVersions: _ts.CompilerOptions): (_ts.ResolvedModule | undefined)[] => {\n      return moduleNames.map(moduleName => {\n        const { resolvedModule } = ts.resolveModuleName(moduleName, containingFile, config.options, serviceHost, moduleResolutionCache, redirectedReference)\n        if (resolvedModule) {\n          fixupResolvedModule(resolvedModule)\n        }\n        return resolvedModule\n      })\n    }\n\n    // language service never calls this, but TS docs recommend that we implement it\n    const getResolvedModuleWithFailedLookupLocationsFromCache: _ts.LanguageServiceHost['getResolvedModuleWithFailedLookupLocationsFromCache'] = (moduleName, containingFile): _ts.ResolvedModuleWithFailedLookupLocations | undefined => {\n      const ret = ts.resolveModuleNameFromCache(moduleName, containingFile, moduleResolutionCache)\n      if (ret && ret.resolvedModule) {\n        fixupResolvedModule(ret.resolvedModule)\n      }\n      return ret\n    }\n\n    const resolveTypeReferenceDirectives: _ts.LanguageServiceHost['resolveTypeReferenceDirectives'] = (typeDirectiveNames: string[], containingFile: string, redirectedReference: _ts.ResolvedProjectReference | undefined, options: _ts.CompilerOptions): (_ts.ResolvedTypeReferenceDirective | undefined)[] => {\n      // Note: seems to be called with empty typeDirectiveNames array for all files.\n      return typeDirectiveNames.map(typeDirectiveName => {\n        const { resolvedTypeReferenceDirective } = ts.resolveTypeReferenceDirective(typeDirectiveName, containingFile, config.options, serviceHost, redirectedReference)\n        if (resolvedTypeReferenceDirective) {\n          fixupResolvedModule(resolvedTypeReferenceDirective)\n        }\n        return resolvedTypeReferenceDirective\n      })\n    }\n\n    return {\n      resolveModuleNames,\n      getResolvedModuleWithFailedLookupLocationsFromCache,\n      resolveTypeReferenceDirectives,\n      isFileKnownToBeInternal,\n      markBucketOfFilenameInternal\n    }\n  }\n\n  // Use full language services when the fast option is disabled.\n  if (!transpileOnly) {\n    const fileContents = new Map<string, string>()\n    const rootFileNames = new Set(config.fileNames)\n    const cachedReadFile = cachedLookup(debugFn('readFile', readFile))\n\n    // Use language services by default (TODO: invert next major version).\n    if (!options.compilerHost) {\n      let projectVersion = 1\n      const fileVersions = new Map(Array.from(rootFileNames).map(fileName => [fileName, 0]))\n\n      const getCustomTransformers = () => {\n        if (typeof transformers === 'function') {\n          const program = service.getProgram()\n          return program ? transformers(program) : undefined\n        }\n\n        return transformers\n      }\n\n      // Create the compiler host for type checking.\n      const serviceHost: _ts.LanguageServiceHost & Required<Pick<_ts.LanguageServiceHost, 'fileExists' | 'readFile'>> = {\n        getProjectVersion: () => String(projectVersion),\n        getScriptFileNames: () => Array.from(rootFileNames),\n        getScriptVersion: (fileName: string) => {\n          const version = fileVersions.get(fileName)\n          return version ? version.toString() : ''\n        },\n        getScriptSnapshot (fileName: string) {\n          // TODO ordering of this with getScriptVersion?  Should they sync up?\n          let contents = fileContents.get(fileName)\n\n          // Read contents into TypeScript memory cache.\n          if (contents === undefined) {\n            contents = cachedReadFile(fileName)\n            if (contents === undefined) return\n\n            fileVersions.set(fileName, 1)\n            fileContents.set(fileName, contents)\n            projectVersion++\n          }\n\n          return ts.ScriptSnapshot.fromString(contents)\n        },\n        readFile: cachedReadFile,\n        readDirectory: ts.sys.readDirectory,\n        getDirectories: cachedLookup(debugFn('getDirectories', ts.sys.getDirectories)),\n        fileExists: cachedLookup(debugFn('fileExists', fileExists)),\n        directoryExists: cachedLookup(debugFn('directoryExists', ts.sys.directoryExists)),\n        realpath: ts.sys.realpath ? cachedLookup(debugFn('realpath', ts.sys.realpath)) : undefined,\n        getNewLine: () => ts.sys.newLine,\n        useCaseSensitiveFileNames: () => ts.sys.useCaseSensitiveFileNames,\n        getCurrentDirectory: () => cwd,\n        getCompilationSettings: () => config.options,\n        getDefaultLibFileName: () => ts.getDefaultLibFilePath(config.options),\n        getCustomTransformers: getCustomTransformers\n      }\n      const { resolveModuleNames, getResolvedModuleWithFailedLookupLocationsFromCache, resolveTypeReferenceDirectives, isFileKnownToBeInternal, markBucketOfFilenameInternal } = createResolverFunctions(serviceHost)\n      serviceHost.resolveModuleNames = resolveModuleNames\n      serviceHost.getResolvedModuleWithFailedLookupLocationsFromCache = getResolvedModuleWithFailedLookupLocationsFromCache\n      serviceHost.resolveTypeReferenceDirectives = resolveTypeReferenceDirectives\n\n      const registry = ts.createDocumentRegistry(ts.sys.useCaseSensitiveFileNames, cwd)\n      const service = ts.createLanguageService(serviceHost, registry)\n\n      const updateMemoryCache = (contents: string, fileName: string) => {\n        // Add to `rootFiles` as necessary, either to make TS include a file it has not seen,\n        // or to trigger a re-classification of files from external to internal.\n        if (!rootFileNames.has(fileName) && !isFileKnownToBeInternal(fileName)) {\n          markBucketOfFilenameInternal(fileName)\n          rootFileNames.add(fileName)\n          // Increment project version for every change to rootFileNames.\n          projectVersion++\n        }\n\n        const previousVersion = fileVersions.get(fileName) || 0\n        const previousContents = fileContents.get(fileName)\n        // Avoid incrementing cache when nothing has changed.\n        if (contents !== previousContents) {\n          fileVersions.set(fileName, previousVersion + 1)\n          fileContents.set(fileName, contents)\n          // Increment project version for every file change.\n          projectVersion++\n        }\n      }\n\n      let previousProgram: _ts.Program | undefined = undefined\n\n      getOutput = (code: string, fileName: string) => {\n        updateMemoryCache(code, fileName)\n\n        const programBefore = service.getProgram()\n        if (programBefore !== previousProgram) {\n          debug(`compiler rebuilt Program instance when getting output for ${fileName}`)\n        }\n\n        const output = service.getEmitOutput(fileName)\n\n        // Get the relevant diagnostics - this is 3x faster than `getPreEmitDiagnostics`.\n        const diagnostics = service.getSemanticDiagnostics(fileName)\n          .concat(service.getSyntacticDiagnostics(fileName))\n\n        const programAfter = service.getProgram()\n\n        debug(\n          'invariant: Is service.getProject() identical before and after getting emit output and diagnostics? (should always be true) ',\n          programBefore === programAfter\n        )\n\n        previousProgram = programAfter\n\n        const diagnosticList = filterDiagnostics(diagnostics, ignoreDiagnostics)\n        if (diagnosticList.length) reportTSError(diagnosticList)\n\n        if (output.emitSkipped) {\n          throw new TypeError(`${relative(cwd, fileName)}: Emit skipped`)\n        }\n\n        // Throw an error when requiring `.d.ts` files.\n        if (output.outputFiles.length === 0) {\n          throw new TypeError(\n            `Unable to require file: ${relative(cwd, fileName)}\\n` +\n            'This is usually the result of a faulty configuration or import. ' +\n            'Make sure there is a `.js`, `.json` or other executable extension with ' +\n            'loader attached before `ts-node` available.'\n          )\n        }\n\n        return [output.outputFiles[1].text, output.outputFiles[0].text]\n      }\n\n      getTypeInfo = (code: string, fileName: string, position: number) => {\n        updateMemoryCache(code, fileName)\n\n        const info = service.getQuickInfoAtPosition(fileName, position)\n        const name = ts.displayPartsToString(info ? info.displayParts : [])\n        const comment = ts.displayPartsToString(info ? info.documentation : [])\n\n        return { name, comment }\n      }\n    } else {\n      const sys: _ts.System & _ts.FormatDiagnosticsHost = {\n        ...ts.sys,\n        ...diagnosticHost,\n        readFile: (fileName: string) => {\n          const cacheContents = fileContents.get(fileName)\n          if (cacheContents !== undefined) return cacheContents\n          const contents = cachedReadFile(fileName)\n          if (contents) fileContents.set(fileName, contents)\n          return contents\n        },\n        readDirectory: ts.sys.readDirectory,\n        getDirectories: cachedLookup(debugFn('getDirectories', ts.sys.getDirectories)),\n        fileExists: cachedLookup(debugFn('fileExists', fileExists)),\n        directoryExists: cachedLookup(debugFn('directoryExists', ts.sys.directoryExists)),\n        resolvePath: cachedLookup(debugFn('resolvePath', ts.sys.resolvePath)),\n        realpath: ts.sys.realpath ? cachedLookup(debugFn('realpath', ts.sys.realpath)) : undefined\n      }\n\n      const host: _ts.CompilerHost = ts.createIncrementalCompilerHost\n        ? ts.createIncrementalCompilerHost(config.options, sys)\n        : {\n          ...sys,\n          getSourceFile: (fileName, languageVersion) => {\n            const contents = sys.readFile(fileName)\n            if (contents === undefined) return\n            return ts.createSourceFile(fileName, contents, languageVersion)\n          },\n          getDefaultLibLocation: () => normalizeSlashes(dirname(compiler)),\n          getDefaultLibFileName: () => normalizeSlashes(join(dirname(compiler), ts.getDefaultLibFileName(config.options))),\n          useCaseSensitiveFileNames: () => sys.useCaseSensitiveFileNames\n        }\n      const { resolveModuleNames, resolveTypeReferenceDirectives, isFileKnownToBeInternal, markBucketOfFilenameInternal } = createResolverFunctions(host)\n      host.resolveModuleNames = resolveModuleNames\n      host.resolveTypeReferenceDirectives = resolveTypeReferenceDirectives\n\n      // Fallback for older TypeScript releases without incremental API.\n      let builderProgram = ts.createIncrementalProgram\n        ? ts.createIncrementalProgram({\n          rootNames: Array.from(rootFileNames),\n          options: config.options,\n          host: host,\n          configFileParsingDiagnostics: config.errors,\n          projectReferences: config.projectReferences\n        })\n        : ts.createEmitAndSemanticDiagnosticsBuilderProgram(\n          Array.from(rootFileNames),\n          config.options,\n          host,\n          undefined,\n          config.errors,\n          config.projectReferences\n        )\n\n      // Read and cache custom transformers.\n      const customTransformers = typeof transformers === 'function'\n        ? transformers(builderProgram.getProgram())\n        : transformers\n\n      // Set the file contents into cache manually.\n      const updateMemoryCache = (contents: string, fileName: string) => {\n        const previousContents = fileContents.get(fileName)\n        const contentsChanged = previousContents !== contents\n        if (contentsChanged) {\n          fileContents.set(fileName, contents)\n        }\n\n        // Add to `rootFiles` when discovered by compiler for the first time.\n        let addedToRootFileNames = false\n        if (!rootFileNames.has(fileName) && !isFileKnownToBeInternal(fileName)) {\n          markBucketOfFilenameInternal(fileName)\n          rootFileNames.add(fileName)\n          addedToRootFileNames = true\n        }\n\n        // Update program when file changes.\n        if (addedToRootFileNames || contentsChanged) {\n          builderProgram = ts.createEmitAndSemanticDiagnosticsBuilderProgram(\n            Array.from(rootFileNames),\n            config.options,\n            host,\n            builderProgram,\n            config.errors,\n            config.projectReferences\n          )\n        }\n      }\n\n      getOutput = (code: string, fileName: string) => {\n        const output: [string, string] = ['', '']\n\n        updateMemoryCache(code, fileName)\n\n        const sourceFile = builderProgram.getSourceFile(fileName)\n        if (!sourceFile) throw new TypeError(`Unable to read file: ${fileName}`)\n\n        const program = builderProgram.getProgram()\n        const diagnostics = ts.getPreEmitDiagnostics(program, sourceFile)\n        const diagnosticList = filterDiagnostics(diagnostics, ignoreDiagnostics)\n        if (diagnosticList.length) reportTSError(diagnosticList)\n\n        const result = builderProgram.emit(sourceFile, (path, file, writeByteOrderMark) => {\n          if (path.endsWith('.map')) {\n            output[1] = file\n          } else {\n            output[0] = file\n          }\n\n          if (options.emit) sys.writeFile(path, file, writeByteOrderMark)\n        }, undefined, undefined, customTransformers)\n\n        if (result.emitSkipped) {\n          throw new TypeError(`${relative(cwd, fileName)}: Emit skipped`)\n        }\n\n        // Throw an error when requiring files that cannot be compiled.\n        if (output[0] === '') {\n          if (program.isSourceFileFromExternalLibrary(sourceFile)) {\n            throw new TypeError(`Unable to compile file from external library: ${relative(cwd, fileName)}`)\n          }\n\n          throw new TypeError(\n            `Unable to require file: ${relative(cwd, fileName)}\\n` +\n            'This is usually the result of a faulty configuration or import. ' +\n            'Make sure there is a `.js`, `.json` or other executable extension with ' +\n            'loader attached before `ts-node` available.'\n          )\n        }\n\n        return output\n      }\n\n      getTypeInfo = (code: string, fileName: string, position: number) => {\n        updateMemoryCache(code, fileName)\n\n        const sourceFile = builderProgram.getSourceFile(fileName)\n        if (!sourceFile) throw new TypeError(`Unable to read file: ${fileName}`)\n\n        const node = getTokenAtPosition(ts, sourceFile, position)\n        const checker = builderProgram.getProgram().getTypeChecker()\n        const symbol = checker.getSymbolAtLocation(node)\n\n        if (!symbol) return { name: '', comment: '' }\n\n        const type = checker.getTypeOfSymbolAtLocation(symbol, node)\n        const signatures = [...type.getConstructSignatures(), ...type.getCallSignatures()]\n\n        return {\n          name: signatures.length ? signatures.map(x => checker.signatureToString(x)).join('\\n') : checker.typeToString(type),\n          comment: ts.displayPartsToString(symbol ? symbol.getDocumentationComment(checker) : [])\n        }\n      }\n\n      // Write `.tsbuildinfo` when `--build` is enabled.\n      if (options.emit && config.options.incremental) {\n        process.on('exit', () => {\n          // Emits `.tsbuildinfo` to filesystem.\n          (builderProgram.getProgram() as any).emitBuildInfo()\n        })\n      }\n    }\n  } else {\n    if (typeof transformers === 'function') {\n      throw new TypeError('Transformers function is unavailable in \"--transpile-only\"')\n    }\n\n    getOutput = (code: string, fileName: string): SourceOutput => {\n      const result = ts.transpileModule(code, {\n        fileName,\n        compilerOptions: config.options,\n        reportDiagnostics: true,\n        transformers: transformers\n      })\n\n      const diagnosticList = filterDiagnostics(result.diagnostics || [], ignoreDiagnostics)\n      if (diagnosticList.length) reportTSError(diagnosticList)\n\n      return [result.outputText, result.sourceMapText as string]\n    }\n\n    getTypeInfo = () => {\n      throw new TypeError('Type information is unavailable in \"--transpile-only\"')\n    }\n  }\n\n  // Create a simple TypeScript compiler proxy.\n  function compile (code: string, fileName: string, lineOffset = 0) {\n    const normalizedFileName = normalizeSlashes(fileName)\n    const [value, sourceMap] = getOutput(code, normalizedFileName)\n    const output = updateOutput(value, normalizedFileName, sourceMap, getExtension)\n    outputCache.set(normalizedFileName, { content: output })\n    return output\n  }\n\n  let active = true\n  const enabled = (enabled?: boolean) => enabled === undefined ? active : (active = !!enabled)\n  const extensions = getExtensions(config)\n  const ignored = (fileName: string) => {\n    if (!active) return true\n    const ext = extname(fileName)\n    if (extensions.tsExtensions.includes(ext) || extensions.jsExtensions.includes(ext)) {\n      const relname = relative(cwd, fileName)\n      return !isScoped(relname) || shouldIgnore(relname)\n    }\n    return true\n  }\n\n  return { ts, config, compile, getTypeInfo, ignored, enabled, options }\n}\n\n/**\n * Check if the filename should be ignored.\n */\nfunction createIgnore (ignore: RegExp[]) {\n  return (relname: string) => {\n    const path = normalizeSlashes(relname)\n\n    return ignore.some(x => x.test(path))\n  }\n}\n\n/**\n * \"Refreshes\" an extension on `require.extensions`.\n *\n * @param {string} ext\n */\nfunction reorderRequireExtension (ext: string) {\n  const old = require.extensions[ext] // tslint:disable-line\n  delete require.extensions[ext] // tslint:disable-line\n  require.extensions[ext] = old // tslint:disable-line\n}\n\n/**\n * Register the extensions to support when importing files.\n */\nfunction registerExtensions (\n  preferTsExts: boolean | null | undefined,\n  extensions: string[],\n  service: Service,\n  originalJsHandler: (m: NodeModule, filename: string) => any\n) {\n  // Register new extensions.\n  for (const ext of extensions) {\n    registerExtension(ext, service, originalJsHandler)\n  }\n\n  if (preferTsExts) {\n    // tslint:disable-next-line\n    const preferredExtensions = new Set([...extensions, ...Object.keys(require.extensions)])\n\n    for (const ext of preferredExtensions) reorderRequireExtension(ext)\n  }\n}\n\n/**\n * Register the extension for node.\n */\nfunction registerExtension (\n  ext: string,\n  service: Service,\n  originalHandler: (m: NodeModule, filename: string) => any\n) {\n  const old = require.extensions[ext] || originalHandler // tslint:disable-line\n\n  require.extensions[ext] = function (m: any, filename) { // tslint:disable-line\n    if (service.ignored(filename)) return old(m, filename)\n\n    if (service.options.experimentalEsmLoader) {\n      assertScriptCanLoadAsCJS(filename)\n    }\n\n    const _compile = m._compile\n\n    m._compile = function (code: string, fileName: string) {\n      debug('module._compile', fileName)\n\n      return _compile.call(this, service.compile(code, fileName), fileName)\n    }\n\n    return old(m, filename)\n  }\n}\n\n/**\n * Do post-processing on config options to support `ts-node`.\n */\nfunction fixConfig (ts: TSCommon, config: _ts.ParsedCommandLine) {\n  // Delete options that *should not* be passed through.\n  delete config.options.out\n  delete config.options.outFile\n  delete config.options.composite\n  delete config.options.declarationDir\n  delete config.options.declarationMap\n  delete config.options.emitDeclarationOnly\n\n  // Target ES5 output by default (instead of ES3).\n  if (config.options.target === undefined) {\n    config.options.target = ts.ScriptTarget.ES5\n  }\n\n  // Target CommonJS modules by default (instead of magically switching to ES6 when the target is ES6).\n  if (config.options.module === undefined) {\n    config.options.module = ts.ModuleKind.CommonJS\n  }\n\n  return config\n}\n\n/**\n * Load TypeScript configuration. Returns the parsed TypeScript config and\n * any `ts-node` options specified in the config file.\n */\nfunction readConfig (\n  cwd: string,\n  ts: TSCommon,\n  rawOptions: CreateOptions\n): {\n  // Parsed TypeScript configuration.\n  config: _ts.ParsedCommandLine\n  // Options pulled from `tsconfig.json`.\n  options: TsConfigOptions\n} {\n  let config: any = { compilerOptions: {} }\n  let basePath = cwd\n  let configFileName: string | undefined = undefined\n\n  const {\n    fileExists = ts.sys.fileExists,\n    readFile = ts.sys.readFile,\n    skipProject = DEFAULTS.skipProject,\n    project = DEFAULTS.project\n  } = rawOptions\n\n  // Read project configuration when available.\n  if (!skipProject) {\n    configFileName = project\n      ? resolve(cwd, project)\n      : ts.findConfigFile(cwd, fileExists)\n\n    if (configFileName) {\n      const result = ts.readConfigFile(configFileName, readFile)\n\n      // Return diagnostics.\n      if (result.error) {\n        return {\n          config: { errors: [result.error], fileNames: [], options: {} },\n          options: {}\n        }\n      }\n\n      config = result.config\n      basePath = dirname(configFileName)\n    }\n  }\n\n  // Fix ts-node options that come from tsconfig.json\n  const tsconfigOptions: TsConfigOptions = Object.assign({}, config['ts-node'])\n\n  // Remove resolution of \"files\".\n  const files = rawOptions.files ?? tsconfigOptions.files ?? DEFAULTS.files\n  if (!files) {\n    config.files = []\n    config.include = []\n  }\n\n  // Override default configuration options `ts-node` requires.\n  config.compilerOptions = Object.assign(\n    {},\n    config.compilerOptions,\n    DEFAULTS.compilerOptions,\n    tsconfigOptions.compilerOptions,\n    rawOptions.compilerOptions,\n    TS_NODE_COMPILER_OPTIONS\n  )\n\n  const fixedConfig = fixConfig(ts, ts.parseJsonConfigFileContent(config, {\n    fileExists,\n    readFile,\n    readDirectory: ts.sys.readDirectory,\n    useCaseSensitiveFileNames: ts.sys.useCaseSensitiveFileNames\n  }, basePath, undefined, configFileName))\n\n  if (tsconfigOptions.require) {\n    // Modules are found relative to the tsconfig file, not the `dir` option\n    const tsconfigRelativeRequire = createRequire(configFileName!)\n    tsconfigOptions.require = tsconfigOptions.require.map((path: string) => {\n      return tsconfigRelativeRequire.resolve(path)\n    })\n  }\n\n  return { config: fixedConfig, options: tsconfigOptions }\n}\n\n/**\n * Internal source output.\n */\ntype SourceOutput = [string, string]\n\n/**\n * Update the output remapping the source map.\n */\nfunction updateOutput (outputText: string, fileName: string, sourceMap: string, getExtension: (fileName: string) => string) {\n  const base64Map = Buffer.from(updateSourceMap(sourceMap, fileName), 'utf8').toString('base64')\n  const sourceMapContent = `data:application/json;charset=utf-8;base64,${base64Map}`\n  const sourceMapLength = `${basename(fileName)}.map`.length + (getExtension(fileName).length - extname(fileName).length)\n\n  return outputText.slice(0, -sourceMapLength) + sourceMapContent\n}\n\n/**\n * Update the source map contents for improved output.\n */\nfunction updateSourceMap (sourceMapText: string, fileName: string) {\n  const sourceMap = JSON.parse(sourceMapText)\n  sourceMap.file = fileName\n  sourceMap.sources = [fileName]\n  delete sourceMap.sourceRoot\n  return JSON.stringify(sourceMap)\n}\n\n/**\n * Filter diagnostics.\n */\nfunction filterDiagnostics (diagnostics: readonly _ts.Diagnostic[], ignore: number[]) {\n  return diagnostics.filter(x => ignore.indexOf(x.code) === -1)\n}\n\n/**\n * Get token at file position.\n *\n * Reference: https://github.com/microsoft/TypeScript/blob/fcd9334f57d85b73dd66ad2d21c02e84822f4841/src/services/utilities.ts#L705-L731\n */\nfunction getTokenAtPosition (ts: typeof _ts, sourceFile: _ts.SourceFile, position: number): _ts.Node {\n  let current: _ts.Node = sourceFile\n\n  outer: while (true) {\n    for (const child of current.getChildren(sourceFile)) {\n      const start = child.getFullStart()\n      if (start > position) break\n\n      const end = child.getEnd()\n      if (position <= end) {\n        current = child\n        continue outer\n      }\n    }\n\n    return current\n  }\n}\n"]}