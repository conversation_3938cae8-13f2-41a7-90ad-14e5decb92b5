<%
const harnessImport = answers.installTestingLibrary
    ? `import { render, fireEvent } from '@testing-library/vue'`
    : `import { createApp } from 'vue'`
const renderCommand = answers.installTestingLibrary
    ? `render(ExampleComponent, { props: { msg: 'WebdriverIO Component Testing' } })`
    : `createApp(ExampleComponent, { msg: 'WebdriverIO Component Testing' }).mount(container)`
%>
import { $, expect } from '@wdio/globals'
<%- harnessImport %>
<% if (answers.installTestingLibrary) { %>
import * as matchers from '@testing-library/jest-dom/matchers'
expect.extend(matchers)
<% } %>
import ExampleComponent from './Component.vue'
import './Component.css'

describe('Vue Component Testing', () => {
    <% if (answers.installTestingLibrary) { %>
    it('should test component with Testing Library', async () => {
        // The render method returns a collection of utilities to query your component.
        const { getByText } = render(ExampleComponent, {
            props: { msg: 'WebdriverIO Component Testing' }
        })

        const component = getByText(/count is 0/i)
        expect(component).toBeInTheDocument()

        await fireEvent.click(component)
        await fireEvent.click(component)

        expect(getByText(/count is 2/i)).toBeInTheDocument()
    })
    <% } else { %>
    let container<%- answers.isUsingTypeScript ? `: Element` : '' %>

    beforeEach(() => {
        container = document.createElement('div')
        container.setAttribute('id', 'app')
        document.body.appendChild(container)
    })

    afterEach(() => {
        container?.remove()
    })
    <% } %>

    it('should test component with WebdriverIO', async () => {
        <%- renderCommand %>

        const component = await $('button*=count is')
        await expect(component).toBePresent()
        await expect(component).toHaveText('count is 0')

        await component.click()
        await component.click()

        await expect(component).toHaveText('count is 2')<%-
        answers.includeVisualTesting ? `
        await expect(component).toMatchElementSnapshot('counterButton')` : '' %>
    })
})
