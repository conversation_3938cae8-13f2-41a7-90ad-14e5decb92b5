"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAlertText = getAlertText;
exports.mobileAcceptAlert = mobileAcceptAlert;
exports.postAcceptAlert = postAcceptAlert;
exports.mobileDismissAlert = mobileDismissAlert;
exports.postDismissAlert = postDismissAlert;
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<string>}
 */
async function getAlertText() {
    return String(await this.uiautomator2.jwproxy.command('/alert/text', 'GET', {}));
}
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} [buttonLabel] The name of the button to click in order to accept the alert.
 * If the name is not provided
 * then the script will try to detect the button automatically.
 * @returns {Promise<void>}
 */
async function mobileAcceptAlert(buttonLabel) {
    await this.uiautomator2.jwproxy.command('/alert/accept', 'POST', { buttonLabel });
}
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<void>}
 */
async function postAcceptAlert() {
    await this.mobileAcceptAlert();
}
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} [buttonLabel] The name of the button to click in order to dismiss the alert.
 * If the name is not provided
 * then the script will try to detect the button automatically.
 * @returns {Promise<void>}
 */
async function mobileDismissAlert(buttonLabel) {
    await this.uiautomator2.jwproxy.command('/alert/dismiss', 'POST', { buttonLabel });
}
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<void>}
 */
async function postDismissAlert() {
    await this.mobileDismissAlert();
}
/**
 * @typedef {import('../driver').AndroidUiautomator2Driver} AndroidUiautomator2Driver
 */
//# sourceMappingURL=alert.js.map