<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import { $ } from '@wdio/globals'`
    : `const { $ } = require('@wdio/globals')` %>
<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import Page from './page${answers.esmSupport ? '.js' : ''}';`
    : "const Page = require('./page');" %>

/**
 * sub page containing specific selectors and methods for a specific page
 */
class SecurePage extends Page {
    /**
     * define selectors using getter methods
     */
    <%- answers.isUsingTypeScript ? "public " : "" %>get flashAlert () {
        return $('#flash');
    }
}

<%- answers.isUsingTypeScript || answers.esmSupport ? "export default": "module.exports =" %> new SecurePage();
