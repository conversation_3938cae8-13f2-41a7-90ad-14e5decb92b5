!function(r){var e=function(r){if("string"!=typeof r)throw new Error("color has to be type of `string`");if("#"===r.substr(0,1))return{hex:r,alpha:1};var e=r.replace(/\s+/g,""),t=/(.*?)rgb(a)??\((\d{1,3}),(\d{1,3}),(\d{1,3})(,([01]|1.0*|0??\.([0-9]{0,})))??\)/.exec(e);if(!t)throw new Error("given color ("+r+") isn't a valid rgb or rgba color");var n=parseInt(t[3],10),o=parseInt(t[4],10),a=parseInt(t[5],10),i=t[6]?/([0-9\.]+)/.exec(t[6])[0]:"1",s=(a|o<<8|n<<16|1<<24).toString(16).slice(1);return"."===i.substr(0,1)&&(i=parseFloat("0"+i)),1<i&&(i=1),i=parseFloat(Math.round(100*i))/100,{hex:"#"+s.toString(16),alpha:i}};"function"==typeof define&&define.amd?define("rgb2hex",function(){return e}):r.rgb2hex=e}(window);
