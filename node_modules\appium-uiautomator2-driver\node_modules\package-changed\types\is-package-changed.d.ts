interface PackageChangedResult {
    hash: string;
    oldHash: string | undefined;
    writeHash(): void;
    isChanged: boolean;
}
interface PackageChangedOptions {
    hashFilename?: string;
    cwd?: string;
    lockfile?: boolean;
    noHashFile?: boolean;
}
interface PackageChangedCallback {
    (result: Omit<PackageChangedResult, 'writeHash'>): Promise<boolean | undefined>;
}
declare function isPackageChanged(options?: PackageChangedOptions): Promise<PackageChangedResult>;
declare function isPackageChanged(options?: PackageChangedOptions, callback?: PackageChangedCallback): Promise<PackageChangedResult | Omit<PackageChangedResult, 'writeHash'>>;
export default isPackageChanged;
