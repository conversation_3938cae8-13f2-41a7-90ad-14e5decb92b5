{"version": 3, "file": "insecure-features.js", "sourceRoot": "", "sources": ["../../lib/insecure-features.ts"], "names": [], "mappings": ";;;;;AAeA,0DAyBC;AASD,0DA6BC;AA9ED,oDAAuB;AACvB,sDAA8B;AAK9B,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAC9B,MAAM,sBAAsB,GAAG,GAAG,CAAC;AAEnC;;;;;GAKG;AACH,SAAgB,uBAAuB;IACrC,IAAI,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACrC,gBAAM,CAAC,IAAI,CACT,2DAA2D;YACzD,uDAAuD,CAC1D,CAAC;QACF,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;IACrC,CAAC;SAAM,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/D,MAAM,qBAAqB,GAAG,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzE,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACtC,gBAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACtD,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IACD,IAAI,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;QACtC,OAAO;IACT,CAAC;IACD,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7D,MAAM,oBAAoB,GAAG,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvE,IAAI,gBAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC;QACpC,OAAO;IACT,CAAC;IACD,gBAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;IACvD,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,uBAAuB,CAErC,MAAsB,EACtB,UAAkB;IAElB,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CACX,8EAA8E;YAC5E,qFAAqF,CACxF,CAAC;QACF,MAAM,CAAC,sBAAsB,GAAG,IAAI,CAAC;IACvC,CAAC;IACD,MAAM,qBAAqB,GAAG,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IACrF,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,CAAC;QACtC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yDAAyD;YACrE,kCAAkC,CACnC,CAAC;QACF,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,MAAM,CAAC,aAAa,GAAG,qBAAqB,CAAC;IAC/C,CAAC;IACD,MAAM,oBAAoB,GAAG,sBAAsB,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACnF,IAAI,gBAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC;QACpC,OAAO;IACT,CAAC;IACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,0DAA0D;QACtE,kCAAkC,CACnC,CAAC;IACF,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/D,MAAM,CAAC,YAAY,GAAG,oBAAoB,CAAC;AAC7C,CAAC;AAED;;;;GAIG;AACH,SAAS,gBAAgB,CAAC,QAAkB;IAC1C,MAAM,SAAS,GAAG,CAAC,QAAgB,EAAE,EAAE;QACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC9D,4DAA4D;QAC5D,mDAAmD;QACnD,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,GAAG,iBAAiB,GAAG,sBAAsB,GAAG,QAAQ,EAAE,CAAC;QACpE,CAAC;QAED,MAAM,CAAC,cAAc,EAAE,WAAW,CAAC,GAAG;YACpC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC;YACnC,QAAQ,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC;SACrC,CAAC;QACF,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CACb,iFAAiF;gBACjF,IAAI,iBAAiB,gEAAgE;gBACrF,2CAA2C,QAAQ,WAAW,CAC/D,CAAC;QACJ,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;IACF,OAAO,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACjC,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,sBAAsB,CAC7B,QAAkB,EAClB,aAAqB,iBAAiB;IAEtC,MAAM,QAAQ,GAAG,CAAC,QAAgB,EAAE,EAAE;QACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC9D,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QAC3D,OAAO,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAClE,CAAC,CAAC;IACF,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC"}