{"version": 3, "file": "plugin-command.d.ts", "sourceRoot": "", "sources": ["../../../lib/cli/plugin-command.js"], "names": [], "mappings": "AAMA;;GAEG;AACH;IACE;;;OAGG;IACH,8BAFW,OAAO,qBAAqB,EAAE,uBAAuB,CAAC,UAAU,CAAC,EAK3E;IAED;;;;;OAKG;IACH,8CAHW,iBAAiB,GACf,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAQ1C;IAED;;;;;OAKG;IACH,sBAHW,mBAAmB,GACjB,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAI1C;IAED;;;;;OAKG;IACH,2BAHW,gBAAgB,GACd,OAAO,CAAC,OAAO,qBAAqB,EAAE,qBAAqB,CAAC,CAIxE;IAED;;;;OAIG;IACH,uCAHW,gBAAgB,GACd,OAAO,CAAC,OAAO,qBAAqB,EAAE,SAAS,CAAC,CAS5D;IAED;;;;;;OAMG;IACH,mBAJW,mBAAmB,GACjB,OAAO,CAAC,MAAM,CAAC,CAO3B;CAkCF;4BAGY,OAAO,eAAe,EAAE,aAAa;yBACrC,OAAO,eAAe,EAAE,UAAU;sBAIlB,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,SAAS,CAAC,OAAO,CAAC;;;;;;;;YAMxC,MAAM;;;;iBACN,WAAW;;;;;;0BAKZ,OAAO,cAAc,EAAE,WAAW;;;;;;;;YAMjC,MAAM;;;;;;;;;YAMN,MAAM;;;;YACN,OAAO;;;;;;;;;YAMP,MAAM;;;;gBACN,MAAM;;;;;;;;;;;;;YAON,MAAM;;gCA/JY,qBAAqB"}