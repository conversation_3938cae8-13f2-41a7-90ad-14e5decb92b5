{"name": "stream-combiner", "version": "0.2.2", "homepage": "https://github.com/dominictarr/stream-combiner", "repository": {"type": "git", "url": "git://github.com/dominictarr/stream-combiner.git"}, "dependencies": {"duplexer": "~0.1.1", "through": "~2.3.4"}, "devDependencies": {"tape": "~2.3.0", "event-stream": "~3.0.7"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": "'<PERSON>' <<EMAIL>> (http://dominictarr.com)", "license": "MIT"}