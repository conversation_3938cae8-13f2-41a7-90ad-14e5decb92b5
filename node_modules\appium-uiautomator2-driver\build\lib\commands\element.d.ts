/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<import('@appium/types').Element>}
 */
export function active(this: import("../driver").AndroidUiautomator2Driver): Promise<import("@appium/types").Element>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} attribute
 * @param {string} elementId
 * @returns {Promise<string?>}
 */
export function getAttribute(this: import("../driver").AndroidUiautomator2Driver, attribute: string, elementId: string): Promise<string | null>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<boolean>}
 */
export function elementDisplayed(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<boolean>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<boolean>}
 */
export function elementEnabled(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<boolean>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<boolean>}
 */
export function elementSelected(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<boolean>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<string>}
 */
export function getName(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<string>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<import('@appium/types').Position>}
 */
export function getLocation(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<import("@appium/types").Position>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<import('@appium/types').Size>}
 */
export function getSize(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<import("@appium/types").Size>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {import('appium-android-driver').DoSetElementValueOpts} params
 * @returns {Promise<void>}
 */
export function doSetElementValue(this: import("../driver").AndroidUiautomator2Driver, params: import("appium-android-driver").DoSetElementValueOpts): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string|string[]} keys
 * @param {string} elementId
 * @returns {Promise<void>}
 */
export function setValueImmediate(this: import("../driver").AndroidUiautomator2Driver, keys: string | string[], elementId: string): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<string>}
 */
export function getText(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<string>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} element
 * @returns {Promise<void>}
 */
export function click(this: import("../driver").AndroidUiautomator2Driver, element: string): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} element
 * @returns {Promise<string>}
 */
export function getElementScreenshot(this: import("../driver").AndroidUiautomator2Driver, element: string): Promise<string>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<void>}
 */
export function clear(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId
 * @returns {Promise<import('@appium/types').Rect>}
 */
export function getElementRect(this: import("../driver").AndroidUiautomator2Driver, elementId: string): Promise<import("@appium/types").Rect>;
/**
 * Sends text to the given element by replacing its previous content
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId The id of the element whose content will be replaced.
 * @param {string} text The actual text to set.
 * @throws {Error} If there was a faulre while setting the text
 * @returns {Promise<void>}
 */
export function mobileReplaceElementValue(this: import("../driver").AndroidUiautomator2Driver, elementId: string, text: string): Promise<void>;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
//# sourceMappingURL=element.d.ts.map