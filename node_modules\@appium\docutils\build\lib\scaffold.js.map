{"version": 3, "file": "scaffold.js", "sourceRoot": "", "sources": ["../../lib/scaffold.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;AA+DH,gDAsFC;AAnJD,6CAAmC;AACnC,qCAAmC;AACnC,0DAA6B;AAC7B,+BAAiC;AAGjC,mCAAsC;AACtC,iCAAgC;AAChC,oDAAuB;AACvB,6BAAqE;AACrE,2CAA4C;AAE5C,MAAM,GAAG,GAAG,IAAA,kBAAS,EAAC,MAAM,CAAC,CAAC;AAC9B,MAAM,SAAS,GAAG,IAAA,kBAAS,EAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAE5C;;;;;;GAMG;AACH,SAAS,SAAS,CAChB,QAAgB,EAChB,OAAmB,EACnB,OAAmB,EACnB,aAAwC,kBAAa;IAErD,OAAO,IAAA,kBAAW,EAChB,QAAQ,EACR,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,EACnD,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CACpD,CAAC;AACJ,CAAC;AAmBD;;;;;;;;GAQG;AACH,SAAgB,kBAAkB,CAChC,eAAuB,EACvB,cAAiB,EACjB,WAAmB,EACnB,EACE,SAAS,GAAG,gBAAC,CAAC,QAAQ,EACtB,WAAW,GAAG,IAAI,CAAC,KAAK,EACxB,SAAS,GAAG,kBAAa,MACa,EAAE;IAE1C,OAAO,KAAK,EAAE,EACZ,SAAS,GAAG,KAAK,EACjB,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,EACnB,WAAW,EAAE,eAAe,EAC5B,IAAI,EACJ,MAAM,GAAG,KAAK,EACd,GAAG,IAAI,EACF,EAAkC,EAAE;QACzC,MAAM,YAAY,GAAG,IAAA,eAAQ,EAAC,GAAG,CAAC,CAAC;QACnC,MAAM,EAAC,OAAO,EAAE,GAAG,EAAC,GAAG,MAAM,IAAA,oBAAe,EAC1C,eAAe,CAAC,CAAC,CAAC,mBAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,EACrD,IAAI,CACL,CAAC;QACF,MAAM,MAAM,GAAG,mBAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,GAAG,IAAI,IAAI,mBAAI,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QACxC,GAAG,CAAC,KAAK,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;QAC3C,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,IAAI,WAAc,CAAC;QACnB,IAAI,MAA6B,CAAC;QAClC,IAAI,CAAC;YACH,WAAW,GAAG,WAAW,CAAC,MAAM,YAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;YAC3D,GAAG,CAAC,KAAK,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,GAAG,GAAG,CAA0B,CAAC;YACvC,IAAI,GAAG,CAAC,IAAI,KAAK,2BAAe,EAAE,CAAC;gBACjC,MAAM,GAAG,CAAC;YACZ,CAAC;YACD,iBAAiB,GAAG,IAAI,CAAC;YACzB,GAAG,CAAC,KAAK,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;YAChD,WAAW,GAAG,EAAO,CAAC;YACtB,KAAK,GAAG,IAAI,CAAC;QACf,CAAC;QAED,MAAM,QAAQ,GAAM,SAAS,CAAC,cAAc,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QACzD,MAAM,gBAAgB,GAAM,gBAAC,CAAC,YAAY,CAAC,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAEtE,iBAAiB,GAAG,iBAAiB,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;QAEnF,IAAI,iBAAiB,EAAE,CAAC;YACtB,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;YAC/C,GAAG,CAAC,KAAK,CAAC,iBAAiB,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;YACxD,GAAG,CAAC,KAAK,CAAC,cAAc,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;YAC1D,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;YAExE,IAAI,MAAM,EAAE,CAAC;gBACX,SAAS,CAAC,IAAI,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBACjE,MAAM,GAAG,EAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAC,CAAC;gBACjD,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,EAAE,CAAC;gBACzB,GAAG,CAAC,IAAI,CAAC,sEAAsE,EAAE,YAAY,CAAC,CAAC;gBAC/F,GAAG,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC;oBACH,MAAM,IAAA,oBAAe,EAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBAC9C,IAAI,KAAK,EAAE,CAAC;wBACV,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;oBAC7C,CAAC;yBAAM,CAAC;wBACN,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;oBACzC,CAAC;gBACH,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,MAAM,GAAG,GAAG,CAA0B,CAAC;oBACvC,MAAM,IAAI,qBAAa,CAAC,sBAAsB,YAAY,aAAa,GAAG,CAAC,OAAO,EAAE,EAAE;wBACpF,KAAK,EAAE,GAAG;qBACX,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,IAAI,CAAC,6BAA6B,EAAE,YAAY,CAAC,CAAC;QACxD,CAAC;QACD,GAAG,CAAC,OAAO,CAAC,GAAG,WAAW,QAAQ,CAAC,CAAC;QACpC,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAC,CAAC;IACjD,CAAC,CAAC;AACJ,CAAC"}