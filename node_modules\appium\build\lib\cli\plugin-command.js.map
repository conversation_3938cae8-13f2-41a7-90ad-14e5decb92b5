{"version": 3, "file": "plugin-command.js", "sourceRoot": "", "sources": ["../../../lib/cli/plugin-command.js"], "names": [], "mappings": ";;;;;AAAA,oDAAuB;AACvB,4EAAsD;AACtD,4CAA2C;AAE3C,MAAM,iBAAiB,GAAG,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AAEtD;;GAEG;AACH,MAAqB,gBAAiB,SAAQ,2BAAmB;IAC/D;;;OAGG;IACH,YAAY,EAAC,MAAM,EAAE,IAAI,EAAC;QACxB,KAAK,CAAC,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,yBAAa,CAAC;IACvC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,OAAO,CAAC,EAAC,MAAM,EAAE,WAAW,EAAE,WAAW,EAAC;QAC9C,OAAO,MAAM,KAAK,CAAC,QAAQ,CAAC;YAC1B,WAAW,EAAE,MAAM;YACnB,WAAW;YACX,WAAW;SACZ,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,SAAS,CAAC,EAAC,MAAM,EAAC;QACtB,OAAO,MAAM,KAAK,CAAC,UAAU,CAAC,EAAC,WAAW,EAAE,MAAM,EAAC,CAAC,CAAC;IACvD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAM,CAAC,EAAC,MAAM,EAAE,MAAM,EAAC;QAC3B,OAAO,MAAM,KAAK,CAAC,OAAO,CAAC,EAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,GAAG,CAAC,EAAC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAC;QACvC,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC;YACtB,WAAW,EAAE,MAAM;YACnB,UAAU;YACV,SAAS;YACT,YAAY,EAAE,IAAI,CAAC,YAAY;SAChC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,MAAM,CAAC,EAAC,MAAM,EAAC;QACnB,OAAO,MAAM,KAAK,CAAC,OAAO,CAAC;YACzB,WAAW,EAAE,MAAM;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,kBAAkB,CAAC,EAAC,OAAO,EAAE,OAAO,EAAC;QACnC,OAAO,UAAU,OAAO,IAAI,OAAO,CAAC,OAAO,yBAAyB,CAAC,KAAK,CAAC;IAC7E,CAAC;IAED;;;;;;;;;OASG;IACH,uBAAuB,CAAC,cAAc,EAAE,WAAW;QACjD,MAAM,aAAa,GAAG,iBAAiB,CAAC,MAAM,CAC5C,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC,EAC/D,EAAE,CACH,CAAC;QAEF,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CACb,qBAAqB,WAAW,oDAAoD;gBAClF,gCAAgC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAClE,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAvGD,mCAuGC;AAED;;;GAGG;AAEH;;;GAGG;AAEH;;;;;;GAMG;AAEH;;GAEG;AAEH;;;;GAIG;AAEH;;;;;GAKG;AAEH;;;;;;GAMG;AAEH;;;;GAIG"}