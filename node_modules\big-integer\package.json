{"name": "big-integer", "version": "1.6.52", "author": "<PERSON> <<EMAIL>>", "description": "An arbitrary length integer library for Javascript", "contributors": [], "bin": {}, "scripts": {"test": "tsc && karma start my.conf.js && node spec/tsDefinitions.js", "minify": "uglifyjs BigInteger.js -o BigInteger.min.js"}, "main": "./BigInteger", "repository": {"type": "git", "url": "**************:peterolson/BigInteger.js.git"}, "keywords": ["math", "big", "bignum", "bigint", "biginteger", "integer", "arbitrary", "precision", "arithmetic"], "devDependencies": {"@types/lodash": "^4.14.175", "@types/node": "^7.10.2", "coveralls": "^3.0.6", "jasmine": "3.5.0", "jasmine-core": "^3.5.0", "karma": "^6.3.4", "karma-cli": "^2.0.0", "karma-coverage": "^2.0.3", "karma-jasmine": "^4.0.1", "karma-phantomjs-launcher": "^1.0.4", "lodash": "^4.17.21", "typescript": "^5.3.2", "uglify-js": "^3.17.4"}, "license": "Unlicense", "engines": {"node": ">=0.6"}, "typings": "./BigInteger.d.ts"}