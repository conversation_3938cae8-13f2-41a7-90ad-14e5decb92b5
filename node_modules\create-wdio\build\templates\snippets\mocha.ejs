<%  if (answers.framework === 'mocha') { %>
    // Options to be passed to Mocha.
    // See the full list at http://mochajs.org/
    mochaOpts: {
        ui: 'bdd',
        timeout: 60000
    },
<% } else if (answers.serenityAdapter === 'mocha') { %>
    // Mocha configuration, see:
    //  https://serenity-js.org/api/mocha-adapter/interface/MochaConfig/
    mochaOpts: {
        timeout: 60000
    },
<% } %>