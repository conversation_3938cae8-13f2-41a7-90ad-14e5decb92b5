{"version": 3, "file": "element.js", "sourceRoot": "", "sources": ["../../../lib/commands/element.js"], "names": [], "mappings": ";;;;;AAQA,wBAOC;AAQD,oCAQC;AAOD,4CAEC;AAOD,wCAEC;AAOD,0CAEC;AAOD,0BAQC;AAOD,kCAQC;AAOD,0BAQC;AAOD,8CAMC;AAQD,8CAUC;AAOD,0BAQC;AAOD,sBAMC;AAOD,oDAQC;AAOD,sBAQC;AAOD,wCA0BC;AAUD,8DASC;AApPD,wDAAyB;AACzB,oDAAuB;AACvB,0CAAwC;AAExC;;;GAGG;AACI,KAAK,UAAU,MAAM;IAC1B,OAAO,8CAA8C,CAAC,CACpD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,iBAAiB,EACjB,KAAK,CACN,CACF,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,YAAY,CAAC,SAAS,EAAE,SAAS;IACrD,OAAO,MAAM,CACX,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,cAAc,SAAS,EAAE,EAC9C,KAAK,EACL,EAAE,CACH,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,gBAAgB,CAAC,SAAS;IAC9C,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;AACjE,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,cAAc,CAAC,SAAS;IAC5C,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,eAAe,CAAC,SAAS;IAC7C,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;AAChE,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,OAAO,CAAC,SAAS;IACrC,OAAO,qBAAqB,CAAC,CAC3B,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,OAAO,EAC5B,KAAK,EACL,EAAE,CACH,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,WAAW,CAAC,SAAS;IACzC,OAAO,+CAA+C,CAAC,CACrD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,WAAW,EAChC,KAAK,EACL,EAAE,CACH,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,OAAO,CAAC,SAAS;IACrC,OAAO,2CAA2C,CAAC,CACjD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,OAAO,EAC5B,KAAK,EACL,EAAE,CACH,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,iBAAiB,CAAC,MAAM;IAC5C,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,MAAM,CAAC,SAAS,QAAQ,EACpC,MAAM,EACN,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,iBAAiB,CAAC,IAAI,EAAE,SAAS;IACrD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,QAAQ,EAC7B,MAAM,EACN;QACE,SAAS;QACT,IAAI,EAAE,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI;QAC5C,OAAO,EAAE,KAAK;KACf,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,OAAO,CAAC,SAAS;IACrC,OAAO,MAAM,CACX,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,OAAO,EAC5B,KAAK,EACL,EAAE,CACH,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,KAAK,CAAC,OAAO;IACjC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,OAAO,QAAQ,EAC3B,MAAM,EACN,EAAC,OAAO,EAAC,CACV,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,oBAAoB,CAAC,OAAO;IAChD,OAAO,MAAM,CACX,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,OAAO,aAAa,EAChC,KAAK,EACL,EAAE,CACH,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,KAAK,CAAC,SAAS;IACnC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,QAAQ,EAC7B,MAAM,EACN;QACE,SAAS;KACV,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,cAAc,CAAC,SAAS;IAC5C,MAAM,YAAY,GAAG,oDAAoD,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC9F,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,8CAA8C,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,CACxF,CAAC;QACF,IAAI,YAAY,CAAC,OAAO,CAAC,kBAAkB,KAAK,kBAAS,CAAC,OAAO,EAAE,CAAC;YAClE,MAAM,CAAC,EAAC,CAAC,EAAE,CAAC,EAAC,EAAE,EAAC,KAAK,EAAE,MAAM,EAAC,CAAC;YAC7B,+EAA+E,CAAC,CAC9E,MAAM,kBAAC,CAAC,GAAG,CAAC;gBACV,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,SAAS,WAAW,EAAE,KAAK,CAAC;gBACrE,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,SAAS,OAAO,EAAE,KAAK,CAAC;aAClE,CAAC,CACH,CAAC;YACJ,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAC,CAAC;QAC/B,CAAC;QACD,OAAO,2CAA2C,CAAC,CACjD,MAAM,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,SAAS,OAAO,EAAE,KAAK,CAAC,CACxE,CAAC;IACJ,CAAC;IACD,OAAO,2CAA2C,CAAC,CACjD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,OAAO,EAC5B,KAAK,CACN,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,yBAAyB,CAAC,SAAS,EAAE,IAAI;IAC7D,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CACrC,YAAY,SAAS,QAAQ,EAC7B,MAAM,EACN;QACE,IAAI;QACJ,OAAO,EAAE,IAAI;KACd,CACF,CAAC;AACJ,CAAC;AAED,2BAA2B;AAE3B;;;GAGG;AACH,SAAS,MAAM,CAAC,CAAC;IACf,OAAO,gBAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,aAAa;AAEb;;GAEG"}