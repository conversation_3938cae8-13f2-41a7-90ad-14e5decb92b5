{"version": 3, "file": "protocol.js", "sourceRoot": "", "sources": ["../../../lib/protocol/protocol.js"], "names": [], "mappings": ";;;;;;AAkGA,kCA2DC;AASD,4BAgDC;AAyVC,4DAAwB;AACxB,4CAAgB;AAChB,wDAAsB;AACtB,8CAAiB;AAEjB,kEAA2B;AApjB7B,oDAAuB;AACvB,6CAA6C;AAC7C,6CAAwC;AACxC,qCAMkB;AAClB,qCAA4D;AAC5D,wDAAyB;AACzB,uCAA4D;AAC5D,4CAA+E;AAC/E,0DAAkD;AAClD,kEAAuC;AACvC,mDAAgE;AAEnD,QAAA,sBAAsB,GAAG,eAAe,CAAC;AACzC,QAAA,sBAAsB,GAAG,eAAe,CAAC;AACzC,QAAA,kBAAkB,GAAG,WAAW,CAAC;AACjC,QAAA,4BAA4B,GAAG,cAAc,CAAC;AAC9C,QAAA,8BAA8B,GAAG,gBAAgB,CAAC;AAE/D,0BAA0B;AAC1B,MAAM,wBAAwB,GAAG,IAAI,GAAG,EAAE,CAAC;AA0hBzC,4DAAwB;AAxhB1B,SAAS,iBAAiB,CAAC,iBAAiB;IAC1C,OAAO,gBAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE,wBAAS,CAAC,CAAC,CAAC,CAAC,qBAAS,CAAC,GAAG,CAAC,CAAC,CAAC,qBAAS,CAAC,OAAO,CAAC;AAClF,CAAC;AAED,SAAS,eAAe,CAAC,MAAM,EAAE,SAAS,GAAG,IAAI;IAC/C,MAAM,SAAS,GAAG,gBAAC,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACrD,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;QACpC,CAAC,CAAC,MAAM,CAAC;IACX,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;QACzB,gEAAgE;QAChE,iEAAiE;QACjE,+BAA+B;QAC/B,OAAO,MAAM,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,uFAAuF;IACvF,OAAO,SAAS,EAAE,QAAQ,IAAI,qBAAS,CAAC,GAAG,CAAC;AAC9C,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAO;IAC/B,OAAO,CAAC,gBAAC,CAAC,QAAQ,CAAC,+BAAsB,EAAE,OAAO,CAAC,CAAC;AACtD,CAAC;AAED;;;;;GAKG;AACH,SAAS,SAAS,CAAC,MAAM,EAAE,SAAS,GAAG,IAAI;IACzC,MAAM,SAAS,GACb,SAAS,IAAI,gBAAC,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAChD,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,MAAM;QAC9C,CAAC,CAAC,MAAM,CAAC;IACb,IAAI,gBAAC,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC;QACtC,OAAO,SAAS,CAAC,GAAG,CAAC;IACvB,CAAC;IAED,MAAM,SAAS,GAAG,IAAA,iCAAuB,EAAC,SAAS,CAAC,CAAC;IACrD,OAAO,gBAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO;IACpC;;;;;OAKG;IACH,IAAI,GAAG,GAAG,OAAO,CAAC;IAClB,IAAI,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC/C,GAAG,GAAG,EAAE,CAAC;QACT,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;IAChC,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO;IACtC;;OAEG;IACH,IAAI,GAAG,GAAG,OAAO,CAAC;IAClB,IAAI,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QACxB,sCAAsC;QACtC,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAgB,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ;IACtD,IAAI,cAAc,GAAG,EAAE,CAAC;IACxB,IAAI,cAAc,GAAG,EAAE,CAAC;IACxB,IAAI,cAAc,GAAG,gBAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAErC,IAAI,SAAS,EAAE,CAAC;QACd,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvB,wCAAwC;YACxC,sDAAsD;YACtD,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,gBAAC,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAC5C,cAAc,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC;YACtC,CAAC;QACH,CAAC;QACD,wCAAwC;QACxC,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvB,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC;QACtC,CAAC;QAED,gFAAgF;QAChF,4EAA4E;QAC5E,4EAA4E;QAC5E,mDAAmD;QACnD,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvB,IAAI,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACpD,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,IAAI,eAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;IACH,CAAC;IAED,iDAAiD;IACjD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAChC,OAAO;IACT,CAAC;IAED,oDAAoD;IACpD,IAAI,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC/C,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED,mDAAmD;IACnD,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QACxC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,qEAAqE;IACrE,KAAK,IAAI,MAAM,IAAI,cAAc,EAAE,CAAC;QAClC,IACE,gBAAC,CAAC,UAAU,CAAC,cAAc,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC,MAAM,KAAK,CAAC;YACjE,gBAAC,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,MAAM,KAAK,CAAC,EACjD,CAAC;YACD,8CAA8C;YAC9C,mBAAmB;YACnB,OAAO;QACT,CAAC;IACH,CAAC;IACD,MAAM,IAAI,eAAM,CAAC,kBAAkB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AACjE,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,aAAa,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ;IACtE,wEAAwE;IACxE,uEAAuE;IACvE,0EAA0E;IAC1E,oEAAoE;IACpE,IAAI,SAAS,GAAG,gBAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,CAAC;IAEhD,mEAAmE;IACnE,uEAAuE;IACvE,yEAAyE;IACzE,OAAO;IACP,IAAI,cAAc,GAAG,aAAa,CAAC,QAAQ,CAAC;IAC5C,IAAI,gBAAC,CAAC,OAAO,CAAC,gBAAC,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;QAC/C,sEAAsE;QACtE,0EAA0E;QAC1E,wEAAwE;QACxE,uEAAuE;QACvE,IAAI,IAAI,GAAG,gBAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3B,KAAK,IAAI,MAAM,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC1C,IAAI,gBAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5C,cAAc,GAAG,MAAM,CAAC;gBACxB,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,6EAA6E;IAC7E,IAAI,IAAI,CAAC;IACT,IAAI,gBAAC,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;QACzC,2EAA2E;QAC3E,yEAAyE;QACzE,uEAAuE;QACvE,2EAA2E;QAC3E,2EAA2E;QAC3E,gDAAgD;QAChD,IAAI,GAAG,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;SAAM,CAAC;QACN,2EAA2E;QAC3E,yBAAyB;QACzB,IAAI,GAAG,gBAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IACD,6EAA6E;IAC7E,WAAW;IACX,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,2BAA2B,CAAC,MAAM,EAAE,SAAS;IACpD,yFAAyF;IACzF,2FAA2F;IAC3F,8DAA8D;IAC9D,IAAI,CAAC,MAAM,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvD,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,mFAAmF;YACjF,qDAAqD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAChF,CAAC;IACJ,CAAC;IACD,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3B,IAAI,CAAC,gBAAC,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,eAAM,CAAC,oBAAoB,CACnC,kFAAkF;YAChF,qCAAqC,CACxC,CAAC;IACJ,CAAC;IACD,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,SAAS,GAAG,EAAC,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAC,CAAC;IAC3C,CAAC;SAAM,CAAC;QACN,SAAS,CAAC,QAAQ,KAAlB,SAAS,CAAC,QAAQ,GAAK,EAAE,EAAC;QAC1B,SAAS,CAAC,QAAQ,KAAlB,SAAS,CAAC,QAAQ,GAAK,EAAE,EAAC;QAC1B,MAAM,YAAY,GAAG,gBAAC,CAAC,UAAU,CAAC,gBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxF,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7B,gBAAG,CAAC,IAAI,CAAC,qEAAqE,YAAY,EAAE,CAAC,CAAC;YAC9F,IAAI,GAAG,gBAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC;QACD,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IACD,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACxD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;GAIG;AACH,SAAS,wBAAwB,CAAC,MAAM;IACtC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,CAAC,EAAC,kBAAmB,CAAC,MAAM,CAAC,CAAC,cAAc,IAAI,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACzF,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACjF,CAAC;IAED,4FAA4F;IAC5F,sFAAsF;IACtF,OAAO,SAAS,SAAS,CAAC,GAAG,EAAE,EAAC,QAAQ,GAAG,6BAAiB,EAAE,cAAc,GAAG,EAAE,EAAC,GAAG,EAAE;QACrF,sEAAsE;QACtE,6CAA6C;QAC7C,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAE3B,MAAM,UAAU,GAAG,EAAC,GAAG,mBAAU,EAAE,GAAG,cAAc,EAAC,CAAC;QAEtD,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACpD,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChD,mCAAmC;gBACnC,YAAY,CACV,GAAG,EACH,MAAM,EACN,GAAG,QAAQ,GAAG,IAAI,EAAE,EACpB,IAAI,EACJ,MAAM,EACN,gBAAgB,CAAC,sDAAsD,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CACxF,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS;IAC9D,IAAI,YAAY,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;QACpC,IAAI,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;QACvB,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,UAAU,GAAG,GAAG,CAAC;QACrB,IAAI,YAAY,CAAC;QACjB,IAAI,eAAe,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEpE,IAAI,CAAC;YACH,qDAAqD;YACrD,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnE,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3C,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAC1C,OAAO,MAAM,IAAI,IAAI,gEAAgE;oBACnF,qFAAqF;oBACrF,yDAAyD,CAC5D,CAAC;YACJ,CAAC;YAED,4DAA4D;YAC5D,+CAA+C;YAC/C,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,eAAM,CAAC,iBAAiB,EAAE,CAAC;YACvC,CAAC;YAED,wFAAwF;YACxF,oFAAoF;YACpF,6FAA6F;YAC7F,kFAAkF;YAClF,4FAA4F;YAC5F,sFAAsF;YACtF,6FAA6F;YAC7F,UAAU;YACV,IAAI,sBAAsB,GAAG,KAAK,CAAC;YACnC,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,sBAAsB,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvF,IACE,CAAC,MAAM,CAAC,kBAAkB;oBAC1B,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAC1E,CAAC;oBACD,MAAM,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBACnC,OAAO;gBACT,CAAC;gBACD,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,CAC3C,qBAAqB;oBACnB,mFAAmF;oBACnF,iEAAiE,CACpE,CAAC;gBACF,sBAAsB,GAAG,IAAI,CAAC;YAChC,CAAC;YAED,yDAAyD;YACzD,qCAAqC;YACrC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClB,MAAM,IAAI,eAAM,CAAC,mBAAmB,EAAE,CAAC;YACzC,CAAC;YAED,2BAA2B;YAC3B,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBAClD,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YACpD,CAAC;YAED,6BAA6B;YAC7B,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBACpD,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,KAAK,8BAAsB,EAAE,CAAC;gBAC5C,wEAAwE;gBACxE,yDAAyD;gBACzD,eAAe,GAAG,iBAAiB,CACjC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,CACxD,CAAC;YACJ,CAAC;YAED,oDAAoD;YACpD,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;YAE1D,8DAA8D;YAC9D,qBAAqB;YACrB,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE,EAAE,eAAe,CAAC,CAAC;YACpF,IAAI,SAAS,CAAC;YACd,6CAA6C;YAC7C,IAAI,uBAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7B,uBAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACpC,CAAC;YAED,gEAAgE;YAChE,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,CAC3C,UAAU;gBACR,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,gBAAgB;gBAC1D,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAC,MAAM,EAAE,+BAAmB,EAAC,CAAC,CAClE,CAAC;YAEF,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,0FAA0F;gBAC1F,0FAA0F;gBAC1F,2EAA2E;gBAC3E,IAAI,CAAC,IAAI,CAAC,EAAC,WAAW,EAAE,GAAG,EAAC,CAAC,CAAC;YAChC,CAAC;YAED,SAAS,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YAE/D,wCAAwC;YACxC,eAAe,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,eAAe,CAAC;YAEnF,sEAAsE;YACtE,mEAAmE;YACnE,IAAI,gBAAC,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,gBAAC,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,CAAC;gBAC/D,eAAe,GAAG,SAAS,CAAC,QAAQ,IAAI,eAAe,CAAC;gBACxD,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;oBACpB,MAAM,SAAS,CAAC,KAAK,CAAC;gBACxB,CAAC;gBACD,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;YAC9B,CAAC;YAED,gCAAgC;YAChC,IAAI,IAAI,CAAC,OAAO,KAAK,8BAAsB,EAAE,CAAC;gBAC5C,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC5B,SAAS,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,KAAK,CACnC,8BAA8B,eAAe,yBAAyB,YAAY,EAAE,CACrF,CAAC;gBACF,IAAI,eAAe,KAAK,qBAAS,CAAC,OAAO,EAAE,CAAC;oBAC1C,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC;qBAAM,IAAI,eAAe,KAAK,qBAAS,CAAC,GAAG,EAAE,CAAC;oBAC7C,SAAS,GAAG;wBACV,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;qBAC3B,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,SAAS,GAAG,IAAA,6BAAmB,EAAC,SAAS,CAAC,CAAC;YAE3C,uDAAuD;YACvD,IAAI,IAAI,CAAC,OAAO,KAAK,8BAAsB,EAAE,CAAC;gBAC5C,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,CAC3C,sBAAsB,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;oBAC1D,MAAM,EAAE,+BAAmB;iBAC5B,CAAC,EAAE,CACL,CAAC;gBACF,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;gBACxF,SAAS,GAAG,IAAI,CAAC;YACnB,CAAC;YAED,wEAAwE;YACxE,IAAI,cAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7B,IACE,cAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC/B,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;oBACxB,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,EACpC,CAAC;oBACD,MAAM,IAAA,mCAA0B,EAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtE,CAAC;qBAAM,IAAI,gBAAC,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;oBACrE,MAAM,IAAA,6BAAoB,EACxB,SAAS,CAAC,KAAK,CAAC,KAAK,EACrB,SAAS,CAAC,KAAK,CAAC,OAAO,EACvB,SAAS,CAAC,KAAK,CAAC,UAAU,CAC3B,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;YAC9B,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,YAAY,CAAC,CAAC,KAAK,CAC3D,aAAa;gBACX,yBAAyB,IAAI,CAAC,OAAO,cAAc,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;oBACvF,MAAM,EAAE,+BAAmB;iBAC5B,CAAC,EAAE,CACP,CAAC;QACJ,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,iEAAiE;YACjE,iDAAiD;YACjD,IAAI,SAAS,CAAC;YACd,IAAI,GAAG,YAAY,KAAK,IAAI,CAAC,gBAAC,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,gBAAC,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;gBAC3E,SAAS,GAAG,GAAG,CAAC;YAClB,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,YAAY,CAAC,CAAC,IAAI,CAC1D,wFAAwF;oBACtF,iDAAiD,CACpD,CAAC;gBACF,SAAS,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,eAAe;gBACb,eAAe,IAAI,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,YAAY,CAAC,CAAC;YAEnF,IAAI,MAAM,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,KAAK,CAAC;YACzC,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,qEAAqE;gBACrE,uCAAuC;gBACvC,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAC1D,CAAC;YACD,IAAI,IAAA,oBAAW,EAAC,GAAG,EAAE,eAAM,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC/C,SAAS,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,YAAY,CAAC,CAAC,KAAK,CAC3D,+CAA+C,MAAM,EAAE,CACxD,CAAC;YACJ,CAAC;YAED,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,IAAA,+BAAsB,EAAC,SAAS,CAAC,CAAC;QAChE,CAAC;QAED,wDAAwD;QACxD,IAAI,gBAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5B,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,eAAe,KAAK,qBAAS,CAAC,GAAG,EAAE,CAAC;oBACtC,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACN,WAAW,CAAC,SAAS,GAAG,YAAY,CAAC;gBACvC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,WAAW,CAAC,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC;YACvD,CAAC;YACD,2CAA2C;YAC3C,IAAI,eAAe,KAAK,qBAAS,CAAC,GAAG,EAAE,CAAC;gBACtC,OAAO,WAAW,CAAC,SAAS,CAAC;YAC/B,CAAC;YAED,WAAW,GAAG,IAAA,sBAAY,EAAC,WAAW,CAAC,CAAC;YACxC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC,CAAC;IACF,4BAA4B;IAC5B,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC3C,kBAAC,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3C,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO;IAClD,0DAA0D;IAC1D,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;QAC9C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,6EAA6E;IAC7E,2CAA2C;IAC3C,IAAI,OAAO,KAAK,8BAAsB,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,2EAA2E;IAC3E,6BAA6B;IAC7B,IAAI,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5F,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG;IACxC,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAC1C,wDAAwD,CACzD,CAAC;IAEF,mDAAmD;IACnD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;QAC3C,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;IACnF,CAAC;IACD,IAAI,CAAC;QACH,MAAM,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAC7E,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,IAAA,oBAAW,EAAC,GAAG,EAAE,eAAM,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC/C,MAAM,GAAG,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,iCAAiC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;AACH,CAAC"}