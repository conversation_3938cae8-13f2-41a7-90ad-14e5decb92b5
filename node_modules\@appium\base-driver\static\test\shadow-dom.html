<!DOCTYPE html>
<html>
<head>
  <title>I am a page title with shadow content</title>
</head>
<body>

<h1>This page is a Shadow DOM sandbox</h1>

<appium-form></appium-form>

<script>
customElements.define('appium-form', class extends HTMLElement {
  connectedCallback () {
    this.attachShadow({mode: 'open'});
    // add html with style, which _will_ cause a circular reference when parsed
    // by Selenium
    this.shadowRoot.innerHTML = `<p>
      <style>
        :host {
          display: block;
        }
      </style>
      <button id="shadowButton">Click me</button>
    </p>`;
    this.shadowRoot.firstElementChild.onclick = (e) => alert(`Inner target: ${e.target.tagName}`);
  }
});

document.onclick = (e) => alert(`Outer target: ${e.target.tagName}`);
</script>

</body>
</html>
