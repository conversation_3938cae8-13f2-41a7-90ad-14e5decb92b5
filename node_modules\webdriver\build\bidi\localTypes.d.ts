/**
 * !!! PLEASE DO NOT EDIT THIS FILE !!!
 *
 * This source file, even though checked in, is auto-generated based on the
 * current development within the WebDriver Bidi spec. Any changes to this
 * file need to come from the specification. You can generate this file by calling
 *
 * ```
 * $ npm run generate:bidi
 * ```
 *
 * from the project root. You can find the scripts that generates this file in
 * ./scripts/bidi/**
 */
export type Message = CommandResponse | ErrorResponse | Event;
export interface CommandResponse extends Extensible {
    type: 'success';
    id: JsUint;
    result: ResultData;
}
export interface ErrorResponse extends Extensible {
    type: 'error';
    id: JsUint | null;
    error: ErrorCode;
    message: string;
    stacktrace?: string;
}
export type ResultData = BrowsingContextResult | EmptyResult | NetworkResult | ScriptResult | SessionResult | StorageResult | WebExtensionResult;
export interface EmptyResult extends Extensible {
}
export interface Event extends Extensible {
    type: 'event';
}
export type Extensible = Record<string, unknown>;
export type EventData = BrowsingContextEvent | InputEvent | LogEvent | NetworkEvent | ScriptEvent;
export type JsInt = number;
export type JsUint = number;
export type ErrorCode = 'invalid argument' | 'invalid selector' | 'invalid session id' | 'invalid web extension' | 'move target out of bounds' | 'no such alert' | 'no such element' | 'no such frame' | 'no such handle' | 'no such history entry' | 'no such intercept' | 'no such node' | 'no such request' | 'no such script' | 'no such storage partition' | 'no such user context' | 'no such web extension' | 'session not created' | 'unable to capture screen' | 'unable to close browser' | 'unable to set cookie' | 'unable to set file input' | 'underspecified storage partition' | 'unknown command' | 'unknown error' | 'unsupported operation';
export type SessionResult = SessionNewResult | SessionStatusResult | SessionSubscribeResult;
export interface SessionCapabilitiesRequest {
    alwaysMatch?: SessionCapabilityRequest;
    firstMatch?: SessionCapabilityRequest[];
}
export interface SessionCapabilityRequest extends Extensible {
    acceptInsecureCerts?: boolean;
    browserName?: string;
    browserVersion?: string;
    platformName?: string;
    proxy?: SessionProxyConfiguration;
    unhandledPromptBehavior?: SessionUserPromptHandler;
}
export type SessionProxyConfiguration = SessionAutodetectProxyConfiguration | SessionDirectProxyConfiguration | SessionManualProxyConfiguration | SessionPacProxyConfiguration | SessionSystemProxyConfiguration;
export interface SessionAutodetectProxyConfiguration extends Extensible {
    proxyType: 'autodetect';
}
export interface SessionDirectProxyConfiguration extends Extensible {
    proxyType: 'direct';
}
export interface SessionManualProxyConfiguration extends SessionSocksProxyConfiguration, Extensible {
    proxyType: 'manual';
    ftpProxy?: string;
    httpProxy?: string;
    sslProxy?: string;
    noProxy?: string[];
}
export interface SessionSocksProxyConfiguration {
    socksProxy: string;
    socksVersion: number;
}
export interface SessionPacProxyConfiguration extends Extensible {
    proxyType: 'pac';
    proxyAutoconfigUrl: string;
}
export interface SessionSystemProxyConfiguration extends Extensible {
    proxyType: 'system';
}
export interface SessionUserPromptHandler {
    alert?: SessionUserPromptHandlerType;
    beforeUnload?: SessionUserPromptHandlerType;
    confirm?: SessionUserPromptHandlerType;
    default?: SessionUserPromptHandlerType;
    file?: SessionUserPromptHandlerType;
    prompt?: SessionUserPromptHandlerType;
}
export type SessionUserPromptHandlerType = 'accept' | 'dismiss' | 'ignore';
export type SessionSubscription = string;
export interface SessionStatusResult {
    ready: boolean;
    message: string;
}
export interface SessionNewResult {
    sessionId: string;
    capabilities: {
        acceptInsecureCerts: boolean;
        browserName: string;
        browserVersion: string;
        platformName: string;
        setWindowRect: boolean;
        userAgent: string;
        proxy?: SessionProxyConfiguration;
        unhandledPromptBehavior?: SessionUserPromptHandler;
        webSocketUrl?: string;
    };
}
export interface SessionSubscribeResult {
    subscription: SessionSubscription;
}
export type BrowserResult = BrowserCreateUserContextResult | BrowserGetUserContextsResult;
export type BrowserClientWindow = string;
export interface BrowserClientWindowInfo {
    active: boolean;
    clientWindow: BrowserClientWindow;
    height: JsUint;
    state: 'fullscreen' | 'maximized' | 'minimized' | 'normal';
    width: JsUint;
    x: JsInt;
    y: JsInt;
}
export type BrowserUserContext = string;
export interface BrowserUserContextInfo {
    userContext: BrowserUserContext;
}
export type BrowserCreateUserContextResult = BrowserUserContextInfo;
export interface BrowserGetClientWindowsResult {
    clientWindows: BrowserClientWindowInfo[];
}
export interface BrowserGetUserContextsResult {
    userContexts: BrowserUserContextInfo[];
}
export type BrowsingContextResult = BrowsingContextCaptureScreenshotResult | BrowsingContextCreateResult | BrowsingContextGetTreeResult | BrowsingContextLocateNodesResult | BrowsingContextNavigateResult | BrowsingContextPrintResult | BrowsingContextTraverseHistoryResult;
export type BrowsingContextEvent = BrowsingContextContextCreated | BrowsingContextContextDestroyed | BrowsingContextDomContentLoaded | BrowsingContextDownloadWillBegin | BrowsingContextFragmentNavigated | BrowsingContextHistoryUpdated | BrowsingContextLoad | BrowsingContextNavigationAborted | BrowsingContextNavigationCommitted | BrowsingContextNavigationFailed | BrowsingContextNavigationStarted | BrowsingContextUserPromptClosed | BrowsingContextUserPromptOpened;
export type BrowsingContextBrowsingContext = string;
export type BrowsingContextInfoList = (BrowsingContextInfo)[];
export interface BrowsingContextInfo {
    children: BrowsingContextInfoList | null;
    clientWindow: BrowserClientWindow;
    context: BrowsingContextBrowsingContext;
    originalOpener: BrowsingContextBrowsingContext | null;
    url: string;
    userContext: BrowserUserContext;
    parent?: BrowsingContextBrowsingContext | null;
}
export type BrowsingContextLocator = BrowsingContextAccessibilityLocator | BrowsingContextCssLocator | BrowsingContextContextLocator | BrowsingContextInnerTextLocator | BrowsingContextXPathLocator;
export interface BrowsingContextAccessibilityLocator {
    type: 'accessibility';
    value: {
        name?: string;
        role?: string;
    };
}
export interface BrowsingContextCssLocator {
    type: 'css';
    value: string;
}
export interface BrowsingContextContextLocator {
    type: 'context';
    value: {
        context: BrowsingContextBrowsingContext;
    };
}
export interface BrowsingContextInnerTextLocator {
    type: 'innerText';
    value: string;
    ignoreCase?: boolean;
    matchType?: 'full' | 'partial';
    maxDepth?: JsUint;
}
export interface BrowsingContextXPathLocator {
    type: 'xpath';
    value: string;
}
export type BrowsingContextNavigation = string;
export interface BrowsingContextBaseNavigationInfo {
    context: BrowsingContextBrowsingContext;
    navigation: BrowsingContextNavigation | null;
    timestamp: JsUint;
    url: string;
}
export interface BrowsingContextNavigationInfo extends BrowsingContextBaseNavigationInfo {
}
export type BrowsingContextUserPromptType = 'alert' | 'beforeunload' | 'confirm' | 'prompt';
export interface BrowsingContextCaptureScreenshotResult {
    data: string;
}
export interface BrowsingContextCreateResult {
    context: BrowsingContextBrowsingContext;
}
export interface BrowsingContextGetTreeResult {
    contexts: BrowsingContextInfoList;
}
export interface BrowsingContextLocateNodesResult {
    nodes: ScriptNodeRemoteValue[];
}
export interface BrowsingContextNavigateResult {
    navigation: BrowsingContextNavigation | null;
    url: string;
}
export interface BrowsingContextPrintResult {
    data: string;
}
export interface BrowsingContextTraverseHistoryResult {
}
export interface BrowsingContextContextCreated {
    method: 'browsingContext.contextCreated';
    params: BrowsingContextInfo;
}
export interface BrowsingContextContextDestroyed {
    method: 'browsingContext.contextDestroyed';
    params: BrowsingContextInfo;
}
export interface BrowsingContextNavigationStarted {
    method: 'browsingContext.navigationStarted';
    params: BrowsingContextNavigationInfo;
}
export interface BrowsingContextFragmentNavigated {
    method: 'browsingContext.fragmentNavigated';
    params: BrowsingContextNavigationInfo;
}
export interface BrowsingContextHistoryUpdated {
    method: 'browsingContext.historyUpdated';
    params: BrowsingContextHistoryUpdatedParameters;
}
export interface BrowsingContextHistoryUpdatedParameters {
    context: BrowsingContextBrowsingContext;
    url: string;
}
export interface BrowsingContextDomContentLoaded {
    method: 'browsingContext.domContentLoaded';
    params: BrowsingContextNavigationInfo;
}
export interface BrowsingContextLoad {
    method: 'browsingContext.load';
    params: BrowsingContextNavigationInfo;
}
export interface BrowsingContextDownloadWillBegin {
    method: 'browsingContext.downloadWillBegin';
    params: BrowsingContextDownloadWillBeginParams;
}
export interface BrowsingContextDownloadWillBeginParams extends BrowsingContextBaseNavigationInfo {
    suggestedFilename: string;
}
export interface BrowsingContextNavigationAborted {
    method: 'browsingContext.navigationAborted';
    params: BrowsingContextNavigationInfo;
}
export interface BrowsingContextNavigationCommitted {
    method: 'browsingContext.navigationCommitted';
    params: BrowsingContextNavigationInfo;
}
export interface BrowsingContextNavigationFailed {
    method: 'browsingContext.navigationFailed';
    params: BrowsingContextNavigationInfo;
}
export interface BrowsingContextUserPromptClosed {
    method: 'browsingContext.userPromptClosed';
    params: BrowsingContextUserPromptClosedParameters;
}
export interface BrowsingContextUserPromptClosedParameters {
    context: BrowsingContextBrowsingContext;
    accepted: boolean;
    type: BrowsingContextUserPromptType;
    userText?: string;
}
export interface BrowsingContextUserPromptOpened {
    method: 'browsingContext.userPromptOpened';
    params: BrowsingContextUserPromptOpenedParameters;
}
export interface BrowsingContextUserPromptOpenedParameters {
    context: BrowsingContextBrowsingContext;
    handler: SessionUserPromptHandlerType;
    message: string;
    type: BrowsingContextUserPromptType;
    defaultValue?: string;
}
export interface NetworkResult extends NetworkAddInterceptResult {
}
export type NetworkEvent = NetworkAuthRequired | NetworkBeforeRequestSent | NetworkFetchError | NetworkResponseCompleted | NetworkResponseStarted;
export interface NetworkAuthChallenge {
    scheme: string;
    realm: string;
}
export interface NetworkBaseParameters {
    context: BrowsingContextBrowsingContext | null;
    isBlocked: boolean;
    navigation: BrowsingContextNavigation | null;
    redirectCount: JsUint;
    request: NetworkRequestData;
    timestamp: JsUint;
    intercepts?: NetworkIntercept[];
}
export type NetworkBytesValue = NetworkStringValue | NetworkBase64Value;
export interface NetworkStringValue {
    type: 'string';
    value: string;
}
export interface NetworkBase64Value {
    type: 'base64';
    value: string;
}
export type NetworkSameSite = 'strict' | 'lax' | 'none';
export interface NetworkCookie {
    name: string;
    value: NetworkBytesValue;
    domain: string;
    path: string;
    size: JsUint;
    httpOnly: boolean;
    secure: boolean;
    sameSite: NetworkSameSite;
    expiry?: JsUint;
}
export interface NetworkFetchTimingInfo {
    timeOrigin: number;
    requestTime: number;
    redirectStart: number;
    redirectEnd: number;
    fetchStart: number;
    dnsStart: number;
    dnsEnd: number;
    connectStart: number;
    connectEnd: number;
    tlsStart: number;
    requestStart: number;
    responseStart: number;
    responseEnd: number;
}
export interface NetworkHeader {
    name: string;
    value: NetworkBytesValue;
}
export interface NetworkInitiator {
    columnNumber?: JsUint;
    lineNumber?: JsUint;
    request?: NetworkRequest;
    stackTrace?: ScriptStackTrace;
    type?: 'parser' | 'script' | 'preflight' | 'other';
}
export type NetworkIntercept = string;
export type NetworkRequest = string;
export interface NetworkRequestData {
    request: NetworkRequest;
    url: string;
    method: string;
    headers: NetworkHeader[];
    cookies: NetworkCookie[];
    headersSize: JsUint;
    bodySize: JsUint | null;
    destination: string;
    initiatorType: string | null;
    timings: NetworkFetchTimingInfo;
}
export interface NetworkResponseContent {
    size: JsUint;
}
export interface NetworkResponseData {
    url: string;
    protocol: string;
    status: JsUint;
    statusText: string;
    fromCache: boolean;
    headers: NetworkHeader[];
    mimeType: string;
    bytesReceived: JsUint;
    headersSize: JsUint | null;
    bodySize: JsUint | null;
    content: NetworkResponseContent;
    authChallenges?: NetworkAuthChallenge[];
}
export interface NetworkAddInterceptResult {
    intercept: NetworkIntercept;
}
export interface NetworkAuthRequired {
    method: 'network.authRequired';
    params: NetworkAuthRequiredParameters;
}
export interface NetworkAuthRequiredParameters extends NetworkBaseParameters {
    response: NetworkResponseData;
}
export interface NetworkBeforeRequestSent {
    method: 'network.beforeRequestSent';
    params: NetworkBeforeRequestSentParameters;
}
export interface NetworkBeforeRequestSentParameters extends NetworkBaseParameters {
    initiator?: NetworkInitiator;
}
export interface NetworkFetchError {
    method: 'network.fetchError';
    params: NetworkFetchErrorParameters;
}
export interface NetworkFetchErrorParameters extends NetworkBaseParameters {
    errorText: string;
}
export interface NetworkResponseCompleted {
    method: 'network.responseCompleted';
    params: NetworkResponseCompletedParameters;
}
export interface NetworkResponseCompletedParameters extends NetworkBaseParameters {
    response: NetworkResponseData;
}
export interface NetworkResponseStarted {
    method: 'network.responseStarted';
    params: NetworkResponseStartedParameters;
}
export interface NetworkResponseStartedParameters extends NetworkBaseParameters {
    response: NetworkResponseData;
}
export type ScriptResult = ScriptAddPreloadScriptResult | ScriptEvaluateResult | ScriptGetRealmsResult;
export type ScriptEvent = ScriptMessage | ScriptRealmCreated | ScriptRealmDestroyed;
export type ScriptChannel = string;
export interface ScriptChannelValue {
    type: 'channel';
    value: ScriptChannelProperties;
}
export interface ScriptChannelProperties {
    channel: ScriptChannel;
    serializationOptions?: ScriptSerializationOptions;
    ownership?: ScriptResultOwnership;
}
export type ScriptEvaluateResult = ScriptEvaluateResultSuccess | ScriptEvaluateResultException;
export interface ScriptEvaluateResultSuccess {
    type: 'success';
    result: ScriptRemoteValue;
    realm: ScriptRealm;
}
export interface ScriptEvaluateResultException {
    type: 'exception';
    exceptionDetails: ScriptExceptionDetails;
    realm: ScriptRealm;
}
export interface ScriptExceptionDetails {
    columnNumber: JsUint;
    exception: ScriptRemoteValue;
    lineNumber: JsUint;
    stackTrace: ScriptStackTrace;
    text: string;
}
export type ScriptHandle = string;
export type ScriptInternalId = string;
export type ScriptLocalValue = ScriptRemoteReference | ScriptPrimitiveProtocolValue | ScriptChannelValue | ScriptArrayLocalValue | ScriptDateLocalValueMap | ScriptMapLocalValue | ScriptObjectLocalValue | ScriptRegExpLocalValueMap | ScriptSetLocalValue;
export type ScriptListLocalValue = (ScriptLocalValue)[];
export interface ScriptArrayLocalValue {
    type: 'array';
    value: ScriptListLocalValue;
}
export interface ScriptDateLocalValueMap extends ScriptDateLocalValue {
}
export interface ScriptDateLocalValue {
    type: 'date';
    value: string;
}
export type ScriptMappingLocalValue = (ScriptLocalValue | ScriptLocalValue)[];
export interface ScriptMapLocalValue {
    type: 'map';
    value: ScriptMappingLocalValue;
}
export interface ScriptObjectLocalValue {
    type: 'object';
    value: ScriptMappingLocalValue;
}
export interface ScriptRegExpValue {
    pattern: string;
    flags?: string;
}
export interface ScriptRegExpLocalValueMap extends ScriptRegExpLocalValue {
}
export interface ScriptRegExpLocalValue {
    type: 'regexp';
    value: ScriptRegExpValue;
}
export interface ScriptSetLocalValue {
    type: 'set';
    value: ScriptListLocalValue;
}
export type ScriptPreloadScript = string;
export type ScriptRealm = string;
export type ScriptPrimitiveProtocolValue = ScriptUndefinedValue | ScriptNullValue | ScriptStringValue | ScriptNumberValue | ScriptBooleanValue | ScriptBigIntValue;
export interface ScriptUndefinedValue {
    type: 'undefined';
}
export interface ScriptNullValue {
    type: null;
}
export interface ScriptStringValue {
    type: 'string';
    value: string;
}
export type ScriptSpecialNumber = 'NaN' | '-0' | 'Infinity' | '-Infinity';
export interface ScriptNumberValue {
    type: 'number';
    value: number | ScriptSpecialNumber;
}
export interface ScriptBooleanValue {
    type: 'boolean';
    value: boolean;
}
export interface ScriptBigIntValue {
    type: 'bigint';
    value: string;
}
export type ScriptRealmInfo = ScriptWindowRealmInfo | ScriptDedicatedWorkerRealmInfo | ScriptSharedWorkerRealmInfo | ScriptServiceWorkerRealmInfo | ScriptWorkerRealmInfo | ScriptPaintWorkletRealmInfo | ScriptAudioWorkletRealmInfo | ScriptWorkletRealmInfo;
export interface ScriptBaseRealmInfo {
    realm: ScriptRealm;
    origin: string;
}
export interface ScriptWindowRealmInfo extends ScriptBaseRealmInfo {
    type: 'window';
    context: BrowsingContextBrowsingContext;
    sandbox?: string;
}
export interface ScriptDedicatedWorkerRealmInfo extends ScriptBaseRealmInfo {
    type: 'dedicated-worker';
    owners: ScriptRealm[];
}
export interface ScriptSharedWorkerRealmInfo extends ScriptBaseRealmInfo {
    type: 'shared-worker';
}
export interface ScriptServiceWorkerRealmInfo extends ScriptBaseRealmInfo {
    type: 'service-worker';
}
export interface ScriptWorkerRealmInfo extends ScriptBaseRealmInfo {
    type: 'worker';
}
export interface ScriptPaintWorkletRealmInfo extends ScriptBaseRealmInfo {
    type: 'paint-worklet';
}
export interface ScriptAudioWorkletRealmInfo extends ScriptBaseRealmInfo {
    type: 'audio-worklet';
}
export interface ScriptWorkletRealmInfo extends ScriptBaseRealmInfo {
    type: 'worklet';
}
export type ScriptRealmType = 'window' | 'dedicated-worker' | 'shared-worker' | 'service-worker' | 'worker' | 'paint-worklet' | 'audio-worklet' | 'worklet';
export type ScriptRemoteReference = ScriptSharedReference | ScriptRemoteObjectReference;
export interface ScriptSharedReference extends Extensible {
    sharedId: ScriptSharedId;
    handle?: ScriptHandle;
}
export interface ScriptRemoteObjectReference extends Extensible {
    handle: ScriptHandle;
    sharedId?: ScriptSharedId;
}
export type ScriptRemoteValue = ScriptPrimitiveProtocolValue | ScriptSymbolRemoteValue | ScriptArrayRemoteValue | ScriptObjectRemoteValue | ScriptFunctionRemoteValue | ScriptRegExpRemoteValue | ScriptDateRemoteValue | ScriptMapRemoteValue | ScriptSetRemoteValue | ScriptWeakMapRemoteValue | ScriptWeakSetRemoteValue | ScriptGeneratorRemoteValue | ScriptErrorRemoteValue | ScriptProxyRemoteValue | ScriptPromiseRemoteValue | ScriptTypedArrayRemoteValue | ScriptArrayBufferRemoteValue | ScriptNodeListRemoteValue | ScriptHtmlCollectionRemoteValue | ScriptNodeRemoteValue | ScriptWindowProxyRemoteValue;
export type ScriptListRemoteValue = (ScriptRemoteValue)[];
export type ScriptMappingRemoteValue = (ScriptRemoteValue | ScriptRemoteValue)[];
export interface ScriptSymbolRemoteValue {
    type: 'symbol';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptArrayRemoteValue {
    type: 'array';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
    value?: ScriptListRemoteValue;
}
export interface ScriptObjectRemoteValue {
    type: 'object';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
    value?: ScriptMappingRemoteValue;
}
export interface ScriptFunctionRemoteValue {
    type: 'function';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptRegExpRemoteValue extends ScriptRegExpLocalValue {
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptDateRemoteValue extends ScriptDateLocalValue {
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptMapRemoteValue {
    type: 'map';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
    value?: ScriptMappingRemoteValue;
}
export interface ScriptSetRemoteValue {
    type: 'set';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
    value?: ScriptListRemoteValue;
}
export interface ScriptWeakMapRemoteValue {
    type: 'weakmap';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptWeakSetRemoteValue {
    type: 'weakset';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptGeneratorRemoteValue {
    type: 'generator';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptErrorRemoteValue {
    type: 'error';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptProxyRemoteValue {
    type: 'proxy';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptPromiseRemoteValue {
    type: 'promise';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptTypedArrayRemoteValue {
    type: 'typedarray';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptArrayBufferRemoteValue {
    type: 'arraybuffer';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptNodeListRemoteValue {
    type: 'nodelist';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
    value?: ScriptListRemoteValue;
}
export interface ScriptHtmlCollectionRemoteValue {
    type: 'htmlcollection';
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
    value?: ScriptListRemoteValue;
}
export interface ScriptNodeRemoteValue {
    type: 'node';
    sharedId?: ScriptSharedId;
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
    value?: ScriptNodeProperties;
}
export interface ScriptNodeProperties {
    nodeType: JsUint;
    childNodeCount: JsUint;
    attributes?: Record<string, string>;
    children?: ScriptNodeRemoteValue[];
    localName?: string;
    mode?: 'open' | 'closed';
    namespaceUri?: string;
    nodeValue?: string;
    shadowRoot?: ScriptNodeRemoteValue | null;
}
export interface ScriptWindowProxyRemoteValue {
    type: 'window';
    value: ScriptWindowProxyProperties;
    handle?: ScriptHandle;
    internalId?: ScriptInternalId;
}
export interface ScriptWindowProxyProperties {
    context: BrowsingContextBrowsingContext;
}
export type ScriptResultOwnership = 'root' | 'none';
export interface ScriptSerializationOptions {
    maxDomDepth?: JsUint | null;
    /**
   * @default null
   */
    maxObjectDepth?: JsUint | null;
    /**
   * @default 'none'
   */
    includeShadowTree?: 'none' | 'open' | 'all';
}
export type ScriptSharedId = string;
export interface ScriptStackFrame {
    columnNumber: JsUint;
    functionName: string;
    lineNumber: JsUint;
    url: string;
}
export interface ScriptStackTrace {
    callFrames: ScriptStackFrame[];
}
export interface ScriptSource {
    realm: ScriptRealm;
    context?: BrowsingContextBrowsingContext;
}
export interface ScriptAddPreloadScriptResult {
    script: ScriptPreloadScript;
}
export interface ScriptGetRealmsResult {
    realms: ScriptRealmInfo[];
}
export interface ScriptMessage {
    method: 'script.message';
    params: ScriptMessageParameters;
}
export interface ScriptMessageParameters {
    channel: ScriptChannel;
    data: ScriptRemoteValue;
    source: ScriptSource;
}
export interface ScriptRealmCreated {
    method: 'script.realmCreated';
    params: ScriptRealmInfo;
}
export interface ScriptRealmDestroyed {
    method: 'script.realmDestroyed';
    params: ScriptRealmDestroyedParameters;
}
export interface ScriptRealmDestroyedParameters {
    realm: ScriptRealm;
}
export type StorageResult = StorageDeleteCookiesResult | StorageGetCookiesResult | StorageSetCookieResult;
export interface StoragePartitionKey extends Extensible {
    userContext?: string;
    sourceOrigin?: string;
}
export interface StorageGetCookiesResult {
    cookies: NetworkCookie[];
    partitionKey: StoragePartitionKey;
}
export interface StorageSetCookieResult {
    partitionKey: StoragePartitionKey;
}
export interface StorageDeleteCookiesResult {
    partitionKey: StoragePartitionKey;
}
export interface LogEvent extends LogEntryAdded {
}
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export type LogEntry = LogGenericLogEntry | LogConsoleLogEntry | LogJavascriptLogEntry;
export interface LogBaseLogEntry {
    level: LogLevel;
    source: ScriptSource;
    text: string | null;
    timestamp: JsUint;
    stackTrace?: ScriptStackTrace;
}
export interface LogGenericLogEntry extends LogBaseLogEntry {
    type: string;
}
export interface LogConsoleLogEntry extends LogBaseLogEntry {
    type: 'console';
    method: string;
    args: ScriptRemoteValue[];
}
export interface LogJavascriptLogEntry extends LogBaseLogEntry {
    type: 'javascript';
}
export interface LogEntryAdded {
    method: 'log.entryAdded';
    params: LogEntry;
}
export interface InputEvent extends InputFileDialogOpened {
}
export interface InputFileDialogOpened {
    method: 'input.fileDialogOpened';
    params: InputFileDialogInfo;
}
export interface InputFileDialogInfo {
    context: BrowsingContextBrowsingContext;
    element?: ScriptSharedReference;
    multiple: boolean;
}
export interface WebExtensionResult extends WebExtensionInstallResult {
}
export type WebExtensionExtension = string;
export interface WebExtensionInstallResult {
    extension: WebExtensionExtension;
}
//# sourceMappingURL=localTypes.d.ts.map