{"version": 3, "file": "worker.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACnC,IAAI,CAACD,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;AAC1B;AAEAC,MAAM,CAACC,OAAO,GAAGJ,QAAQ;;;;;;UCXzB;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;;;ACtBA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAGK,mBAAO,CAAC,GAAY,CAAC;;AAEpC;AACA,IAAIC,kBAAkB,GAAGC,IAAI,CACzB,kCAAkC,GAClC,YAAY,GACZ,gFACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIC,mBAAmB,GAAG,0BAA0B;;AAEpD;;AAEA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,SAAAA,KAAA,EAAW,CAAC;AACpB,CAAC;AACD,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAI,OAAOC,WAAW,KAAK,UAAU,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;EAC9G;EACAJ,MAAM,CAACK,EAAE,GAAG,UAAUC,KAAK,EAAEC,QAAQ,EAAE;IACrCH,gBAAgB,CAACE,KAAK,EAAE,UAAUd,OAAO,EAAE;MACzCe,QAAQ,CAACf,OAAO,CAACgB,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACDR,MAAM,CAACS,IAAI,GAAG,UAAUjB,OAAO,EAAE;IAC/BW,WAAW,CAACX,OAAO,CAAC;EACtB,CAAC;AACH,CAAC,MACI,IAAI,OAAOkB,OAAO,KAAK,WAAW,EAAE;EACvC;;EAEA,IAAIC,aAAa;EACjB,IAAI;IACFA,aAAa,GAAGd,kBAAkB,CAAC,gBAAgB,CAAC;EACtD,CAAC,CAAC,OAAMe,KAAK,EAAE;IACb,IAAIC,OAAA,CAAOD,KAAK,MAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACE,IAAI,KAAK,kBAAkB,EAAE;MACpF;IAAA,CACD,MAAM;MACL,MAAMF,KAAK;IACb;EACF;EAEA,IAAID,aAAa,IACf;EACAA,aAAa,CAACI,UAAU,KAAK,IAAI,EAAE;IACnC,IAAIA,UAAU,GAAIJ,aAAa,CAACI,UAAU;IAC1Cf,MAAM,CAACS,IAAI,GAAGM,UAAU,CAACZ,WAAW,CAACa,IAAI,CAACD,UAAU,CAAC;IACrDf,MAAM,CAACK,EAAE,GAAGU,UAAU,CAACV,EAAE,CAACW,IAAI,CAACD,UAAU,CAAC;IAC1Cf,MAAM,CAACC,IAAI,GAAGS,OAAO,CAACT,IAAI,CAACe,IAAI,CAACN,OAAO,CAAC;EAC1C,CAAC,MAAM;IACLV,MAAM,CAACK,EAAE,GAAGK,OAAO,CAACL,EAAE,CAACW,IAAI,CAACN,OAAO,CAAC;IACpC;IACAV,MAAM,CAACS,IAAI,GAAG,UAAUjB,OAAO,EAAE;MAC/BkB,OAAO,CAACD,IAAI,CAACjB,OAAO,CAAC;IACvB,CAAC;IACD;IACAQ,MAAM,CAACK,EAAE,CAAC,YAAY,EAAE,YAAY;MAClCK,OAAO,CAACT,IAAI,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;IACFD,MAAM,CAACC,IAAI,GAAGS,OAAO,CAACT,IAAI,CAACe,IAAI,CAACN,OAAO,CAAC;EAC1C;AACF,CAAC,MACI;EACH,MAAM,IAAIO,KAAK,CAAC,qCAAqC,CAAC;AACxD;AAEA,SAASC,YAAYA,CAACN,KAAK,EAAE;EAC3B,OAAOO,MAAM,CAACC,mBAAmB,CAACR,KAAK,CAAC,CAACS,MAAM,CAAC,UAASC,OAAO,EAAEC,IAAI,EAAE;IACtE,OAAOJ,MAAM,CAACK,cAAc,CAACF,OAAO,EAAEC,IAAI,EAAE;MAC/CE,KAAK,EAAEb,KAAK,CAACW,IAAI,CAAC;MAClBG,UAAU,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACF,KAAK,EAAE;EACxB,OAAOA,KAAK,IAAK,OAAOA,KAAK,CAACG,IAAI,KAAK,UAAW,IAAK,OAAOH,KAAK,SAAM,KAAK,UAAW;AAC3F;;AAEA;AACAzB,MAAM,CAAC6B,OAAO,GAAG,CAAC,CAAC;;AAEnB;AACA;AACA;AACA;AACA;AACA;AACA7B,MAAM,CAAC6B,OAAO,CAACC,GAAG,GAAG,SAASA,GAAGA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAC1C,IAAIC,CAAC,GAAG,IAAIC,QAAQ,CAAC,UAAU,GAAGH,EAAE,GAAG,2BAA2B,CAAC;EACnE,OAAOE,CAAC,CAACE,KAAK,CAACF,CAAC,EAAED,IAAI,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACAhC,MAAM,CAAC6B,OAAO,CAACA,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;EAC1C,OAAOV,MAAM,CAACiB,IAAI,CAACpC,MAAM,CAAC6B,OAAO,CAAC;AACpC,CAAC;;AAED;AACA;AACA;AACA7B,MAAM,CAACqC,kBAAkB,GAAGC,SAAS;;AAErC;AACA;AACA;AACA;AACA;AACAtC,MAAM,CAACuC,cAAc,GAAG,UAASzB,IAAI,EAAE;EACrC,IAAI0B,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAc;IACrBxC,MAAM,CAACC,IAAI,CAACa,IAAI,CAAC;EACnB,CAAC;EAED,IAAG,CAACd,MAAM,CAACqC,kBAAkB,EAAE;IAC7B,OAAOG,KAAK,CAAC,CAAC;EAChB;EAEA,IAAIC,MAAM,GAAGzC,MAAM,CAACqC,kBAAkB,CAACvB,IAAI,CAAC;EAC5C,IAAIa,SAAS,CAACc,MAAM,CAAC,EAAE;IACrBA,MAAM,CAACb,IAAI,CAACY,KAAK,EAAEA,KAAK,CAAC;EAC3B,CAAC,MAAM;IACLA,KAAK,CAAC,CAAC;EACT;AACF,CAAC;AAED,IAAIE,gBAAgB,GAAG,IAAI;AAE3B1C,MAAM,CAACK,EAAE,CAAC,SAAS,EAAE,UAAUsC,OAAO,EAAE;EACtC,IAAIA,OAAO,KAAK5C,mBAAmB,EAAE;IACnC,OAAOC,MAAM,CAACuC,cAAc,CAAC,CAAC,CAAC;EACjC;EACA,IAAI;IACF,IAAIK,MAAM,GAAG5C,MAAM,CAAC6B,OAAO,CAACc,OAAO,CAACC,MAAM,CAAC;IAE3C,IAAIA,MAAM,EAAE;MACVF,gBAAgB,GAAGC,OAAO,CAACE,EAAE;;MAE7B;MACA,IAAIJ,MAAM,GAAGG,MAAM,CAACT,KAAK,CAACS,MAAM,EAAED,OAAO,CAACG,MAAM,CAAC;MAEjD,IAAInB,SAAS,CAACc,MAAM,CAAC,EAAE;QACrB;QACAA,MAAM,CACDb,IAAI,CAAC,UAAUa,MAAM,EAAE;UACtB,IAAIA,MAAM,YAAYlD,QAAQ,EAAE;YAC9BS,MAAM,CAACS,IAAI,CAAC;cACVoC,EAAE,EAAEF,OAAO,CAACE,EAAE;cACdJ,MAAM,EAAEA,MAAM,CAACjD,OAAO;cACtBoB,KAAK,EAAE;YACT,CAAC,EAAE6B,MAAM,CAAChD,QAAQ,CAAC;UACrB,CAAC,MAAM;YACLO,MAAM,CAACS,IAAI,CAAC;cACVoC,EAAE,EAAEF,OAAO,CAACE,EAAE;cACdJ,MAAM,EAAEA,MAAM;cACd7B,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;UACA8B,gBAAgB,GAAG,IAAI;QACzB,CAAC,CAAC,SACI,CAAC,UAAUK,GAAG,EAAE;UACpB/C,MAAM,CAACS,IAAI,CAAC;YACVoC,EAAE,EAAEF,OAAO,CAACE,EAAE;YACdJ,MAAM,EAAE,IAAI;YACZ7B,KAAK,EAAEM,YAAY,CAAC6B,GAAG;UACzB,CAAC,CAAC;UACFL,gBAAgB,GAAG,IAAI;QACzB,CAAC,CAAC;MACR,CAAC,MACI;QACH;QACA,IAAID,MAAM,YAAYlD,QAAQ,EAAE;UAC9BS,MAAM,CAACS,IAAI,CAAC;YACVoC,EAAE,EAAEF,OAAO,CAACE,EAAE;YACdJ,MAAM,EAAEA,MAAM,CAACjD,OAAO;YACtBoB,KAAK,EAAE;UACT,CAAC,EAAE6B,MAAM,CAAChD,QAAQ,CAAC;QACrB,CAAC,MAAM;UACLO,MAAM,CAACS,IAAI,CAAC;YACVoC,EAAE,EAAEF,OAAO,CAACE,EAAE;YACdJ,MAAM,EAAEA,MAAM;YACd7B,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;QAEA8B,gBAAgB,GAAG,IAAI;MACzB;IACF,CAAC,MACI;MACH,MAAM,IAAIzB,KAAK,CAAC,kBAAkB,GAAG0B,OAAO,CAACC,MAAM,GAAG,GAAG,CAAC;IAC5D;EACF,CAAC,CACD,OAAOG,GAAG,EAAE;IACV/C,MAAM,CAACS,IAAI,CAAC;MACVoC,EAAE,EAAEF,OAAO,CAACE,EAAE;MACdJ,MAAM,EAAE,IAAI;MACZ7B,KAAK,EAAEM,YAAY,CAAC6B,GAAG;IACzB,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA/C,MAAM,CAACgD,QAAQ,GAAG,UAAUnB,OAAO,EAAEoB,OAAO,EAAE;EAE5C,IAAIpB,OAAO,EAAE;IACX,KAAK,IAAIN,IAAI,IAAIM,OAAO,EAAE;MACxB,IAAIA,OAAO,CAACqB,cAAc,CAAC3B,IAAI,CAAC,EAAE;QAChCvB,MAAM,CAAC6B,OAAO,CAACN,IAAI,CAAC,GAAGM,OAAO,CAACN,IAAI,CAAC;MACtC;IACF;EACF;EAEA,IAAI0B,OAAO,EAAE;IACXjD,MAAM,CAACqC,kBAAkB,GAAGY,OAAO,CAACE,WAAW;EACjD;EAEAnD,MAAM,CAACS,IAAI,CAAC,OAAO,CAAC;AACtB,CAAC;AAEDT,MAAM,CAACoD,IAAI,GAAG,UAAUC,OAAO,EAAE;EAC/B,IAAIX,gBAAgB,EAAE;IACpB,IAAIW,OAAO,YAAY9D,QAAQ,EAAE;MAC/BS,MAAM,CAACS,IAAI,CAAC;QACVoC,EAAE,EAAEH,gBAAgB;QACpBY,OAAO,EAAE,IAAI;QACbD,OAAO,EAAEA,OAAO,CAAC7D;MACnB,CAAC,EAAE6D,OAAO,CAAC5D,QAAQ,CAAC;MACpB;IACF;IAEAO,MAAM,CAACS,IAAI,CAAC;MACVoC,EAAE,EAAEH,gBAAgB;MACpBY,OAAO,EAAE,IAAI;MACbD,OAAO,EAAPA;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAI,IAA8B,EAAE;EAClC1D,yBAAW,GAAGK,MAAM,CAACgD,QAAQ;EAC7BrD,yBAAY,GAAGK,MAAM,CAACoD,IAAI;AAC5B", "sources": ["webpack://workerpool/./src/transfer.js", "webpack://workerpool/webpack/bootstrap", "webpack://workerpool/./src/worker.js"], "sourcesContent": ["/**\n * The helper class for transferring data from the worker to the main thread.\n *\n * @param {Object} message The object to deliver to the main thread.\n * @param {Object[]} transfer An array of transferable Objects to transfer ownership of.\n */\nfunction Transfer(message, transfer) {\n  this.message = message;\n  this.transfer = transfer;\n}\n\nmodule.exports = Transfer;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\nvar Transfer = require('./transfer');\n\n// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\'' +\n    ' ? require' +\n    ' : function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message) {\n    postMessage(message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n    worker.exit = process.exit.bind(process);\n  } else {\n    worker.on = process.on.bind(process);\n    // ignore transfer argument since it is not supported by process\n    worker.send = function (message) {\n      process.send(message);\n    };\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(null, arguments);');\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\n/**\n * Custom handler for when the worker is terminated.\n */\nworker.terminationHandler = undefined;\n\n/**\n * Cleanup and exit the worker.\n * @param {Number} code \n * @returns \n */\nworker.cleanupAndExit = function(code) {\n  var _exit = function() {\n    worker.exit(code);\n  }\n\n  if(!worker.terminationHandler) {\n    return _exit();\n  }\n\n  var result = worker.terminationHandler(code);\n  if (isPromise(result)) {\n    result.then(_exit, _exit);\n  } else {\n    _exit();\n  }\n}\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.cleanupAndExit(0);\n  }\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              if (result instanceof Transfer) {\n                worker.send({\n                  id: request.id,\n                  result: result.message,\n                  error: null\n                }, result.transfer);\n              } else {\n                worker.send({\n                  id: request.id,\n                  result: result,\n                  error: null\n                });\n              }\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err)\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        if (result instanceof Transfer) {\n          worker.send({\n            id: request.id,\n            result: result.message,\n            error: null\n          }, result.transfer);\n        } else {\n          worker.send({\n            id: request.id,\n            result: result,\n            error: null\n          });\n        }\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} [methods]\n * @param {WorkerRegisterOptions} [options]\n */\nworker.register = function (methods, options) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n      }\n    }\n  }\n\n  if (options) {\n    worker.terminationHandler = options.onTerminate;\n  }\n\n  worker.send('ready');\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    if (payload instanceof Transfer) {\n      worker.send({\n        id: currentRequestId,\n        isEvent: true,\n        payload: payload.message\n      }, payload.transfer);\n      return;\n    }\n\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n"], "names": ["Transfer", "message", "transfer", "module", "exports", "require", "requireFoolWebpack", "eval", "TERMINATE_METHOD_ID", "worker", "exit", "self", "postMessage", "addEventListener", "on", "event", "callback", "data", "send", "process", "WorkerThreads", "error", "_typeof", "code", "parentPort", "bind", "Error", "convertError", "Object", "getOwnPropertyNames", "reduce", "product", "name", "defineProperty", "value", "enumerable", "isPromise", "then", "methods", "run", "fn", "args", "f", "Function", "apply", "keys", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "cleanupAndExit", "_exit", "result", "currentRequestId", "request", "method", "id", "params", "err", "register", "options", "hasOwnProperty", "onTerminate", "emit", "payload", "isEvent", "add"], "sourceRoot": ""}