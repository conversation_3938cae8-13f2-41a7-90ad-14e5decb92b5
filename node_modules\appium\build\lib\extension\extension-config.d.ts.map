{"version": 3, "file": "extension-config.d.ts", "sourceRoot": "", "sources": ["../../../lib/extension/extension-config.js"], "names": [], "mappings": "AAmrBA;;;;;GAKG;AACH,mDAHW,GAAG,GACD,MAAM,GAAG,SAAS,CAe9B;AArrBD;;;;;GAKG;AACH,+BAAgC,KAAK,CAAC;AACtC;;;GAGG;AACH,iCAAkC,OAAO,CAAC;AAC1C;;;GAGG;AACH,kCAAmC,QAAQ,CAAC;AAC5C;;;GAGG;AACH,+BAAgC,KAAK,CAAC;AACtC;;;GAGG;AACH,+BAAgC,KAAK,CAAC;AAEtC,+BAA+B;AAC/B,4BADW,GAAG,CAAC,WAAW,CAAC,CAOxB;AAEH;;;;;GAKG;AACH,6BAF6B,OAAO,SAAtB,aAAc;IA+jB1B;;;;;;;;;OASG;IACH,oCAkBC;IAED;;;;;;OAMG;IACH,0CAJc,aAAc,eACjB,WAAW,CAAC,SAAO,CAAC,GAClB,WAAW,IAAI,qBAAqB,CAAC,SAAO,CAAC,CAIzD;IA1kBD;;;;OAIG;IACH,wBAIC;IAlCD;;;OAGG;IACH,eAFU,OAAO,CAEH;IAEd;;;;;OAKG;IACH,qBAFU,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAElB;IAEpB,mDAAmD;IACnD,KADW,OAAO,eAAe,EAAE,YAAY,CAC3C;IAEJ,uBAAuB;IACvB,UADW,QAAQ,CACV;IAkBT,2BAEC;IAED,yBAEC;IAED;;;;;;OAMG;IACH,qBAJW,OAAO,CAAC,OAAO,CAAC,eAChB,WAAW,CAAC,OAAO,CAAC,GAClB,kBAAkB,EAAE,CAQhC;IAED;;;;;;OAMG;IACH,qBAJW,OAAO,CAAC,OAAO,CAAC,eAChB,WAAW,CAAC,OAAO,CAAC,GAClB,OAAO,CAAC,MAAM,EAAE,CAAC,CAS7B;IAED;;;;;;OAMG;IAEH,+BALW,WAAW,CAAC,OAAO,CAAC,WACpB,OAAO,CAAC,OAAO,CAAC,GACd,OAAO,CAAC,MAAM,EAAE,CAAC,CAK7B;IAED;;;;OAIG;IACH,wCAHW,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,EAAC,kBAAkB,EAAE,CAAC,eAC1C,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,EAAC,MAAM,EAAE,CAAC;;;MAwCxC;IAED;;;;;;;;;;OAUG;IACH,0BAHW,SAAS,CAAC,OAAO,CAAC,GAChB,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAyDvC;IAED;;;;;;;OAOG;IACH,yBAFa,OAAO,CAAC,OAAO,0BAA0B,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,CAW9E;IAED;;;;;;;;;OASG;IACH,sCAJW,WAAW,CAAC,OAAO,CAAC,WACpB,OAAO,CAAC,OAAO,CAAC,GACd,OAAO,CAAC,MAAM,EAAE,CAAC,CAqF7B;IACD;;;;;;OAMG;IACH,+BAJW,WAAW,CAAC,OAAO,CAAC,WACpB,OAAO,CAAC,OAAO,CAAC,GACd,kBAAkB,EAAE,CA0ChC;IAED;;;;;OAKG;IAEH,sCALW,WAAW,CAAC,OAAO,CAAC,WACpB,OAAO,CAAC,OAAO,CAAC,GACd,kBAAkB,EAAE,CA6BhC;IAED;;;;;OAKG;IAEH,+BALW,WAAW,CAAC,OAAO,CAAC,WACpB,OAAO,CAAC,OAAO,CAAC,GACd,kBAAkB,EAAE,CAMhC;IAED;;;;;OAKG;IACH,sBALW,MAAM,eACN,WAAW,CAAC,OAAO,CAAC,cACpB,2BAA2B,GACzB,OAAO,CAAC,IAAI,CAAC,CAOzB;IAED;;;;;OAKG;IACH,yBALW,OAAO,CAAC,OAAO,CAAC,eAChB,WAAW,CAAC,OAAO,CAAC,cACpB,2BAA2B,GACzB,OAAO,CAAC,IAAI,CAAC,CAUzB;IAED;;;;;;OAMG;IACH,yBAJW,OAAO,CAAC,OAAO,CAAC,cAChB,2BAA2B,GACzB,OAAO,CAAC,IAAI,CAAC,CAOzB;IAED;;;OAGG;IAEH,oBAJW,OAAO,CAAC,OAAO,CAAC,EAAE,GAChB,IAAI,CAkBhB;IAED;;;;;;OAMG;IAEH,uBANW,OAAO,CAAC,OAAO,CAAC,eAChB,WAAW,CAAC,OAAO,CAAC,GAClB,MAAM,CAMlB;IAED;;;;;;OAMG;IACH,wBAHW,MAAM,OAAO,IAAI,CAAC,mBAAmB,GACnC,MAAM,CAOlB;IAED;;;;OAIG;IACH,2BAHW,OAAO,CAAC,OAAO,CAAC,GACd,OAAO,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAuCrC;IAED;;;;;OAKG;IACH,sBAHW,OAAO,CAAC,OAAO,CAAC,GACd,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CActC;IAED;;;OAGG;IACH,qBAHW,MAAM,GACJ,OAAO,CAInB;IA2CD;;;;;;OAMG;IACH,6BAJW,OAAO,CAAC,OAAO,CAAC,eAChB,qBAAqB,CAAC,OAAO,CAAC,GAC5B,OAAO,KAAK,EAAE,YAAY,GAAC,SAAS,CAShD;;CACF;;;;;;;;;;SA4Ba,MAAM;;;;SACN,GAAG;;;;;uCAMH,GAAG,EAAA,KACJ,IAAI;4BAIJ,OAAO,eAAe,EAAE,aAAa;uBACrC,OAAO,YAAY,EAAE,QAAQ;0BAC7B,OAAO,cAAc,EAAE,WAAW;wBAIlB,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC;kCAI3B,OAAO,SAAtB,aAAc,IACf,WAAW,CAAC,OAAO,CAAC,GAAG;IAAC,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;CAAC;oBAI5D,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC;qBAIvB,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC;sBAIxB,OAAO,SAAtB,aAAc,IACf,OAAO,cAAc,EAAE,SAAS,CAAC,OAAO,CAAC;uBAIzB,OAAO,SAAtB,aAAc,IACf,OAAO,kBAAkB,EAAE,UAAU,CAAC,OAAO,CAAC"}