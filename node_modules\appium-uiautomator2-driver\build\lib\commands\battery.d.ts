/**
 * Reads the battery information from the device under test.
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<import('./types').BatteryInfo>} The actual battery info
 */
export function mobileGetBatteryInfo(this: import("../driver").AndroidUiautomator2Driver): Promise<import("./types").BatteryInfo>;
export type BatteryInfo = import("./types").BatteryInfo;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
//# sourceMappingURL=battery.d.ts.map