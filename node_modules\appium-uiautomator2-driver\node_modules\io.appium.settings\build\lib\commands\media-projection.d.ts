/**
 * Creates a new instance of the MediaProjection-based recorder
 * The recorder only works since Android API 29+
 *
 * @this {import('../client').SettingsApp}
 * @returns {MediaProjectionRecorder} The recorder instance
 */
export function makeMediaProjectionRecorder(this: import("../client").SettingsApp): MediaProjectionRecorder;
/**
 * Adjusts the necessary permissions for the
 * Media Projection-based recording service
 *
 * @this {import('../client').SettingsApp}
 * @returns {Promise<boolean>} If the permssions adjustment has actually been made
 */
export function adjustMediaProjectionServicePermissions(this: import("../client").SettingsApp): Promise<boolean>;
export type StartMediaProjectionRecordingOpts = {
    /**
     * Maximum supported resolution on-device (Detected automatically by the app
     * itself), which usually equals to Full HD 1920x1080 on most phones however
     * you can change it to following supported resolutions as well: "1920x1080",
     * "1280x720", "720x480", "320x240", "176x144".
     */
    resolution?: string | undefined;
    /**
     * Maximum allowed duration is 15 minutes; you can increase it if your test
     * takes longer than that.
     */
    maxDurationSec?: number | undefined;
    /**
     * Recording thread priority.
     * If you face performance drops during testing with recording enabled, you
     * can reduce recording priority
     */
    priority?: "high" | "normal" | "low" | undefined;
    /**
     * You can type recording video file name as you want, but recording currently
     * supports only "mp4" format so your filename must end with ".mp4". An
     * invalid file name will fail to start the recording.
     */
    filename?: string | undefined;
};
export type ADB = import("appium-adb").ADB;
/**
 * @typedef {Object} StartMediaProjectionRecordingOpts
 * @property {string} [resolution] Maximum supported resolution on-device (Detected automatically by the app
 * itself), which usually equals to Full HD 1920x1080 on most phones however
 * you can change it to following supported resolutions as well: "1920x1080",
 * "1280x720", "720x480", "320x240", "176x144".
 * @property {number} [maxDurationSec=900] Maximum allowed duration is 15 minutes; you can increase it if your test
 * takes longer than that.
 * @property {'high' | 'normal' | 'low'} [priority='high'] Recording thread priority.
 * If you face performance drops during testing with recording enabled, you
 * can reduce recording priority
 * @property {string} [filename] You can type recording video file name as you want, but recording currently
 * supports only "mp4" format so your filename must end with ".mp4". An
 * invalid file name will fail to start the recording.
 */
declare class MediaProjectionRecorder {
    /**
     * @param {ADB} adb
     */
    constructor(adb: ADB);
    adb: import("appium-adb").default;
    /**
     *
     * @returns {Promise<boolean>}
     */
    isRunning(): Promise<boolean>;
    /**
     *
     * @param {StartMediaProjectionRecordingOpts} opts
     * @returns {Promise<boolean>}
     */
    start(opts?: StartMediaProjectionRecordingOpts): Promise<boolean>;
    /**
     * @returns {Promise<void>}
     */
    cleanup(): Promise<void>;
    /**
     *
     * @returns {Promise<string?>}
     */
    pullRecent(): Promise<string | null>;
    /**
     * @returns {Promise<boolean>}
     */
    stop(): Promise<boolean>;
}
export {};
//# sourceMappingURL=media-projection.d.ts.map