{"version": 3, "file": "static.js", "sourceRoot": "", "sources": ["../../../lib/express/static.js"], "names": [], "mappings": ";;;;;;AA2EQ,8BAAS;AAAE,kDAAmB;AAAE,gDAAkB;AAAE,0BAAO;AA3EnE,gDAAwB;AACxB,sDAA2B;AAC3B,oDAAuB;AACvB,6CAAmC;AACnC,wDAAyB;AAEzB,IAAI,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAqEA,gCAAU;AApE/E,IAAI,gBAAC,CAAC,MAAM,CAAC,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC,EAAE,CAAC;IAC1E,sDAAsD;IACtD,8CAA8C;IAC9C,qBAAA,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC7D,CAAC;AAED,KAAK,UAAU,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;IAC7C,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACrE,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;IACvE,IAAI,MAAM,GAAG;QACX,UAAU;QACV,UAAU,EAAE,IAAI,IAAI,EAAE;QACtB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;QACpC,OAAO,EAAE,MAAM;KAChB,CAAC;IACF,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC1B,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC;IACvD,CAAC;IACD,gBAAG,CAAC,KAAK,CAAC,4CAA4C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAChF,IAAI,KAAK,EAAE,CAAC;QACV,gBAAG,CAAC,KAAK,CAAC,WAAW,KAAK,sBAAsB,CAAC,CAAC;QAClD,MAAM,kBAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IACD,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IACrC,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,qBAAqB,EAAE,EAAC,IAAI,EAAE,GAAG,EAAC,CAAC,CAAC;IAChE,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,SAAS,EAAE,EAAC,IAAI,EAAE,GAAG,EAAC,CAAC,CAAC;IACpD,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,kBAAkB,EAAE;QAC9C,MAAM,EAAE,iBAAiB;QACzB,IAAI,EAAE,GAAG;KACV,CAAC,CAAC;IACH,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,SAAS,CAAC,GAAG,EAAE,GAAG;IAC/B,OAAO,MAAM,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,iBAAiB,CAAC,CAAC;AAC9D,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mBAAmB,CAAC,GAAG,EAAE,GAAG;IACzC,OAAO,MAAM,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,4BAA4B,CAAC,CAAC;AACzE,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAAC,GAAG,EAAE,GAAG;IACxC,OAAO,MAAM,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,4BAA4B,CAAC,CAAC;AACzE,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,OAAO,CAAC,GAAG,EAAE,GAAG;IAC7B,IAAI,MAAM,GAAG,EAAC,OAAO,EAAE,eAAe,EAAC,CAAC;IACxC,gBAAG,CAAC,KAAK,CAAC,yCAAyC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC7E,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,YAAY;IACrC,IAAI,OAAO,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;IAChF,OAAO,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;AACxC,CAAC"}