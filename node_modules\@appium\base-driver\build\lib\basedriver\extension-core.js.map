{"version": 3, "file": "extension-core.js", "sourceRoot": "", "sources": ["../../../lib/basedriver/extension-core.ts"], "names": [], "mappings": ";;;;;;AAAA,6CAAuC;AACvC,6CAAyC;AAOzC,4CAEsB;AACtB,0CAAmC;AACnC,6DAAwD;AACxD,oDAAuB;AACvB,uCAAkD;AAElD,MAAa,aAAa;IASxB,YAAY,SAAkB;QAP9B,iBAAY,GAAkB,6BAA8B,CAAC;QAQ3D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,0BAAY,EAAE,CAAC;IACzC,CAAC;IAED,IAAI,GAAG;QACL,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,IAAI,IAAA,iCAAuB,EAAC,IAAI,CAAC,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,eAAe,CAAC,SAAiB;QAC/B,IAAI,CAAC,IAAI,GAAG,gBAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED,kBAAkB,CAAC,IAAmB;QACpC,MAAM,eAAe,GAAG,gBAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1F,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sCAAsC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,6BAA6B,CAAC,CAAC;QACpH,CAAC;QACD,IAAI,CAAC,YAAY,GAAG;YAClB,GAAG,IAAI,CAAC,YAAY;YACpB,GAAG,IAAI;SACR,CAAC;IACJ,CAAC;IAED,oBAAoB,CAAC,UAAkB,EAAE,UAAkB;QACzD,IAAI,CAAC;YACH,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACvD,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,uBAAuB,CAAC,UAAkB,EAAE,UAAkB;QAC5D,+DAA+D;QAC/D,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/B,MAAM,IAAI,iBAAM,CAAC,mBAAmB,CAClC,sDAAsD;gBACpD,sDAAsD;gBACtD,IAAI,UAAU,IAAI,UAAU,GAAG,CAClC,CAAC;QACJ,CAAC;QAED,iEAAiE;QACjE,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,iBAAM,CAAC,mBAAmB,EAAE,CAAC;QACzC,CAAC;QAED,MAAM,EAAC,OAAO,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;QAC5D,4DAA4D;QAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,iBAAM,CAAC,mBAAmB,EAAE,CAAC;QACzC,CAAC;QAED,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACnB,MAAM,IAAI,iBAAM,CAAC,sBAAsB,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,UAAwB,EAAE,IAAyB,EAAE,MAAsB;QACnH,MAAM,WAAW,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC3D,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACrD,MAAM,EAAC,OAAO,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;QAEpE,4FAA4F;QAC5F,WAAW;QACX,MAAM,IAAI,GAAU,EAAE,CAAC;QACvB,IAAI,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;YAC7B,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,gBAAC,CAAC,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;oBAC7C,MAAM,IAAI,iBAAM,CAAC,oBAAoB,CACnC,OAAO,aAAa,4CAA4C,CACjE,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QACD,IAAI,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;YAC7B,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QACD,MAAM,SAAS,GAAG,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,EAAC,MAAM,EAAE,+BAAmB,EAAC,CAAC,CAAC;QACxF,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,2BAA2B,OAAO,iBAAiB,SAAS,kBAAkB,WAAW,GAAG;YAC1F,WAAW,OAAO,GAAG,CACxB,CAAC;QACF,uFAAuF;QACvF,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAC9G,MAAM,aAAa,GAAG,gBAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC9D,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,+BAA+B,OAAO,SAAS;YAC/C,GAAG,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,EAAC,MAAM,EAAE,+BAAmB,EAAC,CAAC,EAAE,CAC9E,CAAC;QACF,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AA/GD,sCA+GC"}