/**
 * Verify whether a remote path exists on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} remotePath - The remote path to verify.
 * @return {Promise<boolean>} True if the given path exists on the device.
 */
export function fileExists(this: import("../adb.js").ADB, remotePath: string): Promise<boolean>;
/**
 * Get the output of _ls_ command on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} remotePath - The remote path (the first argument to the _ls_ command).
 * @param {string[]} [opts] - Additional _ls_ options.
 * @return {Promise<string[]>} The _ls_ output as an array of split lines.
 *                          An empty array is returned of the given _remotePath_
 *                          does not exist.
 */
export function ls(this: import("../adb.js").ADB, remotePath: string, opts?: string[]): Promise<string[]>;
/**
 * Get the size of the particular file located on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} remotePath - The remote path to the file.
 * @return {Promise<number>} File size in bytes.
 * @throws {Error} If there was an error while getting the size of the given file.
 */
export function fileSize(this: import("../adb.js").ADB, remotePath: string): Promise<number>;
/**
 * Forcefully recursively remove a path on the device under test.
 * Be careful while calling this method.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} path - The path to be removed recursively.
 */
export function rimraf(this: import("../adb.js").ADB, path: string): Promise<void>;
/**
 * Send a file to the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} localPath - The path to the file on the local file system.
 * @param {string} remotePath - The destination path on the remote device.
 * @param {object} [opts] - Additional options mapping. See
 *                        https://github.com/appium/node-teen_process,
 *                        _exec_ method options, for more information about available
 *                        options.
 */
export function push(this: import("../adb.js").ADB, localPath: string, remotePath: string, opts?: object): Promise<void>;
/**
 * Receive a file from the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} remotePath - The source path on the remote device.
 * @param {string} localPath - The destination path to the file on the local file system.
 * @param {import('teen_process').TeenProcessExecOptions} [opts={}] - Additional options mapping. See
 * https://github.com/appium/node-teen_process,
 * _exec_ method options, for more information about available
 * options.
 */
export function pull(this: import("../adb.js").ADB, remotePath: string, localPath: string, opts?: import("teen_process").TeenProcessExecOptions): Promise<void>;
/**
 * Recursively create a new folder on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} remotePath - The new path to be created.
 * @return {Promise<string>} mkdir command output.
 */
export function mkdir(this: import("../adb.js").ADB, remotePath: string): Promise<string>;
//# sourceMappingURL=fs-commands.d.ts.map