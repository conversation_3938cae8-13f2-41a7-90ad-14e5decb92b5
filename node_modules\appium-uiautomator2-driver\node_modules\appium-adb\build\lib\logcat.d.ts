export class Logcat extends EventEmitter<[never]> {
    constructor(opts?: {});
    adb: any;
    clearLogs: any;
    debug: any;
    debugTrace: any;
    maxBufferSize: any;
    /** @type {LRUCache<number, [string, number]>} */
    logs: LRUCache<number, [string, number]>;
    /** @type {number?} */
    logIndexSinceLastRequest: number | null;
    startCapture(opts?: {}): Promise<any>;
    proc: SubProcess | null | undefined;
    /**
     *
     * @param {string} logLine
     * @param {string} [prefix='']
     * @returns {void}
     */
    outputHandler(logLine: string, prefix?: string): void;
    /**
     *
     * @returns {Promise<void>}
     */
    stopCapture(): Promise<void>;
    /**
     * @returns {import('./tools/types').LogEntry[]}
     */
    getLogs(): import("./tools/types").LogEntry[];
    /**
     * @returns {import('./tools/types').LogEntry[]}
     */
    getAllLogs(): import("./tools/types").LogEntry[];
    /**
     * @returns {Promise<void>}
     */
    clear(): Promise<void>;
}
export default Logcat;
import { EventEmitter } from 'node:events';
import { LRUCache } from 'lru-cache';
import { SubProcess } from 'teen_process';
//# sourceMappingURL=logcat.d.ts.map