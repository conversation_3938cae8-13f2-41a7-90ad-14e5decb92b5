/**
 * Performs a simple click/tap gesture
 *
 * @this {AndroidUiautomator2Driver}
 * @param {string} [elementId] The id of the element to be clicked.
 * If the element is missing then both click offset coordinates must be provided.
 * If both the element id and offset are provided then the coordinates are parsed
 * as relative offsets from the top left corner of the element.
 * @param {number} [x] The x coordinate to click on.
 * @param {number} [y] The y coordinate to click on.
 * @returns {Promise<void>}
 * @throws {Error} if provided options are not valid
 */
export function mobileClickGesture(this: import("../driver").AndroidUiautomator2Driver, elementId?: string, x?: number, y?: number): Promise<void>;
/**
 * Performs a click that lasts for the given duration
 *
 * @this {AndroidUiautomator2Driver}
 * @param {string} [elementId] The id of the element to be clicked.
 * If the element is missing then both click offset coordinates must be provided.
 * If both the element id and offset are provided then the coordinates are parsed
 * as relative offsets from the top left corner of the element.
 * @param {number} [x] The x coordinate to click on.
 * @param {number} [y] The y coordinate to click on.
 * @param {number} [duration] Click duration in milliseconds. The value must not be negative.
 * Default is 500.
 * @returns {Promise<void>}
 * @throws {Error} if provided options are not valid
 */
export function mobileLongClickGesture(this: import("../driver").AndroidUiautomator2Driver, elementId?: string, x?: number, y?: number, duration?: number): Promise<void>;
/**
 * Performs a click that lasts for the given duration
 * @this {AndroidUiautomator2Driver}
 * @param {string} [elementId] The id of the element to be clicked.
 * If the element is missing then both click offset coordinates must be provided.
 * If both the element id and offset are provided then the coordinates are parsed
 * as relative offsets from the top left corner of the element.
 * @param {number} [x] The x coordinate to click on.
 * @param {number} [y] The y coordinate to click on.
 * @returns {Promise<void>}
 * @throws {Error} if provided options are not valid
 */
export function mobileDoubleClickGesture(this: import("../driver").AndroidUiautomator2Driver, elementId?: string, x?: number, y?: number): Promise<void>;
/**
 * Drags this object to the specified location.
 * @this {AndroidUiautomator2Driver}
 * @param {string} [elementId] The id of the element to be dragged.
 * If the element id is missing then the start coordinates must be provided.
 * If both the element id and the start coordinates are provided then these
 * coordinates are considered as offsets from the top left element corner.
 * @param {number} [startX] The x coordinate where the dragging starts
 * @param {number} [startY] The y coordinate where the dragging starts
 * @param {number} [endX] The x coordinate where the dragging ends
 * @param {number} [endY] The y coordinate where the dragging ends
 * @param {number} [speed] The speed at which to perform this gesture in pixels per second.
 * The value must not be negative.
 * Default is 2500 * displayDensity.
 * @returns {Promise<void>}
 * @throws {Error} if provided options are not valid
 */
export function mobileDragGesture(this: import("../driver").AndroidUiautomator2Driver, elementId?: string, startX?: number, startY?: number, endX?: number, endY?: number, speed?: number): Promise<void>;
/**
 * Drags to the specified location.
 *
 * @throws {Error} if provided options are not valid
 * @this {AndroidUiautomator2Driver}
 * @param {string} direction Direction of the fling.
 * Acceptable values are: `up`, `down`, `left` and `right` (case insensitive).
 * @param {string} [elementId] The id of the element to be flinged.
 * If the element id is missing then fling bounding area must be provided.
 * If both the element id and the fling bounding area are provided then this
 * area is effectively ignored.
 * @param {number} [left] The left coordinate of the fling bounding area.
 * @param {number} [top] The top coordinate of the fling bounding area.
 * @param {number} [width] The width of the fling bounding area.
 * @param {number} [height] The height of the fling bounding area.
 * @param {number} [speed] The speed at which to perform this gesture in pixels per second.
 * The value must be greater than the minimum fling velocity for the given view (50 by default).
 * Default is 7500 * displayDensity.
 * @returns {Promise<boolean>} True if the object can still scroll in the given direction.
 */
export function mobileFlingGesture(this: import("../driver").AndroidUiautomator2Driver, direction: string, elementId?: string, left?: number, top?: number, width?: number, height?: number, speed?: number): Promise<boolean>;
/**
 * Performs a pinch close gesture.
 * @this {AndroidUiautomator2Driver}
 * @param {number} percent The size of the pinch as a percentage of the pinch area size.
 * Valid values must be float numbers in range 0..1, where 1.0 is 100%
 * @param {string} [elementId] The id of the element to be pinched.
 * If the element id is missing then pinch bounding area must be provided.
 * If both the element id and the pinch bounding area are provided then the
 * area is effectively ignored.
 * @param {number} [left] The left coordinate of the pinch bounding area.
 * @param {number} [top] The top coordinate of the pinch bounding area.
 * @param {number} [width] The width of the pinch bounding area.
 * @param {number} [height] The height of the pinch bounding area.
 * @param {number} [speed] The speed at which to perform this gesture in pixels per second.
 * The value must not be negative.
 * Default is 2500 * displayDensity.
 * @returns {Promise<void>}
 * @throws {Error} if provided options are not valid
 */
export function mobilePinchCloseGesture(this: import("../driver").AndroidUiautomator2Driver, percent: number, elementId?: string, left?: number, top?: number, width?: number, height?: number, speed?: number): Promise<void>;
/**
 * Performs a pinch open gesture.
 * @this {AndroidUiautomator2Driver}
 * @param {number} percent The size of the pinch as a percentage of the pinch area size.
 * Valid values must be float numbers in range 0..1, where 1.0 is 100%
 * @param {string} [elementId] The id of the element to be pinched.
 * If the element id is missing then pinch bounding area must be provided.
 * If both the element id and the pinch bounding area are provided then the
 * area is effectively ignored.
 * @param {number} [left] The left coordinate of the pinch bounding area.
 * @param {number} [top] The top coordinate of the pinch bounding area.
 * @param {number} [width] The width of the pinch bounding area.
 * @param {number} [height] The height of the pinch bounding area.
 * @param {number} [speed] The speed at which to perform this gesture in pixels per second.
 * The value must not be negative.
 * Default is 2500 * displayDensity.
 * @returns {Promise<void>}
 * @throws {Error} if provided options are not valid
 */
export function mobilePinchOpenGesture(this: import("../driver").AndroidUiautomator2Driver, percent: number, elementId?: string, left?: number, top?: number, width?: number, height?: number, speed?: number): Promise<void>;
/**
 * Performs a swipe gesture.
 * @this {AndroidUiautomator2Driver}
 * @param {string} direction Direction of the swipe.
 * Acceptable values are: `up`, `down`, `left` and `right` (case insensitive).
 * @param {number} percent The size of the swipe as a percentage of the swipe area size.
 * Valid values must be float numbers in range 0..1, where 1.0 is 100%.
 * @param {string} [elementId] The id of the element to be swiped.
 * If the element id is missing then swipe bounding area must be provided.
 * If both the element id and the swipe bounding area are provided then the
 * area is effectively ignored.
 * @param {number} [left] The left coordinate of the swipe bounding area.
 * @param {number} [top] The top coordinate of the swipe bounding area.
 * @param {number} [width] The width of the swipe bounding area.
 * @param {number} [height] The height of the swipe bounding area.
 * @param {number} [speed] The speed at which to perform this gesture in pixels per second.
 * The value must not be negative.
 * Default is 5000 * displayDensity.
 * @returns {Promise<void>}
 * @throws {Error} if provided options are not valid
 */
export function mobileSwipeGesture(this: import("../driver").AndroidUiautomator2Driver, direction: string, percent: number, elementId?: string, left?: number, top?: number, width?: number, height?: number, speed?: number): Promise<void>;
/**
 * Performs a scroll gesture.
 *
 * @throws {Error} if provided options are not valid
 * @this {AndroidUiautomator2Driver}
 * @param {string} direction Direction of the scroll.
 * Acceptable values are: `up`, `down`, `left` and `right` (case insensitive).
 * @param {number} percent The size of the scroll as a percentage of the scrolling area size.
 * Valid values must be float numbers greater than zero, where 1.0 is 100%.
 * @param {string} [elementId] The id of the element to be scrolled.
 * If the element id is missing then scroll bounding area must be provided.
 * If both the element id and the scroll bounding area are provided then this
 * area is effectively ignored.
 * @param {number} [left] The left coordinate of the scroll bounding area.
 * @param {number} [top] The top coordinate of the scroll bounding area.
 * @param {number} [width] The width of the scroll bounding area.
 * @param {number} [height] The height of the scroll bounding area.
 * @param {number} [speed] The speed at which to perform this gesture in pixels per second.
 * The value must not be negative.
 * Default is 5000 * displayDensity.
 * @returns {Promise<boolean>} True if the object can still scroll in the given direction.
 */
export function mobileScrollGesture(this: import("../driver").AndroidUiautomator2Driver, direction: string, percent: number, elementId?: string, left?: number, top?: number, width?: number, height?: number, speed?: number): Promise<boolean>;
/**
 * Scrolls the given scrollable element `elementId` until `elementToId`
 * becomes visible. This function returns immediately if the `elementToId`
 * is already visible in the view port. Otherwise it would scroll
 * to the very beginning of the scrollable control and tries to reach the destination element
 * by scrolling its parent to the end step by step. The scroll direction (vertical or horizontal)
 * is detected automatically.
 * @this {AndroidUiautomator2Driver}
 * @param {string} elementId The identifier of the scrollable element, which is going to be scrolled.
 * It is required this element is a valid scrollable container and it was located
 * by `-android uiautomator` strategy.
 * @param {string} elementToId The identifier of the item, which belongs to the scrollable element above,
 * and which should become visible after the scrolling operation is finished.
 * It is required this element was located by `-android uiautomator` strategy.
 * @returns {Promise<void>}
 * @throws {Error} if the scrolling operation cannot be performed
 */
export function mobileScrollBackTo(this: import("../driver").AndroidUiautomator2Driver, elementId: string, elementToId: string): Promise<void>;
/**
 * Scrolls the given scrollable element until the element identified
 * by `strategy` and `selector` becomes visible. This function returns immediately if the
 * destination element is already visible in the view port. Otherwise it would scroll
 * to the very beginning of the scrollable control and tries to reach the destination element
 * by scrolling its parent to the end step by step. The scroll direction (vertical or horizontal)
 * is detected automatically.
 *
 * @this {AndroidUiautomator2Driver}
 * @param {string} strategy The following strategies are supported:
 * - `accessibility id` (UiSelector().description)
 * - `class name` (UiSelector().className)
 * - `-android uiautomator` (UiSelector)
 * @param {string} selector The corresponding lookup value for the given strategy.
 * @param {string} [elementId] The identifier of an element. It is required this element is a valid scrollable container
 * and it was located by `-android uiautomator` strategy.
 * If this property is not provided then the first currently available scrollable view
 * is selected for the interaction.
 * @param {number} [maxSwipes] The maximum number of swipes to perform on the target scrollable view in order to reach
 * the destination element. In case this value is unset then it would be retrieved from the
 * scrollable element itself (via `getMaxSearchSwipes()` property).
 * @returns {Promise<void>}
 * @throws {Error} if the scrolling operation cannot be performed
 */
export function mobileScroll(this: import("../driver").AndroidUiautomator2Driver, strategy: string, selector: string, elementId?: string, maxSwipes?: number): Promise<void>;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
//# sourceMappingURL=gestures.d.ts.map