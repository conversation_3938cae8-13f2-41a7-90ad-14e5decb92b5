{"version": 3, "file": "general-commands.js", "sourceRoot": "", "sources": ["../../../lib/tools/general-commands.js"], "names": [], "mappings": ";;;;;AAaA,4DAGC;AAOD,4BAEC;AAOD,8BAEC;AAOD,oCAEC;AAOD,wCAQC;AAUD,kCA4BC;AAQD,8CAGC;AASD,wCAeC;AAOD,oBAGC;AAOD,4BAGC;AAMD,gCAEC;AAQD,0BAUC;AAUD,8BAEC;AAWD,oCA6BC;AAsBD,oCAeC;AAgBD,gEAKC;AAWD,sEAOC;AAWD,wCAyBC;AArVD,4CAAmC;AACnC,oDAAuB;AACvB,6CAA2C;AAC3C,+CAAgD;AAGhD;;;;;;GAMG;AACI,KAAK,UAAU,wBAAwB;IAC5C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC1D,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,QAAQ;IAC5B,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AACtC,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,SAAS;IAC7B,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACvC,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,YAAY;IAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;AAC1C,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,cAAc;IAClC,IAAI,CAAC;QACH,EAAC,6CAA6C,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU;YACvE,MAAM,YAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACrC,CAAC;IAAC,MAAM,CAAC;QACP,MAAM,IAAI,KAAK,CAAC,2DAA2D;YACzE,8DAA8D,CAAC,CAAC;IACpE,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,WAAW;IAC/B,IAAI,CAAC,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QACjC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YACvE,IAAI,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YAE9C,iDAAiD;YACjD,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACpC,2GAA2G;YAC3G,MAAM,YAAY,GAAG,QAAQ,GAAG,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC;YAC/D,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE,CAAC;gBACtF,eAAG,CAAC,KAAK,CAAC,sBAAsB,QAAQ,CAAC,WAAW,EAAE,wBAAwB,QAAQ,0BAA0B,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;gBAChI,QAAQ,EAAE,CAAC;YACb,CAAC;YAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,eAAG,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YACjD,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,qCAAqC,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CACb,mDAAmD,EAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CACvF,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO,qBAAqB,CAAA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/C,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,iBAAiB;IACrC,IAAI,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/C,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5B,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,cAAc,CAAE,MAAM,GAAG,GAAG;IAChD,oDAAoD;IACpD,eAAG,CAAC,KAAK,CAAC,kBAAkB,MAAM,aAAa,CAAC,CAAC;IACjD,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACjB,OAAO;IACT,CAAC;IACD,IAAI,IAAI,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAChC,8EAA8E;QAC9E,6CAA6C;QAC7C,iFAAiF;QACjF,yFAAyF;QACzF,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACzB,CAAC;IACD,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,IAAI;IACxB,eAAG,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACtC,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,QAAQ;IAC5B,eAAG,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACtC,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAED;;;GAGG;AACH,SAAgB,UAAU;IACxB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,OAAO;IAC3B,IAAI,CAAC;QACH,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAC7B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACpD,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IACpE,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,SAAS,CAAE,OAAO,GAAG,MAAM;IAC/C,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,EAAE,EAAC,OAAO,EAAC,CAAC,CAAC;AACtD,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,YAAY,CAAE,WAAW,EAAE,OAAO,GAAG,EAAE;IACrD,MAAM,GAAG,GAAG,CAAC,cAAc,CAAC,CAAC;IAC7B,MAAM,EACJ,SAAS,EACT,OAAO,EACP,SAAS,EACT,SAAS,GACV,GAAG,OAAO,CAAC;IACZ,IAAI,cAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IAChC,CAAC;IACD,IAAI,cAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC;IAC3C,CAAC;IACD,IAAI,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC3B,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IACvC,CAAC;IACD,IAAI,SAAS,EAAE,CAAC;QACd,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1B,CAAC;IACD,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAEtB,MAAM,OAAO,GAAG;QACd,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW;QAC9B,OAAO;QACP,GAAG,GAAG;KACP,CAAC;IACF,eAAG,CAAC,KAAK,CAAC,4DAA4D,cAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7F,OAAO,IAAI,yBAAU,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACvD,CAAC;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACI,KAAK,UAAU,YAAY;IAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB;WAC1C,gBAAC,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrF,IAAI,CAAC;QACH,OAAO,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;aACpC,KAAK,CAAC,KAAK,CAAC;aACZ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aACpB,MAAM,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,+CAA+C,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,IAAI,gBAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAAE,CAAC;YAC9C,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,GAAG,CAAC;IACZ,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;;GAaG;AACI,KAAK,UAAU,0BAA0B;IAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC1C,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IACtE,OAAO,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC;WAC3C,CAAC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnD,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,6BAA6B;IACjD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IACzC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,cAAI,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC;WAC3D,CAAC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AACxD,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,cAAc,CAAE,SAAS;IAC7C,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IAC7E,+CAA+C;IAC/C,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;IAC9D,IAAI,YAAY,EAAE,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAChC,CAAC;IACD,MAAM,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,YAAY,YAAY,EAAE,CAAC;IACnF,IAAI,MAAM,CAAC;IACX,IAAI,CAAC;QACH,CAAC,EAAC,MAAM,EAAC,GAAG,MAAM,IAAA,mBAAI,EACpB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAC,CACjE,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,+CAA+C,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,IAAI,KAAK,CACb,qBAAqB,YAAY,WAAW;YAC5C,oCAAoC;YACpC,UAAU,GAAG,CAAC,IAAI,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CACpG,CAAC;IACJ,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,qBAAqB,YAAY,mBAAmB,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}