{"name": "@appium/base-driver", "version": "9.18.0", "description": "Base driver class for Appium drivers", "keywords": ["automation", "javascript", "selenium", "webdriver", "ios", "android", "firefoxos", "testing"], "homepage": "https://appium.io", "bugs": {"url": "https://github.com/appium/appium/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/appium.git", "directory": "packages/base-driver"}, "license": "Apache-2.0", "author": "https://github.com/appium", "types": "./build/lib/index.d.ts", "directories": {"lib": "lib"}, "files": ["index.js", "index.d.ts", "lib", "static", "build", "tsconfig.json", "!build/tsconfig.tsbuildinfo"], "scripts": {"test": "run-p test:unit test:types", "test:e2e": "mocha --timeout 20s --slow 10s \"./test/e2e/**/*.spec.js\"", "test:smoke": "node ./index.js", "test:unit": "mocha \"./test/unit/**/*.spec.js\"", "test:types": "tsd"}, "dependencies": {"@appium/support": "^6.1.1", "@appium/types": "^0.26.0", "@colors/colors": "1.6.0", "async-lock": "1.4.1", "asyncbox": "3.0.0", "axios": "1.9.0", "bluebird": "3.7.2", "body-parser": "1.20.3", "express": "4.21.2", "fastest-levenshtein": "1.0.16", "http-status-codes": "2.3.0", "lodash": "4.17.21", "lru-cache": "10.4.3", "method-override": "3.0.0", "morgan": "1.10.0", "path-to-regexp": "8.2.0", "serve-favicon": "2.5.0", "source-map-support": "0.5.21", "type-fest": "4.41.0", "validate.js": "0.13.1"}, "optionalDependencies": {"spdy": "4.0.2"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "publishConfig": {"access": "public"}, "gitHead": "c8fe4412525f7e1fa237813cf83fe7d98f0125eb", "tsd": {"directory": "test/types"}}