{"name": "@wdio/mocha-framework", "version": "9.17.0", "description": "A WebdriverIO plugin. Adapter for Mocha testing framework.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/webdriverio/webdriverio/tree/main/packages/wdio-mocha-framework", "license": "MIT", "engines": {"node": ">=18.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/webdriverio/webdriverio.git", "directory": "packages/wdio-mocha-framework"}, "keywords": ["webdriver", "wdio", "wdio-reporter"], "bugs": {"url": "https://github.com/webdriverio/webdriverio/issues"}, "type": "module", "exports": {".": {"import": "./build/index.js", "types": "./build/index.d.ts"}, "./common": {"source": "./src/common.ts", "import": "./build/common.js"}, "./package.json": "./package.json"}, "types": "./build/index.d.ts", "typeScriptVersion": "3.8.3", "dependencies": {"@types/mocha": "^10.0.6", "@types/node": "^20.11.28", "@wdio/logger": "9.16.2", "@wdio/types": "9.16.2", "@wdio/utils": "9.17.0", "mocha": "^10.3.0"}, "publishConfig": {"access": "public"}, "gitHead": "796df04f0f5c744d5ef4bafd3759995fa4829d6f"}