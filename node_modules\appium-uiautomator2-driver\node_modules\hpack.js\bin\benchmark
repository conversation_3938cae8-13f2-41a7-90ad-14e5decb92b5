#!/usr/bin/env node
var hpack = require('../');

var options = {
  table: { size: 1024 }
};

var compressor = hpack.compressor.create(options);

var vector = [];
for (var i = 0; i < 1024; i++) {
  vector.push({
    name: 'kind-of-big-header-name__',
    value: 'not-so-small value yes!',
    huffman: true,
    neverIndex: true
  });
}
compressor.write(vector);
var input = compressor.read();

console.time('decompressor');
for (var i = 0; i < 2000; i++) {
  var decompressor = hpack.decompressor.create(options);

  decompressor.write(input);
  decompressor.execute();
  while (true) {
    var chunk = decompressor.read();
    if (!chunk)
      break;
  }
}
console.timeEnd('decompressor');
