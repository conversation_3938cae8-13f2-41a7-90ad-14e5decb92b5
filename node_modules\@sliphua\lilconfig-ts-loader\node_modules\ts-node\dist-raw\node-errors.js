exports.codes = {
  ERR_INPUT_TYPE_NOT_ALLOWED: createError<PERSON><PERSON>('ERR_INPUT_TYPE_NOT_ALLOWED'),
  ERR_INVALID_ARG_VALUE: createErrorCtor('ERR_INVALID_ARG_VALUE'),
  ERR_INVALID_MODULE_SPECIFIER: createErrorCtor('ERR_INVALID_MODULE_SPECIFIER'),
  ERR_INVALID_PACKAGE_CONFIG: createErrorCtor('ERR_INVALID_PACKAGE_CONFIG'),
  ERR_INVALID_PACKAGE_TARGET: createErrorCtor('ERR_INVALID_PACKAGE_TARGET'),
  ERR_MANIFEST_DEPENDENCY_MISSING: createErrorCtor('ERR_MANIFEST_DEPENDENCY_MISSING'),
  ERR_MODULE_NOT_FOUND: createErrorCtor('ERR_MODULE_NOT_FOUND'),
  ERR_PACKAGE_IMPORT_NOT_DEFINED: createErrorCtor('ERR_PACKAGE_IMPORT_NOT_DEFINED'),
  ERR_PACKAGE_PATH_NOT_EXPORTED: createErrorCtor('ERR_PACKAGE_PATH_NOT_EXPORTED'),
  ERR_UNSUPPORTED_DIR_IMPORT: createErrorCtor('ERR_UNSUPPORTED_DIR_IMPORT'),
  ERR_UNSUPPORTED_ESM_URL_SCHEME: createErrorCtor('ERR_UNSUPPORTED_ESM_URL_SCHEME'),
}

function createErrorCtor(name) {
  return class CustomError extends Error {
    constructor(...args) {
      super([name, ...args].join(' '))
    }
  }
}
