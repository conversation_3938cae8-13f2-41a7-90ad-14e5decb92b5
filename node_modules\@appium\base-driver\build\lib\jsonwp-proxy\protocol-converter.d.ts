export const COMMAND_URLS_CONFLICTS: {
    commandNames: string[];
    jsonwpConverter: (url: any) => any;
    w3cConverter: (url: any) => any;
}[];
export default ProtocolConverter;
export type ProxyFunction = (url: string, method: string, body?: import("@appium/types").HTTPBody) => Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
declare class ProtocolConverter {
    /**
     *
     * @param {ProxyFunction} proxyFunc
     * @param {import('@appium/types').AppiumLogger | null} [log=null]
     */
    constructor(proxyFunc: ProxyFunction, log?: import("@appium/types").AppiumLogger | null);
    proxyFunc: ProxyFunction;
    _downstreamProtocol: any;
    _log: import("@appium/types").AppiumLogger | null;
    get log(): import("@appium/types").AppiumLogger;
    set downstreamProtocol(value: any);
    get downstreamProtocol(): any;
    /**
     * W3C /timeouts can take as many as 3 timeout types at once, MJSONWP /timeouts only takes one
     * at a time. So if we're using W3C and proxying to MJSONWP and there's more than one timeout type
     * provided in the request, we need to do 3 proxies and combine the result
     *
     * @param {Object} body Request body
     * @return {Object[]} Array of W3C + MJSONWP compatible timeout objects
     */
    getTimeoutRequestObjects(body: any): any[];
    /**
     * Proxy an array of timeout objects and merge the result
     * @param {string} url Endpoint url
     * @param {string} method Endpoint method
     * @param {import('@appium/types').HTTPBody} body Request body
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    proxySetTimeouts(url: string, method: string, body: import("@appium/types").HTTPBody): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
    /**
     *
     * @param {string} url
     * @param {string} method
     * @param {import('@appium/types').HTTPBody} body
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    proxySetWindow(url: string, method: string, body: import("@appium/types").HTTPBody): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
    /**
     *
     * @param {string} url
     * @param {string} method
     * @param {import('@appium/types').HTTPBody} body
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    proxySetValue(url: string, method: string, body: import("@appium/types").HTTPBody): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
    /**
     *
     * @param {string} url
     * @param {string} method
     * @param {import('@appium/types').HTTPBody} body
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    proxySetFrame(url: string, method: string, body: import("@appium/types").HTTPBody): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
    /**
     *
     * @param {string} url
     * @param {string} method
     * @param {import('@appium/types').HTTPBody} body
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    proxyPerformActions(url: string, method: string, body: import("@appium/types").HTTPBody): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
    /**
     *
     * @param {string} url
     * @param {string} method
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    proxyReleaseActions(url: string, method: string): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
    /**
     * Handle "crossing" endpoints for the case
     * when upstream and downstream drivers operate different protocols
     *
     * @param {string} commandName
     * @param {string} url
     * @param {string} method
     * @param {import('@appium/types').HTTPBody} [body]
     * @returns {Promise<[import('@appium/types').ProxyResponse, import('@appium/types').HTTPBody]>}
     */
    convertAndProxy(commandName: string, url: string, method: string, body?: import("@appium/types").HTTPBody): Promise<[import("@appium/types").ProxyResponse, import("@appium/types").HTTPBody]>;
}
//# sourceMappingURL=protocol-converter.d.ts.map