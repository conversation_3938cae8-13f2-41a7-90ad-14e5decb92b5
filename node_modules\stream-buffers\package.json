{"name": "stream-buffers", "version": "3.0.3", "description": "Buffer-backed Streams for reading and writing.", "keywords": ["memory streams", "streams", "buffer streams"], "author": "Sam Day <<EMAIL>>", "main": "./lib/streambuffer.js", "engines": {"node": ">= 0.10.0"}, "devDependencies": {"chai": "^3.4.1", "eslint": "^8.38.0", "istanbul": "^0.4.0", "mocha": "^9.1.2"}, "license": "Unlicense", "repository": {"type": "git", "url": "https://github.com/samcday/node-stream-buffer.git"}, "scripts": {"test": "istanbul test _mocha", "lint": "eslint ."}}