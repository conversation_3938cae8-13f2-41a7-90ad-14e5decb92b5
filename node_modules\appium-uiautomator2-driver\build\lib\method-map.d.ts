export declare const newMethodMap: {
    readonly '/session/:sessionId/appium/device/get_clipboard': {
        readonly POST: {
            readonly command: "getClipboard";
            readonly payloadParams: {
                readonly optional: readonly ["contentType"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/timeouts/implicit_wait': {
        readonly POST: {
            readonly command: "implicitWait";
            readonly payloadParams: {
                readonly required: readonly ["ms"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/ime/available_engines': {
        readonly GET: {
            readonly command: "availableIMEEngines";
        };
    };
    readonly '/session/:sessionId/ime/active_engine': {
        readonly GET: {
            readonly command: "getActiveIMEEngine";
        };
    };
    readonly '/session/:sessionId/ime/activated': {
        readonly GET: {
            readonly command: "isIMEActivated";
        };
    };
    readonly '/session/:sessionId/ime/deactivate': {
        readonly POST: {
            readonly command: "deactivateIMEEngine";
        };
    };
    readonly '/session/:sessionId/ime/activate': {
        readonly POST: {
            readonly command: "activateIMEEngine";
            readonly payloadParams: {
                readonly required: readonly ["engine"];
            };
        };
    };
    readonly '/session/:sessionId/window/:windowhandle/size': {
        readonly GET: {
            readonly command: "getWindowSize";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/keys': {
        readonly POST: {
            readonly command: "keys";
            readonly payloadParams: {
                readonly required: readonly ["value"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/location': {
        readonly GET: {
            readonly command: "getLocation";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/location_in_view': {
        readonly GET: {
            readonly command: "getLocationInView";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/size': {
        readonly GET: {
            readonly command: "getSize";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/lock': {
        readonly POST: {
            readonly command: "lock";
            readonly payloadParams: {
                readonly optional: readonly ["seconds"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/unlock': {
        readonly POST: {
            readonly command: "unlock";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/is_locked': {
        readonly POST: {
            readonly command: "isLocked";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/start_recording_screen': {
        readonly POST: {
            readonly command: "startRecordingScreen";
            readonly payloadParams: {
                readonly optional: readonly ["options"];
            };
        };
    };
    readonly '/session/:sessionId/appium/stop_recording_screen': {
        readonly POST: {
            readonly command: "stopRecordingScreen";
            readonly payloadParams: {
                readonly optional: readonly ["options"];
            };
        };
    };
    readonly '/session/:sessionId/appium/performanceData/types': {
        readonly POST: {
            readonly command: "getPerformanceDataTypes";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/getPerformanceData': {
        readonly POST: {
            readonly command: "getPerformanceData";
            readonly payloadParams: {
                readonly required: readonly ["packageName", "dataType"];
                readonly optional: readonly ["dataReadTimeout"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/press_keycode': {
        readonly POST: {
            readonly command: "pressKeyCode";
            readonly payloadParams: {
                readonly required: readonly ["keycode"];
                readonly optional: readonly ["metastate", "flags"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/long_press_keycode': {
        readonly POST: {
            readonly command: "longPressKeyCode";
            readonly payloadParams: {
                readonly required: readonly ["keycode"];
                readonly optional: readonly ["metastate", "flags"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/finger_print': {
        readonly POST: {
            readonly command: "fingerprint";
            readonly payloadParams: {
                readonly required: readonly ["fingerprintId"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/send_sms': {
        readonly POST: {
            readonly command: "sendSMS";
            readonly payloadParams: {
                readonly required: readonly ["phoneNumber", "message"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/gsm_call': {
        readonly POST: {
            readonly command: "gsmCall";
            readonly payloadParams: {
                readonly required: readonly ["phoneNumber", "action"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/gsm_signal': {
        readonly POST: {
            readonly command: "gsmSignal";
            readonly payloadParams: {
                readonly required: readonly ["signalStrength"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/gsm_voice': {
        readonly POST: {
            readonly command: "gsmVoice";
            readonly payloadParams: {
                readonly required: readonly ["state"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/power_capacity': {
        readonly POST: {
            readonly command: "powerCapacity";
            readonly payloadParams: {
                readonly required: readonly ["percent"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/power_ac': {
        readonly POST: {
            readonly command: "powerAC";
            readonly payloadParams: {
                readonly required: readonly ["state"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/network_speed': {
        readonly POST: {
            readonly command: "networkSpeed";
            readonly payloadParams: {
                readonly required: readonly ["netspeed"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/keyevent': {
        readonly POST: {
            readonly command: "keyevent";
            readonly payloadParams: {
                readonly required: readonly ["keycode"];
                readonly optional: readonly ["metastate"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/current_activity': {
        readonly GET: {
            readonly command: "getCurrentActivity";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/current_package': {
        readonly GET: {
            readonly command: "getCurrentPackage";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/app_state': {
        readonly POST: {
            readonly command: "queryAppState";
            readonly payloadParams: {
                readonly required: readonly [readonly ["appId"], readonly ["bundleId"]];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/toggle_airplane_mode': {
        readonly POST: {
            readonly command: "toggleFlightMode";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/toggle_data': {
        readonly POST: {
            readonly command: "toggleData";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/toggle_wifi': {
        readonly POST: {
            readonly command: "toggleWiFi";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/toggle_location_services': {
        readonly POST: {
            readonly command: "toggleLocationServices";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/open_notifications': {
        readonly POST: {
            readonly command: "openNotifications";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/start_activity': {
        readonly POST: {
            readonly command: "startActivity";
            readonly payloadParams: {
                readonly required: readonly ["appPackage", "appActivity"];
                readonly optional: readonly ["appWaitPackage", "appWaitActivity", "intentAction", "intentCategory", "intentFlags", "optionalIntentArguments", "dontStopAppOnReset"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/system_bars': {
        readonly GET: {
            readonly command: "getSystemBars";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/display_density': {
        readonly GET: {
            readonly command: "getDisplayDensity";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/app/background': {
        readonly POST: {
            readonly command: "background";
            readonly payloadParams: {
                readonly required: readonly ["seconds"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/app/strings': {
        readonly POST: {
            readonly command: "getStrings";
            readonly payloadParams: {
                readonly optional: readonly ["language", "stringFile"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/element/:elementId/value': {
        readonly POST: {
            readonly command: "setValueImmediate";
            readonly payloadParams: {
                readonly required: readonly ["text"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/element/:elementId/replace_value': {
        readonly POST: {
            readonly command: "replaceValue";
            readonly payloadParams: {
                readonly required: readonly ["text"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/network_connection': {
        readonly GET: {
            readonly command: "getNetworkConnection";
            readonly deprecated: true;
        };
        readonly POST: {
            readonly command: "setNetworkConnection";
            readonly payloadParams: {
                readonly unwrap: "parameters";
                readonly required: readonly ["type"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/location': {
        readonly GET: {
            readonly command: "getGeoLocation";
            readonly deprecated: true;
        };
        readonly POST: {
            readonly command: "setGeoLocation";
            readonly payloadParams: {
                readonly required: readonly ["location"];
            };
            readonly deprecated: true;
        };
    };
};
//# sourceMappingURL=method-map.d.ts.map