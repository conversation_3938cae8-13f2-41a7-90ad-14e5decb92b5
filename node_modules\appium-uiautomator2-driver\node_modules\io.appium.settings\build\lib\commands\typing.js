"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.performEditorAction = performEditorAction;
exports.typeUnicode = typeUnicode;
const lodash_1 = __importDefault(require("lodash"));
const constants_js_1 = require("../constants.js");
const utf7_1 = require("./utf7");
const logger_js_1 = require("../logger.js");
/**
 * Performs the given editor action on the focused input field.
 * This method requires <PERSON>pp<PERSON> Settings helper to be installed on the device.
 * No exception is thrown if there was a failure while performing the action.
 * You must investigate the logcat output if something did not work as expected.
 *
 * @this {import('../client').SettingsApp}
 * @param {string|number} action - Either action code or name. The following action
 *                                 names are supported: `normal, unspecified, none,
 *                                 go, search, send, next, done, previous`
 */
async function performEditorAction(action) {
    this.log.debug(logger_js_1.LOG_PREFIX, `Performing editor action: ${action}`);
    await this.adb.runInImeContext(constants_js_1.APPIUM_IME, async () => await this.adb.shell(['input', 'text', `/${action}/`]));
}
/**
 * Types the given Unicode string.
 * It is expected that the focus is already put
 * to the destination input field before this method is called.
 *
 * @this {import('../client').SettingsApp}
 * @param {string} text The string to type
 * @returns {Promise<boolean>} `true` if the input text has been successfully sent to adb
 */
async function typeUnicode(text) {
    if (lodash_1.default.isNil(text)) {
        return false;
    }
    text = `${text}`;
    this.log.debug(logger_js_1.LOG_PREFIX, `Typing ${text.length} character${text.length === 1 ? '' : 's'}`);
    if (!text) {
        return false;
    }
    await this.adb.runInImeContext(constants_js_1.UNICODE_IME, async () => await this.adb.inputText(utf7_1.imap.encode(text)));
    return true;
}
//# sourceMappingURL=typing.js.map