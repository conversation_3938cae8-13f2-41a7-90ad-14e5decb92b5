{"name": "http-deceiver", "version": "1.2.7", "description": "Deceive HTTP parser", "main": "lib/deceiver.js", "scripts": {"test": "mocha --reporter=spec test/*-test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/indutny/http-deceiver.git"}, "keywords": ["http", "net", "deceive"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/http-deceiver/issues"}, "homepage": "https://github.com/indutny/http-deceiver#readme", "devDependencies": {"handle-thing": "^1.0.1", "mocha": "^2.2.5", "readable-stream": "^2.0.1", "stream-pair": "^1.0.0"}}