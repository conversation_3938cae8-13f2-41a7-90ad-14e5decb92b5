"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSettingsApkPath = getSettingsApkPath;
const node_path_1 = __importDefault(require("node:path"));
const node_fs_1 = __importDefault(require("node:fs"));
const MODULE_NAME = 'io.appium.settings';
/**
 * Tries to synchronously detect the absolute path to the folder
 * where the given `moduleName` is located.
 *
 * @returns {string} Full path to the module root
 */
function getModuleRootSync() {
    let currentDir = __dirname;
    let isAtFsRoot = false;
    while (!isAtFsRoot) {
        const manifestPath = node_path_1.default.join(currentDir, 'package.json');
        try {
            if (node_fs_1.default.existsSync(manifestPath) &&
                JSON.parse(node_fs_1.default.readFileSync(manifestPath, 'utf8')).name === MODULE_NAME) {
                return currentDir;
            }
        }
        catch { }
        currentDir = node_path_1.default.dirname(currentDir);
        isAtFsRoot = currentDir.length <= node_path_1.default.dirname(currentDir).length;
    }
    throw new Error(`Cannot find the root of the ${MODULE_NAME} Node.js module`);
}
/**
 * Get the full path to the Settings APK
 *
 * @returns {string}
 */
function getSettingsApkPath() {
    return node_path_1.default.resolve(getModuleRootSync(), 'apks', 'settings_apk-debug.apk');
}
//# sourceMappingURL=utils.js.map