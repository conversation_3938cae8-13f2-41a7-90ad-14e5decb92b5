{"name": "io.appium.settings", "version": "5.14.12", "description": "App for dealing with Android settings", "main": "./build/lib/index.js", "types": "./build/lib/index.d.ts", "scripts": {"bump-gradle-version": "node scripts/gradle-version-update.js --package-version=${npm_package_version} && git add app/build.gradle", "lint": "./gradlew lint", "js:lint": "eslint .", "js:lint:fix": "npm run lint -- --fix", "move-apks": "rm -rf apks && mkdir -p apks && cp app/build/outputs/apk/debug/settings_apk-debug.apk apks", "build": "tsc -b && ./gradlew clean assembleDebug && npm run move-apks", "prepare": "npm run build", "version": "npm run bump-gradle-version && npm run build", "clean": "npm run build -- --clean", "js:test": "mocha --exit --timeout 1m \"./test/unit/**/*-specs.js\""}, "repository": {"type": "git", "url": "https://github.com/appium/io.appium.settings.git"}, "engines": {"node": ">=14", "npm": ">=8"}, "keywords": ["appium", "android", "settings"], "files": ["lib", "build/lib", "apks", "!.DS_Store", "NOTICE.txt", "CHANGELOG.md"], "author": "Appium Contributors", "license": "Apache-2.0", "bugs": {"url": "https://github.com/appium/io.appium.settings/issues"}, "homepage": "https://github.com/appium/io.appium.settings", "dependencies": {"@appium/logger": "^1.3.0", "asyncbox": "^3.0.0", "bluebird": "^3.5.1", "lodash": "^4.2.1", "semver": "^7.5.4", "source-map-support": "^0.x", "teen_process": "^2.0.0"}, "devDependencies": {"@appium/test-support": "^3.0.1", "@appium/eslint-config-appium-ts": "^1.x", "@appium/tsconfig": "^0.x", "@appium/types": "^0.x", "@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@types/bluebird": "^3.5.38", "@types/lodash": "^4.14.196", "@types/node": "^24.0.0", "@types/teen_process": "^2.0.2", "appium-adb": "^12.4.0", "chai": "^5.1.1", "chai-as-promised": "^8.0.0", "conventional-changelog-conventionalcommits": "^9.0.0", "mocha": "^11.0.1", "prettier": "^3.0.0", "semantic-release": "^24.0.0", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}