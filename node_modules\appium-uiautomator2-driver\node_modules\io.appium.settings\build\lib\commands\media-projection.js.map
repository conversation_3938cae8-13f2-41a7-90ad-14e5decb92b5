{"version": 3, "file": "media-projection.js", "sourceRoot": "", "sources": ["../../../lib/commands/media-projection.js"], "names": [], "mappings": ";;;;;AA+JA,kEAEC;AASD,0FAMC;AAhLD,uCAA0C;AAC1C,wDAAyB;AACzB,oDAAuB;AACvB,2DAA6B;AAC7B,0DAA6B;AAC7B,4CAMsB;AAEtB,MAAM,4BAA4B,GAAG,CAAC,GAAG,IAAI,CAAC;AAC9C,MAAM,yBAAyB,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3C,MAAM,eAAe,GAAG,oCAAoC,8BAAkB,QAAQ,CAAC;AAEvF;;;;;;;;;;;;;;GAcG;AAEH,MAAM,uBAAuB;IAC3B;;OAEG;IACH,YAAY,GAAG;QACb,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAAS;QACb,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAClC,SAAS;YACT,UAAU;YACV,UAAU;YACV,kCAAsB;SACvB,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,QAAQ,CAAC,kCAAsB,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE;QACnB,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACrB,MAAM,EAAC,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAC,GAAG,IAAI,CAAC;QAC9D,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,mCAAuB,EAAE,IAAI,EAAE,kCAAsB,CAAC,CAAC;QAC1F,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,kBAAkB,EAAE,GAAG,cAAc,EAAE,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;QAC9C,CAAC;QACD,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,MAAM,IAAI,kBAAC,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9B,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpB,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC;oBAC9B,OAAO,MAAM,CACX,IAAI,KAAK,CACP,uDAAuD,4BAA4B,MAAM;wBACvF,kDAAkD,CACrD,CACF,CAAC;gBACJ,CAAC;gBACD,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,4BAA4B,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,SAAS,eAAe,IAAI,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU;QACd,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/D,IAAI,gBAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,mBAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,yFAAyF;QACzF,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,eAAe,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,EAAC,OAAO,EAAE,MAAM,EAAC,CAAC,CAAC;QACvF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YACnB,IAAI;YACJ,OAAO;YACP,IAAI;YACJ,mCAAuB;YACvB,IAAI;YACJ,iCAAqB;SACtB,CAAC,CAAC;QACH,IAAI,CAAC;YACH,MAAM,IAAA,2BAAgB,EAAC,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;gBAC5D,MAAM,EAAE,yBAAyB;gBACjC,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC;QACL,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,KAAK,CACb,6EAA6E;gBAC3E,GAAG,yBAAyB,IAAI,CACnC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED;;;;;;GAMG;AACH,SAAgB,2BAA2B;IACzC,OAAO,IAAI,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/C,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,uCAAuC;IAC3D,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC;QACvC,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,8BAAkB,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;QACtF,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG"}