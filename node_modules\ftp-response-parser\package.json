{"name": "ftp-response-parser", "id": "ftp-response-parser", "version": "1.0.1", "description": "Parser for FTP server responses", "keywords": ["ftp", "response", "protocol", "files", "server", "client", "async"], "author": "<PERSON><PERSON> <<EMAIL>> (http://sergimansilla.com)", "homepage": "https://github.com/sergi/ftp-response-parser", "repository": {"type": "git", "url": "https://github.com/sergi/ftp-response-parser.git"}, "bugs": {"url": "https://github.com/sergi/ftp-response-parser/issues"}, "dependencies": {"readable-stream": "^1.0.31"}, "devDependencies": {"tape": "~2.1.0", "resumer": "~0.0.0"}, "main": "./index.js", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test.js"}, "licenses": [{"type": "MIT", "url": "https://github.com/sergi/ftp-response-parser/blob/master/LICENSE"}]}