"use strict";
/**
 * Portions Copyright (c) 2017 <PERSON>
 * @see https://github.com/w3c-webdriver/w3c-webdriver
 * @module
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Key = void 0;
/**
 * @group Actions
 */
var Key;
(function (Key) {
    Key["NULL"] = "\uE000";
    Key["CANCEL"] = "\uE001";
    Key["HELP"] = "\uE002";
    Key["BACKSPACE"] = "\uE003";
    Key["TAB"] = "\uE004";
    Key["CLEAR"] = "\uE005";
    Key["RETURN"] = "\uE006";
    Key["ENTER"] = "\uE007";
    Key["SHIFT"] = "\uE008";
    Key["CONTROL"] = "\uE009";
    Key["ALT"] = "\uE00A";
    Key["PAUSE"] = "\uE00B";
    Key["ESCAPE"] = "\uE00C";
    Key["SPACE"] = "\uE00D";
    Key["PAGE_UP"] = "\uE00E";
    Key["PAGE_DOWN"] = "\uE00F";
    Key["END"] = "\uE010";
    Key["HOME"] = "\uE011";
    Key["LEFT"] = "\uE012";
    Key["UP"] = "\uE013";
    Key["RIGHT"] = "\uE014";
    Key["DOWN"] = "\uE015";
    Key["INSERT"] = "\uE016";
    Key["DELETE"] = "\uE017";
    Key["SEMICOLON"] = "\uE018";
    Key["EQUALS"] = "\uE019";
    Key["NUMPAD0"] = "\uE01A";
    Key["NUMPAD1"] = "\uE01B";
    Key["NUMPAD2"] = "\uE01C";
    Key["NUMPAD3"] = "\uE01D";
    Key["NUMPAD4"] = "\uE01E";
    Key["NUMPAD5"] = "\uE01F";
    Key["NUMPAD6"] = "\uE020";
    Key["NUMPAD7"] = "\uE021";
    Key["NUMPAD8"] = "\uE022";
    Key["NUMPAD9"] = "\uE023";
    Key["MULTIPLY"] = "\uE024";
    Key["ADD"] = "\uE025";
    Key["SEPARATOR"] = "\uE026";
    Key["SUBTRACT"] = "\uE027";
    Key["DECIMAL"] = "\uE028";
    Key["DIVIDE"] = "\uE029";
    Key["F1"] = "\uE031";
    Key["F2"] = "\uE032";
    Key["F3"] = "\uE033";
    Key["F4"] = "\uE034";
    Key["F5"] = "\uE035";
    Key["F6"] = "\uE036";
    Key["F7"] = "\uE037";
    Key["F8"] = "\uE038";
    Key["F9"] = "\uE039";
    Key["F10"] = "\uE03A";
    Key["F11"] = "\uE03B";
    Key["F12"] = "\uE03C";
    Key["META"] = "\uE03D";
    Key["ZENKAKUHANKAKU"] = "\uE040";
    Key["R_SHIFT"] = "\uE050";
    Key["R_CONTROL"] = "\uE051";
    Key["R_ALT"] = "\uE052";
    Key["R_META"] = "\uE053";
    Key["R_PAGEUP"] = "\uE054";
    Key["R_PAGEDOWN"] = "\uE055";
    Key["R_END"] = "\uE056";
    Key["R_HOME"] = "\uE057";
    Key["R_ARROWLEFT"] = "\uE058";
    Key["R_ARROWUP"] = "\uE059";
    Key["R_ARROWRIGHT"] = "\uE05A";
    Key["R_ARROWDOWN"] = "\uE05B";
    Key["R_INSERT"] = "\uE05C";
    Key["R_DELETE"] = "\uE05D";
})(Key || (exports.Key = Key = {}));
//# sourceMappingURL=action.js.map