{"name": "select-hose", "version": "2.0.0", "description": "Select protocol using first bytes of incoming data and hose stuff to the handler", "main": "lib/hose.js", "scripts": {"test": "jscs lib/*.js test/*.js && jshint lib/*.js && mocha --reporter=spec test/*-test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/indutny/select-hose.git"}, "keywords": ["hose", "select", "balance"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/select-hose/issues"}, "homepage": "https://github.com/indutny/select-hose#readme", "devDependencies": {"handle-thing": "^1.2.0", "jscs": "^1.13.1", "jshint": "^2.8.0", "mocha": "^2.2.5", "stream-pair": "^1.0.3"}}