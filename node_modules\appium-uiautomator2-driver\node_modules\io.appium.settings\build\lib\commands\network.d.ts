/**
 * Change the state of WiFi on the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @param {boolean} on - True to enable and false to disable it.
 * @param {boolean} [isEmulator=false] - Set it to true if the device under test
 *                                       is an emulator rather than a real device.
 */
export function setWifiState(this: import("../client").SettingsApp, on: boolean, isEmulator?: boolean): Promise<void>;
/**
 * Change the state of Data transfer on the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @param {boolean} on - True to enable and false to disable it.
 * @param {boolean} [isEmulator=false] - Set it to true if the device under test
 *                                       is an emulator rather than a real device.
 */
export function setDataState(this: import("../client").SettingsApp, on: boolean, isEmulator?: boolean): Promise<void>;
//# sourceMappingURL=network.d.ts.map