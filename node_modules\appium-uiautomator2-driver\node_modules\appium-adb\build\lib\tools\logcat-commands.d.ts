/**
 * Start the logcat process to gather logs.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').LogcatOpts} [opts={}]
 * @throws {Error} If restart fails.
 */
export function startLogcat(this: import("../adb.js").ADB, opts?: import("./types").LogcatOpts): Promise<void>;
export class startLogcat {
    /**
     * Start the logcat process to gather logs.
     *
     * @this {import('../adb.js').ADB}
     * @param {import('./types').LogcatOpts} [opts={}]
     * @throws {Error} If restart fails.
     */
    constructor(this: import("../adb.js").ADB, opts?: import("./types").LogcatOpts);
    logcat: Logcat;
    _logcatStartupParams: import("./types").LogcatOpts;
}
/**
 * Stop the active logcat process which gathers logs.
 * The call will be ignored if no logcat process is running.
 * @this {import('../adb.js').ADB}
 */
export function stopLogcat(this: import("../adb.js").ADB): Promise<void>;
export class stopLogcat {
    logcat: any;
}
/**
 * Retrieve the output from the currently running logcat process.
 * The logcat process should be executed by {2link #startLogcat} method.
 *
 * @this {import('../adb.js').ADB}
 * @return {import('./types').LogEntry[]} The collected logcat output.
 * @throws {Error} If logcat process is not running.
 */
export function getLogcatLogs(this: import("../adb.js").ADB): import("./types").LogEntry[];
/**
 * Set the callback for the logcat output event.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').LogcatListener} listener - Listener function
 * @throws {Error} If logcat process is not running.
 */
export function setLogcatListener(this: import("../adb.js").ADB, listener: import("./types").LogcatListener): void;
/**
 * Removes the previously set callback for the logcat output event.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').LogcatListener} listener
 * The listener function, which has been previously
 * passed to `setLogcatListener`
 * @throws {Error} If logcat process is not running.
 */
export function removeLogcatListener(this: import("../adb.js").ADB, listener: import("./types").LogcatListener): void;
import { Logcat } from '../logcat';
//# sourceMappingURL=logcat-commands.d.ts.map