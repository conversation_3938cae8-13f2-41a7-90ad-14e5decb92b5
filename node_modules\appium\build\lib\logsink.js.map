{"version": 3, "file": "logsink.js", "sourceRoot": "", "sources": ["../../lib/logsink.js"], "names": [], "mappings": ";;;;;AAoDA,oBAkDC;AAKD,sBAGC;AA4OD,0CAEC;AA5VD,4DAAuC;AACvC,qCAAyD;AACzD,6CAAmC;AACnC,oDAAuB;AACvB,mCAAkC;AAClC,yCAAqC;AAErC,oDAAoD;AACpD,MAAM,CAAC,cAAc,GAAG,gBAAS,CAAC;AAClC,8DAA8D;AAC9D,gBAAS,CAAC,KAAK,GAAG,MAAM,CAAC;AACzB,MAAM,UAAU,GAAG;IACjB,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACT,CAAC;AACF,MAAM,UAAU,GAAG;IACjB,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,MAAM;IACb,IAAI,EAAE,QAAQ;IACd,KAAK,EAAE,KAAK;CACb,CAAC;AACF,MAAM,qBAAqB,GAAG;IAC5B,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,OAAO;IAChB,KAAK,EAAE,OAAO;IACd,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,OAAO;CACf,CAAC;AACF,MAAM,kBAAkB,GAAG,yBAAyB,CAAC,CAAC,uCAAuC;AAE7F,4DAA4D;AAC5D,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,MAAM,SAAS,GAAG,GAAG,CAAC;AACtB,uCAAuC;AACvC,MAAM,YAAY,GAAG,IAAI,oBAAQ,CAAC;IAChC,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,wBAAwB;IAClD,cAAc,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,wCAAwC;AACxC,IAAI,GAAG,GAAG,IAAI,CAAC;AAEf;;;;GAIG;AACI,KAAK,UAAU,IAAI,CAAC,IAAI;IAC7B,gBAAS,CAAC,KAAK,GAAG,QAAQ,CAAC;IAE3B,4EAA4E;IAC5E,KAAK,EAAE,CAAC;IAER,MAAM,UAAU,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChD,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5E,GAAG,GAAG,IAAA,sBAAY,EAAC;QACjB,UAAU;QACV,MAAM,EAAE,UAAU;KACnB,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;IACvC,gEAAgE;IAChE,gBAAS,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,4BAA4B,CAAA,EAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAC,EAAE,EAAE;QAC3E,MAAM,EAAC,gBAAgB,EAAC,GAAG,gBAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;QACnE,uBAAuB;QACvB,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,gBAAgB,EAAE,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClC,CAAC;QACD,IAAI,MAAM,EAAE,CAAC;YACX,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,GAAG,GAAG,OAAO,CAAC;QAClB,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,QAAQ;iBACzB,GAAG,CAAC,iBAAiB,CAAC;iBACtB,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;iBACjE,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,GAAG,GAAG,GAAG,WAAW,IAAI,GAAG,EAAE,CAAC;QAChC,CAAC;QACD,MAAM,YAAY,GAAG,qBAAqB,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC;QAC5D,IAAI,CAAC;YACH,uCAAuC,CAAA,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;YAChE,IAAI,gBAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACpE,sCAAsC;gBACtC,OAAO,CAAC,KAAK,CACX,oBAAoB,gBAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAC,MAAM,EAAE,EAAE,EAAC,CAAC,2BAA2B;oBAC5E,uCAAuC,cAAc,qBAAqB,CAAC,CAAC,OAAO,EAAE,CACtF,CAAC;gBACF,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,KAAK;IACnB,GAAG,EAAE,KAAK,EAAE,CAAC;IACb,gBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC;AAED,wBAAwB;AACxB,MAAM,cAAc,GAAG,gBAAM,CAAC,QAAQ,CAAC;IACrC,MAAM,EAAE,UAAU;CACnB,CAAC,CAAC;AAEH,0CAA0C;AAC1C,MAAM,gBAAgB,GAAG,IAAA,gBAAM,EAAC,SAAS,UAAU,CAAC,IAAI;IACtD,OAAO;QACL,GAAG,IAAI;QACP,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC;QAClC,OAAO,EAAE,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;KACjF,CAAC;AACJ,CAAC,CAAC,EAAE,CAAC;AAEL;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,IAAI,EAAE,MAAM;IAC1C,OAAO,IAAI,oBAAU,CAAC,OAAO,CAAC;QAC5B,oDAAoD;QACpD,IAAI,EAAE,SAAS;QACf,gBAAgB,EAAE,IAAI;QACtB,WAAW,EAAE,KAAK;QAClB,IAAI,EAAE,KAAK;QACX,KAAK,EAAE,MAAM;QACb,YAAY,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,EAAE,gBAAM,CAAC,OAAO,CACpB,eAAe,CAAC,IAAI,CAAC,EACrB,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,gBAAgB,EAC3D,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CACtB;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAAC,IAAI,EAAE,MAAM;IACvC,OAAO,IAAI,oBAAU,CAAC,IAAI,CAAC;QACzB,oDAAoD;QACpD,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI,CAAC,OAAO;QACtB,QAAQ,EAAE,CAAC;QACX,gBAAgB,EAAE,IAAI;QACtB,WAAW,EAAE,KAAK;QAClB,IAAI,EAAE,KAAK;QACX,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,gBAAM,CAAC,OAAO,CACpB,gBAAgB,EAChB,eAAe,CAAC,IAAI,CAAC,EACrB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CACvB;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAAC,IAAI,EAAE,MAAM;IACvC,IAAI,IAAI,GAAG,WAAW,CAAC;IACvB,IAAI,IAAI,GAAG,IAAI,CAAC;IAEhB,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACtC,CAAC;IAED,OAAO,IAAI,oBAAU,CAAC,IAAI,CAAC;QACzB,oDAAoD;QACpD,IAAI,EAAE,MAAM;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI,EAAE,GAAG;QACT,gBAAgB,EAAE,IAAI;QACtB,WAAW,EAAE,KAAK;QAClB,IAAI,EAAE,KAAK;QACX,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,gBAAM,CAAC,OAAO,CACpB,gBAAgB,EAChB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CACvB;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,gBAAgB,CAAC,IAAI;IAClC,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,qBAAqB;IACrB,IAAI,eAAe,CAAC;IACpB,qBAAqB;IACrB,IAAI,YAAY,CAAC;IACjB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;QAC9C,wGAAwG;QACxG,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,CAAC,eAAe,EAAE,YAAY,CAAC,GAAG,OAAO,CAAC;IAC5C,CAAC;SAAM,CAAC;QACN,eAAe,GAAG,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;IACjD,CAAC;IAED,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC;IAE/D,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,IAAI,CAAC;YACH,iGAAiG;YACjG,+FAA+F;YAC/F,gFAAgF;YAChF,IAAI,MAAM,YAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,MAAM,YAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChC,CAAC;YAED,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,sCAAsC;YACtC,OAAO,CAAC,GAAG,CACT,oCAAoC,IAAI,CAAC,OAAO,iBAAiB,GAAG,aAAa,CAAC,CAAC,OAAO,EAAE,CAC7F,CAAC;QACJ,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,IAAI,CAAC;YACH,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,sCAAsC;YACtC,OAAO,CAAC,GAAG,CACT,sCAAsC,IAAI,CAAC,OAAO,OAAO;gBACvD,sBAAsB,CAAC,CAAC,OAAO,EAAE,CACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,IAAI;IAC7B,OAAO,IAAI,IAAI,GAAG,CAAC;AACrB,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,cAAc,CAAC,IAAI;IAC1B,IAAI,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,MAAM,IAAI,GAAG,IAAA,eAAO,EAAC,IAAI,CAAC,CAAC;QAC3B,UAAU,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;QACxD,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACrC,CAAC;IACD,OAAO,aAAa,UAAU,IAAI,IAAI,SAAS,CAAC;AAClD,CAAC;AAED;;;;GAIG;AACH,SAAS,SAAS,CAAC,IAAI,EAAE,aAAa;IACpC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QACrD,OAAO,gBAAM,CAAC,OAAO,CACnB,IAAA,gBAAM,EAAC,CAAC,IAAI,EAAE,EAAE;YACd,MAAM,QAAQ,GAAG,EAAC,GAAG,IAAI,EAAC,CAAC;YAC3B,MAAM,WAAW,GAAG,gBAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;YAE5D,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACxC,OAAO,QAAQ,CAAC,SAAS,CAAC;YAC5B,CAAC;YAED,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5B,QAAQ,CAAC,OAAO,GAAG,EAAC,GAAG,WAAW,EAAC,CAAC;YACtC,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,EAAE,EACJ,gBAAM,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,SAAS,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAC,CAAC,CACvE,CAAC;IACJ,CAAC;IAED,OAAO,gBAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;QAC5B,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7E,CAAC;QACD,OAAO,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7C,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAS,eAAe,CAAC,IAAI;IAC3B,OAAO,gBAAM,CAAC,SAAS,CAAC;QACtB,MAAM;YACJ,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YACtB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,KAAK,CAAC,CAAC;YACrE,CAAC;YACD,0DAA0D;YAC1D,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7E,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,IAAI;IAClC,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;AAC9C,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,IAAI;IAC7B,OAAO,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC;AACxD,CAAC;AAED,kBAAe,IAAI,CAAC;AAEpB;;;GAGG"}