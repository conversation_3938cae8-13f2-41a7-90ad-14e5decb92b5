{"version": 3, "file": "doctor.js", "sourceRoot": "", "sources": ["../../lib/doctor.js"], "names": [], "mappings": ";;;AAMA,gBAEC;AAQD,kBAEC;AAQD,gCAEC;AAQD,kCAEC;AAtCD;;;;;GAKG;AACH,SAAgB,EAAE,CAAC,OAAO;IACxB,OAAO,EAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAC,CAAC;AAC9C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,GAAG,CAAC,OAAO;IACzB,OAAO,EAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAC,CAAC;AAC/C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,UAAU,CAAC,OAAO;IAChC,OAAO,EAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAC,CAAC;AAC7C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,OAAO;IACjC,OAAO,EAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAC,CAAC;AAC9C,CAAC;AAED;;;GAGG;AACH,MAAa,eAAgB,SAAQ,KAAK;CAAG;AAA7C,0CAA6C;AAE7C;;GAEG"}