{"name": "portscanner", "description": "Asynchronous port scanner for Node.js", "scripts": {"coverage": "nyc npm run test", "test": "ava", "lint": "standard"}, "keywords": ["portscanner", "port", "scanner", "checker", "status"], "version": "2.2.0", "preferGlobal": false, "homepage": "https://github.com/baalexander/node-portscanner", "author": ["<PERSON> <<EMAIL>> (https://github.com/baalexander)", "<PERSON> <<EMAIL>> (http://massalabs.com)"], "repository": {"type": "git", "url": "git://github.com/baalexander/node-portscanner.git"}, "bugs": {"url": "https://github.com/baalexander/node-portscanner/issues"}, "directories": {"lib": "./lib"}, "main": "./lib/portscanner.js", "dependencies": {"async": "^2.6.0", "is-number-like": "^1.0.3"}, "devDependencies": {"ava": "^0.4.2", "nyc": "^11.3.0", "eslint": "^3.10.2", "eslint-config-standard": "^6.2.1", "standard": "^8.5.0"}, "engines": {"node": ">=0.4", "npm": ">=1.0.0"}, "license": "MIT"}