{"version": 3, "file": "driver.js", "sourceRoot": "", "sources": ["../../../lib/basedriver/driver.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAqC;AACrC,yCAkBuB;AACvB,wDAAyB;AACzB,oDAAuB;AACvB,0DAA2D;AAC3D,gDAAiD;AACjD,0CAA8E;AAC9E,iDAAiE;AACjE,iCAAkC;AAClC,mDAAqC;AAErC,MAAM,kBAAkB,GAAG,qBAAqB,CAAC;AACjD,MAAM,mBAAmB,GAAG,mBAAmB,CAAC;AAChD,MAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,MAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,MAAM,4BAA4B,GAAG,sBAAsB,CAAC;AAE5D,MAAa,UAQX,SAAQ,iBAAuB;IAY/B,YAAY,IAAiB,EAAE,kBAAkB,GAAG,IAAI;QACtD,KAAK,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,GAAG,EAAmB,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,EAAwB,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG;IACH,IAAc,sBAAsB;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,gBAAC,CAAC,KAAK,CAAC,EAAE,EAAE,oCAA4B,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,cAAc,CAAc,GAAW,EAAE,GAAG,IAAW;QAC3D,4DAA4D;QAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;YAC5B,mGAAmG;YACnG,IAAI,CAAC,QAAQ,GAAG,IAAA,4BAAiB,EAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QACpC,CAAC;aAAM,IAAI,GAAG,KAAK,iCAAsB,EAAE,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;QAC1C,CAAC;QAED,sEAAsE;QACtE,8CAA8C;QAC9C,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,MAAM,IAAI,iBAAM,CAAC,iBAAiB,CAAC,wCAAwC,CAAC,CAAC;QAC/E,CAAC;QAED,4DAA4D;QAC5D,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACpC,MAAM,IAAI,iBAAM,CAAC,sBAAsB,EAAE,CAAC;QAC5C,CAAC;QAED,MAAM,iBAAiB,GAAG,KAAK,IAAI,EAAE;YACnC,IAAI,0BAA0B,GAAmC,IAAI,CAAC;YACtE,IAAI,0BAA0B,GAAmC,IAAI,CAAC;YACtE,IAAI,8BAA8B,GAAG,KAAK,CAAC;YAC3C,MAAM,oBAAoB,GAAG,CAAC,CAAQ,EAAE,EAAE;gBACxC,8BAA8B,GAAG,IAAI,CAAC;gBACtC,0BAA0B,EAAE,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC;YACF,IAAI,CAAC;gBACH,OAAO,MAAM,kBAAC,CAAC,IAAI,CAAC;oBAClB,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;oBAClB,4DAA4D;oBAC5D,uDAAuD;oBACvD,IAAI,kBAAC,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBACxB,0BAA0B,GAAG,OAAO,CAAC;wBACrC,0BAA0B,GAAG,MAAM,CAAC;wBACpC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,4BAA4B,EAAE,oBAAoB,CAAC,CAAC;oBAC7E,CAAC,CAAC;iBACH,CAAC,CAAC;YACL,CAAC;oBAAS,CAAC;gBACT,IAAI,0BAA0B,IAAI,0BAA0B,EAAE,CAAC;oBAC7D,yCAAyC;oBACzC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,4BAA4B,EAAE,oBAAoB,CAAC,CAAC;oBACrF,0BAA0B,GAAG,IAAI,CAAC;oBAClC,+CAA+C;oBAC/C,0BAA0B,EAAE,EAAE,CAAC;oBAC/B,0BAA0B,GAAG,IAAI,CAAC;gBACpC,CAAC;gBAED,uEAAuE;gBACvE,sEAAsE;gBACtE,yEAAyE;gBACzE,0EAA0E;gBAC1E,uEAAuE;gBACvE,gBAAgB;gBAChB,IAAI,CAAC,8BAA8B,IAAI,IAAI,CAAC,sBAAsB,IAAI,GAAG,KAAK,iCAAsB,EAAE,CAAC;oBACrG,6BAA6B;oBAC7B,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,kBAAkB,GAAG,UAAU,CAAC,IAAI,CAAC;QAC3C,wCAAwC;QACxC,MAAM,gBAAgB,GAAW,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,kBAAkB,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC;QACtG,IAAI,IAAI,CAAC,sBAAsB,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,mBAAmB,GAAG,oBAAoB,IAAI,CAAC,WAAW,CAAC,IAAI,mBAAmB;gBAClF,GAAG,cAAI,CAAC,SAAS,CAAC,YAAY,EAAE,gBAAgB,EAAE,IAAI,CAAC,IAAI,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;gBACnG,gCAAgC,CACjC,CAAC;QACJ,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB;YACrC,CAAC,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;YAC9E,CAAC,CAAC,MAAM,iBAAiB,EAAE,CAAC;QAE9B,4CAA4C;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAC,CAAC,CAAC;QAC5D,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;YAC5B,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;QACrC,CAAC;aAAM,IAAI,GAAG,KAAK,iCAAsB,EAAE,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,MAAa,IAAI,iBAAM,CAAC,iBAAiB,CAAC,wCAAwC,CAAC;QAEnF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC,CAAC,kCAAkC;QAC7F,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QACpC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,wCAAwC;QACxC,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpC,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,OAAO,CAAC,4BAA4B;QAEnE,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;YAC1C,IAAI,CAAC,GAAG,CAAC,IAAI,CACX,kCAAkC;gBAChC,GAAG,IAAI,CAAC,mBAAmB,GAAG,MAAM,wBAAwB,CAC/D,CAAC;YACF,MAAM,YAAY,GAChB,yBAAyB;gBACzB,GAAG,IAAI,CAAC,mBAAmB,GAAG,MAAM,WAAW;gBAC/C,iDAAiD;gBACjD,wCAAwC,CAAC;YAC3C,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;QAC9D,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC/B,CAAC;IAED,YAAY,CAAC,MAAoB,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QACzE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAE7C,mBAAmB;QACnB,MAAM,aAAa,GAAG,EAAE,CAAC;QACzB,KAAK,MAAM,QAAQ,IAAI;YACrB,gBAAgB;YAChB,qBAAqB;YACrB,WAAW;YACX,2BAA2B;SAC5B,EAAE,CAAC;YACF,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;gBAAS,CAAC;YACT,wBAAwB;YACxB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACpB,CAAC;QACH,CAAC;QACD,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,aAAa,CACjB,gBAAkC,EAClC,gBAAmC,EACnC,eAAkC;IAClC,6DAA6D;IAC7D,UAAyB;QAEzB,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YAC5B,MAAM,IAAI,iBAAM,CAAC,sBAAsB,CACrC,sDAAsD,CACvD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QAEjB,MAAM,YAAY,GAAG,gBAAC,CAAC,SAAS,CAC9B,CAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAAC,wBAAS,CAAC,CACtE,CAAC;QACF,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,iBAAM,CAAC,sBAAsB,CACrC,qDAAqD;gBACnD,yFAAyF,CAC5F,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,KAAK,CACZ,2CAA2C,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CACnF,CAAC;QAEF,IAAI,IAAmB,CAAC;QACxB,IAAI,CAAC;YACH,IAAI,GAAG,IAAA,kCAAmB,EACxB,YAAY,EACZ,IAAI,CAAC,sBAAsB,EAC3B,IAAI,CAAC,kBAAkB,CACP,CAAC;YACnB,IAAI,GAAG,IAAA,sBAAO,EAAC,IAAI,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,GAAG,CAAkB,CAAC;QAC/E,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,iBAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,CAAC,SAAS,GAAG,cAAI,CAAC,MAAM,EAAE,CAAC;QAC/B,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,oEAAoE;QACpE,IAAI,CAAC,IAAI,GAAG,EAAC,GAAG,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAC,CAAC;QAE7D,mBAAmB;QACnB,uEAAuE;QACvE,yEAAyE;QACzE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CACb,0DAA0D;gBACxD,oDAAoD;gBACpD,mDAAmD,CACtD,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAC9B,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;QACjE,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;QAEnE,oEAAoE;QACpE,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YACrE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,gBAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC,mBAAmB,GAAI,IAAI,CAAC,IAAI,CAAC,iBAA4B,GAAG,IAAI,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;YAC1B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,gBAAgB,EAAE,IAAA,uBAAa,EAAC,IAAI,CAAC,SAAS,CAAC;SAChD,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,oCAAoC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAEpE,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAiB,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,WAAW;QACf,MAAM,GAAG,GAA0B,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,GAAG,CAAC,IAAI,CAAC;gBACP,EAAE,EAAE,IAAI,CAAC,SAAS;gBAClB,YAAY,EAAE,IAAI,CAAC,IAAI;aACxB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,UAAU;QACd,OAAO,CACL,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CACxC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,4BAA4B;QAChC,OAAO,EAAC,YAAY,EAAE,IAAI,CAAC,IAAI,EAAC,CAAC;IACnC,CAAC;IAED,6DAA6D;IAC7D,KAAK,CAAC,aAAa,CAAC,SAAyB;QAC3C,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACpC,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE,CAAC;YACpE,wDAAwD;YACxD,+BAA+B;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC9C,KAAK,MAAM,GAAG,IAAI,gBAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;IAED,YAAY,CAAC,IAAqB;QAChC,MAAM,SAAS,GAAG,gBAAC,CAAC,UAAU,CAAC,gBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,gBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;QAClF,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;YACzF,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,IAAS;QAC3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,IAAA,2BAAY,EAAC,IAAI,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAC/B,IAAI,iBAAM,CAAC,sBAAsB,CAC/B,uDAAuD;gBACrD,wBAAwB,CAAC,CAAC,OAAO,EAAE,CACtC,CACF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAExB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,WAAqB;QACxC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,mDAAmD,CAAC,CAAC;QACzF,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,gDAAgD,CAAC,CAAC;QACtF,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;CACF;AAvZD,gCAuZC;AAED,6CAA2B;AAE3B,kBAAe,UAAU,CAAC"}