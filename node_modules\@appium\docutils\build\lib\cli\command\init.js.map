{"version": 3, "file": "init.js", "sourceRoot": "", "sources": ["../../../../lib/cli/command/init.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;AAEH,oDAAuB;AAEvB,qCAAgC;AAChC,yCAAuC;AACvC,qCAAqC;AACrC,oCAA2C;AAE3C,MAAM,GAAG,GAAG,IAAA,kBAAS,EAAC,MAAM,CAAC,CAAC;AAE9B,IAAK,gBAIJ;AAJD,WAAK,gBAAgB;IACnB,6CAAyB,CAAA;IACzB,2CAAuB,CAAA;IACvB,yDAAqC,CAAA;AACvC,CAAC,EAJI,gBAAgB,KAAhB,gBAAgB,QAIpB;AAED;;;;GAIG;AACH,MAAM,IAAI,GAAG;IACX,SAAS,EAAE;QACT,WAAW,EAAE,kBAAkB;QAC/B,KAAK,EAAE,gBAAgB,CAAC,MAAM;QAC9B,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ;KAClB;IACD,GAAG,EAAE;QACH,OAAO,EAAE,GAAG;QACZ,kBAAkB,EAAE,qBAAqB;QACzC,WAAW,EAAE,sBAAsB;QACnC,KAAK,EAAE,gBAAgB,CAAC,KAAK;QAC7B,SAAS,EAAE,IAAI;QACf,IAAI,EAAE,QAAQ;KACf;IACD,SAAS,EAAE;QACT,QAAQ,EAAE,iDAAiD;QAC3D,KAAK,EAAE,gBAAgB,CAAC,QAAQ;QAChC,IAAI,EAAE,SAAS;KAChB;IACD,KAAK,EAAE;QACL,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,mCAAmC;QAC7C,KAAK,EAAE,gBAAgB,CAAC,QAAQ;QAChC,IAAI,EAAE,SAAS;KAChB;IACD,OAAO,EAAE;QACP,KAAK,EAAE,GAAG;QACV,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,CAAC,KAAwB,EAAE,EAAE,CAAC,gBAAC,CAAC,SAAS,CAAC,KAAK,CAAC;QACxD,WAAW,EAAE,4CAA4C;QACzD,KAAK,EAAE,CAAC;QACR,KAAK,EAAE,gBAAgB,CAAC,MAAM;QAC9B,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ;KAClB;IACD,MAAM,EAAE;QACN,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,6BAA6B;QAC1C,KAAK,EAAE,gBAAgB,CAAC,QAAQ;QAChC,IAAI,EAAE,SAAS;KAChB;IACD,YAAY,EAAE;QACZ,kBAAkB,EAAE,cAAc;QAClC,WAAW,EAAE,oCAAoC;QACjD,KAAK,EAAE,gBAAgB,CAAC,MAAM;QAC9B,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ;KAClB;IACD,cAAc,EAAE;QACd,kBAAkB,EAAE,gBAAgB;QACpC,QAAQ,EAAE,+BAA+B;QACzC,KAAK,EAAE,gBAAgB,CAAC,KAAK;QAC7B,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;KACf;IACD,MAAM,EAAE;QACN,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,uCAAuC;QACpD,KAAK,EAAE,gBAAgB,CAAC,QAAQ;QAChC,IAAI,EAAE,SAAS;KAChB;IACD,aAAa,EAAE;QACb,kBAAkB,EAAE,sBAAsB;QAC1C,WAAW,EAAE,6BAA6B;QAC1C,KAAK,EAAE,gBAAgB,CAAC,KAAK;QAC7B,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ;KAClB;IACD,WAAW,EAAE;QACX,kBAAkB,EAAE,2BAA2B;QAC/C,WAAW,EAAE,8BAA8B;QAC3C,KAAK,EAAE,gBAAgB,CAAC,MAAM;QAC9B,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ;KAClB;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,qBAAqB;QACzC,WAAW,EAAE,6BAA6B;QAC1C,KAAK,EAAE,gBAAgB,CAAC,MAAM;QAC9B,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ;KAClB;IACD,kBAAkB,EAAE;QAClB,kBAAkB,EAAE,qBAAqB;QACzC,WAAW,EAAE,kBAAkB;QAC/B,KAAK,EAAE,gBAAgB,CAAC,MAAM;QAC9B,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ;KAClB;IACD,WAAW,EAAE;QACX,kBAAkB,EAAE,0BAA0B;QAC9C,WAAW,EAAE,cAAc;QAC3B,KAAK,EAAE,gBAAgB,CAAC,MAAM;QAC9B,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ;KAClB;IACD,eAAe,EAAE;QACf,kBAAkB,EAAE,iBAAiB;QACrC,QAAQ,EAAE,uCAAuC;QACjD,KAAK,EAAE,gBAAgB,CAAC,QAAQ;QAChC,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,YAAY;KACtB;IACD,UAAU,EAAE;QACV,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,gCAAgC;QAC7C,KAAK,EAAE,gBAAgB,CAAC,QAAQ;QAChC,IAAI,EAAE,SAAS;KAChB;IACD,OAAO,EAAE;QACP,KAAK,EAAE,IAAI;QACX,QAAQ,EAAE,iDAAiD;QAC3D,KAAK,EAAE,gBAAgB,CAAC,QAAQ;QAChC,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,OAAO;QAClB,OAAO,EAAE,QAAQ;KAClB;CACyC,CAAC;AAI7C,kBAAe;IACb,OAAO,EAAE,MAAM;IACf,QAAQ,EAAE,uCAAuC;IACjD,OAAO,CAAC,KAAK;QACX,OAAO,KAAK;aACT,OAAO,CAAC,IAAI,CAAC;aACb,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAA,yBAAiB,EAAC,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IAClF,CAAC;IACD,KAAK,CAAC,OAAO,CAAC,IAAI;QAChB,MAAM,IAAI,GAAG,IAAA,gBAAS,EAAC,MAAM,CAAC,CAAC;QAC/B,MAAM,IAAA,WAAI,EAAC,EAAC,GAAG,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAC,CAAC,CAAC;QAC5D,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;IACrC,CAAC;CACoC,CAAC"}