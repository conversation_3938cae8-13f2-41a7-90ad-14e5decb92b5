import WikipediaPage from '../pageobjects/wikipedia.page.js'
import { expect } from '@wdio/globals'

describe('Wikipedia App - Mobile Testing Challenge', () => {

    before(async () => {
        // Create screenshots directory if it doesn't exist
        await driver.execute('mobile: shell', {
            command: 'mkdir',
            args: ['-p', '/sdcard/screenshots']
        }).catch(() => {
            console.log('Screenshots directory creation skipped (may already exist)')
        })
    })

    beforeEach(async () => {
        console.log('\n=== Starting Test Case ===')

        // Wait for the app to load
        await WikipediaPage.waitForPageLoad()

        // Skip onboarding if it appears
        await WikipediaPage.skipOnboarding()

        // Take initial screenshot
        await WikipediaPage.takeScreenshot('app_launch')
    })

    /**
     * CHALLENGE REQUIREMENT TEST:
     * - Launch app ✓
     * - Navigate to screen with interactive element ✓
     * - Perform user interaction (search) ✓
     * - Verify UI element appears (search results) ✓
     * - Take screenshot ✓
     */
    it('should search for a topic and display results with screenshot', async () => {
        const searchTerm = 'Selenium WebDriver'

        console.log(`Step 1: Performing search for "${searchTerm}"`)

        // Perform search interaction
        await WikipediaPage.search(searchTerm)

        // Take screenshot after search input
        await WikipediaPage.takeScreenshot('after_search_input')

        console.log('Step 2: Verifying search results appear')

        // Verify that search results appear (UI element verification)
        const resultsCount = await WikipediaPage.getSearchResultsCount()
        expect(resultsCount).toBeGreaterThan(0)
        console.log(`Found ${resultsCount} search results`)

        // Take screenshot of search results
        await WikipediaPage.takeScreenshot('search_results')

        console.log('Step 3: Selecting first search result')

        // Select first result to navigate to article
        const selectedResult = await WikipediaPage.selectFirstResult()
        console.log(`Selected result: ${selectedResult}`)

        console.log('Step 4: Verifying article page loaded')

        // Wait for article to load and verify
        await WikipediaPage.waitForArticleLoad()
        const articleTitle = await WikipediaPage.getArticleTitle()

        // Verify article title is displayed (final UI verification)
        expect(articleTitle).toBeTruthy()
        expect(articleTitle.length).toBeGreaterThan(0)
        console.log(`Article loaded successfully: "${articleTitle}"`)

        // Take final screenshot of the article
        await WikipediaPage.takeScreenshot('article_loaded')

        console.log('✅ Test completed successfully - All requirements met')
    })

    /**
     * Additional test for form interaction and UI verification
     */
    it('should interact with search form and verify UI elements', async () => {
        console.log('Step 1: Verifying search input is displayed')

        // Verify search input is displayed (form element)
        const searchInput = WikipediaPage.searchInput
        await expect(searchInput).toBeDisplayed()

        console.log('Step 2: Interacting with search form')

        // Fill form field with search term
        const searchTerm = 'Mobile Testing'
        await WikipediaPage.search(searchTerm)

        // Verify search input contains the entered text
        const inputValue = await searchInput.getValue()
        expect(inputValue).toContain(searchTerm)

        console.log('Step 3: Verifying search suggestions appear')

        // Verify UI elements appear after interaction
        const resultsCount = await WikipediaPage.getSearchResultsCount()
        expect(resultsCount).toBeGreaterThan(0)

        // Take screenshot of form interaction result
        await WikipediaPage.takeScreenshot('form_interaction_result')

        console.log('✅ Form interaction test completed successfully')
    })

    /**
     * Test for list scrolling and interaction
     */
    it('should scroll through search results list', async () => {
        console.log('Step 1: Performing search to get results list')

        await WikipediaPage.search('Android Testing')

        console.log('Step 2: Verifying list elements are displayed')

        // Verify list appears
        const resultsCount = await WikipediaPage.getSearchResultsCount()
        expect(resultsCount).toBeGreaterThan(0)

        // Take screenshot before scrolling
        await WikipediaPage.takeScreenshot('before_scroll')

        console.log('Step 3: Scrolling through the list')

        // Perform scroll interaction
        await driver.execute('mobile: scroll', {
            direction: 'down',
            percent: 0.5
        })

        // Take screenshot after scrolling
        await WikipediaPage.takeScreenshot('after_scroll')

        console.log('✅ List scrolling test completed successfully')
    })

    afterEach(async () => {
        console.log('=== Cleaning up after test ===')

        // Reset app state
        try {
            await WikipediaPage.navigateBack()
            await WikipediaPage.clearSearch()
        } catch (error) {
            console.log('Cleanup completed with minor issues (expected)')
        }

        // Take final cleanup screenshot
        await WikipediaPage.takeScreenshot('test_cleanup')

        console.log('=== Test Case Completed ===\n')
    })
})
