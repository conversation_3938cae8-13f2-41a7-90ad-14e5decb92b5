import { expect } from '@wdio/globals'
import WikipediaPage from '../pageobjects/wikipedia.page.js'

describe('Wikipedia App Tests', () => {
    beforeEach(async () => {
        // Wait for the app to load
        await WikipediaPage.waitForPageLoad()
        
        // Skip onboarding if it appears
        await WikipediaPage.skipOnboarding()
    })

    it('should be able to search for a topic', async () => {
        const searchTerm = 'Selenium'
        
        // Perform search
        await WikipediaPage.search(searchTerm)
        
        // Verify search results appear
        await WikipediaPage.selectFirstResult()
        
        // Add assertion to verify we're on the article page
        // This would depend on the specific elements available after navigation
        await driver.pause(2000) // Wait for page to load
        
        // You can add more specific assertions here based on the Wikipedia app structure
        console.log('Search test completed successfully')
    })

    it('should be able to access settings', async () => {
        // Open settings
        await WikipediaPage.openSettings()
        
        // Add assertions to verify settings page opened
        await driver.pause(2000)
        
        console.log('Settings test completed successfully')
    })

    it('should display search input on app launch', async () => {
        // Verify search input is displayed
        const searchInput = await WikipediaPage.searchInput
        await expect(searchInput).toBeDisplayed()
        
        console.log('Search input visibility test completed successfully')
    })

    afterEach(async () => {
        // Reset app state if needed
        try {
            await driver.back()
            await driver.back()
        } catch (error) {
            // Ignore navigation errors
        }
    })
})
