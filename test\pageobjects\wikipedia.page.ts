import { $ } from '@wdio/globals'

/**
 * Wikipedia App Page Object
 */
class WikipediaPage {
    /**
     * Define selectors using Page Object Model
     */
    get searchInput() {
        return $('id:search_src_text')
    }

    get searchContainer() {
        return $('id:search_container')
    }

    get searchResults() {
        return $$('id:page_list_item_title')
    }

    get firstSearchResult() {
        return $('id:page_list_item_title')
    }

    get searchSuggestions() {
        return $$('id:page_list_item_container')
    }

    get skipButton() {
        return $('id:fragment_onboarding_skip_button')
    }

    get continueButton() {
        return $('id:fragment_onboarding_forward_button')
    }

    get doneButton() {
        return $('id:fragment_onboarding_done_button')
    }

    get menuButton() {
        return $('~More options')
    }

    get settingsOption() {
        return $('android=new UiSelector().text("Settings")')
    }

    get articleTitle() {
        return $('id:view_page_title_text')
    }

    get articleContent() {
        return $('id:page_web_view')
    }

    get backButton() {
        return $('~Navigate up')
    }

    get clearSearchButton() {
        return $('id:search_close_btn')
    }

    /**
     * Page actions
     */
    async skipOnboarding() {
        try {
            // Handle multiple onboarding screens
            const maxAttempts = 5
            let attempts = 0

            while (attempts < maxAttempts) {
                try {
                    if (await this.skipButton.isDisplayed()) {
                        await this.skipButton.click()
                        console.log('Clicked skip button')
                        break
                    }
                    if (await this.continueButton.isDisplayed()) {
                        await this.continueButton.click()
                        console.log('Clicked continue button')
                    }
                    if (await this.doneButton.isDisplayed()) {
                        await this.doneButton.click()
                        console.log('Clicked done button')
                        break
                    }
                } catch (e) {
                    // Element not found, continue
                }
                attempts++
                await driver.pause(1000)
            }
        } catch (error) {
            console.log('Onboarding handling completed or not needed')
        }
    }

    async search(query: string) {
        await this.searchInput.waitForDisplayed({ timeout: 10000 })
        await this.searchInput.click()
        await this.searchInput.clearValue()
        await this.searchInput.setValue(query)

        // Wait for search suggestions to appear
        await driver.pause(2000)

        try {
            await driver.hideKeyboard()
        } catch (e) {
            console.log('Keyboard already hidden or not available')
        }
    }

    async selectFirstResult() {
        await this.firstSearchResult.waitForDisplayed({ timeout: 10000 })
        const resultText = await this.firstSearchResult.getText()
        console.log(`Selecting first result: ${resultText}`)
        await this.firstSearchResult.click()
        return resultText
    }

    async getSearchResultsCount() {
        await this.searchResults[0].waitForDisplayed({ timeout: 10000 })
        return await this.searchResults.length
    }

    async openMenu() {
        await this.menuButton.waitForDisplayed({ timeout: 5000 })
        await this.menuButton.click()
    }

    async openSettings() {
        await this.openMenu()
        await this.settingsOption.waitForDisplayed({ timeout: 5000 })
        await this.settingsOption.click()
    }

    async waitForPageLoad() {
        await this.searchInput.waitForDisplayed({ timeout: 15000 })
    }

    async waitForArticleLoad() {
        await this.articleTitle.waitForDisplayed({ timeout: 15000 })
    }

    async getArticleTitle() {
        await this.articleTitle.waitForDisplayed({ timeout: 10000 })
        return await this.articleTitle.getText()
    }

    async navigateBack() {
        try {
            await this.backButton.click()
        } catch (e) {
            await driver.back()
        }
    }

    async clearSearch() {
        try {
            if (await this.clearSearchButton.isDisplayed()) {
                await this.clearSearchButton.click()
            }
        } catch (e) {
            console.log('Clear search button not available')
        }
    }

    async takeScreenshot(filename: string) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        const screenshotPath = `./screenshots/${filename}_${timestamp}.png`
        await driver.saveScreenshot(screenshotPath)
        console.log(`Screenshot saved: ${screenshotPath}`)
        return screenshotPath
    }
}

export default new WikipediaPage()
