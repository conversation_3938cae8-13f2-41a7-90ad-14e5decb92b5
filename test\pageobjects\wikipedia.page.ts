import { $ } from '@wdio/globals'

/**
 * Wikipedia App Page Object
 */
class WikipediaPage {
    /**
     * Define selectors using Page Object Model
     */
    get searchInput() {
        return $('~Search Wikipedia')
    }

    get searchButton() {
        return $('id:search_src_text')
    }

    get firstSearchResult() {
        return $('id:page_list_item_title')
    }

    get skipButton() {
        return $('id:fragment_onboarding_skip_button')
    }

    get continueButton() {
        return $('id:fragment_onboarding_forward_button')
    }

    get doneButton() {
        return $('id:fragment_onboarding_done_button')
    }

    get menuButton() {
        return $('~More options')
    }

    get settingsOption() {
        return $('android=new UiSelector().text("Settings")')
    }

    /**
     * Page actions
     */
    async skipOnboarding() {
        try {
            if (await this.skipButton.isDisplayed()) {
                await this.skipButton.click()
            }
        } catch (error) {
            console.log('Skip button not found or already skipped onboarding')
        }
    }

    async search(query: string) {
        await this.searchInput.click()
        await this.searchInput.setValue(query)
        await driver.hideKeyboard()
    }

    async selectFirstResult() {
        await this.firstSearchResult.waitForDisplayed({ timeout: 10000 })
        await this.firstSearchResult.click()
    }

    async openMenu() {
        await this.menuButton.click()
    }

    async openSettings() {
        await this.openMenu()
        await this.settingsOption.click()
    }

    async waitForPageLoad() {
        await this.searchInput.waitForDisplayed({ timeout: 15000 })
    }
}

export default new WikipediaPage()
