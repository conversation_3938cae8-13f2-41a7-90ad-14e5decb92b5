/**
 * @typedef {Object} Location
 * @property {number|string} longitude - Valid longitude value.
 * @property {number|string} latitude - Valid latitude value.
 * @property {number|string|null} [altitude] - Valid altitude value.
 * @property {number|string|null} [satellites=12] - Number of satellites being tracked (1-12).
 * This value is ignored on real devices.
 * @property {number|string|null} [speed] - Valid speed value.
 * https://developer.android.com/reference/android/location/Location#setSpeed(float)
 * @property {number|string|null} [bearing] - Valid bearing value.
 * https://developer.android.com/reference/android/location/Location#setBearing(float)
 * @property {number|string|null} [accuracy] - Valid accuracy value.
 * https://developer.android.com/reference/android/location/Location#setAccuracy(float),
 * https://developer.android.com/reference/android/location/Criteria
 * Should be greater than 0.0 meters/second for real devices or 0.0 knots
 * for emulators.
 */
/**
 * Emulate geolocation coordinates on the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @param {Location} location - Location object. The `altitude` value is ignored
 * while mocking the position.
 * @param {boolean} [isEmulator=false] - Set it to true if the device under test
 *                                       is an emulator rather than a real device.
 */
export function setGeoLocation(this: import("../client").SettingsApp, location: Location, isEmulator?: boolean): Promise<void>;
/**
 * Get the current cached GPS location from the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @returns {Promise<Location>} The current location
 * @throws {Error} If the current location cannot be retrieved
 */
export function getGeoLocation(this: import("../client").SettingsApp): Promise<Location>;
/**
 * Sends an async request to refresh the GPS cache.
 * This feature only works if the device under test has
 * Google Play Services installed. In case the vanilla
 * LocationManager is used the device API level must be at
 * version 30 (Android R) or higher.
 *
 * @this {import('../client').SettingsApp}
 * @param {number} timeoutMs The maximum number of milliseconds
 * to block until GPS cache is refreshed. Providing zero or a negative
 * value to it skips waiting completely.
 *
 * @throws {Error} If the GPS cache cannot be refreshed.
 */
export function refreshGeoLocationCache(this: import("../client").SettingsApp, timeoutMs?: number): Promise<void>;
export type Location = {
    /**
     * - Valid longitude value.
     */
    longitude: number | string;
    /**
     * - Valid latitude value.
     */
    latitude: number | string;
    /**
     * - Valid altitude value.
     */
    altitude?: string | number | null | undefined;
    /**
     * - Number of satellites being tracked (1-12).
     * This value is ignored on real devices.
     */
    satellites?: string | number | null | undefined;
    /**
     * - Valid speed value.
     * https://developer.android.com/reference/android/location/Location#setSpeed(float)
     */
    speed?: string | number | null | undefined;
    /**
     * - Valid bearing value.
     * https://developer.android.com/reference/android/location/Location#setBearing(float)
     */
    bearing?: string | number | null | undefined;
    /**
     * - Valid accuracy value.
     * https://developer.android.com/reference/android/location/Location#setAccuracy(float),
     * https://developer.android.com/reference/android/location/Criteria
     * Should be greater than 0.0 meters/second for real devices or 0.0 knots
     * for emulators.
     */
    accuracy?: string | number | null | undefined;
};
//# sourceMappingURL=geolocation.d.ts.map