/**
 * Install multiple APKs with `install-multiple` option.
 * @this {AndroidUiautomator2Driver}
 * @param {string[]} apks The list of APKs to install. Each APK should be a path to a apk
 * or downloadable URL as HTTP/HTTPS.
 * @param {import('./types').InstallOptions} [options] Installation options.
 * @throws {Error} if an error occured while installing the given APKs.
 * @returns {Promise<void>}
 */
export function mobileInstallMultipleApks(this: import("../driver").AndroidUiautomator2Driver, apks: string[], options?: import("./types").InstallOptions): Promise<void>;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
export type StringRecord<T = any> = import("@appium/types").StringRecord<T>;
//# sourceMappingURL=app-management.d.ts.map