{"version": 3, "file": "device-settings.js", "sourceRoot": "", "sources": ["../../../lib/tools/device-settings.js"], "names": [], "mappings": ";;;;;AA0BA,8CAKC;AAaD,8CAMC;AAMD,oDAEC;AAMD,kDAEC;AAMD,gDAEC;AAMD,4DAEC;AAMD,0DAEC;AAMD,wDAEC;AAMD,4BAEC;AAMD,0CAEC;AASD,sCAOC;AASD,4CAQC;AAUD,oCAkBC;AAOD,0CAUC;AAYD,gCAEC;AAWD,gCAEC;AASD,kCAOC;AASD,gDAUC;AASD,oDAaC;AAQD,8DAOC;AA0CD,gDAaC;AAWD,8DAUC;AASD,8CAIC;AASD,4CAEC;AASD,0CAEC;AAaD,kDAiDC;AAWD,oCAUC;AAWD,oCAUC;AAYD,wDAWC;AAYD,4DASC;AASD,4CAKC;AAQD,0CAQC;AAQD,wCAMC;AASD,4BAWC;AAaD,sDAmBC;AAQD,4BAKC;AAQD,4BAGC;AAYD,sCAIC;AAmBD,8CAEC;AAQD,oDAGC;AA9sBD,4CAAmC;AACnC,oDAAuB;AACvB,uCAAyC;AACzC,6CAAuC;AACvC,wDAAyB;AAEzB,MAAM,oBAAoB,GAAG;IAC3B,yBAAyB;IACzB,4BAA4B;IAC5B,wBAAwB;CACzB,CAAC;AACF,MAAM,sBAAsB,GAAG;IAC7B,8BAA8B;IAC9B,0BAA0B;IAC1B,mBAAmB;CACpB,CAAC;AAEF;;;;;;;;GAQG;AACI,KAAK,UAAU,iBAAiB,CAAE,QAAQ;IAC/C,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;IACrD,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;IACxB,eAAG,CAAC,KAAK,CAAC,4BAA4B,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;IAC3D,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;;;;;;GAUG;AACI,KAAK,UAAU,iBAAiB,CAAE,IAAI,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE;IAC3D,MAAM,EAAC,UAAU,GAAG,IAAI,EAAC,GAAG,IAAI,CAAC;IACjC,eAAG,CAAC,KAAK,CAAC,4BAA4B,IAAI,SAAS,GAAG,GAAG,CAAC,CAAC;IAC3D,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE;QACvC,UAAU;KACX,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,oBAAoB;IACxC,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;AAC9D,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,mBAAmB;IACvC,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;AAC7D,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,kBAAkB;IACtC,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,wBAAwB;IAC5C,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,CAAC;AACpE,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,uBAAuB;IAC3C,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,sBAAsB;IAC1C,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;AAC3D,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,QAAQ;IAC5B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;AAC1D,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,eAAe;IACnC,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,CAAC;AACjE,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,aAAa;IACjC,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9C,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,8BAA8B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnE,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACxB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,gBAAgB;IACpC,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IACjD,IAAI,OAAO,GAAG,IAAI,MAAM,CAAC,iCAAiC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzE,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;QACnC,IAAI,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;QACpD,OAAO,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC;IACrD,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,YAAY,CAAE,SAAS,EAAE,SAAS;IACtD,IAAI,KAAK,GAAG,GAAG,SAAS,IAAI,SAAS,EAAE,CAAC;IACxC,IAAI,gBAAC,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,0DAA0D,KAAK,EAAE,CAAC,CAAC;IACrF,CAAC;IACD,IAAI,gBAAC,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,yDAAyD,KAAK,EAAE,CAAC,CAAC;IACpF,CAAC;IAED,iCAAiC;IACjC,MAAM,gBAAgB,GAAG;QACvB,CAAC,YAAY,EAAE,KAAK,CAAC;QACrB,CAAC,wBAAwB,EAAE,SAAS,CAAC;QACrC,CAAC,wBAAwB,EAAE,GAAG,SAAS,EAAE,CAAC;KAC3C,CAAC;IACF,KAAK,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,gBAAgB,EAAE,CAAC;QAC1D,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;IAC5D,CAAC;AACH,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,eAAe;IACnC,MAAM,gBAAgB,GAAG;QACvB,YAAY;QACZ,wBAAwB;QACxB,wBAAwB;QACxB,kCAAkC,CAAC,yFAAyF;KAC7H,CAAC;IACF,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;QACvC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC;AAED;;;;;;;;;GASG;AACI,KAAK,UAAU,UAAU,CAAE,SAAS,EAAE,OAAO,EAAE,KAAK;IACzD,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;AAC/E,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,UAAU,CAAE,SAAS,EAAE,OAAO;IAClD,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;AACnE,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,WAAW;IAC/B,eAAG,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACtC,IAAI,CAAC;QACH,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;IAC9D,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,2CAA2C,EAAC,oBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACnG,CAAC;AACH,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,kBAAkB;IACtC,eAAG,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IAC5C,IAAI,CAAC;QACH,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,CAAC;IAClE,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CACb,yCAAyC;YACzC,mBAAmB,EAAC,mBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CACvD,CAAC;IACJ,CAAC;AACH,CAAC;AAGD;;;;;GAKG;AACI,KAAK,UAAU,oBAAoB;IACxC,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;QAClC,qHAAqH;QACrH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,4BAA4B,CAAC,CAAC;QAC7E,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;aAC5B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aACpB,MAAM,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAED,iCAAiC;IACjC,OAAO,gBAAC,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,qBAAqB,CAAC,CAAC,EAAE,MAAM,CAAC;QACrF,CAAC,CAAC,CAAC,KAAK,CAAC;QACT,CAAC,CAAC,EAAE,CAAC;AACT,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,yBAAyB,CAAE,OAAO;IACtD,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;QAClC,qHAAqH;QACrH,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,4BAA4B,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAC3F,OAAO;IACT,CAAC;IACD,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAC5F,CAAC;AAED;;;;;GAKG;AACH,SAAS,oCAAoC,CAAE,CAAC;IAC9C,IAAI,gBAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,mDAAmD,CAAC,EAAE,CAAC;QAC/E,CAAC,CAAC,OAAO,GAAG,6EAA6E,CAAC,CAAC,OAAO,EAAE,CAAC;IACvG,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACI,KAAK,UAAU,kBAAkB,CAAE,KAAK,EAAE,WAAW,GAAG,KAAK;IAClE,IAAI,CAAC;QACH,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,uBAAuB,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACrG,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,oCAAoC,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC;QACD,eAAG,CAAC,IAAI,CACN,+BAA+B,sBAAsB,SAAS,KAAK,KAAK;YACxE,mBAAmB,GAAG,CAAC,OAAO,EAAE,CACjC,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,yBAAyB,CAAE,WAAW,GAAG,KAAK;IAClE,IAAI,CAAC;QACH,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/F,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,oCAAoC,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC;QACD,eAAG,CAAC,IAAI,CAAC,0BAA0B,sBAAsB,sBAAsB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IAChG,CAAC;AACH,CAAC;AAGD;;;;;GAKG;AACI,KAAK,UAAU,iBAAiB;IACrC,OAAO,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;QAClC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,IAAI,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC9E,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,gBAAgB;IACpC,OAAO,MAAM,IAAI,CAAC,mBAAmB,EAAE,IAAI,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAClF,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,eAAe;IACnC,OAAO,MAAM,IAAI,CAAC,kBAAkB,EAAE,IAAI,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAChF,CAAC;AAED;;;;;;;;;;GAUG;AACI,KAAK,UAAU,mBAAmB,CAAE,QAAQ,EAAE,OAAO,EAAE,MAAM;IAClE,MAAM,WAAW,GAAG,gBAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzC,MAAM,UAAU,GAAG,gBAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACvC,IAAI,CAAC,WAAW,IAAI,CAAC,UAAU,EAAE,CAAC;QAChC,eAAG,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAC7D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,UAAU,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;IAClD,MAAM,SAAS,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;IAChD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;IAC1C,OAAO,sBAAsB,CAAC,CAAC,MAAM,IAAA,wBAAa,EAAC,CAAC,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE;QACrE,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;YAClB,eAAG,CAAC,KAAK,CAAC,qBAAqB,UAAU,IAAI,SAAS,EAAE,CAAC,CAAC;YAC1D,IAAI,cAAc,CAAC;YACnB,IAAI,WAAW,EAAE,CAAC;gBAChB,cAAc,GAAG,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;gBAChE,eAAG,CAAC,KAAK,CAAC,oBAAoB,cAAc,EAAE,CAAC,CAAC;gBAChD,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,cAAc,EAAE,CAAC;oBACjD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,IAAI,aAAa,CAAC;YAClB,IAAI,UAAU,EAAE,CAAC;gBACf,aAAa,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC9D,eAAG,CAAC,KAAK,CAAC,mBAAmB,aAAa,EAAE,CAAC,CAAC;gBAC9C,IAAI,CAAC,WAAW,IAAI,SAAS,KAAK,aAAa,EAAE,CAAC;oBAChD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,OAAO,UAAU,KAAK,cAAc,IAAI,SAAS,KAAK,aAAa,CAAC;QACtE,CAAC;QACD,MAAM,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;QAClE,qBAAqB;QACrB,MAAM,cAAc,GAAG,MAAM;YAC3B,CAAC,CAAC,GAAG,UAAU,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,SAAS,EAAE;YACtD,CAAC,CAAC,GAAG,UAAU,IAAI,SAAS,EAAE,CAAC;QACjC,eAAG,CAAC,KAAK,CAAC,qBAAqB,cAAc,qBAAqB,YAAY,GAAG,CAAC,CAAC;QACnF,MAAM,eAAe,GAAG,IAAI,gBAAC,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAC,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACzG,MAAM,kBAAkB,GAAG,CAAC,qBAAqB,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9F,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/B,OAAO,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC;QACD,MAAM,cAAc,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,gBAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,gBAAC,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC;QACvG,IAAI,CAAC,WAAW,IAAI,UAAU,EAAE,CAAC;YAC/B,OAAO,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,YAAY,CAAE,EAAE,EAAE,UAAU,GAAG,KAAK;IACxD,IAAI,UAAU,EAAE,CAAC;QACf,2DAA2D;QAC3D,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE;YAC3D,UAAU,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;SAC1C,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;AAC3F,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,YAAY,CAAE,EAAE,EAAE,UAAU,GAAG,KAAK;IACxD,IAAI,UAAU,EAAE,CAAC;QACf,2DAA2D;QAC3D,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE;YAC3D,UAAU,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;SAC1C,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AACxE,CAAC;AAGD;;;;;;;;GAQG;AACI,KAAK,UAAU,sBAAsB;IAC1C,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;QAClC,gDAAgD;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,eAAG,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;IAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC;IACxE,OAAO,gBAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;SAC9B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,gBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3B,MAAM,CAAC,OAAO,CAAC,CAAC;AACrB,CAAC;AAED;;;;;;;;;GASG;AACI,KAAK,UAAU,wBAAwB,CAAE,GAAG,QAAQ;IACzD,IAAI,gBAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;QACzD,gDAAgD;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,eAAG,CAAC,IAAI,CAAC,UAAU,cAAI,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;IAC/G,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC7F,OAAO,IAAI,CAAC;AACd,CAAC;AAGD;;;;;GAKG;AACI,KAAK,UAAU,gBAAgB;IACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;IACnE,OAAO,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAClC,iCAAiC;IACjC,mGAAmG;AACrG,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,eAAe,CAAE,EAAE;IACvC,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;QAClC,kEAAkE;QAClE,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,OAAO;IACT,CAAC;IAED,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AACxF,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,cAAc,CAAE,EAAE;IACtC,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;IACrF,CAAC;IAED,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,mBAAmB,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AAC5E,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,QAAQ,CAAE,EAAE;IAChC,MAAM,EAAC,MAAM,EAAE,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE;QACnF,YAAY,EAAE,MAAM;KACrB,CAAC,CAAC;IACH,MAAM,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC;IAChC,eAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAClB,IAAI,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACvC,MAAM,IAAI,KAAK,CACb,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,uDAAuD,CACxF,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;;;;;;;GAUG;AACI,KAAK,UAAU,qBAAqB,CAAE,EAAE;IAC7C,MAAM,IAAI,GAAG;QACX,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,qCAAqC;QAC3C,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;KACvC,CAAC;IACF,IAAI,CAAC;QACH,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,+CAA+C,CAAA,CAAC,CAAC,CAAC,CAAC;QAC/D,gDAAgD;QAChD,IAAI,gBAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,mBAAmB,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAC,UAAU,EAAE,IAAI,EAAC,CAAC,CAAC;gBAC3C,OAAO;YACT,CAAC;YAAC,MAAM,CAAC,CAAA,CAAC;QACZ,CAAC;QACD,MAAM,GAAG,CAAC;IACZ,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,QAAQ;IAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IAC1D,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACpC,+BAA+B;IAC/B,2FAA2F;AAC7F,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,QAAQ;IAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC9D,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,CAAC;AAED;;;;;;;;;GASG;AACI,KAAK,UAAU,aAAa;IACjC,OAAO,CAAC,MAAM,kBAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAC1C,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAC9D,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnB,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACI,KAAK,UAAU,iBAAiB,CAAE,KAAK;IAC5C,MAAM,kBAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACpF,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,oBAAoB;IACxC,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;IACpD,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,4BAA4B;AAE5B;;;;;GAKG;AACH,SAAS,qBAAqB,CAAE,OAAO;IACrC,MAAM,CAAC,GAAG,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnD,OAAO,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/C,CAAC;AAED,aAAa"}