<script lang="ts">
let count = 0
const increment = () => {
    count += 1
}
</script>

<main id="root">
    <div>
        <a href="https://webdriver.io/docs/component-testing" target="_blank">
            <img src="https://webdriver.io/assets/images/robot-3677788dd63849c56aa5cb3f332b12d5.svg" className="logo"
                alt="WebdriverIO logo" />
        </a>
    </div>
    <h1>WebdriverIO Component Testing</h1>

    <div class="card">
        <button on:click={increment}>
            count is {count}
        </button>
        <p>
            Edit <code>src/Component.test.<%- answers.isUsingTypeScript ? `ts` : 'js' %></code> and save to test HMR
        </p>
    </div>

    <p class="read-the-docs">
        Click on the Vite and Svelte logos to learn more
    </p>
</main>

<style>
    .logo {
        height: 6em;
        padding: 1.5em;
        will-change: filter;
        transition: filter 300ms;
    }
    .logo:hover {
        filter: drop-shadow(0 0 2em #646cffaa);
    }
    .logo.svelte:hover {
        filter: drop-shadow(0 0 2em #ff3e00aa);
    }
    .read-the-docs {
        color: #888;
    }
</style>
