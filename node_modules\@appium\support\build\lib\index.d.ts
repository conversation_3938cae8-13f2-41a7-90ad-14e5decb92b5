import * as tempDir from './tempdir';
import * as system from './system';
import * as util from './util';
import { fs } from './fs';
import * as net from './net';
import * as plist from './plist';
import { mkdirp } from './mkdirp';
import * as logger from './logging';
import * as process from './process';
import * as zip from './zip';
import * as imageUtil from './image-util';
import * as mjpeg from './mjpeg';
import * as node from './node';
import * as timing from './timing';
import * as env from './env';
import * as console from './console';
import * as doctor from './doctor';
export { npm } from './npm';
declare const cancellableDelay: typeof util.cancellableDelay;
export { tempDir, system, util, fs, cancellableDelay, plist, mkdirp, logger, process, zip, imageUtil, net, mjpeg, node, timing, env, console, doctor, };
declare const _default: {
    tempDir: typeof tempDir;
    system: typeof system;
    util: typeof util;
    fs: {
        hasAccess(path: PathLike): Promise<boolean>;
        isExecutable(path: PathLike): Promise<boolean>;
        exists(path: PathLike): Promise<boolean>;
        rimraf(filepath: PathLike): Promise<void>;
        rimrafSync(filepath: PathLike): void;
        mkdir(filepath: string | Buffer | URL, opts?: import("fs").MakeDirectoryOptions): Promise<string | undefined>;
        copyFile(source: string, destination: string, opts?: import("ncp").Options): Promise<void>;
        md5(filePath: PathLike): Promise<string>;
        mv: (from: string, to: string, opts?: import("mv").Options) => import("bluebird")<void>;
        which: typeof import("which");
        glob: (pattern: string, opts?: import("glob").GlobOptions) => import("bluebird")<string[]>;
        sanitizeName: typeof import("sanitize-filename");
        hash(filePath: PathLike, algorithm?: string): Promise<string>;
        walk(dir: string, opts?: import("klaw").Options): import("klaw").Walker;
        mkdirp(dir: PathLike): Promise<string | undefined>;
        walkDir(dir: string, recursive: boolean, callback: WalkDirCallback): Promise<string | null>;
        readPackageJsonFrom(dir: string, opts?: import("read-pkg").Options): import("read-pkg").NormalizedPackageJson;
        findRoot(dir: string): string;
        access: typeof import("fs/promises").access;
        appendFile: typeof import("fs/promises").appendFile;
        chmod: typeof import("fs/promises").chmod;
        close: (arg1: number) => import("bluebird")<any>;
        constants: typeof import("fs").constants;
        createWriteStream: typeof import("fs").createWriteStream;
        createReadStream: typeof import("fs").createReadStream;
        lstat: typeof import("fs/promises").lstat;
        open: (path: import("./fs").PathLike, flags: import("fs").OpenMode, mode?: import("fs").Mode) => Promise<number>;
        openFile: typeof import("fs/promises").open;
        readdir: typeof import("fs/promises").readdir;
        read: import("./fs").ReadFn<NodeJS.ArrayBufferView>;
        readFile: typeof import("fs/promises").readFile;
        readlink: typeof import("fs/promises").readlink;
        realpath: typeof import("fs/promises").realpath;
        rename: typeof import("fs/promises").rename;
        stat: typeof import("fs/promises").stat;
        symlink: typeof import("fs/promises").symlink;
        unlink: typeof import("fs/promises").unlink;
        write: (arg1: number, arg2: string) => import("bluebird")<number>;
        writeFile: typeof import("fs/promises").writeFile;
        F_OK: number;
        R_OK: number;
        W_OK: number;
        X_OK: number;
    };
    cancellableDelay: typeof util.cancellableDelay;
    plist: typeof plist;
    mkdirp: (dir: PathLike) => Promise<string | undefined>;
    logger: typeof logger;
    process: typeof process;
    zip: typeof zip;
    imageUtil: typeof imageUtil;
    net: typeof net;
    mjpeg: typeof mjpeg;
    node: typeof node;
    timing: typeof timing;
    env: typeof env;
    console: typeof console;
    doctor: typeof doctor;
};
export default _default;
export type { ConsoleOpts } from './console';
export type { ReadFn, WalkDirCallback } from './fs';
export type { NetOptions, DownloadOptions, AuthCredentials, NotHttpUploadOptions, HttpUploadOptions, } from './net';
export type { InstallPackageOpts, ExecOpts, NpmInstallReceipt } from './npm';
export type { Affixes, OpenedAffixes } from './tempdir';
export type { PluralizeOptions, EncodingOptions, LockFileOptions, NonEmptyString } from './util';
export type { ExtractAllOptions, ZipEntry, ZipOptions, ZipCompressionOptions, ZipSourceOptions, } from './zip';
//# sourceMappingURL=index.d.ts.map