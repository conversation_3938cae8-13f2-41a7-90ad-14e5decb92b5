{"program": {"fileInfos": {"../node_modules/typescript/lib/lib.d.ts": {"version": "2dc8c927c9c162a773c6bb3cdc4f3286c23f10eedc67414028f9cb5951610f60", "signature": "2dc8c927c9c162a773c6bb3cdc4f3286c23f10eedc67414028f9cb5951610f60", "affectsGlobalScope": false}, "../node_modules/typescript/lib/lib.es5.d.ts": {"version": "c9a1f03d6ba0fe3c871eb0dd81622e78fbb61ade70878b34d48a341a690c59e9", "signature": "c9a1f03d6ba0fe3c871eb0dd81622e78fbb61ade70878b34d48a341a690c59e9", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.d.ts": {"version": "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "signature": "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "affectsGlobalScope": false}, "../node_modules/typescript/lib/lib.es2016.d.ts": {"version": "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "signature": "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "affectsGlobalScope": false}, "../node_modules/typescript/lib/lib.es2017.d.ts": {"version": "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "signature": "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "affectsGlobalScope": false}, "../node_modules/typescript/lib/lib.es2018.d.ts": {"version": "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "signature": "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "affectsGlobalScope": false}, "../node_modules/typescript/lib/lib.dom.d.ts": {"version": "38130cdd16bd2318b9362f9d60dd9670f7e38708bb6131cf11fc78a41b2c34a0", "signature": "38130cdd16bd2318b9362f9d60dd9670f7e38708bb6131cf11fc78a41b2c34a0", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.webworker.importscripts.d.ts": {"version": "7fac8cb5fc820bc2a59ae11ef1c5b38d3832c6d0dfaec5acdb5569137d09a481", "signature": "7fac8cb5fc820bc2a59ae11ef1c5b38d3832c6d0dfaec5acdb5569137d09a481", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.scripthost.d.ts": {"version": "097a57355ded99c68e6df1b738990448e0bf170e606707df5a7c0481ff2427cd", "signature": "097a57355ded99c68e6df1b738990448e0bf170e606707df5a7c0481ff2427cd", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.core.d.ts": {"version": "46ee15e9fefa913333b61eaf6b18885900b139867d89832a515059b62cf16a17", "signature": "46ee15e9fefa913333b61eaf6b18885900b139867d89832a515059b62cf16a17", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.collection.d.ts": {"version": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "signature": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.generator.d.ts": {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "signature": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.iterable.d.ts": {"version": "42f5e41e5893da663dbf0394268f54f1da4b43dc0ddd2ea4bf471fe5361d6faf", "signature": "42f5e41e5893da663dbf0394268f54f1da4b43dc0ddd2ea4bf471fe5361d6faf", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.promise.d.ts": {"version": "0b7a905675e6cb4211c128f0a3aa47d414b275180a299a9aad5d3ec298abbfc4", "signature": "0b7a905675e6cb4211c128f0a3aa47d414b275180a299a9aad5d3ec298abbfc4", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.proxy.d.ts": {"version": "dfff68b3c34338f6b307a25d4566de15eed7973b0dc5d69f9fde2bcac1c25315", "signature": "dfff68b3c34338f6b307a25d4566de15eed7973b0dc5d69f9fde2bcac1c25315", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.reflect.d.ts": {"version": "cb609802a8698aa28b9c56331d4b53f590ca3c1c3a255350304ae3d06017779d", "signature": "cb609802a8698aa28b9c56331d4b53f590ca3c1c3a255350304ae3d06017779d", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.symbol.d.ts": {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "signature": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts": {"version": "4670208dd7da9d6c774ab1b75c1527a810388c7989c4905de6aaea8561cb9dce", "signature": "4670208dd7da9d6c774ab1b75c1527a810388c7989c4905de6aaea8561cb9dce", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2016.array.include.d.ts": {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "signature": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2017.object.d.ts": {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "signature": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts": {"version": "b5e4c2d67aa844ed503b29cd4ca2ede1a229ac7fe874613b2c996fa9c581a25f", "signature": "b5e4c2d67aa844ed503b29cd4ca2ede1a229ac7fe874613b2c996fa9c581a25f", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2017.string.d.ts": {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "signature": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2017.intl.d.ts": {"version": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "signature": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts": {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "signature": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts": {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "signature": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts": {"version": "a40c4d82bf13fcded295ac29f354eb7d40249613c15e07b53f2fc75e45e16359", "signature": "a40c4d82bf13fcded295ac29f354eb7d40249613c15e07b53f2fc75e45e16359", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2018.intl.d.ts": {"version": "df9c8a72ca8b0ed62f5470b41208a0587f0f73f0a7db28e5a1272cf92537518e", "signature": "df9c8a72ca8b0ed62f5470b41208a0587f0f73f0a7db28e5a1272cf92537518e", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2018.promise.d.ts": {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "signature": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2018.regexp.d.ts": {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "signature": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.es2020.bigint.d.ts": {"version": "7b5a10e3c897fabece5a51aa85b4111727d7adb53c2734b5d37230ff96802a09", "signature": "7b5a10e3c897fabece5a51aa85b4111727d7adb53c2734b5d37230ff96802a09", "affectsGlobalScope": true}, "../node_modules/typescript/lib/lib.esnext.intl.d.ts": {"version": "89bf2b7a601b73ea4311eda9c41f86a58994fec1bee3b87c4a14d68d9adcdcbd", "signature": "89bf2b7a601b73ea4311eda9c41f86a58994fec1bee3b87c4a14d68d9adcdcbd", "affectsGlobalScope": true}, "../src/find-package.ts": {"version": "a70cae3955aa4f22ffb4fda26ff6d2f0e37f3e8e8f049c65a59a824d3f721ea8", "signature": "008ed984b7f57ea15f8d5636218690b3d4c54e7d3ab559de64904f3e00dee65c", "affectsGlobalScope": false}, "../src/get-package-hash.ts": {"version": "e7386b70471418e25baf92baeeb1627f5e8b526041cdf353e41363fa10caf784", "signature": "f57d3a9bc7c97df2823448a104f21fce9ec0ef48c86b9b55fa1d003453c32f89", "affectsGlobalScope": false}, "../src/get-packagelock-hash.ts": {"version": "0c0972f24f6e19d10805841cc310a64f7e90bc7e8eabda5232c74dfa756da49b", "signature": "f772c5fcc2aa44a61dafd355a4c69af119762c5adc30151520dc252d4879d570", "affectsGlobalScope": false}, "../src/get-packagelock.ts": {"version": "c06ed510445c58b41225e8ce86c7cd3ee6d7fd1aba0f99fced64b6c667f95091", "signature": "43bf1d212cb891f6751d686f68a3ee6651f33db41731a7cfdc45061422fbb100", "affectsGlobalScope": false}, "../src/is-package-changed.ts": {"version": "903cbba2e36bcaed5d6c93f9bda57933415563fae7b80ff9ed0f58740410162d", "signature": "7375c4070c250792059930f831038f95b307f091ec534c235224173abf0443c6", "affectsGlobalScope": false}, "../src/index.ts": {"version": "8fd5484c91ed34cf0aabccb1f8f222077a96d6b40ec4b4d10b41985e8d9ab890", "signature": "b383680e4aa4fcc240c7e6f0e670e73e54694e877f094cdfc171d5e308e87721", "affectsGlobalScope": false}, "../node_modules/@babel/types/lib/index.d.ts": {"version": "cd5a5fd244fa901f9c331e45b833ea5a4efa162acdc129c6e52e5d74e924752b", "signature": "cd5a5fd244fa901f9c331e45b833ea5a4efa162acdc129c6e52e5d74e924752b", "affectsGlobalScope": false}, "../node_modules/@types/babel__generator/index.d.ts": {"version": "b25c5f2970d06c729f464c0aeaa64b1a5b5f1355aa93554bb5f9c199b8624b1e", "signature": "b25c5f2970d06c729f464c0aeaa64b1a5b5f1355aa93554bb5f9c199b8624b1e", "affectsGlobalScope": false}, "../node_modules/@types/babel__traverse/index.d.ts": {"version": "ef91066d2057cca50511e3af2d4aa54e07913a15f9cee1748f256139318eb413", "signature": "ef91066d2057cca50511e3af2d4aa54e07913a15f9cee1748f256139318eb413", "affectsGlobalScope": false}, "../node_modules/@babel/parser/typings/babel-parser.d.ts": {"version": "8678956904af215fe917b2df07b6c54f876fa64eb1f8a158e4ff38404cef3ff4", "signature": "8678956904af215fe917b2df07b6c54f876fa64eb1f8a158e4ff38404cef3ff4", "affectsGlobalScope": false}, "../node_modules/@types/babel__template/index.d.ts": {"version": "3e0a34f7207431d967dc32d593d1cda0c23975e9484bc8895b39d96ffca4a0d8", "signature": "3e0a34f7207431d967dc32d593d1cda0c23975e9484bc8895b39d96ffca4a0d8", "affectsGlobalScope": false}, "../node_modules/@types/babel__core/index.d.ts": {"version": "a66e700ed470a0cb52d14f3376c1605c70fec8e9659e45f7e22ad07fcd06ae04", "signature": "a66e700ed470a0cb52d14f3376c1605c70fec8e9659e45f7e22ad07fcd06ae04", "affectsGlobalScope": false}, "../node_modules/@types/node/globals.d.ts": {"version": "1332704d6761c94b85753fc3cadc36109f53e5d92fad137ac3de18083febedf3", "signature": "1332704d6761c94b85753fc3cadc36109f53e5d92fad137ac3de18083febedf3", "affectsGlobalScope": true}, "../node_modules/@types/node/async_hooks.d.ts": {"version": "c438b413e94ff76dfa20ae005f33a1c84f2480d1d66e0fd687501020d0de9b50", "signature": "c438b413e94ff76dfa20ae005f33a1c84f2480d1d66e0fd687501020d0de9b50", "affectsGlobalScope": false}, "../node_modules/@types/node/buffer.d.ts": {"version": "bc6a78961535181265845bf9b9e8a147ffd0ca275097ceb670a9b92afa825152", "signature": "bc6a78961535181265845bf9b9e8a147ffd0ca275097ceb670a9b92afa825152", "affectsGlobalScope": false}, "../node_modules/@types/node/child_process.d.ts": {"version": "987249e7b75023387c5fd9dc2f959ee777cb8989b7900f5a9eb4a67d290f2cba", "signature": "987249e7b75023387c5fd9dc2f959ee777cb8989b7900f5a9eb4a67d290f2cba", "affectsGlobalScope": false}, "../node_modules/@types/node/cluster.d.ts": {"version": "123ec69e4b3a686eb49afd94ebe3292a5c84a867ecbcb6bb84bdd720a12af803", "signature": "123ec69e4b3a686eb49afd94ebe3292a5c84a867ecbcb6bb84bdd720a12af803", "affectsGlobalScope": false}, "../node_modules/@types/node/console.d.ts": {"version": "eb5197aade83cb0e360ac407289c53a8009e8fdae7939892a0240d30444496b6", "signature": "eb5197aade83cb0e360ac407289c53a8009e8fdae7939892a0240d30444496b6", "affectsGlobalScope": true}, "../node_modules/@types/node/constants.d.ts": {"version": "90c85ddbb8de82cd19198bda062065fc51b7407c0f206f2e399e65a52e979720", "signature": "90c85ddbb8de82cd19198bda062065fc51b7407c0f206f2e399e65a52e979720", "affectsGlobalScope": false}, "../node_modules/@types/node/crypto.d.ts": {"version": "c5ecc351d5eaa36dc682b4c398b57a9d37c108857b71a09464a06e0185831ac2", "signature": "c5ecc351d5eaa36dc682b4c398b57a9d37c108857b71a09464a06e0185831ac2", "affectsGlobalScope": false}, "../node_modules/@types/node/dgram.d.ts": {"version": "7e050b767ed10c7ffbc01f314defbf420bf0b5d54ce666e1c87507c035dfc191", "signature": "7e050b767ed10c7ffbc01f314defbf420bf0b5d54ce666e1c87507c035dfc191", "affectsGlobalScope": false}, "../node_modules/@types/node/dns.d.ts": {"version": "7db7569fbb3e2b01ba8751c761cdd3f0debd104170d5665b7dc20a11630df3a9", "signature": "7db7569fbb3e2b01ba8751c761cdd3f0debd104170d5665b7dc20a11630df3a9", "affectsGlobalScope": false}, "../node_modules/@types/node/domain.d.ts": {"version": "cde4d7f6274468180fa39847b183aec22626e8212ff885d535c53f4cd7c225fd", "signature": "cde4d7f6274468180fa39847b183aec22626e8212ff885d535c53f4cd7c225fd", "affectsGlobalScope": true}, "../node_modules/@types/node/events.d.ts": {"version": "365e8358843a23944be84b810b1f774ea0223adfccc085f30fd7f4c13dc8a09f", "signature": "365e8358843a23944be84b810b1f774ea0223adfccc085f30fd7f4c13dc8a09f", "affectsGlobalScope": true}, "../node_modules/@types/node/fs.d.ts": {"version": "f87f95c015900102a5c0edcdebb17596de1382510e6cc17ab79f375d3505bb12", "signature": "f87f95c015900102a5c0edcdebb17596de1382510e6cc17ab79f375d3505bb12", "affectsGlobalScope": false}, "../node_modules/@types/node/fs/promises.d.ts": {"version": "05b5679a897598ebe556ee93415b3af1f456e674ea82e4d7afcd716bfe43aa98", "signature": "05b5679a897598ebe556ee93415b3af1f456e674ea82e4d7afcd716bfe43aa98", "affectsGlobalScope": false}, "../node_modules/@types/node/http.d.ts": {"version": "35fe02b2c10616cc7ac3db9c15778df7ed9c99276889efdd9be31f342841cfcd", "signature": "35fe02b2c10616cc7ac3db9c15778df7ed9c99276889efdd9be31f342841cfcd", "affectsGlobalScope": false}, "../node_modules/@types/node/http2.d.ts": {"version": "1c3fe66943f587685aa4a42f664f794655da1767401d2f85910177dac78aa45d", "signature": "1c3fe66943f587685aa4a42f664f794655da1767401d2f85910177dac78aa45d", "affectsGlobalScope": false}, "../node_modules/@types/node/https.d.ts": {"version": "c969bf4c7cdfe4d5dd28aa09432f99d09ad1d8d8b839959646579521d0467d1a", "signature": "c969bf4c7cdfe4d5dd28aa09432f99d09ad1d8d8b839959646579521d0467d1a", "affectsGlobalScope": false}, "../node_modules/@types/node/inspector.d.ts": {"version": "6c3857edaeeaaf43812f527830ebeece9266b6e8eb5271ab6d2f0008306c9947", "signature": "6c3857edaeeaaf43812f527830ebeece9266b6e8eb5271ab6d2f0008306c9947", "affectsGlobalScope": false}, "../node_modules/@types/node/module.d.ts": {"version": "bc6a77e750f4d34584e46b1405b771fb69a224197dd6bafe5b0392a29a70b665", "signature": "bc6a77e750f4d34584e46b1405b771fb69a224197dd6bafe5b0392a29a70b665", "affectsGlobalScope": false}, "../node_modules/@types/node/net.d.ts": {"version": "8de97668870cfb20fc9d355d2ef379e897bdd8a98c889c7d8a6de40ee408ad52", "signature": "8de97668870cfb20fc9d355d2ef379e897bdd8a98c889c7d8a6de40ee408ad52", "affectsGlobalScope": false}, "../node_modules/@types/node/os.d.ts": {"version": "ed4ae81196cccc10f297d228bca8d02e31058e6d723a3c5bc4be5fb3c61c6a34", "signature": "ed4ae81196cccc10f297d228bca8d02e31058e6d723a3c5bc4be5fb3c61c6a34", "affectsGlobalScope": false}, "../node_modules/@types/node/path.d.ts": {"version": "84044697c8b3e08ef24e4b32cfe6440143d07e469a5e34bda0635276d32d9f35", "signature": "84044697c8b3e08ef24e4b32cfe6440143d07e469a5e34bda0635276d32d9f35", "affectsGlobalScope": false}, "../node_modules/@types/node/perf_hooks.d.ts": {"version": "4982d94cb6427263c8839d8d6324a8bbe129e931deb61a7380f8fad17ba2cfc0", "signature": "4982d94cb6427263c8839d8d6324a8bbe129e931deb61a7380f8fad17ba2cfc0", "affectsGlobalScope": false}, "../node_modules/@types/node/process.d.ts": {"version": "e64979f6084c279bf8cd58dbc9203567f68f1ed19a8ad91351f078f03323ddf6", "signature": "e64979f6084c279bf8cd58dbc9203567f68f1ed19a8ad91351f078f03323ddf6", "affectsGlobalScope": true}, "../node_modules/@types/node/punycode.d.ts": {"version": "3f6a1fd73c9dc3bd7f4b79bc075297ca6527904df69b0f2c2c94e4c4c7d9a32c", "signature": "3f6a1fd73c9dc3bd7f4b79bc075297ca6527904df69b0f2c2c94e4c4c7d9a32c", "affectsGlobalScope": false}, "../node_modules/@types/node/querystring.d.ts": {"version": "884560fda6c3868f925f022adc3a1289fe6507bbb45adb10fa1bbcc73a941bb0", "signature": "884560fda6c3868f925f022adc3a1289fe6507bbb45adb10fa1bbcc73a941bb0", "affectsGlobalScope": false}, "../node_modules/@types/node/readline.d.ts": {"version": "6b2bb67b0942bcfce93e1d6fad5f70afd54940a2b13df7f311201fba54b2cbe9", "signature": "6b2bb67b0942bcfce93e1d6fad5f70afd54940a2b13df7f311201fba54b2cbe9", "affectsGlobalScope": false}, "../node_modules/@types/node/repl.d.ts": {"version": "acbed967a379b3e9f73237ba9473f8b337eeea14b7dc64d445430b5d695751da", "signature": "acbed967a379b3e9f73237ba9473f8b337eeea14b7dc64d445430b5d695751da", "affectsGlobalScope": false}, "../node_modules/@types/node/stream.d.ts": {"version": "272a46cc8f494677af587d17939e61e7db2b1925633e4da0c186ba17f6732521", "signature": "272a46cc8f494677af587d17939e61e7db2b1925633e4da0c186ba17f6732521", "affectsGlobalScope": false}, "../node_modules/@types/node/string_decoder.d.ts": {"version": "d67e08745494b000da9410c1ae2fdc9965fc6d593fe0f381a47491f75417d457", "signature": "d67e08745494b000da9410c1ae2fdc9965fc6d593fe0f381a47491f75417d457", "affectsGlobalScope": false}, "../node_modules/@types/node/timers.d.ts": {"version": "b40652bf8ce4a18133b31349086523b219724dca8df3448c1a0742528e7ad5b9", "signature": "b40652bf8ce4a18133b31349086523b219724dca8df3448c1a0742528e7ad5b9", "affectsGlobalScope": false}, "../node_modules/@types/node/tls.d.ts": {"version": "a0545ca910ec6b318b8e1d63813c980c3be44824cf217b6477a56fbe9c7927d5", "signature": "a0545ca910ec6b318b8e1d63813c980c3be44824cf217b6477a56fbe9c7927d5", "affectsGlobalScope": false}, "../node_modules/@types/node/trace_events.d.ts": {"version": "a77fdb357c78b70142b2fdbbfb72958d69e8f765fd2a3c69946c1018e89d4638", "signature": "a77fdb357c78b70142b2fdbbfb72958d69e8f765fd2a3c69946c1018e89d4638", "affectsGlobalScope": false}, "../node_modules/@types/node/tty.d.ts": {"version": "3c2ac350c3baa61fd2b1925844109e098f4376d0768a4643abc82754fd752748", "signature": "3c2ac350c3baa61fd2b1925844109e098f4376d0768a4643abc82754fd752748", "affectsGlobalScope": false}, "../node_modules/@types/node/url.d.ts": {"version": "80ffc1786a5dab91b4aa59a72720f02f25df8b7c76b593e04d5e381aec284ccb", "signature": "80ffc1786a5dab91b4aa59a72720f02f25df8b7c76b593e04d5e381aec284ccb", "affectsGlobalScope": false}, "../node_modules/@types/node/util.d.ts": {"version": "5ef157fbb39494a581bd24f21b60488fe248d452c479738b5e41b48720ea69b8", "signature": "5ef157fbb39494a581bd24f21b60488fe248d452c479738b5e41b48720ea69b8", "affectsGlobalScope": false}, "../node_modules/@types/node/v8.d.ts": {"version": "289be113bad7ee27ee7fa5b1e373c964c9789a5e9ed7db5ddcb631371120b953", "signature": "289be113bad7ee27ee7fa5b1e373c964c9789a5e9ed7db5ddcb631371120b953", "affectsGlobalScope": false}, "../node_modules/@types/node/vm.d.ts": {"version": "baf0b82ffc5d2616f44a6fb1f81e8d798545bebf0c30f5d8b003a1dba1acfb3f", "signature": "baf0b82ffc5d2616f44a6fb1f81e8d798545bebf0c30f5d8b003a1dba1acfb3f", "affectsGlobalScope": false}, "../node_modules/@types/node/worker_threads.d.ts": {"version": "c6a5b34f1e725019445754f1e733585f113e0dced75f137bd3c4af5853d3f6ab", "signature": "c6a5b34f1e725019445754f1e733585f113e0dced75f137bd3c4af5853d3f6ab", "affectsGlobalScope": false}, "../node_modules/@types/node/zlib.d.ts": {"version": "15fbe50526244954eb2f933546bca6cdcf0db16c9428d099b3b386c1db5799ab", "signature": "15fbe50526244954eb2f933546bca6cdcf0db16c9428d099b3b386c1db5799ab", "affectsGlobalScope": false}, "../node_modules/@types/node/ts3.4/base.d.ts": {"version": "d44028ae0127eb3e9fcfa5f55a8b81d64775ce15aca1020fe25c511bbb055834", "signature": "d44028ae0127eb3e9fcfa5f55a8b81d64775ce15aca1020fe25c511bbb055834", "affectsGlobalScope": false}, "../node_modules/@types/node/globals.global.d.ts": {"version": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "signature": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "affectsGlobalScope": true}, "../node_modules/@types/node/wasi.d.ts": {"version": "4e0a4d84b15692ea8669fe4f3d05a4f204567906b1347da7a58b75f45bae48d3", "signature": "4e0a4d84b15692ea8669fe4f3d05a4f204567906b1347da7a58b75f45bae48d3", "affectsGlobalScope": false}, "../node_modules/@types/node/ts3.6/base.d.ts": {"version": "ad1ae5ae98eceb9af99061e83e867b9897d267aebc8f3b938c9424deabadf4bb", "signature": "ad1ae5ae98eceb9af99061e83e867b9897d267aebc8f3b938c9424deabadf4bb", "affectsGlobalScope": false}, "../node_modules/@types/node/assert.d.ts": {"version": "d0c575d48d6dad75648017ff18762eb97f9398cc9486541b3070e79ce12719e6", "signature": "d0c575d48d6dad75648017ff18762eb97f9398cc9486541b3070e79ce12719e6", "affectsGlobalScope": false}, "../node_modules/@types/node/base.d.ts": {"version": "e61a21e9418f279bc480394a94d1581b2dee73747adcbdef999b6737e34d721b", "signature": "e61a21e9418f279bc480394a94d1581b2dee73747adcbdef999b6737e34d721b", "affectsGlobalScope": false}, "../node_modules/@types/node/index.d.ts": {"version": "5825520e2099309182c6e2a2b3061b060d42a098c58f67d1754880a7e2cce99a", "signature": "5825520e2099309182c6e2a2b3061b060d42a098c58f67d1754880a7e2cce99a", "affectsGlobalScope": false}, "../node_modules/@types/graceful-fs/index.d.ts": {"version": "cb6cf0480ee1aa9f706db1f6f5add596a1aa10e8c4beb1817f2318384ba684dc", "signature": "cb6cf0480ee1aa9f706db1f6f5add596a1aa10e8c4beb1817f2318384ba684dc", "affectsGlobalScope": false}, "../node_modules/@types/istanbul-lib-coverage/index.d.ts": {"version": "de18acda71730bac52f4b256ce7511bb56cc21f6f114c59c46782eff2f632857", "signature": "de18acda71730bac52f4b256ce7511bb56cc21f6f114c59c46782eff2f632857", "affectsGlobalScope": false}, "../node_modules/@types/istanbul-lib-report/index.d.ts": {"version": "7eb06594824ada538b1d8b48c3925a83e7db792f47a081a62cf3e5c4e23cf0ee", "signature": "7eb06594824ada538b1d8b48c3925a83e7db792f47a081a62cf3e5c4e23cf0ee", "affectsGlobalScope": false}, "../node_modules/@types/istanbul-reports/index.d.ts": {"version": "905c3e8f7ddaa6c391b60c05b2f4c3931d7127ad717a080359db3df510b7bdab", "signature": "905c3e8f7ddaa6c391b60c05b2f4c3931d7127ad717a080359db3df510b7bdab", "affectsGlobalScope": false}, "../node_modules/jest-diff/build/cleanupsemantic.d.ts": {"version": "d8aab31ba8e618cc3eea10b0945de81cb93b7e8150a013a482332263b9305322", "signature": "d8aab31ba8e618cc3eea10b0945de81cb93b7e8150a013a482332263b9305322", "affectsGlobalScope": false}, "../node_modules/jest-diff/build/types.d.ts": {"version": "69da61a7b5093dac77fa3bec8be95dcf9a74c95a0e9161edb98bb24e30e439d2", "signature": "69da61a7b5093dac77fa3bec8be95dcf9a74c95a0e9161edb98bb24e30e439d2", "affectsGlobalScope": false}, "../node_modules/jest-diff/build/difflines.d.ts": {"version": "561eca7a381b96d6ccac6e4061e6d2ae53f5bc44203f3fd9f5b26864c32ae6e9", "signature": "561eca7a381b96d6ccac6e4061e6d2ae53f5bc44203f3fd9f5b26864c32ae6e9", "affectsGlobalScope": false}, "../node_modules/jest-diff/build/printdiffs.d.ts": {"version": "62ea38627e3ebab429f7616812a9394d327c2bc271003dfba985de9b4137369f", "signature": "62ea38627e3ebab429f7616812a9394d327c2bc271003dfba985de9b4137369f", "affectsGlobalScope": false}, "../node_modules/jest-diff/build/index.d.ts": {"version": "b4439890c168d646357928431100daac5cbdee1d345a34e6bf6eca9f3abe22bc", "signature": "b4439890c168d646357928431100daac5cbdee1d345a34e6bf6eca9f3abe22bc", "affectsGlobalScope": false}, "../node_modules/pretty-format/build/types.d.ts": {"version": "5d72971a459517c44c1379dab9ed248e87a61ba0a1e0f25c9d67e1e640cd9a09", "signature": "5d72971a459517c44c1379dab9ed248e87a61ba0a1e0f25c9d67e1e640cd9a09", "affectsGlobalScope": false}, "../node_modules/pretty-format/build/index.d.ts": {"version": "02d734976af36f4273d930bea88b3e62adf6b078cf120c1c63d49aa8d8427c5c", "signature": "02d734976af36f4273d930bea88b3e62adf6b078cf120c1c63d49aa8d8427c5c", "affectsGlobalScope": false}, "../node_modules/@types/jest/index.d.ts": {"version": "480dcf599750efc664ba6edba1ed91fb5761ef5ae0e45e475b69a994e312db73", "signature": "480dcf599750efc664ba6edba1ed91fb5761ef5ae0e45e475b69a994e312db73", "affectsGlobalScope": true}, "../node_modules/@types/json-schema/index.d.ts": {"version": "b2be568d8ce95fcb26eebd04c035d94825655fdf689bf67d799f5ff8cbbb1024", "signature": "b2be568d8ce95fcb26eebd04c035d94825655fdf689bf67d799f5ff8cbbb1024", "affectsGlobalScope": false}, "../node_modules/@types/yargs-parser/index.d.ts": {"version": "fdfbe321c556c39a2ecf791d537b999591d0849e971dd938d88f460fea0186f6", "signature": "fdfbe321c556c39a2ecf791d537b999591d0849e971dd938d88f460fea0186f6", "affectsGlobalScope": false}, "../node_modules/@types/yargs/index.d.ts": {"version": "587e091276e4676b63121333f417206cd84cadf91b09949303431920d19bc04f", "signature": "587e091276e4676b63121333f417206cd84cadf91b09949303431920d19bc04f", "affectsGlobalScope": false}}, "options": {"incremental": true, "target": 1, "module": 1, "declaration": true, "outDir": "./", "strict": true, "moduleResolution": 2, "esModuleInterop": true, "declarationDir": "../types", "configFilePath": "../tsconfig.json"}, "referencedMap": {"../node_modules/@babel/parser/typings/babel-parser.d.ts": ["../node_modules/@babel/types/lib/index.d.ts"], "../node_modules/@types/babel__core/index.d.ts": ["../node_modules/@babel/parser/typings/babel-parser.d.ts", "../node_modules/@babel/types/lib/index.d.ts", "../node_modules/@types/babel__generator/index.d.ts", "../node_modules/@types/babel__template/index.d.ts", "../node_modules/@types/babel__traverse/index.d.ts"], "../node_modules/@types/babel__generator/index.d.ts": ["../node_modules/@babel/types/lib/index.d.ts"], "../node_modules/@types/babel__template/index.d.ts": ["../node_modules/@babel/parser/typings/babel-parser.d.ts", "../node_modules/@babel/types/lib/index.d.ts"], "../node_modules/@types/babel__traverse/index.d.ts": ["../node_modules/@babel/types/lib/index.d.ts"], "../node_modules/@types/graceful-fs/index.d.ts": ["../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/index.d.ts"], "../node_modules/@types/istanbul-lib-report/index.d.ts": ["../node_modules/@types/istanbul-lib-coverage/index.d.ts"], "../node_modules/@types/istanbul-reports/index.d.ts": ["../node_modules/@types/istanbul-lib-report/index.d.ts"], "../node_modules/@types/jest/index.d.ts": ["../node_modules/jest-diff/build/index.d.ts", "../node_modules/pretty-format/build/index.d.ts"], "../node_modules/@types/node/base.d.ts": ["../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/ts3.6/base.d.ts"], "../node_modules/@types/node/child_process.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/cluster.d.ts": ["../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/console.d.ts": ["../node_modules/@types/node/util.d.ts"], "../node_modules/@types/node/constants.d.ts": ["../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/os.d.ts"], "../node_modules/@types/node/crypto.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/dgram.d.ts": ["../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/domain.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/fs.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/fs/promises.d.ts": ["../node_modules/@types/node/fs.d.ts"], "../node_modules/@types/node/http.d.ts": ["../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/http2.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/https.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/index.d.ts": ["../node_modules/@types/node/base.d.ts"], "../node_modules/@types/node/inspector.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/module.d.ts": ["../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/net.d.ts": ["../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/perf_hooks.d.ts": ["../node_modules/@types/node/async_hooks.d.ts"], "../node_modules/@types/node/process.d.ts": ["../node_modules/@types/node/tty.d.ts"], "../node_modules/@types/node/readline.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/repl.d.ts": ["../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/vm.d.ts"], "../node_modules/@types/node/stream.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/tls.d.ts": ["../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/ts3.4/base.d.ts": ["../node_modules/@types/node/async_hooks.d.ts", "../node_modules/@types/node/buffer.d.ts", "../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/cluster.d.ts", "../node_modules/@types/node/console.d.ts", "../node_modules/@types/node/constants.d.ts", "../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dgram.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/domain.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/globals.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/http2.d.ts", "../node_modules/@types/node/https.d.ts", "../node_modules/@types/node/inspector.d.ts", "../node_modules/@types/node/module.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/os.d.ts", "../node_modules/@types/node/path.d.ts", "../node_modules/@types/node/perf_hooks.d.ts", "../node_modules/@types/node/process.d.ts", "../node_modules/@types/node/punycode.d.ts", "../node_modules/@types/node/querystring.d.ts", "../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/repl.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/string_decoder.d.ts", "../node_modules/@types/node/timers.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/trace_events.d.ts", "../node_modules/@types/node/tty.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/v8.d.ts", "../node_modules/@types/node/vm.d.ts", "../node_modules/@types/node/worker_threads.d.ts", "../node_modules/@types/node/zlib.d.ts"], "../node_modules/@types/node/ts3.6/base.d.ts": ["../node_modules/@types/node/globals.global.d.ts", "../node_modules/@types/node/ts3.4/base.d.ts", "../node_modules/@types/node/wasi.d.ts"], "../node_modules/@types/node/tty.d.ts": ["../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/url.d.ts": ["../node_modules/@types/node/querystring.d.ts"], "../node_modules/@types/node/v8.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/worker_threads.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/vm.d.ts"], "../node_modules/@types/node/zlib.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/yargs/index.d.ts": ["../node_modules/@types/yargs-parser/index.d.ts"], "../node_modules/jest-diff/build/difflines.d.ts": ["../node_modules/jest-diff/build/cleanupsemantic.d.ts", "../node_modules/jest-diff/build/types.d.ts"], "../node_modules/jest-diff/build/index.d.ts": ["../node_modules/jest-diff/build/cleanupsemantic.d.ts", "../node_modules/jest-diff/build/difflines.d.ts", "../node_modules/jest-diff/build/printdiffs.d.ts", "../node_modules/jest-diff/build/types.d.ts"], "../node_modules/jest-diff/build/printdiffs.d.ts": ["../node_modules/jest-diff/build/cleanupsemantic.d.ts", "../node_modules/jest-diff/build/types.d.ts"], "../node_modules/pretty-format/build/index.d.ts": ["../node_modules/pretty-format/build/types.d.ts"], "../src/find-package.ts": ["../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/path.d.ts"], "../src/get-package-hash.ts": ["../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/fs.d.ts"], "../src/get-packagelock-hash.ts": ["../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/fs.d.ts"], "../src/get-packagelock.ts": ["../node_modules/@types/node/path.d.ts"], "../src/index.ts": ["../src/is-package-changed.ts"], "../src/is-package-changed.ts": ["../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/path.d.ts", "../src/find-package.ts", "../src/get-package-hash.ts", "../src/get-packagelock-hash.ts", "../src/get-packagelock.ts"]}, "exportedModulesMap": {"../node_modules/@babel/parser/typings/babel-parser.d.ts": ["../node_modules/@babel/types/lib/index.d.ts"], "../node_modules/@types/babel__core/index.d.ts": ["../node_modules/@babel/parser/typings/babel-parser.d.ts", "../node_modules/@babel/types/lib/index.d.ts", "../node_modules/@types/babel__generator/index.d.ts", "../node_modules/@types/babel__template/index.d.ts", "../node_modules/@types/babel__traverse/index.d.ts"], "../node_modules/@types/babel__generator/index.d.ts": ["../node_modules/@babel/types/lib/index.d.ts"], "../node_modules/@types/babel__template/index.d.ts": ["../node_modules/@babel/parser/typings/babel-parser.d.ts", "../node_modules/@babel/types/lib/index.d.ts"], "../node_modules/@types/babel__traverse/index.d.ts": ["../node_modules/@babel/types/lib/index.d.ts"], "../node_modules/@types/graceful-fs/index.d.ts": ["../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/index.d.ts"], "../node_modules/@types/istanbul-lib-report/index.d.ts": ["../node_modules/@types/istanbul-lib-coverage/index.d.ts"], "../node_modules/@types/istanbul-reports/index.d.ts": ["../node_modules/@types/istanbul-lib-report/index.d.ts"], "../node_modules/@types/jest/index.d.ts": ["../node_modules/jest-diff/build/index.d.ts", "../node_modules/pretty-format/build/index.d.ts"], "../node_modules/@types/node/base.d.ts": ["../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/ts3.6/base.d.ts"], "../node_modules/@types/node/child_process.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/cluster.d.ts": ["../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/console.d.ts": ["../node_modules/@types/node/util.d.ts"], "../node_modules/@types/node/constants.d.ts": ["../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/os.d.ts"], "../node_modules/@types/node/crypto.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/dgram.d.ts": ["../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/domain.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/fs.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/fs/promises.d.ts": ["../node_modules/@types/node/fs.d.ts"], "../node_modules/@types/node/http.d.ts": ["../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/http2.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/https.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/index.d.ts": ["../node_modules/@types/node/base.d.ts"], "../node_modules/@types/node/inspector.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/module.d.ts": ["../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/net.d.ts": ["../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/perf_hooks.d.ts": ["../node_modules/@types/node/async_hooks.d.ts"], "../node_modules/@types/node/process.d.ts": ["../node_modules/@types/node/tty.d.ts"], "../node_modules/@types/node/readline.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/repl.d.ts": ["../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/vm.d.ts"], "../node_modules/@types/node/stream.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/tls.d.ts": ["../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/ts3.4/base.d.ts": ["../node_modules/@types/node/async_hooks.d.ts", "../node_modules/@types/node/buffer.d.ts", "../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/cluster.d.ts", "../node_modules/@types/node/console.d.ts", "../node_modules/@types/node/constants.d.ts", "../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dgram.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/domain.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/globals.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/http2.d.ts", "../node_modules/@types/node/https.d.ts", "../node_modules/@types/node/inspector.d.ts", "../node_modules/@types/node/module.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/os.d.ts", "../node_modules/@types/node/path.d.ts", "../node_modules/@types/node/perf_hooks.d.ts", "../node_modules/@types/node/process.d.ts", "../node_modules/@types/node/punycode.d.ts", "../node_modules/@types/node/querystring.d.ts", "../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/repl.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/string_decoder.d.ts", "../node_modules/@types/node/timers.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/trace_events.d.ts", "../node_modules/@types/node/tty.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/v8.d.ts", "../node_modules/@types/node/vm.d.ts", "../node_modules/@types/node/worker_threads.d.ts", "../node_modules/@types/node/zlib.d.ts"], "../node_modules/@types/node/ts3.6/base.d.ts": ["../node_modules/@types/node/globals.global.d.ts", "../node_modules/@types/node/ts3.4/base.d.ts", "../node_modules/@types/node/wasi.d.ts"], "../node_modules/@types/node/tty.d.ts": ["../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/url.d.ts": ["../node_modules/@types/node/querystring.d.ts"], "../node_modules/@types/node/v8.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/worker_threads.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/vm.d.ts"], "../node_modules/@types/node/zlib.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/yargs/index.d.ts": ["../node_modules/@types/yargs-parser/index.d.ts"], "../node_modules/jest-diff/build/difflines.d.ts": ["../node_modules/jest-diff/build/cleanupsemantic.d.ts", "../node_modules/jest-diff/build/types.d.ts"], "../node_modules/jest-diff/build/index.d.ts": ["../node_modules/jest-diff/build/cleanupsemantic.d.ts", "../node_modules/jest-diff/build/difflines.d.ts", "../node_modules/jest-diff/build/printdiffs.d.ts", "../node_modules/jest-diff/build/types.d.ts"], "../node_modules/jest-diff/build/printdiffs.d.ts": ["../node_modules/jest-diff/build/cleanupsemantic.d.ts", "../node_modules/jest-diff/build/types.d.ts"], "../node_modules/pretty-format/build/index.d.ts": ["../node_modules/pretty-format/build/types.d.ts"], "../src/index.ts": ["../src/is-package-changed.ts"]}, "semanticDiagnosticsPerFile": ["../node_modules/@babel/parser/typings/babel-parser.d.ts", "../node_modules/@babel/types/lib/index.d.ts", "../node_modules/@types/babel__core/index.d.ts", "../node_modules/@types/babel__generator/index.d.ts", "../node_modules/@types/babel__template/index.d.ts", "../node_modules/@types/babel__traverse/index.d.ts", "../node_modules/@types/graceful-fs/index.d.ts", "../node_modules/@types/istanbul-lib-coverage/index.d.ts", "../node_modules/@types/istanbul-lib-report/index.d.ts", "../node_modules/@types/istanbul-reports/index.d.ts", "../node_modules/@types/jest/index.d.ts", "../node_modules/@types/json-schema/index.d.ts", "../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/async_hooks.d.ts", "../node_modules/@types/node/base.d.ts", "../node_modules/@types/node/buffer.d.ts", "../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/cluster.d.ts", "../node_modules/@types/node/console.d.ts", "../node_modules/@types/node/constants.d.ts", "../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dgram.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/domain.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/globals.d.ts", "../node_modules/@types/node/globals.global.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/http2.d.ts", "../node_modules/@types/node/https.d.ts", "../node_modules/@types/node/index.d.ts", "../node_modules/@types/node/inspector.d.ts", "../node_modules/@types/node/module.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/os.d.ts", "../node_modules/@types/node/path.d.ts", "../node_modules/@types/node/perf_hooks.d.ts", "../node_modules/@types/node/process.d.ts", "../node_modules/@types/node/punycode.d.ts", "../node_modules/@types/node/querystring.d.ts", "../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/repl.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/string_decoder.d.ts", "../node_modules/@types/node/timers.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/trace_events.d.ts", "../node_modules/@types/node/ts3.4/base.d.ts", "../node_modules/@types/node/ts3.6/base.d.ts", "../node_modules/@types/node/tty.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/v8.d.ts", "../node_modules/@types/node/vm.d.ts", "../node_modules/@types/node/wasi.d.ts", "../node_modules/@types/node/worker_threads.d.ts", "../node_modules/@types/node/zlib.d.ts", "../node_modules/@types/yargs-parser/index.d.ts", "../node_modules/@types/yargs/index.d.ts", "../node_modules/jest-diff/build/cleanupsemantic.d.ts", "../node_modules/jest-diff/build/difflines.d.ts", "../node_modules/jest-diff/build/index.d.ts", "../node_modules/jest-diff/build/printdiffs.d.ts", "../node_modules/jest-diff/build/types.d.ts", "../node_modules/pretty-format/build/index.d.ts", "../node_modules/pretty-format/build/types.d.ts", "../node_modules/typescript/lib/lib.d.ts", "../node_modules/typescript/lib/lib.dom.d.ts", "../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../node_modules/typescript/lib/lib.es2015.core.d.ts", "../node_modules/typescript/lib/lib.es2015.d.ts", "../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../node_modules/typescript/lib/lib.es2016.d.ts", "../node_modules/typescript/lib/lib.es2017.d.ts", "../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../node_modules/typescript/lib/lib.es2017.object.d.ts", "../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../node_modules/typescript/lib/lib.es2017.string.d.ts", "../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../node_modules/typescript/lib/lib.es2018.d.ts", "../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../node_modules/typescript/lib/lib.es5.d.ts", "../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../node_modules/typescript/lib/lib.scripthost.d.ts", "../node_modules/typescript/lib/lib.webworker.importscripts.d.ts", "../src/find-package.ts", "../src/get-package-hash.ts", "../src/get-packagelock-hash.ts", "../src/get-packagelock.ts", "../src/index.ts", "../src/is-package-changed.ts"]}, "version": "4.0.5"}