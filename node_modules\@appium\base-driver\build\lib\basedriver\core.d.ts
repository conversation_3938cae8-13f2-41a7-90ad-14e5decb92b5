import type { AppiumLogger, Constraints, Core, Driver, DriverOpts, EventHistory, HTTPMethod, InitialOpts, Protocol, RouteMatcher, StringRecord } from '@appium/types';
import AsyncLock from 'async-lock';
import { DeviceSettings } from './device-settings';
import * as helpers from './helpers';
import { ExtensionCore } from './extension-core';
export declare class DriverCore<const C extends Constraints, Settings extends StringRecord = StringRecord> extends ExtensionCore implements Core<C, Settings> {
    /**
     * Make the basedriver version available so for any driver which inherits from this package, we
     * know which version of basedriver it inherited from
     */
    static baseVersion: string;
    sessionId: string | null;
    sessionCreationTimestampMs: number;
    opts: DriverOpts<C>;
    initialOpts: InitialOpts;
    helpers: typeof helpers;
    /**
     * basePath is used for several purposes, for example in setting up
     * proxying to other drivers, since we need to know what the base path
     * of any incoming request might look like. We set it to the default
     * initially but it is automatically updated during any actual program
     * execution by the routeConfiguringFunction, which is necessarily run as
     * the entrypoint for any Appium server
     */
    basePath: string;
    relaxedSecurityEnabled: boolean;
    allowInsecure: string[];
    denyInsecure: string[];
    newCommandTimeoutMs: number;
    implicitWaitMs: number;
    locatorStrategies: string[];
    webLocatorStrategies: string[];
    managedDrivers: Driver[];
    noCommandTimer: NodeJS.Timeout | null;
    protected _eventHistory: EventHistory;
    /**
     * TODO: remove this._log and use this.log instead
     */
    protected _log: AppiumLogger;
    shutdownUnexpectedly: boolean;
    shouldValidateCaps: boolean;
    protected commandsQueueGuard: AsyncLock;
    /**
     * settings should be instantiated by drivers which extend BaseDriver, but
     * we set it to an empty DeviceSettings instance here to make sure that the
     * default settings are applied even if an extending driver doesn't utilize
     * the settings functionality itself
     */
    settings: DeviceSettings<Settings>;
    protocol?: Protocol;
    constructor(opts?: InitialOpts, shouldValidateCaps?: boolean);
    /**
     * Set a callback handler if needed to execute a custom piece of code
     * when the driver is shut down unexpectedly. Multiple calls to this method
     * will cause the handler to be executed multiple times
     *
     * @param handler The code to be executed on unexpected shutdown.
     * The function may accept one argument, which is the actual error instance, which
     * caused the driver to shut down.
     */
    onUnexpectedShutdown(handler: (...args: any[]) => void): void;
    /**
     * This property is used by AppiumDriver to store the data of the
     * specific driver sessions. This data can be later used to adjust
     * properties for driver instances running in parallel.
     * Override it in inherited driver classes if necessary.
     */
    get driverData(): {};
    /**
     * This property controls the way the `executeCommand` method
     * handles new driver commands received from the client.
     * Override it for inherited classes only in special cases.
     *
     * @return If the returned value is true (default) then all the commands
     *   received by the particular driver instance are going to be put into the queue,
     *   so each following command will not be executed until the previous command
     *   execution is completed. False value disables that queue, so each driver command
     *   is executed independently and does not wait for anything.
     */
    get isCommandsQueueEnabled(): boolean;
    get eventHistory(): EventHistory;
    /**
     * API method for driver developers to log timings for important events
     */
    logEvent(eventName: string): void;
    /**
     * @privateRemarks Overridden in appium driver, but here so that individual drivers can be
     * tested with clients that poll
     */
    getStatus(): Promise<{}>;
    /**
     * method required by MJSONWP in order to determine whether it should
     * respond with an invalid session response
     */
    sessionExists(sessionId: string): boolean;
    /**
     * method required by MJSONWP in order to determine if the command should
     * be proxied directly to the driver
     */
    driverForSession(sessionId: string): Core<Constraints> | null;
    isMjsonwpProtocol(): boolean;
    isW3CProtocol(): boolean;
    setProtocolMJSONWP(): void;
    setProtocolW3C(): void;
    /**
     * Check whether a given feature is enabled via its name
     *
     * @param name - name of feature/command
     */
    isFeatureEnabled(name: string): boolean;
    /**
     * Assert that a given feature is enabled and throw a helpful error if it's
     * not
     *
     * @param name - name of feature/command
     * @deprecated Use {@link assertFeatureEnabled} instead
     */
    ensureFeatureEnabled(name: string): void;
    /**
     * Assert that a given feature is enabled and throw a helpful error if it's
     * not
     *
     * @param name - name of feature/command
     */
    assertFeatureEnabled(name: string): void;
    validateLocatorStrategy(strategy: string, webContext?: boolean): void;
    /**
     * If this driver has requested proxying of bidi connections to an upstream bidi endpoint, this
     * method should be overridden to return the URL of that websocket, to indicate that bidi
     * proxying is enabled. Otherwise, a null return will indicate that bidi proxying should not be
     * active and bidi commands will be handled by this driver.
     *
     * @returns {string | null}
     */
    get bidiProxyUrl(): string | null;
    proxyActive(sessionId: string): boolean;
    getProxyAvoidList(sessionId: string): RouteMatcher[];
    canProxy(sessionId: string): boolean;
    /**
     * Whether a given command route (expressed as method and url) should not be
     * proxied according to this driver
     *
     * @param sessionId - the current sessionId (in case the driver runs
     * multiple session ids and requires it). This is not used in this method but
     * should be made available to overridden methods.
     * @param method - HTTP method of the route
     * @param url - url of the route
     * @param [body] - webdriver request body
     *
     * @returns whether the route should be avoided
     */
    proxyRouteIsAvoided(sessionId: string, method: HTTPMethod, url: string, body?: any): boolean;
    /**
     *
     * @param {Driver} driver
     */
    addManagedDriver(driver: Driver): void;
    getManagedDrivers(): Driver<Constraints, StringRecord, StringRecord, import("@appium/types").DefaultCreateSessionResult<Constraints>, void, StringRecord>[];
    clearNewCommandTimeout(): Promise<void>;
}
//# sourceMappingURL=core.d.ts.map