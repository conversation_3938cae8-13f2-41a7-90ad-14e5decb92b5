<%- answers.isUsingTypeScript || answers.esmSupport
    ? `import { browser } from '@wdio/globals'`
    : `const { browser } = require('@wdio/globals')` %>

/**
* main page object containing all methods, selectors and functionality
* that is shared across all page objects
*/
<%- answers.isUsingTypeScript || answers.esmSupport ? "export default" : "module.exports =" %> class Page {
    /**
    * Opens a sub page of the page
    * @param path path of the sub page (e.g. /path/to/page.html)
    */
    <%- answers.isUsingTypeScript ? "public " : "" %>open (path<%- answers.isUsingTypeScript ? ": string" : "" %>) {
        return browser.url(`https://the-internet.herokuapp.com/${path}`)
    }
}
