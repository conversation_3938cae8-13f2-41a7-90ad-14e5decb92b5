/**
 * Check the emulator state.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if Emulator is visible to adb.
 */
export function isEmulatorConnected(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Verify the emulator is connected.
 *
 * @this {import('../adb.js').ADB}
 * @throws {Error} If Emulator is not visible to adb.
 */
export function verifyEmulatorConnected(this: import("../adb.js").ADB): Promise<void>;
/**
 * Emulate fingerprint touch event on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} fingerprintId - The ID of the fingerprint.
 */
export function fingerprint(this: import("../adb.js").ADB, fingerprintId: string): Promise<void>;
/**
 * Change the display orientation on the connected emulator.
 * The orientation is changed (PI/2 is added) every time
 * this method is called.
 * @this {import('../adb.js').ADB}
 */
export function rotate(this: import("../adb.js").ADB): Promise<void>;
/**
 * Emulate power state change on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').PowerAcStates} [state='on'] - Either 'on' or 'off'.
 */
export function powerAC(this: import("../adb.js").ADB, state?: import("./types").PowerAcStates): Promise<void>;
/**
 * Emulate sensors values on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} sensor - Sensor type declared in SENSORS items.
 * @param {import('./types').Sensors} value  - Number to set as the sensor value.
 * @throws {TypeError} - If sensor type or sensor value is not defined
 */
export function sensorSet(this: import("../adb.js").ADB, sensor: string, value: import("./types").Sensors): Promise<void>;
/**
 * Emulate power capacity change on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} [percent=100] - Percentage value in range [0, 100].
 */
export function powerCapacity(this: import("../adb.js").ADB, percent?: string | number): Promise<void>;
/**
 * Emulate power off event on the connected emulator.
 * @this {import('../adb.js').ADB}
 */
export function powerOFF(this: import("../adb.js").ADB): Promise<void>;
/**
 * Emulate send SMS event on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} phoneNumber - The phone number of message sender.
 * @param {string} [message=''] - The message content.
 * @throws {TypeError} If phone number has invalid format.
 */
export function sendSMS(this: import("../adb.js").ADB, phoneNumber: string | number, message?: string): Promise<void>;
/**
 * Emulate GSM call event on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} phoneNumber - The phone number of the caller.
 * @param {import('./types').GsmCallActions} action - One of available GSM call actions.
 * @throws {TypeError} If phone number has invalid format.
 * @throws {TypeError} If _action_ value is invalid.
 */
export function gsmCall(this: import("../adb.js").ADB, phoneNumber: string | number, action: import("./types").GsmCallActions): Promise<void>;
/**
 * Emulate GSM signal strength change event on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').GsmSignalStrength} [strength=4] - A number in range [0, 4];
 * @throws {TypeError} If _strength_ value is invalid.
 */
export function gsmSignal(this: import("../adb.js").ADB, strength?: import("./types").GsmSignalStrength): Promise<void>;
/**
 * Emulate GSM voice event on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').GsmVoiceStates} [state='on'] - Either 'on' or 'off'.
 * @throws {TypeError} If _state_ value is invalid.
 */
export function gsmVoice(this: import("../adb.js").ADB, state?: import("./types").GsmVoiceStates): Promise<void>;
/**
 * Emulate network speed change event on the connected emulator.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').NetworkSpeed} [speed='full']
 *  One of possible NETWORK_SPEED values.
 * @throws {TypeError} If _speed_ value is invalid.
 */
export function networkSpeed(this: import("../adb.js").ADB, speed?: import("./types").NetworkSpeed): Promise<void>;
/**
 * Executes a command through emulator telnet console interface and returns its output
 *
 * @this {import('../adb.js').ADB}
 * @param {string[]|string} cmd - The actual command to execute. See
 * https://developer.android.com/studio/run/emulator-console for more details
 * on available commands
 * @param {import('./types').ExecTelnetOptions} [opts={}]
 * @returns {Promise<string>} The command output
 * @throws {Error} If there was an error while connecting to the Telnet console
 * or if the given command returned non-OK response
 */
export function execEmuConsoleCommand(this: import("../adb.js").ADB, cmd: string[] | string, opts?: import("./types").ExecTelnetOptions): Promise<string>;
/**
 * Retrieves emulator version from the file system
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<import('./types').EmuVersionInfo>} If no version info could be parsed then an empty
 * object is returned
 */
export function getEmuVersionInfo(this: import("../adb.js").ADB): Promise<import("./types").EmuVersionInfo>;
/**
 * Retrieves emulator image properties from the local file system
 *
 * @this {import('../adb.js').ADB}
 * @param {string} avdName Emulator name. Should NOT start with '@' character
 * @throws {Error} if there was a failure while extracting the properties
 * @returns {Promise<import('./types').StringRecord>} The content of emulator image properties file.
 * Usually this configuration .ini file has the following content:
 *   avd.ini.encoding=UTF-8
 *   path=/Users/<USER>/.android/avd/Pixel_XL_API_30.avd
 *   path.rel=avd/Pixel_XL_API_30.avd
 *   target=android-30
 */
export function getEmuImageProperties(this: import("../adb.js").ADB, avdName: string): Promise<import("./types").StringRecord>;
/**
 * Check if given emulator exists in the list of available avds.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} avdName - The name of emulator to verify for existence.
 * Should NOT start with '@' character
 * @throws {Error} If the emulator with given name does not exist.
 */
export function checkAvdExist(this: import("../adb.js").ADB, avdName: string): Promise<boolean>;
/**
 * Send an arbitrary Telnet command to the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} command - The command to be sent.
 * @return {Promise<string>} The actual output of the given command.
 */
export function sendTelnetCommand(this: import("../adb.js").ADB, command: string): Promise<string>;
//# sourceMappingURL=emulator-commands.d.ts.map