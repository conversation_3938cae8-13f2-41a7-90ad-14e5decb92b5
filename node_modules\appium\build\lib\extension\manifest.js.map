{"version": 3, "file": "manifest.js", "sourceRoot": "", "sources": ["../../../lib/extension/manifest.js"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;AAEH,wDAAyB;AACzB,6CAAwC;AACxC,oDAAuB;AACvB,gDAAwB;AACxB,gDAAwB;AACxB,4CAA0E;AAC1E,yDAAsE;AACtE,uDAAmD;AACnD,+DAA8C;AAE9C;;;GAGG;AACH,MAAM,sBAAsB,GAAG,GAAG,uBAAW,GAAG,CAAC;AAEjD;;;GAGG;AACH,MAAM,sBAAsB,GAAG,GAAG,uBAAW,GAAG,CAAC;AAEjD;;GAEG;AACH,MAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1C,CAAC,sBAAsB,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;IAC3C,CAAC,sBAAsB,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;IAC3C,SAAS,EAAE,8BAAkB;CAC9B,CAAC,CAAC;AAEH;;;;;;;;GAQG;AACH,SAAS,WAAW,CAAC,KAAK;IACxB,OAAO,CACL,gBAAC,CAAC,aAAa,CAAC,KAAK,CAAC;QACtB,gBAAC,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;QAC7B,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QACtB,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAC1B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,QAAQ,CAAC,KAAK;IACrB,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,YAAY,IAAI,KAAK,CAAC,MAAM,IAAI,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACnG,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,QAAQ,CAAC,KAAK;IACrB,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,YAAY,IAAI,KAAK,CAAC,MAAM,IAAI,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACnG,CAAC;AAED;;;;GAIG;AACH,MAAa,QAAQ;IA4CnB;;;;;;OAMG;IACH,YAAY,UAAU;;QAlDtB;;;;;WAKG;QACH,iCAAM;QAEN;;;WAGG;QACH,uCAAY;QAEZ;;;;WAIG;QACH,yCAAc;QAEd;;;;;;;;WAQG;QACH,oCAAS;QAET;;;;;;;;WAQG;QACH,oCAAS;QAUP,uBAAA,IAAI,wBAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,kBAAS,gBAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,MAAA,CAAC;IAClD,CAAC;IAiBD;;;;OAIG;IACH,KAAK,CAAC,2BAA2B,CAAC,mBAAmB,GAAG,KAAK;QAC3D,4EAA4E;QAC5E,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB;;;;;WAKG;QACH,MAAM,OAAO,GAAG,KAAK,EAAE,QAAQ,EAAE,OAAO,GAAG,KAAK,EAAE,EAAE;YAClD,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;gBAC5D,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrB,MAAM,WAAW,GAAG,OAAO,IAAI,mBAAmB,CAAC,CAAC,CAAC,mCAAgB,CAAC,CAAC,CAAC,mCAAgB,CAAC;oBACzF,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;oBACzE,SAAS,GAAG,SAAS,IAAI,OAAO,CAAC;gBACnC,CAAC;YACH,CAAC;YAAC,MAAM,CAAC,CAAA,CAAC;QACZ,CAAC,CAAC;QAEF;;;WAGG;QACH,MAAM,KAAK,GAAG;YACZ,gDAAgD;YAChD,iEAAiE;YACjE,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,uBAAA,IAAI,4BAAY,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC;SAC3D,CAAC;QAEF,gCAAgC;QAChC,MAAM,SAAS,GAAG,MAAM,YAAE,CAAC,IAAI,CAAC,oCAAoC,EAAE;YACpE,GAAG,EAAE,uBAAA,IAAI,4BAAY;YACrB,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QACH,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,gCAAgC;QAChC,MAAM,kBAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAEnB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,IAAI;QACZ,OAAO,OAAO,CAAC,uBAAA,IAAI,sBAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,IAAI;QACZ,OAAO,OAAO,CAAC,uBAAA,IAAI,sBAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;OAQG;IACH,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,GAAG,mCAAgB;QACtE,MAAM,aAAa,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE5C;;WAEG;QACH,MAAM,QAAQ,GAAG;YACf,OAAO,EAAE,qBAAqB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;YAC7C,OAAO,EAAE,qBAAqB,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YAChD,aAAa,EAAE,OAAO,CAAC,gBAAgB,EAAE,MAAM;YAC/C,WAAW;YACX,WAAW,EAAE,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE;YACjD,WAAW,EAAE,aAAa;SAC3B,CAAC;QAEF,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACtB,MAAM,KAAK,GAAG;gBACZ,GAAG,gBAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC;gBACvC,GAAG,QAAQ;aACZ,CAAC;YACF,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,KAAK,EAAE,uBAAA,IAAI,sBAAM,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;gBACrE,IAAI,CAAC,YAAY;gBACf,sBAAsB,CAAC,CAAC,uBAAW,CAAC,EACpC,OAAO,CAAC,MAAM,CAAC,UAAU;gBACzB,mCAAmC,CAAC,CAAC,KAAK,CAAC,CAC5C,CAAC;gBACF,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG;gBACZ,GAAG,gBAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC;gBACvC,GAAG,QAAQ;aACZ,CAAC;YACF,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,KAAK,EAAE,uBAAA,IAAI,sBAAM,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;gBACrE,IAAI,CAAC,YAAY;gBACf,sBAAsB,CAAC,CAAC,uBAAW,CAAC,EACpC,OAAO,CAAC,MAAM,CAAC,UAAU;gBACzB,mCAAmC,CAAC,CAAC,KAAK,CAAC,CAC5C,CAAC;gBACF,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,SAAS,CACjB,oBAAoB,aAAa,uBAAuB,uBAAW,gBAAgB,uBAAW,GAAG,CAClG,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACH,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO;QACpC,MAAM,IAAI,GAAG,gBAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAClC,uBAAA,IAAI,sBAAM,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;QAC1C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,GAAG;QACd,uBAAA,IAAI,sBAAM,CAAC,SAAS,GAAG,GAAG,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,eAAe,CAAC,OAAO,EAAE,OAAO;QAC9B,OAAO,uBAAA,IAAI,sBAAM,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,uBAAA,IAAI,4BAAY,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,uBAAA,IAAI,8BAAc,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,uBAAA,IAAI,sBAAM,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACH,gBAAgB,CAAC,OAAO;QACtB,OAAO,uBAAA,IAAI,sBAAM,EAAC,qBAAsB,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,uBAAA,IAAI,yBAAS,EAAE,CAAC;YAClB,MAAM,uBAAA,IAAI,yBAAS,CAAC;YACpB,OAAO,uBAAA,IAAI,sBAAM,CAAC;QACpB,CAAC;QAED,uBAAA,IAAI,qBAAY,CAAC,KAAK,IAAI,EAAE;YAC1B,2BAA2B;YAC3B,IAAI,IAAI,CAAC;YACT;;;eAGG;YACH,IAAI,WAAW,GAAG,KAAK,CAAC;YACxB,MAAM,uBAAA,IAAI,sDAAiB,MAArB,IAAI,CAAmB,CAAC;YAC9B,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,uBAAA,IAAI,8BAAc,EAAE,MAAM,CAAC,CAAC;gBAC3D,IAAI,GAAG,cAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC1B,IAAI,GAAG,gBAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;oBAC1C,WAAW,GAAG,IAAI,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,IAAI,uBAAA,IAAI,8BAAc,EAAE,CAAC;wBACvB,MAAM,IAAI,KAAK,CACb,wDAAwD;4BACtD,eAAe,uBAAA,IAAI,8BAAc,8CAC/B,GAAG,CAAC,OACN,EAAE,CACL,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,KAAK,CACb,0DAA0D,GAAG,CAAC,OAAO,EAAE,CACxE,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAED,uBAAA,IAAI,kBAAS,IAAI,MAAA,CAAC;YAElB;;;eAGG;YACH,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,8BAAkB,EAAE,CAAC;gBAC/D,WAAW,GAAG,MAAM,IAAA,6BAAO,EAAC,IAAI,CAAC,CAAC;YACpC,CAAC;YAED,MAAM,mBAAmB,GAAG,MAAM,aAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE3E;;;;;;;;;;eAUG;YACH,IAAI,WAAW,IAAI,CAAC,mBAAmB,IAAI,CAAC,MAAM,IAAA,kCAAgB,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtF,WAAW,GAAG,CAAC,MAAM,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,CAAC,IAAI,WAAW,CAAC;YAC7F,CAAC;YAED,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YACrB,CAAC;QACH,CAAC,CAAC,EAAE,MAAA,CAAC;QAEL,IAAI,CAAC;YACH,MAAM,uBAAA,IAAI,yBAAS,CAAC;YACpB,OAAO,uBAAA,IAAI,sBAAM,CAAC;QACpB,CAAC;gBAAS,CAAC;YACT,uBAAA,IAAI,qBAAY,SAAS,MAAA,CAAC;QAC5B,CAAC;IACH,CAAC;IAyBD;;;;;;;OAOG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,uBAAA,IAAI,yBAAS,EAAE,CAAC;YAClB,OAAO,uBAAA,IAAI,yBAAS,CAAC;QACvB,CAAC;QACD,uBAAA,IAAI,qBAAY,CAAC,KAAK,IAAI,EAAE;YAC1B,MAAM,uBAAA,IAAI,sDAAiB,MAArB,IAAI,CAAmB,CAAC;YAC9B,IAAI,CAAC;gBACH,MAAM,YAAE,CAAC,MAAM,CAAC,cAAI,CAAC,OAAO,CAAC,uBAAA,IAAI,8BAAc,CAAC,CAAC,CAAC;YACpD,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CACb,gEAAgE,cAAI,CAAC,OAAO,CAC1E,uBAAA,IAAI,8BAAc,CACnB,qBAAqB,GAAG,CAAC,OAAO,EAAE,CACpC,CAAC;YACJ,CAAC;YACD,IAAI,CAAC;gBACH,MAAM,YAAE,CAAC,SAAS,CAAC,uBAAA,IAAI,8BAAc,EAAE,cAAI,CAAC,SAAS,CAAC,uBAAA,IAAI,sBAAM,CAAC,EAAE,MAAM,CAAC,CAAC;gBAC3E,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CACb,yCAAyC,uBAAA,IAAI,8BAAc,sBACzD,uBAAA,IAAI,4BACN,mDAAmD,GAAG,CAAC,OAAO,EAAE,CACjE,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,EAAE,MAAA,CAAC;QACL,IAAI,CAAC;YACH,OAAO,MAAM,uBAAA,IAAI,yBAAS,CAAC;QAC7B,CAAC;gBAAS,CAAC;YACT,uBAAA,IAAI,qBAAY,SAAS,MAAA,CAAC;QAC5B,CAAC;IACH,CAAC;;AAtaH,4BAuaC;;AA/DC;;;;;GAKG;AACH,KAAK;IACH,IAAI,CAAC,uBAAA,IAAI,8BAAc,EAAE,CAAC;QACxB,uBAAA,IAAI,0BAAiB,MAAM,aAAG,CAAC,mBAAmB,CAAC,uBAAA,IAAI,4BAAY,CAAC,MAAA,CAAC;QAErE,wBAAwB;QACxB,IAAI,cAAI,CAAC,QAAQ,CAAC,uBAAA,IAAI,4BAAY,EAAE,uBAAA,IAAI,8BAAc,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,KAAK,CACb,4EACE,IAAI,CAAC,UACP,oBAAoB,uBAAA,IAAI,8BAAc,EAAE,CACzC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,uBAAA,IAAI,8BAAc,CAAC;AAC5B,CAAC;AArUD;;;;GAIG;AACI,oBAAW,GAAG,gBAAC,CAAC,OAAO;AAC5B;;;GAGG;AACH,SAAS,YAAY,CAAC,UAAU;IAC9B,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;AAClC,CAAC,CACF,AARiB,CAQhB;AAoWJ;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;GAGG"}