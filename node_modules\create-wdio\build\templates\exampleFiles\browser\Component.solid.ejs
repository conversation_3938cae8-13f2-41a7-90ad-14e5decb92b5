import { createSignal } from 'solid-js'
import './Component.css'

export default function ExampleComponent () {
    const [count, setCount] = createSignal(0)

    return (
        <>
            <div>
                <a href="https://webdriver.io/docs/component-testing" target="_blank">
                    <img src="https://webdriver.io/assets/images/robot-3677788dd63849c56aa5cb3f332b12d5.svg" className="logo" alt="WebdriverIO logo" />
                </a>
            </div>
            <h1>WebdriverIO Component Testing</h1>
            <div class="card">
                <button onClick={() => setCount((count) => count + 1)}>
                    count is {count}
                </button>
                <p>
                    Edit <code>src/Component.test.<%- answers.isUsingTypeScript ? `tsx` : 'jsx' %></code> and save to test HMR
                </p>
            </div>
            <p class="read-the-docs">
                Click on the Vite and Preact logos to learn more
            </p>
        </>
    )
}
