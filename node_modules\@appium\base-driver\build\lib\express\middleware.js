"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleIdempotency = void 0;
exports.allowCrossDomain = allowCrossDomain;
exports.allowCrossDomainAsyncExecute = allowCrossDomainAsyncExecute;
exports.fixPythonContentType = fixPythonContentType;
exports.handleLogContext = handleLogContext;
exports.defaultToJSONContentType = defaultToJSONContentType;
exports.handleUpgrade = handleUpgrade;
exports.catchAllHandler = catchAllHandler;
exports.catch404Handler = catch404Handler;
const lodash_1 = __importDefault(require("lodash"));
const logger_1 = __importDefault(require("./logger"));
const protocol_1 = require("../protocol");
var idempotency_1 = require("./idempotency");
Object.defineProperty(exports, "handleIdempotency", { enumerable: true, get: function () { return idempotency_1.handleIdempotency; } });
const path_to_regexp_1 = require("path-to-regexp");
const support_1 = require("@appium/support");
const session_1 = require("../helpers/session");
/**
 *
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @param {import('express').NextFunction} next
 * @returns {any}
 */
function allowCrossDomain(req, res, next) {
    try {
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, OPTIONS, DELETE');
        res.header('Access-Control-Allow-Headers', 'Cache-Control, Pragma, Origin, X-Requested-With, Content-Type, Accept, User-Agent');
        // need to respond 200 to OPTIONS
        if ('OPTIONS' === req.method) {
            return res.sendStatus(200);
        }
    }
    catch (err) {
        logger_1.default.error(`Unexpected error: ${err.stack}`);
    }
    next();
}
/**
 * @param {string} basePath
 * @returns {import('express').RequestHandler}
 */
function allowCrossDomainAsyncExecute(basePath) {
    return (req, res, next) => {
        // there are two paths for async responses, so cover both
        // https://regex101.com/r/txYiEz/1
        const receiveAsyncResponseRegExp = new RegExp(`${lodash_1.default.escapeRegExp(basePath)}/session/[a-f0-9-]+/(appium/)?receive_async_response`);
        if (!receiveAsyncResponseRegExp.test(req.url)) {
            return next();
        }
        allowCrossDomain(req, res, next);
    };
}
/**
 *
 * @param {string} basePath
 * @returns {import('express').RequestHandler}
 */
function fixPythonContentType(basePath) {
    return (req, res, next) => {
        // hack because python client library gives us wrong content-type
        if (new RegExp(`^${lodash_1.default.escapeRegExp(basePath)}`).test(req.path) &&
            (req.headers['user-agent'] ?? '').startsWith('Python')) {
            if (req.headers['content-type'] === 'application/x-www-form-urlencoded') {
                req.headers['content-type'] = 'application/json; charset=utf-8';
            }
        }
        next();
    };
}
/**
 *
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @param {import('express').NextFunction} next
 * @returns {any}
 */
function handleLogContext(req, res, next) {
    const requestId = fetchHeaderValue(req, 'x-request-id') || support_1.util.uuidV4();
    const sessionId = SESSION_ID_PATTERN.exec(req.url)?.[1];
    const sessionInfo = sessionId ? { sessionId, sessionSignature: (0, session_1.calcSignature)(sessionId) } : {};
    const isSensitiveHeaderValue = fetchHeaderValue(req, 'x-appium-is-sensitive');
    logger_1.default.updateAsyncContext({
        requestId,
        ...sessionInfo,
        isSensitive: ['true', '1', 'yes'].includes(lodash_1.default.toLower(isSensitiveHeaderValue)),
    }, true);
    return next();
}
/**
 *
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @param {import('express').NextFunction} next
 * @returns {any}
 */
function defaultToJSONContentType(req, res, next) {
    if (!req.headers['content-type']) {
        req.headers['content-type'] = 'application/json; charset=utf-8';
    }
    next();
}
/**
 *
 * @param {import('@appium/types').StringRecord<import('@appium/types').WSServer>} webSocketsMapping
 * @returns {import('express').RequestHandler}
 */
function handleUpgrade(webSocketsMapping) {
    return (req, res, next) => {
        if (!req.headers?.upgrade || lodash_1.default.toLower(req.headers.upgrade) !== 'websocket') {
            return next();
        }
        let currentPathname;
        try {
            currentPathname = new URL(req.url ?? '').pathname;
        }
        catch {
            currentPathname = req.url ?? '';
        }
        for (const [pathname, wsServer] of lodash_1.default.toPairs(webSocketsMapping)) {
            if ((0, path_to_regexp_1.match)(pathname)(currentPathname)) {
                return wsServer.handleUpgrade(req, req.socket, Buffer.from(''), (ws) => {
                    wsServer.emit('connection', ws, req);
                });
            }
        }
        logger_1.default.info(`Did not match the websocket upgrade request at ${currentPathname} to any known route`);
        next();
    };
}
/**
 * @param {Error} err
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @param {import('express').NextFunction} next
 */
function catchAllHandler(err, req, res, next) {
    if (res.headersSent) {
        return next(err);
    }
    logger_1.default.error(`Uncaught error: ${err.message}`);
    logger_1.default.error('Sending generic error response');
    const error = protocol_1.errors.UnknownError;
    res.status(error.w3cStatus()).json(patchWithSessionId(req, {
        status: error.code(),
        value: {
            error: error.error(),
            message: `An unknown server-side error occurred while processing the command: ${err.message}`,
            stacktrace: err.stack,
        },
    }));
    logger_1.default.error(err);
}
/**
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 */
function catch404Handler(req, res) {
    logger_1.default.debug(`No route found for ${req.url}`);
    const error = protocol_1.errors.UnknownCommandError;
    res.status(error.w3cStatus()).json(patchWithSessionId(req, {
        status: error.code(),
        value: {
            error: error.error(),
            message: 'The requested resource could not be found, or a request was ' +
                'received using an HTTP method that is not supported by the mapped ' +
                'resource',
            stacktrace: '',
        },
    }));
}
const SESSION_ID_PATTERN = /\/session\/([^/]+)/;
/**
 * @param {import('express').Request} req
 * @param {any} body
 * @returns {any}
 */
function patchWithSessionId(req, body) {
    const match = SESSION_ID_PATTERN.exec(req.url);
    if (match) {
        body.sessionId = match[1];
    }
    return body;
}
/**
 * @param {import('express').Request} req
 * @param {string} name
 * @returns {string | undefined}
 */
function fetchHeaderValue(req, name) {
    return lodash_1.default.isArray(req.headers[name])
        ? req.headers[name][0]
        : req.headers[name];
}
//# sourceMappingURL=middleware.js.map