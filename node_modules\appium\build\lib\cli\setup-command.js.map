{"version": 3, "file": "setup-command.js", "sourceRoot": "", "sources": ["../../../lib/cli/setup-command.js"], "names": [], "mappings": ";;;;;;AA2CA,4CAaC;AAMD,sDAOC;AAUD,0CAkBC;AAjGD,oDAAuB;AACvB,4CAIsB;AACtB,2CAAgD;AAChD,6CAA6C;AAC7C,uDAA4B;AAE5B;;GAEG;AACU,QAAA,iBAAiB,GAAG,QAAQ,CAAC;AAC7B,QAAA,kBAAkB,GAAG,SAAS,CAAC;AAC/B,QAAA,kBAAkB,GAAG,SAAS,CAAC;AAC/B,QAAA,gBAAgB,GAAG,OAAO,CAAC;AAExC;;;GAGG;AACH,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM;AAChC,oBAAoB,CAAC,CAAC;IACpB,MAAM,EAAE,gBAAC,CAAC,IAAI,CAAC,0BAAc,CAAC;IAC9B,OAAO,EAAE,gBAAC,CAAC,IAAI,CAAC,2BAAe,CAAC;IAChC,OAAO,EAAE,gBAAC,CAAC,IAAI,CAAC,4BAAgB,CAAC;CAClC,CAAC,CACH,CAAC;AACF,MAAM,kBAAkB,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAE1D,MAAM,oBAAoB,GAAG,CAAC,SAAS,CAAC,CAAC;AAEzC;;GAEG;AACU,QAAA,eAAe,GAAG,CAAC,QAAQ,CAAC,CAAC;AAE1C;;;;GAIG;AACH,SAAgB,gBAAgB,CAAC,UAAU;IACzC,OAAO,gBAAC,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE;QACnD,IAAI,gBAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE,MAAM,CAAC,EAAE,CAAC;YAC3C,OAAO,gBAAM,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,gBAAC,CAAC,QAAQ,CAAC,oBAAoB,EAAE,MAAM,CAAC,EAAE,CAAC;YAC7C,OAAO,gBAAM,CAAC,SAAS,EAAE,CAAC;QAC5B,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AAEL,CAAC;AAED;;;GAGG;AACH,SAAgB,qBAAqB;IACnC,IAAI,gBAAM,CAAC,KAAK,EAAE,EAAE,CAAC;QACnB,OAAO,OAAO,CAAC;IACjB,CAAC;SAAM,IAAI,gBAAM,CAAC,SAAS,EAAE,EAAE,CAAC;QAC9B,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,eAAe,CAAC,aAAa,EAAE,YAAY,EAAE,YAAY;IAC7E,QAAQ,aAAa,CAAC,YAAY,EAAE,CAAC;QACnC,KAAK,0BAAkB;YACrB,MAAM,sBAAsB,CAAC,YAAY,CAAC,CAAC;YAC3C,MAAM,mBAAmB,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM;QACR,KAAK,0BAAkB;YACrB,MAAM,mBAAmB,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM,mBAAmB,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM;QACR,KAAK,wBAAgB;YACnB,MAAM,kBAAkB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YACrD,MAAM;QACR;YACE,MAAM,kBAAkB,CAAC,YAAY,CAAC,CAAC;YACvC,MAAM,mBAAmB,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM;IACV,CAAC;AACH,CAAC;AAAA,CAAC;AAEF;;;;;;GAMG;AACH,KAAK,UAAU,kBAAkB,CAAC,YAAY,EAAE,YAAY;IAC1D,KAAK,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI;QAC9B,CAAC,QAAQ,EAAE,YAAY,CAAC;QACxB,CAAC,QAAQ,EAAE,YAAY,CAAC;KACzB,EAAE,CAAC;QACF,KAAK,MAAM,aAAa,IAAI,gBAAC,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC1G,IAAI,CAAC;gBACH,MAAM,kBAAkB,CACtB,aAAa,EACb,oBAAoB,CAAC,kCAAkC,CAAC,CAAC,OAAO,CAAC,EAAE,aAAa,EAAE,WAAW,CAAC;gBAC9F,wCAAwC,CAAC,CAAC,MAAM,CAAC,CAClD,CAAC;YACJ,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,gBAAG,CAAC,IAAI,CACN,GAAG,aAAa,IAAI,OAAO,2DAA2D;oBACtF,mBAAmB,CAAC,CAAC,KAAK,EAAE,CAC7B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,YAAY,GAAG,wCAAwC,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC;QACpF,IAAI,CAAC,MAAM,YAAE,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;YACnC,SAAS;QACX,CAAC;QAED,MAAM,YAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9B,IAAI,MAAM,YAAE,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,iBAAiB,YAAY,wCAAwC,CAAC,CAAC;QACnG,CAAC;aAAM,CAAC;YACN,gBAAG,CAAC,IAAI,CAAC,wBAAwB,OAAO,iBAAiB,YAAY,GAAG,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,kBAAkB,CAAC,YAAY;IAC5C,MAAM,cAAc,CAAC,yBAAiB,EAAE,YAAY,CAAC,CAAC;AACxD,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,mBAAmB,CAAC,YAAY;IAC7C,MAAM,cAAc,CAAC,0BAAkB,EAAE,YAAY,CAAC,CAAC;AACzD,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,sBAAsB,CAAC,YAAY;IAChD,MAAM,cAAc,CAAC,0BAAkB,EAAE,YAAY,CAAC,CAAC;AACzD,CAAC;AAED;;;;;GAKG;AACH,KAAK,UAAU,cAAc,CAAC,UAAU,EAAE,YAAY;IACpD,KAAK,MAAM,UAAU,IAAI,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;QACtD,MAAM,gBAAgB,CAAC,UAAU,EAAE,oBAAoB,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,YAAY,CAAC,CAAC;IAC1G,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,mBAAmB,CAAC,YAAY;IAC7C,KAAK,MAAM,UAAU,IAAI,uBAAe,EAAE,CAAC;QACzC,MAAM,gBAAgB,CAAC,UAAU,EAAE,oBAAoB,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,YAAY,CAAC,CAAC;IAC1G,CAAC;AACH,CAAC;AAED;;;;;;GAMG;AACH,KAAK,UAAU,gBAAgB,CAAC,aAAa,EAAE,mBAAmB,EAAE,eAAe;IACjF,IAAI,gBAAC,CAAC,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;QACxE,gBAAG,CAAC,IAAI,CAAC,GAAG,aAAa,KAAK,eAAe,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,OAAO,0BAA0B;YAChH,4BAA4B,CAAC,CAAC;QAChC,OAAO;IACT,CAAC;IACD,MAAM,IAAA,+BAAmB,EAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;GAMG;AACH,KAAK,UAAU,kBAAkB,CAAC,aAAa,EAAE,mBAAmB,EAAE,eAAe;IACnF,IAAI,CAAC,gBAAC,CAAC,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;QACzE,gBAAG,CAAC,IAAI,CAAC,GAAG,aAAa,KAAK,eAAe,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,OAAO,sBAAsB;YAC5G,yBAAyB,CAAC,CAAC;QAC7B,OAAO;IACT,CAAC;IACD,MAAM,IAAA,+BAAmB,EAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,oBAAoB,CAAC,gBAAgB,EAAE,aAAa,EAAE,OAAO;IACpE,OAAO,CAAC,gBAAgB,KAAK,QAAQ,CAAC;QACpC,CAAC,CAAC,EAAC,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAC;QAC7E,CAAC,CAAC,EAAC,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAC,CAAC;AAClF,CAAC;AAED;;;;;GAKG;AAEH;;GAEG"}