{"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "5.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}}