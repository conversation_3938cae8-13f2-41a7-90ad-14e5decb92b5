"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isWriteable = isWriteable;
exports.signApp = signApp;
const path_1 = __importDefault(require("path"));
const support_1 = require("appium/support");
/**
 * @param {string} filePath
 * @returns {Promise<boolean>}
 */
async function isWriteable(filePath) {
    try {
        await support_1.fs.access(filePath, support_1.fs.constants.W_OK);
        if (support_1.system.isWindows()) {
            // On operating systems, where access-control policies may
            // limit access to the file system, `fs.access` does not work
            // as expected. See https://groups.google.com/forum/#!topic/nodejs/qmZtIwDRSYo
            // for more details
            await support_1.fs.close(await support_1.fs.open(filePath, 'r+'));
        }
        return true;
    }
    catch {
        return false;
    }
}
/**
 *
 * @param {import('appium-adb').ADB} adb
 * @param {string} appPath
 * @returns {Promise<void>}
 */
async function signApp(adb, appPath) {
    if (!await isWriteable(appPath)) {
        throw new Error(`The application at '${appPath}' is not writeable. ` +
            `Please grant write permissions to this file or to its parent folder '${path_1.default.dirname(appPath)}' ` +
            `for the Appium process, so it could sign the application`);
    }
    await adb.sign(appPath);
}
//# sourceMappingURL=helpers.js.map