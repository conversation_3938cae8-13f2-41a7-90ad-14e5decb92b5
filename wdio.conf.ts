import type { Options } from '@wdio/types'

export const config: Options.Testrunner = {
    //
    // ====================
    // Runner Configuration
    // ====================
    runner: 'local',
    
    //
    // ==================
    // Specify Test Files
    // ==================
    specs: [
        './test/specs/**/*.ts'
    ],
    exclude: [
        // 'path/to/excluded/files'
    ],
    
    //
    // ============
    // Capabilities
    // ============
    maxInstances: 1,
    capabilities: [{
        platformName: 'Android',
        'appium:deviceName': 'emulator-5554',
        'appium:platformVersion': '16',
        'appium:automationName': 'UiAutomator2',
        'appium:app': 'C:\\Users\\<USER>\\Documents\\taller\\org-wikipedia.apk',
        'appium:appWaitActivity': 'org.wikipedia.*',
        'appium:newCommandTimeout': 240,
        'appium:autoGrantPermissions': true,
        'appium:noReset': false,
        'appium:adbExecTimeout': 60000,
        'appium:androidInstallTimeout': 90000
    }],
    
    //
    // ===================
    // Test Configurations
    // ===================
    logLevel: 'info',
    bail: 0,
    baseUrl: 'http://localhost',
    waitforTimeout: 10000,
    connectionRetryTimeout: 120000,
    connectionRetryCount: 3,
    
    services: [
        ['appium', {
            args: {
                address: 'localhost',
                port: 4723,
                relaxedSecurity: true
            },
            logPath: './logs/',
            env: {
                JAVA_HOME: 'C:\\Program Files\\Android\\Android Studio\\jbr',
                ANDROID_HOME: 'C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk',
                ANDROID_SDK_ROOT: 'C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk',
                PATH: process.env.PATH + ';C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools'
            }
        }]
    ],
    
    framework: 'mocha',
    reporters: ['spec'],
    
    mochaOpts: {
        ui: 'bdd',
        timeout: 60000
    },
    
    //
    // =====
    // Hooks
    // =====
    onPrepare: function (config, capabilities) {
        // Set Android SDK environment variables
        process.env.ANDROID_HOME = process.env.ANDROID_HOME || 'C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk'
        process.env.ANDROID_SDK_ROOT = process.env.ANDROID_SDK_ROOT || 'C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk'
        console.log('Android SDK paths set:', {
            ANDROID_HOME: process.env.ANDROID_HOME,
            ANDROID_SDK_ROOT: process.env.ANDROID_SDK_ROOT
        })
    },
    
    onComplete: function(exitCode, config, capabilities, results) {
        // do something after test run
    },
    
    before: function (capabilities, specs) {
        // do something before test suite starts
    },
    
    after: function (result, capabilities, specs) {
        // do something after test suite ends
    },
    
    beforeTest: function (test, context) {
        // do something before test starts
    },
    
    afterTest: function(test, context, { error, result, duration, passed, retries }) {
        // do something after test ends
    }
}
