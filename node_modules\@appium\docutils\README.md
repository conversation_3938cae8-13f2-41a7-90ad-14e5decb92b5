# @appium/docutils

> Documentation-building utilities for Appium

[![NPM version](http://img.shields.io/npm/v/@appium/docutils.svg)](https://npmjs.org/package/@appium/docutils)
[![Downloads](http://img.shields.io/npm/dm/@appium/docutils.svg)](https://npmjs.org/package/@appium/docutils)

## Setup for Main Appium Docs

This package does not need to be run directly. Instead, execute this command in the
`packages/appium` directory:

```bash
npm run dev:docs:en
```

> [!NOTE]
> This will only build the English docs. See the `scripts` field of the `package.json` for
other languages.

## Setup for Appium Drivers and Plugins

See the Appium docs on [Building Documentation](http://appium.io/docs/en/latest/developing/build-docs/).

## License

Apache-2.0
