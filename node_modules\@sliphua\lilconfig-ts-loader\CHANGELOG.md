## [3.0.2](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/compare/v3.0.1...v3.0.2) (2020-09-05)


### Bug Fixes

* remove `yarn` from `engines` declarations ([26e9aec](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/26e9aec31b0465ab65568c1b192376c84be4fd41)), closes [#77](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/issues/77)
* use valid version of Yarn in `engines` ([09983de](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/09983de925080107f4a90e822b3dca6a79fc9b56)), closes [#77](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/issues/77)

## [3.0.1](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/compare/v3.0.0...v3.0.1) (2020-09-04)


### Bug Fixes

* publish only necessary files to npm ([edc924e](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/edc924ecb5fff5541129b8646a5668a61e776293))

# [3.0.0](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/compare/v2.0.0...v3.0.0) (2020-06-20)


### Build System

* change minimum supported Node version ([c450d41](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/c450d41))


### BREAKING CHANGES

* change minimum supported version of Node to v10

# [2.0.0](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/compare/v1.0.2...v2.0.0) (2020-06-20)


### Features

* **deps:** update `cosmiconfig` ([dc9a903](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/dc9a903))


### BREAKING CHANGES

* **deps:** `cosmiconfig` API has changed in latest version

## [1.0.2](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/compare/v1.0.1...v1.0.2) (2020-06-20)


### Bug Fixes

* **dependencies:** limit cosmiconfig peer dependency with upper boundary ([ec0c4f2](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/ec0c4f2))

## [1.0.1](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/compare/v1.0.0...v1.0.1) (2019-07-13)


### Bug Fixes

* **package:** add missing dependency "make-error" ([d8c8948](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/d8c8948))
* **security:** upgrade dependencies to fix CVE-2018-16469 ([10dc113](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/10dc113))

# 1.0.0 (2018-10-13)


### Bug Fixes

* **sourcemaps:** use inline sourcemaps for tests ([db5f6d4](https://github.com/EndemolShineGroup/cosmiconfig-typescript-loader/commit/db5f6d4))
