"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startLogcat = startLogcat;
exports.stopLogcat = stopLogcat;
exports.getLogcatLogs = getLogcatLogs;
exports.setLogcatListener = setLogcatListener;
exports.removeLogcatListener = removeLogcatListener;
const lodash_1 = __importDefault(require("lodash"));
const logcat_1 = require("../logcat");
/**
 * Start the logcat process to gather logs.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').LogcatOpts} [opts={}]
 * @throws {Error} If restart fails.
 */
async function startLogcat(opts = {}) {
    if (!lodash_1.default.isEmpty(this.logcat)) {
        throw new Error("Trying to start logcat capture but it's already started!");
    }
    this.logcat = new logcat_1.Logcat({
        adb: this.executable,
        debug: false,
        debugTrace: false,
        clearDeviceLogsOnStart: !!this.clearDeviceLogsOnStart,
    });
    await this.logcat.startCapture(opts);
    this._logcatStartupParams = opts;
}
/**
 * Stop the active logcat process which gathers logs.
 * The call will be ignored if no logcat process is running.
 * @this {import('../adb.js').ADB}
 */
async function stopLogcat() {
    if (lodash_1.default.isEmpty(this.logcat)) {
        return;
    }
    try {
        await this.logcat.stopCapture();
    }
    finally {
        this.logcat = undefined;
    }
}
/**
 * Retrieve the output from the currently running logcat process.
 * The logcat process should be executed by {2link #startLogcat} method.
 *
 * @this {import('../adb.js').ADB}
 * @return {import('./types').LogEntry[]} The collected logcat output.
 * @throws {Error} If logcat process is not running.
 */
function getLogcatLogs() {
    if (lodash_1.default.isEmpty(this.logcat)) {
        throw new Error(`Can't get logcat logs since logcat hasn't started`);
    }
    return this.logcat.getLogs();
}
/**
 * Set the callback for the logcat output event.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').LogcatListener} listener - Listener function
 * @throws {Error} If logcat process is not running.
 */
function setLogcatListener(listener) {
    if (lodash_1.default.isEmpty(this.logcat)) {
        throw new Error("Logcat process hasn't been started");
    }
    this.logcat.on('output', listener);
}
/**
 * Removes the previously set callback for the logcat output event.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').LogcatListener} listener
 * The listener function, which has been previously
 * passed to `setLogcatListener`
 * @throws {Error} If logcat process is not running.
 */
function removeLogcatListener(listener) {
    if (lodash_1.default.isEmpty(this.logcat)) {
        throw new Error("Logcat process hasn't been started");
    }
    this.logcat.removeListener('output', listener);
}
//# sourceMappingURL=logcat-commands.js.map