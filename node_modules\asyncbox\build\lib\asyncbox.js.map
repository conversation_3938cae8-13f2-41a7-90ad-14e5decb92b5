{"version": 3, "file": "asyncbox.js", "sourceRoot": "", "sources": ["../../lib/asyncbox.js"], "names": [], "mappings": ";;;;;;AAAA,wDAAyB;AACzB,oDAAuB;AAEvB,MAAM,oBAAoB,GAAG,IAAI,CAAC,CAAC,6CAA6C;AAEhF;;;;GAIG;AACH,KAAK,UAAU,KAAK,CAAE,EAAE;IACtB,OAAO,MAAM,kBAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC;AAmOC,sBAAK;AAjOP;;;;;;;;;;;;GAYG;AACH,KAAK,UAAU,SAAS,CAAE,EAAE,EAAE,EAC5B,WAAW,GAAG,oBAAoB,EAClC,UAAU,GAAG,IAAI,EACjB,UAAU,GAAG,IAAI,GAClB,GAAG,EAAE;IACJ,IAAI,EAAE,GAAG,WAAW,EAAE;QACpB,OAAO,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC;KACxB;IACD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IAC9B,IAAI,QAAQ,CAAC;IACb,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,GAAG;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,KAAK,CAAC,UAAU,CAAC,CAAC;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxB,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC;QACxB,SAAS,GAAG,SAAS,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;QACrC,IAAI,gBAAC,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAC5B,UAAU,CAAC,EAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,GAAG,EAAE,EAAC,CAAC,CAAC;SAC7D;KACF,QAAQ,QAAQ,GAAG,CAAC,EAAE;AACzB,CAAC;AAgM0C,8BAAS;AA9LpD;;;;;;;GAOG;AACH,KAAK,UAAU,KAAK,CAAE,KAAK,EAAE,EAAE,EAAE,GAAG,IAAI;IACtC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,IAAI,GAAG,KAAK,CAAC;IACjB,IAAI,GAAG,GAAG,IAAI,CAAC;IACf,OAAO,CAAC,IAAI,IAAI,KAAK,GAAG,KAAK,EAAE;QAC7B,KAAK,EAAE,CAAC;QACR,IAAI;YACF,GAAG,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YACxB,IAAI,GAAG,IAAI,CAAC;SACb;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,KAAK,IAAI,KAAK,EAAE;gBAClB,MAAM,GAAG,CAAC;aACX;SACF;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAqKQ,sBAAK;AAnKd;;;;;;;;;GASG;AACH,KAAK,UAAU,aAAa,CAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,IAAI;IACvD,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,OAAO,GAAG,KAAK,IAAI,EAAE;QACvB,KAAK,EAAE,CAAC;QACR,IAAI,GAAG,CAAC;QACR,IAAI;YACF,GAAG,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;SACzB;QAAC,OAAO,CAAC,EAAE;YACV,4CAA4C;YAC5C,IAAI,KAAK,KAAK,KAAK,EAAE;gBACnB,MAAM,KAAK,CAAC,OAAO,CAAC,CAAC;aACtB;YACD,MAAM,CAAC,CAAC;SACT;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IACF,OAAO,MAAM,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACrC,CAAC;AAwIoC,sCAAa;AAtIlD,MAAM,QAAQ,GAAG,kBAAC,CAAC,GAAG,CAAC;AAsIuC,4BAAQ;AApItE;;;;;;;GAOG;AACH,SAAS,OAAO,CAAE,QAAQ,EAAE,EAAE;IAC5B,OAAO,kBAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACzC,CAAC;AA0He,0BAAO;AAxHvB;;;;GAIG;AACH,SAAS,UAAU,CAAE,WAAW;IAC9B,oDAAoD;IACpD,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;QAC/C,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,IAAI;YAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzB,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5B,CAAC,CAAC;KACH;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAwGwB,gCAAU;AAtGnC;;;GAGG;AACH,SAAS,QAAQ,CAAE,EAAE,EAAE,GAAG,IAAI;IAC5B,kBAAC,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAChC,CAAC;AAgGmD,4BAAQ;AA9F5D;;;;;GAKG;AACH,KAAK,UAAU,QAAQ,CAAE,IAAI,EAAE,MAAM,EAAE,aAAa,GAAG,IAAI;IACzD,IAAI,aAAa,EAAE;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;KACnC;IAED,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;QACrB,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;KAClC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AA+EC,4BAAQ;AA7EV;;;;;;GAMG;AACH,KAAK,UAAU,WAAW,CAAE,IAAI,EAAE,MAAM,EAAE,aAAa,GAAG,IAAI;IAC5D,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,aAAa,EAAE;QACjB,IAAI,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;gBACZ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACvB;SACF;KACF;SAAM;QACL,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;YACrB,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACpB;SACF;KACF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAqDW,kCAAW;AAnDvB;;;;;;;;;;;;;;;;GAgBG;AACH,KAAK,UAAU,gBAAgB,CAAE,MAAM,EAAE,OAAO,GAAG,EAAE;IACnD,6EAA6E;IAC7E,MAAM,IAAI,GAAG,gBAAC,CAAC,QAAQ,CAAC,OAAO,EAAE;QAC/B,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE,GAAG;KAChB,CAAC,CAAC;IACH,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAC,CAAC,IAAI,CAAC;IACzE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC3B,MAAM,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;IACpC,4BAA4B;IAC5B,MAAM,IAAI,GAAG,KAAK,UAAU,IAAI;QAC9B,MAAM,MAAM,GAAG,MAAM,MAAM,EAAE,CAAC;QAC9B,IAAI,MAAM,EAAE;YACV,OAAO,MAAM,CAAC;SACf;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;QAC7B,MAAM,aAAa,GAAG,KAAK,GAAG,GAAG,CAAC;QAClC,IAAI,GAAG,GAAG,KAAK,EAAE;YACf,KAAK,CAAC,cAAc,MAAM,YAAY,CAAC,CAAC;YACxC,MAAM,kBAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC;YACxD,OAAO,MAAM,IAAI,EAAE,CAAC;SACrB;QACD,gFAAgF;QAChF,MAAM,KAAK;YACT,CAAC,CAAC,CAAC,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAChD,CAAC,CAAC,IAAI,KAAK,CAAC,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;IACnE,CAAC,CAAC;IACF,OAAO,MAAM,IAAI,EAAE,CAAC;AACtB,CAAC;AAIwB,4CAAgB;AAGzC;;;;;;;GAOG;AAEH;;;;;;GAMG;AAEH;;;;;;GAMG;AAEH;;;;;GAKG"}