/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<string>}
 */
export function mobileViewportScreenshot(this: import("../driver").AndroidUiautomator2Driver): Promise<string>;
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<string>}
 */
export function getViewportScreenshot(this: import("../driver").AndroidUiautomator2Driver): Promise<string>;
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<string>}
 */
export function getScreenshot(this: import("../driver").AndroidUiautomator2Driver): Promise<string>;
/**
 * Retrieves screenshots of each display available to Android.
 * This functionality is only supported since Android 10.
 * @this {AndroidUiautomator2Driver}
 * @param {number | string} [displayId] Android display identifier to take a screenshot for.
 * If not provided then screenshots of all displays are going to be returned.
 * If no matches were found then an error is thrown.
 * @returns {Promise<import('@appium/types').StringRecord<import('./types').Screenshot>>}
 */
export function mobileScreenshots(this: import("../driver").AndroidUiautomator2Driver, displayId?: number | string): Promise<import("@appium/types").StringRecord<import("./types").Screenshot>>;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
//# sourceMappingURL=screenshot.d.ts.map