{"name": "@types/sinonjs__fake-timers", "version": "8.1.5", "description": "TypeScript definitions for @sinonjs/fake-timers", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sinonjs__fake-timers", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "Nemo157", "url": "https://github.com/Nemo157"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rogierschouten"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON>sha<PERSON>", "url": "https://github.com/zyishai"}, {"name": "Re<PERSON><PERSON>", "githubUsername": "rem<PERSON><PERSON><PERSON>", "url": "https://github.com/remcohaszing"}, {"name": "<PERSON><PERSON>", "githubUsername": "JadenSimon", "url": "https://github.com/JadenSimon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sinonjs__fake-timers"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "ac02b09bd69bca309a0c11d5747e87b33dce4bc3dad14158ef12fcfb775225d3", "typeScriptVersion": "4.5"}