import { type AppiumServer, type BaseDriverCapConstraints, type Capabilities, type Constraints, type DefaultCreateSessionResult, type Driver, type DriverCaps, type DriverData, type MultiSessionData, type ServerArgs, type StringRecord, type W3CDriverCaps, type InitialOpts, type DefaultDeleteSessionResult, type SingularSessionData, type SessionCapabilities } from '@appium/types';
import { DriverCore } from './core';
export declare class BaseDriver<const C extends Constraints, CArgs extends StringRecord = StringRecord, Settings extends StringRecord = StringRecord, CreateResult = DefaultCreateSessionResult<C>, DeleteResult = DefaultDeleteSessionResult, SessionData extends StringRecord = StringRecord> extends DriverCore<C, Settings> implements Driver<C, CArgs, Settings, CreateResult, DeleteResult, SessionData> {
    cliArgs: CArgs & ServerArgs;
    caps: DriverCaps<C>;
    originalCaps: W3CDriverCaps<C>;
    desiredCapConstraints: C;
    server?: AppiumServer;
    serverHost?: string;
    serverPort?: number;
    serverPath?: string;
    constructor(opts: InitialOpts, shouldValidateCaps?: boolean);
    /**
     * Contains the base constraints plus whatever the subclass wants to add.
     *
     * Subclasses _shouldn't_ need to use this. If you need to use this, please create
     * an issue:
     * @see {@link https://github.com/appium/appium/issues/new}
     */
    protected get _desiredCapConstraints(): Readonly<BaseDriverCapConstraints & C>;
    /**
     * This is the main command handler for the driver. It wraps command
     * execution with timeout logic, checking that we have a valid session,
     * and ensuring that we execute commands one at a time. This method is called
     * by MJSONWP's express router.
     */
    executeCommand<T = unknown>(cmd: string, ...args: any[]): Promise<T>;
    startUnexpectedShutdown(err?: Error): Promise<void>;
    startNewCommandTimeout(): Promise<void>;
    assignServer(server: AppiumServer, host: string, port: number, path: string): void;
    reset(): Promise<void>;
    /**
     *
     * Historically the first two arguments were reserved for JSONWP capabilities.
     * Appium 2 has dropped the support of these, so now we only accept capability
     * objects in W3C format and thus allow any of the three arguments to represent
     * the latter.
     */
    createSession(w3cCapabilities1: W3CDriverCaps<C>, w3cCapabilities2?: W3CDriverCaps<C>, w3cCapabilities?: W3CDriverCaps<C>, driverData?: DriverData[]): Promise<CreateResult>;
    /**
     * Returns the session id and capabilities for the session
     * @deprecated Use AppiumDriver.getAppiumSessions instead for getting the session data.
     */
    getSessions(): Promise<MultiSessionData<C>[]>;
    /**
     * Returns capabilities for the session and event history (if applicable)
     * @deprecated Use {@linkcode getAppiumSessionCapabilities} instead for getting the capabilities.
     * Use {@linkcode EventCommands.getLogEvents} instead to get the event history.
     */
    getSession(): Promise<SingularSessionData<C, SessionData>>;
    /**
     * Returns capabilities for the session
     */
    getAppiumSessionCapabilities(): Promise<SessionCapabilities<C>>;
    deleteSession(sessionId?: string | null): Promise<void>;
    logExtraCaps(caps: Capabilities<C>): void;
    validateDesiredCaps(caps: any): caps is DriverCaps<C>;
    updateSettings(newSettings: Settings): Promise<void>;
    getSettings(): Promise<Settings>;
}
export * from './commands';
export default BaseDriver;
//# sourceMappingURL=driver.d.ts.map