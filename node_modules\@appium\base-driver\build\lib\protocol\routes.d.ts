/**
 *
 * @param {string} endpoint
 * @param {import('@appium/types').HTTPMethod} [method]
 * @param {string} [basePath=DEFAULT_BASE_PATH]
 * @returns {string|undefined}
 */
export function routeToCommandName(endpoint: string, method?: import("@appium/types").HTTPMethod, basePath?: string): string | undefined;
/**
 * define the routes, mapping of HTTP methods to particular driver commands, and
 * any parameters that are expected in a request parameters can be `required` or
 * `optional`
 * @satisfies {import('@appium/types').MethodMap<import('../basedriver/driver').BaseDriver>}
 */
export const METHOD_MAP: {
    readonly '/status': {
        readonly GET: {
            readonly command: "getStatus";
        };
    };
    readonly '/session': {
        readonly POST: {
            readonly command: "createSession";
            readonly payloadParams: {
                readonly validate: (jsonObj: any) => false | "we require one of \"desiredCapabilities\" or \"capabilities\" object";
                readonly optional: readonly ["desiredCapabilities", "requiredCapabilities", "capabilities"];
            };
        };
    };
    readonly '/session/:sessionId': {
        readonly GET: {
            readonly command: "getSession";
            readonly deprecated: true;
        };
        readonly DELETE: {
            readonly command: "deleteSession";
        };
    };
    readonly '/session/:sessionId/timeouts': {
        readonly GET: {
            readonly command: "getTimeouts";
        };
        readonly POST: {
            readonly command: "timeouts";
            readonly payloadParams: {
                readonly validate: (jsonObj: any, protocolName: string) => "W3C protocol expects any of script, pageLoad or implicit to be set" | "MJSONWP protocol requires type and ms" | undefined;
                readonly optional: readonly ["type", "ms", "script", "pageLoad", "implicit"];
            };
        };
    };
    readonly '/session/:sessionId/window/handles': {
        readonly GET: {
            readonly command: "getWindowHandles";
        };
    };
    readonly '/session/:sessionId/url': {
        readonly GET: {
            readonly command: "getUrl";
        };
        readonly POST: {
            readonly command: "setUrl";
            readonly payloadParams: {
                readonly required: readonly ["url"];
            };
        };
    };
    readonly '/session/:sessionId/forward': {
        readonly POST: {
            readonly command: "forward";
        };
    };
    readonly '/session/:sessionId/back': {
        readonly POST: {
            readonly command: "back";
        };
    };
    readonly '/session/:sessionId/refresh': {
        readonly POST: {
            readonly command: "refresh";
        };
    };
    readonly '/session/:sessionId/screenshot': {
        readonly GET: {
            readonly command: "getScreenshot";
        };
    };
    readonly '/session/:sessionId/frame': {
        readonly POST: {
            readonly command: "setFrame";
            readonly payloadParams: {
                readonly required: readonly ["id"];
            };
        };
    };
    readonly '/session/:sessionId/frame/parent': {
        readonly POST: {
            readonly command: "switchToParentFrame";
        };
    };
    readonly '/session/:sessionId/window': {
        readonly GET: {
            readonly command: "getWindowHandle";
        };
        readonly POST: {
            readonly command: "setWindow";
            readonly payloadParams: {
                readonly optional: readonly ["name", "handle"];
                readonly makeArgs: (jsonObj: any) => any[];
                readonly validate: (jsonObj: any) => false | "we require one of \"name\" or \"handle\" to be set";
            };
        };
        readonly DELETE: {
            readonly command: "closeWindow";
        };
    };
    readonly '/session/:sessionId/window/maximize': {
        readonly POST: {
            readonly command: "maximizeWindow";
        };
    };
    readonly '/session/:sessionId/window/minimize': {
        readonly POST: {
            readonly command: "minimizeWindow";
        };
    };
    readonly '/session/:sessionId/window/fullscreen': {
        readonly POST: {
            readonly command: "fullScreenWindow";
        };
    };
    readonly '/session/:sessionId/window/new': {
        readonly POST: {
            readonly command: "createNewWindow";
            readonly payloadParams: {
                readonly optional: readonly ["type"];
            };
        };
    };
    readonly '/session/:sessionId/cookie': {
        readonly GET: {
            readonly command: "getCookies";
        };
        readonly POST: {
            readonly command: "setCookie";
            readonly payloadParams: {
                readonly required: readonly ["cookie"];
            };
        };
        readonly DELETE: {
            readonly command: "deleteCookies";
        };
    };
    readonly '/session/:sessionId/cookie/:name': {
        readonly GET: {
            readonly command: "getCookie";
        };
        readonly DELETE: {
            readonly command: "deleteCookie";
        };
    };
    readonly '/session/:sessionId/source': {
        readonly GET: {
            readonly command: "getPageSource";
        };
    };
    readonly '/session/:sessionId/title': {
        readonly GET: {
            readonly command: "title";
        };
    };
    readonly '/session/:sessionId/element': {
        readonly POST: {
            readonly command: "findElement";
            readonly payloadParams: {
                readonly required: readonly ["using", "value"];
            };
        };
    };
    readonly '/session/:sessionId/elements': {
        readonly POST: {
            readonly command: "findElements";
            readonly payloadParams: {
                readonly required: readonly ["using", "value"];
            };
        };
    };
    readonly '/session/:sessionId/element/active': {
        readonly GET: {
            readonly command: "active";
        };
        readonly POST: {
            readonly command: "active";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId': {
        readonly GET: {};
    };
    readonly '/session/:sessionId/element/:elementId/element': {
        readonly POST: {
            readonly command: "findElementFromElement";
            readonly payloadParams: {
                readonly required: readonly ["using", "value"];
            };
        };
    };
    readonly '/session/:sessionId/element/:elementId/elements': {
        readonly POST: {
            readonly command: "findElementsFromElement";
            readonly payloadParams: {
                readonly required: readonly ["using", "value"];
            };
        };
    };
    readonly '/session/:sessionId/element/:elementId/click': {
        readonly POST: {
            readonly command: "click";
        };
    };
    readonly '/session/:sessionId/element/:elementId/text': {
        readonly GET: {
            readonly command: "getText";
        };
    };
    readonly '/session/:sessionId/element/:elementId/value': {
        readonly POST: {
            readonly command: "setValue";
            readonly payloadParams: {
                readonly validate: (jsonObj: any) => false | "we require one of \"text\" or \"value\" params";
                readonly optional: readonly ["value", "text"];
                readonly makeArgs: (jsonObj: any) => any[];
            };
        };
    };
    readonly '/session/:sessionId/element/:elementId/name': {
        readonly GET: {
            readonly command: "getName";
        };
    };
    readonly '/session/:sessionId/element/:elementId/clear': {
        readonly POST: {
            readonly command: "clear";
        };
    };
    readonly '/session/:sessionId/element/:elementId/selected': {
        readonly GET: {
            readonly command: "elementSelected";
        };
    };
    readonly '/session/:sessionId/element/:elementId/enabled': {
        readonly GET: {
            readonly command: "elementEnabled";
        };
    };
    readonly '/session/:sessionId/element/:elementId/attribute/:name': {
        readonly GET: {
            readonly command: "getAttribute";
        };
    };
    readonly '/session/:sessionId/element/:elementId/displayed': {
        readonly GET: {
            readonly command: "elementDisplayed";
        };
    };
    readonly '/session/:sessionId/element/:elementId/shadow': {
        readonly GET: {
            readonly command: "elementShadowRoot";
        };
    };
    readonly '/session/:sessionId/shadow/:shadowId/element': {
        readonly POST: {
            readonly command: "findElementFromShadowRoot";
            readonly payloadParams: {
                readonly required: readonly ["using", "value"];
            };
        };
    };
    readonly '/session/:sessionId/shadow/:shadowId/elements': {
        readonly POST: {
            readonly command: "findElementsFromShadowRoot";
            readonly payloadParams: {
                readonly required: readonly ["using", "value"];
            };
        };
    };
    readonly '/session/:sessionId/element/:elementId/css/:propertyName': {
        readonly GET: {
            readonly command: "getCssProperty";
        };
    };
    readonly '/session/:sessionId/element/:elementId/property/:name': {
        readonly GET: {
            readonly command: "getProperty";
        };
    };
    readonly 'session/:sessionId/element/:elementId/computedrole': {
        readonly GET: {
            readonly command: "getComputedRole";
        };
    };
    readonly 'session/:sessionId/element/:elementId/computedlabel': {
        readonly GET: {
            readonly command: "getComputedLabel";
        };
    };
    readonly '/session/:sessionId/actions': {
        readonly POST: {
            readonly command: "performActions";
            readonly payloadParams: {
                readonly required: readonly ["actions"];
            };
        };
        readonly DELETE: {
            readonly command: "releaseActions";
        };
    };
    readonly '/session/:sessionId/alert/text': {
        readonly GET: {
            readonly command: "getAlertText";
        };
        readonly POST: {
            readonly command: "setAlertText";
            readonly payloadParams: {
                validate: (jsonObj: any) => false | "either \"text\" or \"value\" must be set";
                optional: string[];
                makeArgs: (jsonObj: any) => any[];
            };
        };
    };
    readonly '/session/:sessionId/alert/accept': {
        readonly POST: {
            readonly command: "postAcceptAlert";
        };
    };
    readonly '/session/:sessionId/alert/dismiss': {
        readonly POST: {
            readonly command: "postDismissAlert";
        };
    };
    readonly '/session/:sessionId/element/:elementId/rect': {
        readonly GET: {
            readonly command: "getElementRect";
        };
    };
    readonly '/session/:sessionId/execute/sync': {
        readonly POST: {
            readonly command: "execute";
            readonly payloadParams: {
                readonly required: readonly ["script", "args"];
            };
        };
    };
    readonly '/session/:sessionId/execute/async': {
        readonly POST: {
            readonly command: "executeAsync";
            readonly payloadParams: {
                readonly required: readonly ["script", "args"];
            };
        };
    };
    readonly '/session/:sessionId/element/:elementId/screenshot': {
        readonly GET: {
            readonly command: "getElementScreenshot";
        };
    };
    readonly '/session/:sessionId/window/rect': {
        readonly GET: {
            readonly command: "getWindowRect";
        };
        readonly POST: {
            readonly command: "setWindowRect";
            readonly payloadParams: {
                readonly optional: readonly ["x", "y", "width", "height"];
            };
        };
    };
    readonly '/session/:sessionId/ime/available_engines': {
        readonly GET: {
            readonly command: "availableIMEEngines";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/ime/active_engine': {
        readonly GET: {
            readonly command: "getActiveIMEEngine";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/ime/activated': {
        readonly GET: {
            readonly command: "isIMEActivated";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/ime/deactivate': {
        readonly POST: {
            readonly command: "deactivateIMEEngine";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/ime/activate': {
        readonly POST: {
            readonly command: "activateIMEEngine";
            readonly payloadParams: {
                readonly required: readonly ["engine"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/rotation': {
        readonly GET: {
            readonly command: "getRotation";
        };
        readonly POST: {
            readonly command: "setRotation";
            readonly payloadParams: {
                readonly required: readonly ["x", "y", "z"];
            };
        };
    };
    readonly '/session/:sessionId/location': {
        readonly GET: {
            readonly command: "getGeoLocation";
            readonly deprecated: true;
        };
        readonly POST: {
            readonly command: "setGeoLocation";
            readonly payloadParams: {
                readonly required: readonly ["location"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/orientation': {
        readonly GET: {
            readonly command: "getOrientation";
        };
        readonly POST: {
            readonly command: "setOrientation";
            readonly payloadParams: {
                readonly required: readonly ["orientation"];
            };
        };
    };
    readonly '/session/:sessionId/context': {
        readonly GET: {
            readonly command: "getCurrentContext";
        };
        readonly POST: {
            readonly command: "setContext";
            readonly payloadParams: {
                readonly required: readonly ["name"];
            };
        };
    };
    readonly '/session/:sessionId/contexts': {
        readonly GET: {
            readonly command: "getContexts";
        };
    };
    readonly '/session/:sessionId/network_connection': {
        readonly GET: {
            readonly command: "getNetworkConnection";
            readonly deprecated: true;
        };
        readonly POST: {
            readonly command: "setNetworkConnection";
            readonly payloadParams: {
                readonly unwrap: "parameters";
                readonly required: readonly ["type"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/receive_async_response': {
        readonly POST: {
            readonly command: "receiveAsyncResponse";
            readonly payloadParams: {
                readonly required: readonly ["status", "value"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/appium/sessions': {
        readonly GET: {
            readonly command: "getAppiumSessions";
        };
    };
    readonly '/session/:sessionId/appium/capabilities': {
        readonly GET: {
            readonly command: "getAppiumSessionCapabilities";
        };
    };
    readonly '/session/:sessionId/appium/device/system_time': {
        readonly GET: {
            readonly command: "getDeviceTime";
            readonly payloadParams: {
                readonly optional: readonly ["format"];
            };
        };
        readonly POST: {
            readonly command: "getDeviceTime";
            readonly payloadParams: {
                readonly optional: readonly ["format"];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/install_app': {
        readonly POST: {
            readonly command: "installApp";
            readonly payloadParams: {
                readonly required: readonly ["appPath"];
                readonly optional: readonly ["options"];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/activate_app': {
        readonly POST: {
            readonly command: "activateApp";
            readonly payloadParams: {
                readonly required: readonly [readonly ["appId"], readonly ["bundleId"]];
                readonly optional: readonly ["options"];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/remove_app': {
        readonly POST: {
            readonly command: "removeApp";
            readonly payloadParams: {
                readonly required: readonly [readonly ["appId"], readonly ["bundleId"]];
                readonly optional: readonly ["options"];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/terminate_app': {
        readonly POST: {
            readonly command: "terminateApp";
            readonly payloadParams: {
                readonly required: readonly [readonly ["appId"], readonly ["bundleId"]];
                readonly optional: readonly ["options"];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/app_installed': {
        readonly POST: {
            readonly command: "isAppInstalled";
            readonly payloadParams: {
                readonly required: readonly [readonly ["appId"], readonly ["bundleId"]];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/app_state': {
        readonly GET: {
            readonly command: "queryAppState";
            readonly payloadParams: {
                readonly required: readonly [readonly ["appId"], readonly ["bundleId"]];
            };
        };
        readonly POST: {
            readonly command: "queryAppState";
            readonly payloadParams: {
                readonly required: readonly [readonly ["appId"], readonly ["bundleId"]];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/hide_keyboard': {
        readonly POST: {
            readonly command: "hideKeyboard";
            readonly payloadParams: {
                readonly optional: readonly ["strategy", "key", "keyCode", "keyName"];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/is_keyboard_shown': {
        readonly GET: {
            readonly command: "isKeyboardShown";
        };
    };
    readonly '/session/:sessionId/appium/device/push_file': {
        readonly POST: {
            readonly command: "pushFile";
            readonly payloadParams: {
                readonly required: readonly ["path", "data"];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/pull_file': {
        readonly POST: {
            readonly command: "pullFile";
            readonly payloadParams: {
                readonly required: readonly ["path"];
            };
        };
    };
    readonly '/session/:sessionId/appium/device/pull_folder': {
        readonly POST: {
            readonly command: "pullFolder";
            readonly payloadParams: {
                readonly required: readonly ["path"];
            };
        };
    };
    readonly '/session/:sessionId/appium/settings': {
        readonly POST: {
            readonly command: "updateSettings";
            readonly payloadParams: {
                readonly required: readonly ["settings"];
            };
        };
        readonly GET: {
            readonly command: "getSettings";
        };
    };
    readonly '/session/:sessionId/appium/events': {
        readonly POST: {
            readonly command: "getLogEvents";
            readonly payloadParams: {
                readonly optional: readonly ["type"];
            };
        };
    };
    readonly '/session/:sessionId/appium/log_event': {
        readonly POST: {
            readonly command: "logCustomEvent";
            readonly payloadParams: {
                readonly required: readonly ["vendor", "event"];
            };
        };
    };
    readonly '/session/:sessionId/appium/commands': {
        readonly GET: {
            readonly command: "listCommands";
        };
    };
    readonly '/session/:sessionId/appium/extensions': {
        readonly GET: {
            readonly command: "listExtensions";
        };
    };
    readonly '/session/:sessionId/se/log': {
        readonly POST: {
            readonly command: "getLog";
            readonly payloadParams: {
                readonly required: readonly ["type"];
            };
        };
    };
    readonly '/session/:sessionId/se/log/types': {
        readonly GET: {
            readonly command: "getLogTypes";
        };
    };
    readonly '/session/:sessionId/:vendor/cdp/execute': {
        readonly POST: {
            readonly command: "executeCdp";
            readonly payloadParams: {
                readonly required: readonly ["cmd", "params"];
            };
        };
    };
    readonly '/session/:sessionId/webauthn/authenticator': {
        readonly POST: {
            readonly command: "addVirtualAuthenticator";
            readonly payloadParams: {
                readonly required: readonly ["protocol", "transport"];
                readonly optional: readonly ["hasResidentKey", "hasUserVerification", "isUserConsenting", "isUserVerified"];
            };
        };
    };
    readonly '/session/:sessionId/webauthn/authenticator/:authenticatorId': {
        readonly DELETE: {
            readonly command: "removeVirtualAuthenticator";
        };
    };
    readonly '/session/:sessionId/webauthn/authenticator/:authenticatorId/credential': {
        readonly POST: {
            readonly command: "addAuthCredential";
            readonly payloadParams: {
                readonly required: readonly ["credentialId", "isResidentCredential", "rpId", "privateKey"];
                readonly optional: readonly ["userHandle", "signCount"];
            };
        };
    };
    readonly '/session/:sessionId/webauthn/authenticator/:authenticatorId/credentials': {
        readonly GET: {
            readonly command: "getAuthCredential";
        };
        readonly DELETE: {
            readonly command: "removeAllAuthCredentials";
        };
    };
    readonly '/session/:sessionId/webauthn/authenticator/:authenticatorId/credentials/:credentialId': {
        readonly DELETE: {
            readonly command: "removeAuthCredential";
        };
    };
    readonly '/session/:sessionId/webauthn/authenticator/:authenticatorId/uv': {
        readonly POST: {
            readonly command: "setUserAuthVerified";
            readonly payloadParams: {
                readonly required: readonly ["isUserVerified"];
            };
        };
    };
    readonly '/sessions': {
        readonly GET: {
            readonly command: "getSessions";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/timeouts/async_script': {
        readonly POST: {
            readonly command: "asyncScriptTimeout";
            readonly payloadParams: {
                readonly required: readonly ["ms"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/timeouts/implicit_wait': {
        readonly POST: {
            readonly command: "implicitWait";
            readonly payloadParams: {
                readonly required: readonly ["ms"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/window_handle': {
        readonly GET: {
            readonly command: "getWindowHandle";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/window/handle': {
        readonly GET: {
            readonly command: "getWindowHandle";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/window_handles': {
        readonly GET: {
            readonly command: "getWindowHandles";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/execute': {
        readonly POST: {
            readonly command: "execute";
            readonly payloadParams: {
                readonly required: readonly ["script", "args"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/execute_async': {
        readonly POST: {
            readonly command: "executeAsync";
            readonly payloadParams: {
                readonly required: readonly ["script", "args"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/window/:windowhandle/size': {
        readonly GET: {
            readonly command: "getWindowSize";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/window/:windowhandle/position': {
        readonly POST: {
            readonly deprecated: true;
        };
        readonly GET: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/window/:windowhandle/maximize': {
        readonly POST: {
            readonly command: "maximizeWindow";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/submit': {
        readonly POST: {
            readonly command: "submit";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/keys': {
        readonly POST: {
            readonly command: "keys";
            readonly payloadParams: {
                readonly required: readonly ["value"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/equals/:otherId': {
        readonly GET: {
            readonly command: "equalsElement";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/location': {
        readonly GET: {
            readonly command: "getLocation";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/location_in_view': {
        readonly GET: {
            readonly command: "getLocationInView";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/size': {
        readonly GET: {
            readonly command: "getSize";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/moveto': {
        readonly POST: {
            readonly command: "moveTo";
            readonly payloadParams: {
                readonly optional: readonly ["element", "xoffset", "yoffset"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/click': {
        readonly POST: {
            readonly command: "clickCurrent";
            readonly payloadParams: {
                readonly optional: readonly ["button"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/buttondown': {
        readonly POST: {
            readonly command: "buttonDown";
            readonly payloadParams: {
                readonly optional: readonly ["button"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/buttonup': {
        readonly POST: {
            readonly command: "buttonUp";
            readonly payloadParams: {
                readonly optional: readonly ["button"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/doubleclick': {
        readonly POST: {
            readonly command: "doubleClick";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/click': {
        readonly POST: {
            readonly command: "click";
            readonly payloadParams: {
                readonly required: readonly ["element"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/down': {
        readonly POST: {
            readonly command: "touchDown";
            readonly payloadParams: {
                readonly required: readonly ["x", "y"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/up': {
        readonly POST: {
            readonly command: "touchUp";
            readonly payloadParams: {
                readonly required: readonly ["x", "y"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/move': {
        readonly POST: {
            readonly command: "touchMove";
            readonly payloadParams: {
                readonly required: readonly ["x", "y"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/scroll': {
        readonly POST: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/doubleclick': {
        readonly POST: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/longclick': {
        readonly POST: {
            readonly command: "touchLongClick";
            readonly payloadParams: {
                readonly required: readonly ["elements"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/flick': {
        readonly POST: {
            readonly command: "flick";
            readonly payloadParams: {
                readonly optional: readonly ["element", "xspeed", "yspeed", "xoffset", "yoffset", "speed"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/local_storage': {
        readonly GET: {
            readonly deprecated: true;
        };
        readonly POST: {
            readonly deprecated: true;
        };
        readonly DELETE: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/local_storage/key/:key': {
        readonly GET: {
            readonly deprecated: true;
        };
        readonly DELETE: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/local_storage/size': {
        readonly GET: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/session_storage': {
        readonly GET: {
            readonly deprecated: true;
        };
        readonly POST: {
            readonly deprecated: true;
        };
        readonly DELETE: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/session_storage/key/:key': {
        readonly GET: {
            readonly deprecated: true;
        };
        readonly DELETE: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/session_storage/size': {
        readonly GET: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/application_cache/status': {
        readonly GET: {
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/alert_text': {
        readonly GET: {
            readonly command: "getAlertText";
            readonly deprecated: true;
        };
        readonly POST: {
            readonly command: "setAlertText";
            readonly payloadParams: {
                validate: (jsonObj: any) => false | "either \"text\" or \"value\" must be set";
                optional: string[];
                makeArgs: (jsonObj: any) => any[];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/accept_alert': {
        readonly POST: {
            readonly command: "postAcceptAlert";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/dismiss_alert': {
        readonly POST: {
            readonly command: "postDismissAlert";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/screenshot/:elementId': {
        readonly GET: {
            readonly command: "getElementScreenshot";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/element/:elementId/pageIndex': {
        readonly GET: {
            readonly command: "getPageIndex";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/perform': {
        readonly POST: {
            readonly command: "performTouch";
            readonly payloadParams: {
                readonly wrap: "actions";
                readonly required: readonly ["actions"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/touch/multi/perform': {
        readonly POST: {
            readonly command: "performMultiAction";
            readonly payloadParams: {
                readonly required: readonly ["actions"];
                readonly optional: readonly ["elementId"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/shake': {
        readonly POST: {
            readonly command: "mobileShake";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/lock': {
        readonly POST: {
            readonly command: "lock";
            readonly payloadParams: {
                readonly optional: readonly ["seconds"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/unlock': {
        readonly POST: {
            readonly command: "unlock";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/is_locked': {
        readonly POST: {
            readonly command: "isLocked";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/start_recording_screen': {
        readonly POST: {
            readonly command: "startRecordingScreen";
            readonly payloadParams: {
                readonly optional: readonly ["options"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/stop_recording_screen': {
        readonly POST: {
            readonly command: "stopRecordingScreen";
            readonly payloadParams: {
                readonly optional: readonly ["options"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/performanceData/types': {
        readonly POST: {
            readonly command: "getPerformanceDataTypes";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/getPerformanceData': {
        readonly POST: {
            readonly command: "getPerformanceData";
            readonly payloadParams: {
                readonly required: readonly ["packageName", "dataType"];
                readonly optional: readonly ["dataReadTimeout"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/press_keycode': {
        readonly POST: {
            readonly command: "pressKeyCode";
            readonly payloadParams: {
                readonly required: readonly ["keycode"];
                readonly optional: readonly ["metastate", "flags"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/long_press_keycode': {
        readonly POST: {
            readonly command: "longPressKeyCode";
            readonly payloadParams: {
                readonly required: readonly ["keycode"];
                readonly optional: readonly ["metastate", "flags"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/finger_print': {
        readonly POST: {
            readonly command: "fingerprint";
            readonly payloadParams: {
                readonly required: readonly ["fingerprintId"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/send_sms': {
        readonly POST: {
            readonly command: "sendSMS";
            readonly payloadParams: {
                readonly required: readonly ["phoneNumber", "message"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/gsm_call': {
        readonly POST: {
            readonly command: "gsmCall";
            readonly payloadParams: {
                readonly required: readonly ["phoneNumber", "action"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/gsm_signal': {
        readonly POST: {
            readonly command: "gsmSignal";
            readonly payloadParams: {
                readonly required: readonly ["signalStrength"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/gsm_voice': {
        readonly POST: {
            readonly command: "gsmVoice";
            readonly payloadParams: {
                readonly required: readonly ["state"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/power_capacity': {
        readonly POST: {
            readonly command: "powerCapacity";
            readonly payloadParams: {
                readonly required: readonly ["percent"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/power_ac': {
        readonly POST: {
            readonly command: "powerAC";
            readonly payloadParams: {
                readonly required: readonly ["state"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/network_speed': {
        readonly POST: {
            readonly command: "networkSpeed";
            readonly payloadParams: {
                readonly required: readonly ["netspeed"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/keyevent': {
        readonly POST: {
            readonly command: "keyevent";
            readonly payloadParams: {
                readonly required: readonly ["keycode"];
                readonly optional: readonly ["metastate"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/current_activity': {
        readonly GET: {
            readonly command: "getCurrentActivity";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/current_package': {
        readonly GET: {
            readonly command: "getCurrentPackage";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/toggle_airplane_mode': {
        readonly POST: {
            readonly command: "toggleFlightMode";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/toggle_data': {
        readonly POST: {
            readonly command: "toggleData";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/toggle_wifi': {
        readonly POST: {
            readonly command: "toggleWiFi";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/toggle_location_services': {
        readonly POST: {
            readonly command: "toggleLocationServices";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/open_notifications': {
        readonly POST: {
            readonly command: "openNotifications";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/start_activity': {
        readonly POST: {
            readonly command: "startActivity";
            readonly payloadParams: {
                readonly required: readonly ["appPackage", "appActivity"];
                readonly optional: readonly ["appWaitPackage", "appWaitActivity", "intentAction", "intentCategory", "intentFlags", "optionalIntentArguments", "dontStopAppOnReset"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/system_bars': {
        readonly GET: {
            readonly command: "getSystemBars";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/display_density': {
        readonly GET: {
            readonly command: "getDisplayDensity";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/simulator/touch_id': {
        readonly POST: {
            readonly command: "touchId";
            readonly payloadParams: {
                readonly required: readonly ["match"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/simulator/toggle_touch_id_enrollment': {
        readonly POST: {
            readonly command: "toggleEnrollTouchId";
            readonly payloadParams: {
                readonly optional: readonly ["enabled"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/app/launch': {
        readonly POST: {
            readonly command: "launchApp";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/app/close': {
        readonly POST: {
            readonly command: "closeApp";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/app/reset': {
        readonly POST: {
            readonly command: "reset";
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/app/background': {
        readonly POST: {
            readonly command: "background";
            readonly payloadParams: {
                readonly required: readonly ["seconds"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/app/end_test_coverage': {
        readonly POST: {
            readonly command: "endCoverage";
            readonly payloadParams: {
                readonly required: readonly ["intent", "path"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/app/strings': {
        readonly POST: {
            readonly command: "getStrings";
            readonly payloadParams: {
                readonly optional: readonly ["language", "stringFile"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/element/:elementId/value': {
        readonly POST: {
            readonly command: "setValueImmediate";
            readonly payloadParams: {
                readonly required: readonly ["text"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/element/:elementId/replace_value': {
        readonly POST: {
            readonly command: "replaceValue";
            readonly payloadParams: {
                readonly required: readonly ["text"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/receive_async_response': {
        readonly POST: {
            readonly command: "receiveAsyncResponse";
            readonly payloadParams: {
                readonly required: readonly ["response"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/set_clipboard': {
        readonly POST: {
            readonly command: "setClipboard";
            readonly payloadParams: {
                readonly required: readonly ["content"];
                readonly optional: readonly ["contentType", "label"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/appium/device/get_clipboard': {
        readonly POST: {
            readonly command: "getClipboard";
            readonly payloadParams: {
                readonly optional: readonly ["contentType"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/log': {
        readonly POST: {
            readonly command: "getLog";
            readonly payloadParams: {
                readonly required: readonly ["type"];
            };
            readonly deprecated: true;
        };
    };
    readonly '/session/:sessionId/log/types': {
        readonly GET: {
            readonly command: "getLogTypes";
            readonly deprecated: true;
        };
    };
};
export const ALL_COMMANDS: any[];
export const NO_SESSION_ID_COMMANDS: string[];
//# sourceMappingURL=routes.d.ts.map