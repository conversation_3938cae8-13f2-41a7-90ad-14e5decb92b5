var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// node_modules/leven/index.js
var require_leven = __commonJS({
  "node_modules/leven/index.js"(exports2, module2) {
    "use strict";
    var array = [];
    var charCodeCache = [];
    var leven2 = (left, right) => {
      if (left === right) {
        return 0;
      }
      const swap = left;
      if (left.length > right.length) {
        left = right;
        right = swap;
      }
      let leftLength = left.length;
      let rightLength = right.length;
      while (leftLength > 0 && left.charCodeAt(~-leftLength) === right.charCodeAt(~-rightLength)) {
        leftLength--;
        rightLength--;
      }
      let start = 0;
      while (start < leftLength && left.charCodeAt(start) === right.charCodeAt(start)) {
        start++;
      }
      leftLength -= start;
      rightLength -= start;
      if (leftLength === 0) {
        return rightLength;
      }
      let bCharCode;
      let result;
      let temp;
      let temp2;
      let i = 0;
      let j = 0;
      while (i < leftLength) {
        charCodeCache[i] = left.charCodeAt(start + i);
        array[i] = ++i;
      }
      while (j < rightLength) {
        bCharCode = right.charCodeAt(start + j);
        temp = j++;
        result = j;
        for (i = 0; i < leftLength; i++) {
          temp2 = bCharCode === charCodeCache[i] ? temp : temp + 1;
          temp = array[i];
          result = array[i] = temp > result ? temp2 > result ? result + 1 : temp2 : temp2 > temp ? temp + 1 : temp2;
        }
      }
      return result;
    };
    module2.exports = leven2;
    module2.exports.default = leven2;
  }
});

// node_modules/jsonpointer/jsonpointer.js
var require_jsonpointer = __commonJS({
  "node_modules/jsonpointer/jsonpointer.js"(exports2) {
    var hasExcape = /~/;
    var escapeMatcher = /~[01]/g;
    function escapeReplacer(m) {
      switch (m) {
        case "~1":
          return "/";
        case "~0":
          return "~";
      }
      throw new Error("Invalid tilde escape: " + m);
    }
    function untilde(str) {
      if (!hasExcape.test(str)) return str;
      return str.replace(escapeMatcher, escapeReplacer);
    }
    function setter(obj, pointer2, value) {
      var part;
      var hasNextPart;
      for (var p = 1, len = pointer2.length; p < len; ) {
        if (pointer2[p] === "constructor" || pointer2[p] === "prototype" || pointer2[p] === "__proto__") return obj;
        part = untilde(pointer2[p++]);
        hasNextPart = len > p;
        if (typeof obj[part] === "undefined") {
          if (Array.isArray(obj) && part === "-") {
            part = obj.length;
          }
          if (hasNextPart) {
            if (pointer2[p] !== "" && pointer2[p] < Infinity || pointer2[p] === "-") obj[part] = [];
            else obj[part] = {};
          }
        }
        if (!hasNextPart) break;
        obj = obj[part];
      }
      var oldValue = obj[part];
      if (value === void 0) delete obj[part];
      else obj[part] = value;
      return oldValue;
    }
    function compilePointer(pointer2) {
      if (typeof pointer2 === "string") {
        pointer2 = pointer2.split("/");
        if (pointer2[0] === "") return pointer2;
        throw new Error("Invalid JSON pointer.");
      } else if (Array.isArray(pointer2)) {
        for (const part of pointer2) {
          if (typeof part !== "string" && typeof part !== "number") {
            throw new Error("Invalid JSON pointer. Must be of type string or number.");
          }
        }
        return pointer2;
      }
      throw new Error("Invalid JSON pointer.");
    }
    function get(obj, pointer2) {
      if (typeof obj !== "object") throw new Error("Invalid input object.");
      pointer2 = compilePointer(pointer2);
      var len = pointer2.length;
      if (len === 1) return obj;
      for (var p = 1; p < len; ) {
        obj = obj[untilde(pointer2[p++])];
        if (len === p) return obj;
        if (typeof obj !== "object" || obj === null) return void 0;
      }
    }
    function set(obj, pointer2, value) {
      if (typeof obj !== "object") throw new Error("Invalid input object.");
      pointer2 = compilePointer(pointer2);
      if (pointer2.length === 0) throw new Error("Invalid JSON pointer for set.");
      return setter(obj, pointer2, value);
    }
    function compile(pointer2) {
      var compiled = compilePointer(pointer2);
      return {
        get: function(object) {
          return get(object, compiled);
        },
        set: function(object, value) {
          return set(object, compiled, value);
        }
      };
    }
    exports2.get = get;
    exports2.set = set;
    exports2.compile = compile;
  }
});

// src/index.js
var src_exports = {};
__export(src_exports, {
  default: () => src_default
});
module.exports = __toCommonJS(src_exports);

// node_modules/@humanwhocodes/momoa/dist/momoa.js
var CHAR_0 = 48;
var CHAR_1 = 49;
var CHAR_9 = 57;
var CHAR_BACKSLASH = 92;
var CHAR_DOLLAR = 36;
var CHAR_DOT = 46;
var CHAR_DOUBLE_QUOTE = 34;
var CHAR_LOWER_A = 97;
var CHAR_LOWER_E = 101;
var CHAR_LOWER_F = 102;
var CHAR_LOWER_N = 110;
var CHAR_LOWER_T = 116;
var CHAR_LOWER_U = 117;
var CHAR_LOWER_X = 120;
var CHAR_LOWER_Z = 122;
var CHAR_MINUS = 45;
var CHAR_NEWLINE = 10;
var CHAR_PLUS = 43;
var CHAR_RETURN = 13;
var CHAR_SINGLE_QUOTE = 39;
var CHAR_SLASH = 47;
var CHAR_SPACE = 32;
var CHAR_TAB = 9;
var CHAR_UNDERSCORE = 95;
var CHAR_UPPER_A = 65;
var CHAR_UPPER_E = 69;
var CHAR_UPPER_F = 70;
var CHAR_UPPER_N = 78;
var CHAR_UPPER_X = 88;
var CHAR_UPPER_Z = 90;
var CHAR_LOWER_B = 98;
var CHAR_LOWER_R = 114;
var CHAR_LOWER_V = 118;
var CHAR_LINE_SEPARATOR = 8232;
var CHAR_PARAGRAPH_SEPARATOR = 8233;
var CHAR_LOWER_L = 108;
var CHAR_LOWER_S = 115;
var CHAR_UPPER_I = 73;
var CHAR_STAR = 42;
var CHAR_VTAB = 11;
var CHAR_FORM_FEED = 12;
var CHAR_NBSP = 160;
var CHAR_BOM = 65279;
var CHAR_NON_BREAKING_SPACE = 160;
var CHAR_EN_QUAD = 8192;
var CHAR_EM_QUAD = 8193;
var CHAR_EN_SPACE = 8194;
var CHAR_EM_SPACE = 8195;
var CHAR_THREE_PER_EM_SPACE = 8196;
var CHAR_FOUR_PER_EM_SPACE = 8197;
var CHAR_SIX_PER_EM_SPACE = 8198;
var CHAR_FIGURE_SPACE = 8199;
var CHAR_PUNCTUATION_SPACE = 8200;
var CHAR_THIN_SPACE = 8201;
var CHAR_HAIR_SPACE = 8202;
var CHAR_NARROW_NO_BREAK_SPACE = 8239;
var CHAR_MEDIUM_MATHEMATICAL_SPACE = 8287;
var CHAR_IDEOGRAPHIC_SPACE = 12288;
var LBRACKET = "[";
var RBRACKET = "]";
var LBRACE = "{";
var RBRACE = "}";
var COLON = ":";
var COMMA = ",";
var TRUE = "true";
var FALSE = "false";
var NULL = "null";
var NAN$1 = "NaN";
var INFINITY$1 = "Infinity";
var QUOTE = '"';
var expectedKeywords = /* @__PURE__ */ new Map([
  [CHAR_LOWER_T, [CHAR_LOWER_R, CHAR_LOWER_U, CHAR_LOWER_E]],
  [CHAR_LOWER_F, [CHAR_LOWER_A, CHAR_LOWER_L, CHAR_LOWER_S, CHAR_LOWER_E]],
  [CHAR_LOWER_N, [CHAR_LOWER_U, CHAR_LOWER_L, CHAR_LOWER_L]]
]);
var escapeToChar = /* @__PURE__ */ new Map([
  [CHAR_DOUBLE_QUOTE, QUOTE],
  [CHAR_BACKSLASH, "\\"],
  [CHAR_SLASH, "/"],
  [CHAR_LOWER_B, "\b"],
  [CHAR_LOWER_N, "\n"],
  [CHAR_LOWER_F, "\f"],
  [CHAR_LOWER_R, "\r"],
  [CHAR_LOWER_T, "	"]
]);
var json5EscapeToChar = new Map([
  ...escapeToChar,
  [CHAR_LOWER_V, "\v"],
  [CHAR_0, "\0"]
]);
var charToEscape = /* @__PURE__ */ new Map([
  [QUOTE, QUOTE],
  ["\\", "\\"],
  ["/", "/"],
  ["\b", "b"],
  ["\n", "n"],
  ["\f", "f"],
  ["\r", "r"],
  ["	", "t"]
]);
var json5CharToEscape = new Map([
  ...charToEscape,
  ["\v", "v"],
  ["\0", "0"],
  ["\u2028", "u2028"],
  ["\u2029", "u2029"]
]);
var knownTokenTypes = /* @__PURE__ */ new Map([
  [LBRACKET, "LBracket"],
  [RBRACKET, "RBracket"],
  [LBRACE, "LBrace"],
  [RBRACE, "RBrace"],
  [COLON, "Colon"],
  [COMMA, "Comma"],
  [TRUE, "Boolean"],
  [FALSE, "Boolean"],
  [NULL, "Null"]
]);
var knownJSON5TokenTypes = new Map([
  ...knownTokenTypes,
  [NAN$1, "Number"],
  [INFINITY$1, "Number"]
]);
var json5LineTerminators = /* @__PURE__ */ new Set([
  CHAR_NEWLINE,
  CHAR_RETURN,
  CHAR_LINE_SEPARATOR,
  CHAR_PARAGRAPH_SEPARATOR
]);
var ErrorWithLocation = class extends Error {
  /**
   * Creates a new instance.
   * @param {string} message The error message to report. 
   * @param {Location} loc The location information for the error.
   */
  constructor(message, { line, column, offset }) {
    super(`${message} (${line}:${column})`);
    this.line = line;
    this.column = column;
    this.offset = offset;
  }
};
var UnexpectedChar = class extends ErrorWithLocation {
  /**
   * Creates a new instance.
   * @param {number} unexpected The character that was found.
   * @param {Location} loc The location information for the found character.
   */
  constructor(unexpected, loc) {
    super(`Unexpected character '${String.fromCharCode(unexpected)}' found.`, loc);
  }
};
var UnexpectedToken = class extends ErrorWithLocation {
  /**
   * Creates a new instance.
   * @param {Token} token The token that was found. 
   */
  constructor(token) {
    super(`Unexpected token ${token.type} found.`, token.loc.start);
  }
};
var UnexpectedEOF = class extends ErrorWithLocation {
  /**
   * Creates a new instance.
   * @param {Location} loc The location information for the found character.
   */
  constructor(loc) {
    super("Unexpected end of input found.", loc);
  }
};
var ID_Start = /[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/;
var ID_Continue = /[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/;
var CHAR_CR = 13;
var CHAR_LF = 10;
var CharCodeReader = class {
  /**
   * The text to read from.
   * @type {string}
   */
  #text = "";
  /**
   * The current line number.
   * @type {number}
   */
  #line = 1;
  /**
   * The current column number.
   * @type {number}
   */
  #column = 0;
  /**
   * The current offset in the text.
   * @type {number}
   */
  #offset = -1;
  /**
   * Whether the last character read was a new line.
   * @type {boolean}
   */
  #newLine = false;
  /**
   * The last character code read.
   * @type {number}
   */
  #last = -1;
  /**
   * Whether the reader has ended.
   * @type {boolean}
   */
  #ended = false;
  /**
   * Creates a new instance.
   * @param {string} text The text to read from
   */
  constructor(text) {
    this.#text = text;
  }
  /**
   * Ends the reader.
   * @returns {void}
   */
  #end() {
    if (this.#ended) {
      return;
    }
    this.#column++;
    this.#offset++;
    this.#last = -1;
    this.#ended = true;
  }
  /**
   * Returns the current position of the reader.
   * @returns {Location} An object with line, column, and offset properties.
   */
  locate() {
    return {
      line: this.#line,
      column: this.#column,
      offset: this.#offset
    };
  }
  /**
   * Reads the next character code in the text.
   * @returns {number} The next character code, or -1 if there are no more characters.
   */
  next() {
    if (this.#offset >= this.#text.length - 1) {
      this.#end();
      return -1;
    }
    this.#offset++;
    const charCode = this.#text.charCodeAt(this.#offset);
    if (this.#newLine) {
      this.#line++;
      this.#column = 1;
      this.#newLine = false;
    } else {
      this.#column++;
    }
    if (charCode === CHAR_CR) {
      this.#newLine = true;
      if (this.peek() === CHAR_LF) {
        this.#offset++;
      }
    } else if (charCode === CHAR_LF) {
      this.#newLine = true;
    }
    this.#last = charCode;
    return charCode;
  }
  /**
   * Peeks at the next character code in the text.
   * @returns {number} The next character code, or -1 if there are no more characters.
   */
  peek() {
    if (this.#offset === this.#text.length - 1) {
      return -1;
    }
    return this.#text.charCodeAt(this.#offset + 1);
  }
  /**
   * Returns the last character code read.
   * @returns {number} The last character code read.
   */
  current() {
    return this.#last;
  }
};
var INFINITY = "Infinity";
var NAN = "NaN";
var keywordStarts = /* @__PURE__ */ new Set([CHAR_LOWER_T, CHAR_LOWER_F, CHAR_LOWER_N]);
var whitespace = /* @__PURE__ */ new Set([CHAR_SPACE, CHAR_TAB, CHAR_NEWLINE, CHAR_RETURN]);
var json5Whitespace = /* @__PURE__ */ new Set([
  ...whitespace,
  CHAR_VTAB,
  CHAR_FORM_FEED,
  CHAR_NBSP,
  CHAR_LINE_SEPARATOR,
  CHAR_PARAGRAPH_SEPARATOR,
  CHAR_BOM,
  CHAR_NON_BREAKING_SPACE,
  CHAR_EN_QUAD,
  CHAR_EM_QUAD,
  CHAR_EN_SPACE,
  CHAR_EM_SPACE,
  CHAR_THREE_PER_EM_SPACE,
  CHAR_FOUR_PER_EM_SPACE,
  CHAR_SIX_PER_EM_SPACE,
  CHAR_FIGURE_SPACE,
  CHAR_PUNCTUATION_SPACE,
  CHAR_THIN_SPACE,
  CHAR_HAIR_SPACE,
  CHAR_NARROW_NO_BREAK_SPACE,
  CHAR_MEDIUM_MATHEMATICAL_SPACE,
  CHAR_IDEOGRAPHIC_SPACE
]);
var DEFAULT_OPTIONS$1 = {
  mode: "json",
  ranges: false
};
function isDigit(c) {
  return c >= CHAR_0 && c <= CHAR_9;
}
function isHexDigit(c) {
  return isDigit(c) || c >= CHAR_UPPER_A && c <= CHAR_UPPER_F || c >= CHAR_LOWER_A && c <= CHAR_LOWER_F;
}
function isPositiveDigit(c) {
  return c >= CHAR_1 && c <= CHAR_9;
}
function isKeywordStart(c) {
  return keywordStarts.has(c);
}
function isNumberStart(c) {
  return isDigit(c) || c === CHAR_DOT || c === CHAR_MINUS;
}
function isJSON5NumberStart(c) {
  return isNumberStart(c) || c === CHAR_PLUS;
}
function isStringStart(c, json5) {
  return c === CHAR_DOUBLE_QUOTE || json5 && c === CHAR_SINGLE_QUOTE;
}
function isJSON5IdentifierStart(c) {
  if (c === CHAR_DOLLAR || c === CHAR_UNDERSCORE || c === CHAR_BACKSLASH) {
    return true;
  }
  if (c >= CHAR_LOWER_A && c <= CHAR_LOWER_Z || c >= CHAR_UPPER_A && c <= CHAR_UPPER_Z) {
    return true;
  }
  if (c === 8204 || c === 8205) {
    return true;
  }
  const ct = String.fromCharCode(c);
  return ID_Start.test(ct);
}
function isJSON5IdentifierPart(c) {
  if (isJSON5IdentifierStart(c) || isDigit(c)) {
    return true;
  }
  const ct = String.fromCharCode(c);
  return ID_Continue.test(ct);
}
function tokenize(text, options) {
  options = Object.freeze({
    ...DEFAULT_OPTIONS$1,
    ...options
  });
  const json5 = options.mode === "json5";
  const allowComments = options.mode !== "json";
  const reader = new CharCodeReader(text);
  const tokens = [];
  const isEscapedCharacter = json5 ? json5EscapeToChar.has.bind(json5EscapeToChar) : escapeToChar.has.bind(escapeToChar);
  const isJSON5LineTerminator = json5 ? json5LineTerminators.has.bind(json5LineTerminators) : () => false;
  const isJSON5HexEscape = json5 ? (c2) => c2 === CHAR_LOWER_X : () => false;
  const isWhitespace = json5 ? json5Whitespace.has.bind(json5Whitespace) : whitespace.has.bind(whitespace);
  function createToken(tokenType, length, startLoc, endLoc) {
    const endOffset = startLoc.offset + length;
    let range = options.ranges ? {
      range: (
        /** @type {Range} */
        [startLoc.offset, endOffset]
      )
    } : void 0;
    return {
      type: tokenType,
      loc: {
        start: startLoc,
        end: endLoc || {
          line: startLoc.line,
          column: startLoc.column + length,
          offset: endOffset
        }
      },
      ...range
    };
  }
  function readCharSequence(text2) {
    for (let i = 0; i < text2.length; i++) {
      if (reader.peek() !== text2.charCodeAt(i)) {
        return false;
      }
      reader.next();
    }
    return true;
  }
  function readKeyword(c2) {
    let sequence = expectedKeywords.get(c2);
    let value = String.fromCharCode(c2);
    for (let j = 0; j < sequence.length; j++) {
      const nc = reader.next();
      if (sequence[j] !== nc) {
        unexpected(nc);
      }
      value += String.fromCharCode(nc);
    }
    return {
      value,
      c: reader.next()
    };
  }
  function readJSON5Identifier(c2) {
    let value = "";
    do {
      value += String.fromCharCode(c2);
      if (c2 === CHAR_BACKSLASH) {
        c2 = reader.next();
        if (c2 !== CHAR_LOWER_U) {
          unexpected(c2);
        }
        value += String.fromCharCode(c2);
        const result = readHexDigits(4);
        value += result.value;
        c2 = result.c;
      }
      c2 = reader.next();
    } while (c2 > -1 && isJSON5IdentifierPart(c2));
    return { value, c: c2 };
  }
  function readHexDigits(count) {
    let value = "";
    for (let i = 0; i < count; i++) {
      c = reader.next();
      if (isHexDigit(c)) {
        value += String.fromCharCode(c);
        continue;
      }
      unexpected(c);
    }
    return { value, c };
  }
  function readString(c2) {
    const delimiter = c2;
    let length = 1;
    c2 = reader.next();
    while (c2 !== -1 && c2 !== delimiter) {
      if (c2 === CHAR_BACKSLASH) {
        length++;
        c2 = reader.next();
        if (isEscapedCharacter(c2) || isJSON5LineTerminator(c2)) {
          length++;
        } else if (c2 === CHAR_LOWER_U) {
          length++;
          const result = readHexDigits(4);
          length += result.value.length;
          c2 = result.c;
        } else if (isJSON5HexEscape(c2)) {
          length++;
          const result = readHexDigits(2);
          length += result.value.length;
          c2 = result.c;
        } else if (!json5) {
          unexpected(c2);
        }
      } else {
        length++;
      }
      c2 = reader.next();
    }
    if (c2 === -1) {
      unexpectedEOF();
    }
    length++;
    return { length, c: reader.next() };
  }
  function readNumber(c2) {
    let length = 0;
    if (c2 === CHAR_MINUS || json5 && c2 === CHAR_PLUS) {
      length++;
      c2 = reader.next();
      if (json5) {
        if (c2 === CHAR_UPPER_I && readCharSequence("nfinity")) {
          return { length: INFINITY.length + 1, c: reader.next() };
        }
        if (c2 === CHAR_UPPER_N && readCharSequence("aN")) {
          return { length: NAN.length + 1, c: reader.next() };
        }
      }
      if (!isDigit(c2)) {
        unexpected(c2);
      }
    }
    if (c2 === CHAR_0) {
      length++;
      c2 = reader.next();
      if (json5 && (c2 === CHAR_LOWER_X || c2 === CHAR_UPPER_X)) {
        length++;
        c2 = reader.next();
        if (!isHexDigit(c2)) {
          unexpected(c2);
        }
        do {
          length++;
          c2 = reader.next();
        } while (isHexDigit(c2));
      } else if (isDigit(c2)) {
        unexpected(c2);
      }
    } else {
      if (!json5 || c2 !== CHAR_DOT) {
        if (!isPositiveDigit(c2)) {
          unexpected(c2);
        }
        do {
          length++;
          c2 = reader.next();
        } while (isDigit(c2));
      }
    }
    if (c2 === CHAR_DOT) {
      let digitCount = -1;
      do {
        length++;
        digitCount++;
        c2 = reader.next();
      } while (isDigit(c2));
      if (!json5 && digitCount === 0) {
        if (c2) {
          unexpected(c2);
        } else {
          unexpectedEOF();
        }
      }
    }
    if (c2 === CHAR_LOWER_E || c2 === CHAR_UPPER_E) {
      length++;
      c2 = reader.next();
      if (c2 === CHAR_PLUS || c2 === CHAR_MINUS) {
        length++;
        c2 = reader.next();
      }
      if (c2 === -1) {
        unexpectedEOF();
      }
      if (!isDigit(c2)) {
        unexpected(c2);
      }
      while (isDigit(c2)) {
        length++;
        c2 = reader.next();
      }
    }
    return { length, c: c2 };
  }
  function readComment(c2) {
    let length = 1;
    c2 = reader.next();
    if (c2 === CHAR_SLASH) {
      do {
        length += 1;
        c2 = reader.next();
      } while (c2 > -1 && c2 !== CHAR_RETURN && c2 !== CHAR_NEWLINE);
      return { length, c: c2, multiline: false };
    }
    if (c2 === CHAR_STAR) {
      while (c2 > -1) {
        length += 1;
        c2 = reader.next();
        if (c2 === CHAR_STAR) {
          length += 1;
          c2 = reader.next();
          if (c2 === CHAR_SLASH) {
            length += 1;
            c2 = reader.next();
            return { length, c: c2, multiline: true };
          }
        }
      }
      unexpectedEOF();
    }
    unexpected(c2);
  }
  function unexpected(c2) {
    throw new UnexpectedChar(c2, reader.locate());
  }
  function unexpectedEOF() {
    throw new UnexpectedEOF(reader.locate());
  }
  let c = reader.next();
  while (c > -1) {
    while (isWhitespace(c)) {
      c = reader.next();
    }
    if (c === -1) {
      break;
    }
    const start = reader.locate();
    const ct = String.fromCharCode(c);
    if (json5) {
      if (knownJSON5TokenTypes.has(ct)) {
        tokens.push(createToken(knownJSON5TokenTypes.get(ct), 1, start));
        c = reader.next();
      } else if (isJSON5IdentifierStart(c)) {
        const result = readJSON5Identifier(c);
        let value = result.value;
        c = result.c;
        if (knownJSON5TokenTypes.has(value)) {
          tokens.push(createToken(knownJSON5TokenTypes.get(value), value.length, start));
        } else {
          tokens.push(createToken("Identifier", value.length, start));
        }
      } else if (isJSON5NumberStart(c)) {
        const result = readNumber(c);
        c = result.c;
        tokens.push(createToken("Number", result.length, start));
      } else if (isStringStart(c, json5)) {
        const result = readString(c);
        c = result.c;
        tokens.push(createToken("String", result.length, start, reader.locate()));
      } else if (c === CHAR_SLASH && allowComments) {
        const result = readComment(c);
        c = result.c;
        tokens.push(createToken(!result.multiline ? "LineComment" : "BlockComment", result.length, start, reader.locate()));
      } else {
        unexpected(c);
      }
    } else {
      const ct2 = String.fromCharCode(c);
      if (knownTokenTypes.has(ct2)) {
        tokens.push(createToken(knownTokenTypes.get(ct2), 1, start));
        c = reader.next();
      } else if (isKeywordStart(c)) {
        const result = readKeyword(c);
        let value = result.value;
        c = result.c;
        tokens.push(createToken(knownTokenTypes.get(value), value.length, start));
      } else if (isNumberStart(c)) {
        const result = readNumber(c);
        c = result.c;
        tokens.push(createToken("Number", result.length, start));
      } else if (isStringStart(c, json5)) {
        const result = readString(c);
        c = result.c;
        tokens.push(createToken("String", result.length, start));
      } else if (c === CHAR_SLASH && allowComments) {
        const result = readComment(c);
        c = result.c;
        tokens.push(createToken(!result.multiline ? "LineComment" : "BlockComment", result.length, start, reader.locate()));
      } else {
        unexpected(c);
      }
    }
  }
  return tokens;
}
var types = {
  /**
   * Creates a document node.
   * @param {ValueNode} body The body of the document.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {DocumentNode} The document node.
   */
  document(body, parts = {}) {
    return {
      type: "Document",
      body,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates a string node.
   * @param {string} value The value for the string.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {StringNode} The string node.
   */
  string(value, parts = {}) {
    return {
      type: "String",
      value,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates a number node.
   * @param {number} value The value for the number.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {NumberNode} The number node.
   */
  number(value, parts = {}) {
    return {
      type: "Number",
      value,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates a boolean node.
   * @param {boolean} value The value for the boolean.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {BooleanNode} The boolean node.
   */
  boolean(value, parts = {}) {
    return {
      type: "Boolean",
      value,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates a null node.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {NullNode} The null node.
   */
  null(parts = {}) {
    return {
      type: "Null",
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates an array node.
   * @param {Array<ElementNode>} elements The elements to add.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {ArrayNode} The array node.
   */
  array(elements, parts = {}) {
    return {
      type: "Array",
      elements,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates an element node.
   * @param {ValueNode} value The value for the element.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {ElementNode} The element node.
   */
  element(value, parts = {}) {
    return {
      type: "Element",
      value,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates an object node.
   * @param {Array<MemberNode>} members The members to add.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {ObjectNode} The object node.
   */
  object(members, parts = {}) {
    return {
      type: "Object",
      members,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates a member node.
   * @param {StringNode|IdentifierNode} name The name for the member.
   * @param {ValueNode} value The value for the member.
   * @param {NodeParts} parts Additional properties for the node. 
   * @returns {MemberNode} The member node.
   */
  member(name, value, parts = {}) {
    return {
      type: "Member",
      name,
      value,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates an identifier node.
   * @param {string} name The name for the identifier.
   * @param {NodeParts} parts Additional properties for the node.
   * @returns {IdentifierNode} The identifier node.
   */
  identifier(name, parts = {}) {
    return {
      type: "Identifier",
      name,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates a NaN node.
   * @param {Sign} sign The sign for the Infinity.
   * @param {NodeParts} parts Additional properties for the node.
   * @returns {NaNNode} The NaN node.
   */
  nan(sign = "", parts = {}) {
    return {
      type: "NaN",
      sign,
      loc: parts.loc,
      ...parts
    };
  },
  /**
   * Creates an Infinity node.
   * @param {Sign} sign The sign for the Infinity.
   * @param {NodeParts} parts Additional properties for the node.
   * @returns {InfinityNode} The Infinity node.
   */
  infinity(sign = "", parts = {}) {
    return {
      type: "Infinity",
      sign,
      loc: parts.loc,
      ...parts
    };
  }
};
var DEFAULT_OPTIONS = {
  mode: "json",
  ranges: false,
  tokens: false
};
function getStringValue(value, token, json5 = false) {
  let result = "";
  let escapeIndex = value.indexOf("\\");
  let lastIndex = 0;
  while (escapeIndex >= 0) {
    result += value.slice(lastIndex, escapeIndex);
    const escapeChar = value.charAt(escapeIndex + 1);
    const escapeCharCode = escapeChar.charCodeAt(0);
    if (json5 && json5EscapeToChar.has(escapeCharCode)) {
      result += json5EscapeToChar.get(escapeCharCode);
      lastIndex = escapeIndex + 2;
    } else if (escapeToChar.has(escapeCharCode)) {
      result += escapeToChar.get(escapeCharCode);
      lastIndex = escapeIndex + 2;
    } else if (escapeChar === "u") {
      const hexCode = value.slice(escapeIndex + 2, escapeIndex + 6);
      if (hexCode.length < 4 || /[^0-9a-f]/i.test(hexCode)) {
        throw new ErrorWithLocation(
          `Invalid unicode escape \\u${hexCode}.`,
          {
            line: token.loc.start.line,
            column: token.loc.start.column + escapeIndex,
            offset: token.loc.start.offset + escapeIndex
          }
        );
      }
      result += String.fromCharCode(parseInt(hexCode, 16));
      lastIndex = escapeIndex + 6;
    } else if (json5 && escapeChar === "x") {
      const hexCode = value.slice(escapeIndex + 2, escapeIndex + 4);
      if (hexCode.length < 2 || /[^0-9a-f]/i.test(hexCode)) {
        throw new ErrorWithLocation(
          `Invalid hex escape \\x${hexCode}.`,
          {
            line: token.loc.start.line,
            column: token.loc.start.column + escapeIndex,
            offset: token.loc.start.offset + escapeIndex
          }
        );
      }
      result += String.fromCharCode(parseInt(hexCode, 16));
      lastIndex = escapeIndex + 4;
    } else if (json5 && json5LineTerminators.has(escapeCharCode)) {
      lastIndex = escapeIndex + 2;
      if (escapeChar === "\r" && value.charAt(lastIndex) === "\n") {
        lastIndex++;
      }
    } else {
      if (json5) {
        result += escapeChar;
        lastIndex = escapeIndex + 2;
      } else {
        throw new ErrorWithLocation(
          `Invalid escape \\${escapeChar}.`,
          {
            line: token.loc.start.line,
            column: token.loc.start.column + escapeIndex,
            offset: token.loc.start.offset + escapeIndex
          }
        );
      }
    }
    escapeIndex = value.indexOf("\\", lastIndex);
  }
  result += value.slice(lastIndex);
  return result;
}
function getLiteralValue(value, token, json5 = false) {
  switch (token.type) {
    case "Boolean":
      return value === "true";
    case "Number":
      return Number(value);
    case "String":
      return getStringValue(value.slice(1, -1), token, json5);
    default:
      throw new TypeError(`Unknown token type "${token.type}.`);
  }
}
function parse(text, options) {
  options = Object.freeze({
    ...DEFAULT_OPTIONS,
    ...options
  });
  const tokens = tokenize(text, {
    mode: options.mode,
    ranges: options.ranges
  });
  let tokenIndex = 0;
  const json5 = options.mode === "json5";
  function nextNoComments() {
    return tokens[tokenIndex++];
  }
  function nextSkipComments() {
    const nextToken = tokens[tokenIndex++];
    if (nextToken && nextToken.type.endsWith("Comment")) {
      return nextSkipComments();
    }
    return nextToken;
  }
  const next = options.mode === "json" ? nextNoComments : nextSkipComments;
  function assertTokenType(token, type) {
    if (!token || token.type !== type) {
      throw new UnexpectedToken(token);
    }
  }
  function assertTokenTypes(token, types2) {
    if (!token || !types2.includes(token.type)) {
      throw new UnexpectedToken(token);
    }
  }
  function createRange(start, end) {
    return options.ranges ? {
      range: [start.offset, end.offset]
    } : void 0;
  }
  function createLiteralNode(token) {
    const range = createRange(token.loc.start, token.loc.end);
    const value = getLiteralValue(
      text.slice(token.loc.start.offset, token.loc.end.offset),
      token,
      json5
    );
    const loc = {
      start: {
        ...token.loc.start
      },
      end: {
        ...token.loc.end
      }
    };
    const parts = { loc, ...range };
    switch (token.type) {
      case "String":
        return types.string(
          /** @type {string} */
          value,
          parts
        );
      case "Number":
        return types.number(
          /** @type {number} */
          value,
          parts
        );
      case "Boolean":
        return types.boolean(
          /** @type {boolean} */
          value,
          parts
        );
      default:
        throw new TypeError(`Unknown token type ${token.type}.`);
    }
  }
  function createJSON5IdentifierNode(token) {
    const range = createRange(token.loc.start, token.loc.end);
    const identifier = text.slice(token.loc.start.offset, token.loc.end.offset);
    const loc = {
      start: {
        ...token.loc.start
      },
      end: {
        ...token.loc.end
      }
    };
    const parts = { loc, ...range };
    if (token.type !== "Identifier") {
      let sign = "";
      if (identifier[0] === "+" || identifier[0] === "-") {
        sign = identifier[0];
      }
      return types[identifier.includes("NaN") ? "nan" : "infinity"](
        /** @type {Sign} */
        sign,
        parts
      );
    }
    return types.identifier(identifier, parts);
  }
  function createNullNode(token) {
    const range = createRange(token.loc.start, token.loc.end);
    return types.null({
      loc: {
        start: {
          ...token.loc.start
        },
        end: {
          ...token.loc.end
        }
      },
      ...range
    });
  }
  function parseProperty(token) {
    if (json5) {
      assertTokenTypes(token, ["String", "Identifier", "Number"]);
    } else {
      assertTokenType(token, "String");
    }
    let key = token.type === "String" ? (
      /** @type {StringNode} */
      createLiteralNode(token)
    ) : (
      /** @type {IdentifierNode|NaNNode|InfinityNode} */
      createJSON5IdentifierNode(token)
    );
    if (json5 && (key.type === "NaN" || key.type === "Infinity")) {
      if (key.sign !== "") {
        throw new UnexpectedToken(token);
      }
      key = types.identifier(key.type, { loc: key.loc, ...createRange(key.loc.start, key.loc.end) });
    }
    token = next();
    assertTokenType(token, "Colon");
    const value = parseValue();
    const range = createRange(key.loc.start, value.loc.end);
    return types.member(
      /** @type {StringNode|IdentifierNode} */
      key,
      value,
      {
        loc: {
          start: {
            ...key.loc.start
          },
          end: {
            ...value.loc.end
          }
        },
        ...range
      }
    );
  }
  function parseObject(firstToken) {
    assertTokenType(firstToken, "LBrace");
    const members = [];
    let token = next();
    if (token && token.type !== "RBrace") {
      do {
        members.push(parseProperty(token));
        token = next();
        if (!token) {
          throw new UnexpectedEOF(members[members.length - 1].loc.end);
        }
        if (token.type === "Comma") {
          token = next();
          if (json5 && token.type === "RBrace") {
            break;
          }
        } else {
          break;
        }
      } while (token);
    }
    assertTokenType(token, "RBrace");
    const range = createRange(firstToken.loc.start, token.loc.end);
    return types.object(members, {
      loc: {
        start: {
          ...firstToken.loc.start
        },
        end: {
          ...token.loc.end
        }
      },
      ...range
    });
  }
  function parseArray(firstToken) {
    assertTokenType(firstToken, "LBracket");
    const elements = [];
    let token = next();
    if (token && token.type !== "RBracket") {
      do {
        const value = parseValue(token);
        elements.push(types.element(
          value,
          { loc: value.loc }
        ));
        token = next();
        if (token.type === "Comma") {
          token = next();
          if (json5 && token.type === "RBracket") {
            break;
          }
        } else {
          break;
        }
      } while (token);
    }
    assertTokenType(token, "RBracket");
    const range = createRange(firstToken.loc.start, token.loc.end);
    return types.array(elements, {
      loc: {
        start: {
          ...firstToken.loc.start
        },
        end: {
          ...token.loc.end
        }
      },
      ...range
    });
  }
  function parseValue(token) {
    token = token || next();
    switch (token.type) {
      case "String":
      case "Boolean":
        return createLiteralNode(token);
      case "Number":
        if (json5) {
          let tokenText = text.slice(token.loc.start.offset, token.loc.end.offset);
          if (tokenText[0] === "+" || tokenText[0] === "-") {
            tokenText = tokenText.slice(1);
          }
          if (tokenText === "NaN" || tokenText === "Infinity") {
            return createJSON5IdentifierNode(token);
          }
        }
        return createLiteralNode(token);
      case "Null":
        return createNullNode(token);
      case "LBrace":
        return parseObject(token);
      case "LBracket":
        return parseArray(token);
      default:
        throw new UnexpectedToken(token);
    }
  }
  const docBody = parseValue();
  const unexpectedToken = next();
  if (unexpectedToken) {
    throw new UnexpectedToken(unexpectedToken);
  }
  const docParts = {
    loc: {
      start: {
        line: 1,
        column: 1,
        offset: 0
      },
      end: {
        ...docBody.loc.end
      }
    }
  };
  if (options.tokens) {
    docParts.tokens = tokens;
  }
  if (options.ranges) {
    docParts.range = [
      docParts.loc.start.offset,
      docParts.loc.end.offset
    ];
  }
  return types.document(docBody, docParts);
}

// src/utils.js
var eq = (x) => (y) => x === y;
var not = (fn) => (x) => !fn(x);
var getValues = (o) => Object.values(o);
var notUndefined = (x) => x !== void 0;
var isXError = (x) => (error) => error.keyword === x;
var isRequiredError = isXError("required");
var isAnyOfError = isXError("anyOf");
var isEnumError = isXError("enum");
var getErrors = (node) => node && node.errors ? node.errors.map(
  (e) => e.keyword === "errorMessage" ? { ...e.params.errors[0], message: e.message } : e
) : [];
var getChildren = (node) => node && getValues(node.children) || [];
var getSiblings = (parent) => (node) => getChildren(parent).filter(not(eq(node)));
var concatAll = (xs) => (ys) => ys.reduce((zs, z) => zs.concat(z), xs);

// src/validation-errors/required.js
var import_colors = require("kleur/colors");

// src/code-frame-columns.js
var NEWLINE = /\r\n|[\n\r\u2028\u2029]/;
function getMarkerLines(loc, source) {
  const startLoc = {
    ...loc.start
  };
  const endLoc = {
    ...startLoc,
    ...loc.end
  };
  const linesAbove = 2;
  const linesBelow = 3;
  const startLine = startLoc.line;
  const startColumn = startLoc.column;
  const endLine = endLoc.line;
  const endColumn = endLoc.column;
  const start = Math.max(startLine - (linesAbove + 1), 0);
  const end = Math.min(source.length, endLine + linesBelow);
  const lineDiff = endLine - startLine;
  const markerLines = {};
  if (lineDiff) {
    for (let i = 0; i <= lineDiff; i++) {
      const lineNumber = i + startLine;
      if (!startColumn) {
        markerLines[lineNumber] = true;
      } else if (i === 0) {
        const sourceLength = source[lineNumber - 1].length;
        markerLines[lineNumber] = [startColumn, sourceLength - startColumn + 1];
      } else if (i === lineDiff) {
        markerLines[lineNumber] = [0, endColumn];
      } else {
        const sourceLength = source[lineNumber - i].length;
        markerLines[lineNumber] = [0, sourceLength];
      }
    }
  } else {
    if (startColumn === endColumn) {
      if (startColumn) {
        markerLines[startLine] = [startColumn, 0];
      } else {
        markerLines[startLine] = true;
      }
    } else {
      markerLines[startLine] = [startColumn, endColumn - startColumn];
    }
  }
  return { start, end, markerLines };
}
function codeFrameColumns(rawLines, loc, opts = {}) {
  const lines = rawLines.split(NEWLINE);
  const { start, end, markerLines } = getMarkerLines(loc, lines);
  const numberMaxWidth = String(end).length;
  return rawLines.split(NEWLINE, end).slice(start, end).map((line, index) => {
    const number = start + 1 + index;
    const paddedNumber = ` ${String(number)}`.slice(-numberMaxWidth);
    const gutter = ` ${paddedNumber} |`;
    const hasMarker = markerLines[number];
    const lastMarkerLine = !markerLines[number + 1];
    if (hasMarker) {
      let markerLine = "";
      if (Array.isArray(hasMarker)) {
        const markerSpacing = line.slice(0, Math.max(hasMarker[0] - 1, 0)).replace(/[^\t]/g, " ");
        const numberOfMarkers = hasMarker[1] || 1;
        markerLine = [
          "\n ",
          gutter.replace(/\d/g, " "),
          " ",
          markerSpacing,
          "^".repeat(numberOfMarkers)
        ].join("");
        if (lastMarkerLine && opts.message) {
          markerLine += " " + opts.message;
        }
      }
      return [
        ">",
        gutter,
        line.length > 0 ? ` ${line}` : "",
        markerLine
      ].join("");
    } else {
      return [" ", gutter, line.length > 0 ? ` ${line}` : ""].join("");
    }
  }).join("\n");
}

// src/json/utils.js
var getPointers = (dataPath) => {
  return dataPath.split("/").slice(1).map((pointer2) => pointer2.split("~1").join("/").split("~0").join("~"));
};

// src/json/get-meta-from-path.js
function getMetaFromPath(jsonAst, dataPath, includeIdentifierLocation) {
  const pointers = getPointers(dataPath);
  const lastPointerIndex = pointers.length - 1;
  return pointers.reduce((obj, pointer2, idx) => {
    switch (obj.type) {
      case "Object": {
        const filtered = obj.members.filter(
          (child) => child.name.value === pointer2
        );
        if (filtered.length !== 1) {
          throw new Error(`Couldn't find property ${pointer2} of ${dataPath}`);
        }
        const { name, value } = filtered[0];
        return includeIdentifierLocation && idx === lastPointerIndex ? name : value;
      }
      case "Array":
        return obj.elements[pointer2].value;
      default:
        console.log(obj);
    }
  }, jsonAst.body);
}

// src/json/get-decorated-data-path.js
function getDecoratedDataPath(jsonAst, dataPath) {
  let decoratedPath = "";
  getPointers(dataPath).reduce((obj, pointer2) => {
    switch (obj.type) {
      case "Element":
        obj = obj.value;
      /* eslint-disable-next-line no-fallthrough -- explicitly want fallthrough here */
      case "Object": {
        decoratedPath += `/${pointer2}`;
        const filtered = obj.members.filter(
          (child) => child.name.value === pointer2
        );
        if (filtered.length !== 1) {
          throw new Error(`Couldn't find property ${pointer2} of ${dataPath}`);
        }
        return filtered[0].value;
      }
      case "Array": {
        decoratedPath += `/${pointer2}${getTypeName(obj.elements[pointer2])}`;
        return obj.elements[pointer2];
      }
      default:
        console.log(obj);
    }
  }, jsonAst.body);
  return decoratedPath;
}
function getTypeName(obj) {
  if (!obj || !obj.elements) {
    return "";
  }
  const type = obj.elements.filter(
    (child) => child && child.name && child.name.value === "type"
  );
  if (!type.length) {
    return "";
  }
  return type[0].value && `:${type[0].value.value}` || "";
}

// src/validation-errors/base.js
var BaseValidationError = class {
  constructor(options = { isIdentifierLocation: false }, { data, schema, jsonAst, jsonRaw }) {
    this.options = options;
    this.data = data;
    this.schema = schema;
    this.jsonAst = jsonAst;
    this.jsonRaw = jsonRaw;
  }
  getLocation(dataPath = this.instancePath) {
    const { isIdentifierLocation, isSkipEndLocation } = this.options;
    const { loc } = getMetaFromPath(
      this.jsonAst,
      dataPath,
      isIdentifierLocation
    );
    return {
      start: loc.start,
      end: isSkipEndLocation ? void 0 : loc.end
    };
  }
  getDecoratedPath(dataPath = this.instancePath) {
    const decoratedPath = getDecoratedDataPath(this.jsonAst, dataPath);
    return decoratedPath;
  }
  getCodeFrame(message, dataPath = this.instancePath) {
    return codeFrameColumns(this.jsonRaw, this.getLocation(dataPath), {
      message
    });
  }
  /**
   * @return {string}
   */
  get instancePath() {
    return typeof this.options.instancePath !== "undefined" ? this.options.instancePath : this.options.dataPath;
  }
  print() {
    throw new Error(
      `Implement the 'print' method inside ${this.constructor.name}!`
    );
  }
  getError() {
    throw new Error(
      `Implement the 'getError' method inside ${this.constructor.name}!`
    );
  }
};

// src/validation-errors/required.js
var REQUIRED = (0, import_colors.bold)("REQUIRED");
var RequiredValidationError = class extends BaseValidationError {
  getLocation(dataPath = this.instancePath) {
    const { start } = super.getLocation(dataPath);
    return { start };
  }
  print() {
    const { message, params } = this.options;
    const line = (0, import_colors.red)(`${REQUIRED} ${message}`);
    const output = [`${line}
`];
    return output.concat(
      this.getCodeFrame(`${(0, import_colors.magenta)(params.missingProperty)} is missing here!`)
    );
  }
  getError() {
    const { message } = this.options;
    return {
      ...this.getLocation(),
      error: `${this.getDecoratedPath()} ${message}`,
      path: this.instancePath
    };
  }
};

// src/validation-errors/additional-prop.js
var import_colors2 = require("kleur/colors");
var ADDITIONAL_PROPERTY = (0, import_colors2.bold)("ADDITIONAL PROPERTY");
var AdditionalPropValidationError = class extends BaseValidationError {
  constructor(...args) {
    super(...args);
    this.options.isIdentifierLocation = true;
  }
  print() {
    const { message, params } = this.options;
    const line = (0, import_colors2.red)(`${ADDITIONAL_PROPERTY} ${message}`);
    const output = [`${line}
`];
    return output.concat(
      this.getCodeFrame(
        `${(0, import_colors2.magenta)(params.additionalProperty)} is not expected to be here!`,
        `${this.instancePath}/${params.additionalProperty}`
      )
    );
  }
  getError() {
    const { params } = this.options;
    return {
      ...this.getLocation(`${this.instancePath}/${params.additionalProperty}`),
      error: `${this.getDecoratedPath()} Property ${params.additionalProperty} is not expected to be here`,
      path: this.instancePath
    };
  }
};

// src/validation-errors/enum.js
var import_colors3 = require("kleur/colors");
var import_leven = __toESM(require_leven());
var import_jsonpointer = __toESM(require_jsonpointer());
var ENUM = (0, import_colors3.bold)("ENUM");
var EnumValidationError = class extends BaseValidationError {
  print() {
    const {
      message,
      params: { allowedValues }
    } = this.options;
    const bestMatch = this.findBestMatch();
    const line1 = (0, import_colors3.red)(`${ENUM} ${message}`);
    const line2 = (0, import_colors3.red)(`(${allowedValues.join(", ")})`);
    const output = [line1, `${line2}
`];
    return output.concat(
      this.getCodeFrame(
        bestMatch !== null ? `Did you mean ${(0, import_colors3.magenta)(bestMatch)} here?` : `Unexpected value, should be equal to one of the allowed values`
      )
    );
  }
  getError() {
    const { message, params } = this.options;
    const bestMatch = this.findBestMatch();
    const allowedValues = params.allowedValues.join(", ");
    const output = {
      ...this.getLocation(),
      error: `${this.getDecoratedPath()} ${message}: ${allowedValues}`,
      path: this.instancePath
    };
    if (bestMatch !== null) {
      output.suggestion = `Did you mean ${bestMatch}?`;
    }
    return output;
  }
  findBestMatch() {
    const {
      params: { allowedValues }
    } = this.options;
    const currentValue = this.instancePath === "" ? this.data : import_jsonpointer.default.get(this.data, this.instancePath);
    if (!currentValue) {
      return null;
    }
    const bestMatch = allowedValues.map((value) => ({
      value,
      weight: (0, import_leven.default)(value, currentValue.toString())
    })).sort(
      (x, y) => x.weight > y.weight ? 1 : x.weight < y.weight ? -1 : 0
    )[0];
    return allowedValues.length === 1 || bestMatch.weight < bestMatch.value.length ? bestMatch.value : null;
  }
};

// src/validation-errors/default.js
var import_colors4 = require("kleur/colors");
var DefaultValidationError = class extends BaseValidationError {
  print() {
    const { keyword, message } = this.options;
    const line = (0, import_colors4.red)(`${(0, import_colors4.bold)(keyword.toUpperCase())} ${message}`);
    const output = [`${line}
`];
    return output.concat(this.getCodeFrame(`${(0, import_colors4.magenta)(keyword)} ${message}`));
  }
  getError() {
    const { keyword, message } = this.options;
    return {
      ...this.getLocation(),
      error: `${this.getDecoratedPath()}: ${keyword} ${message}`,
      path: this.instancePath
    };
  }
};

// src/helpers.js
var JSON_POINTERS_REGEX = /\/[\w_-]+(\/\d+)?/g;
function makeTree(ajvErrors = []) {
  const root = { children: {} };
  ajvErrors.forEach((ajvError) => {
    const instancePath = typeof ajvError.instancePath !== "undefined" ? ajvError.instancePath : ajvError.dataPath;
    const paths = instancePath === "" ? [""] : instancePath.match(JSON_POINTERS_REGEX);
    paths && paths.reduce((obj, path, i) => {
      obj.children[path] = obj.children[path] || { children: {}, errors: [] };
      if (i === paths.length - 1) {
        obj.children[path].errors.push(ajvError);
      }
      return obj.children[path];
    }, root);
  });
  return root;
}
function filterRedundantErrors(root, parent, key) {
  getErrors(root).forEach((error) => {
    if (isRequiredError(error)) {
      root.errors = [error];
      root.children = {};
    }
  });
  if (getErrors(root).some(isAnyOfError)) {
    if (Object.keys(root.children).length > 0) {
      delete root.errors;
    }
  }
  if (root.errors && root.errors.length && getErrors(root).every(isEnumError)) {
    if (getSiblings(parent)(root).filter(notUndefined).some(getErrors)) {
      delete parent.children[key];
    }
  }
  Object.entries(root.children).forEach(
    ([key2, child]) => filterRedundantErrors(child, root, key2)
  );
}
function createErrorInstances(root, options) {
  const errors = getErrors(root);
  if (errors.length && errors.every(isEnumError)) {
    const uniqueValues = new Set(
      concatAll([])(errors.map((e) => e.params.allowedValues))
    );
    const allowedValues = [...uniqueValues];
    const error = errors[0];
    return [
      new EnumValidationError(
        {
          ...error,
          params: { allowedValues }
        },
        options
      )
    ];
  } else {
    return concatAll(
      errors.reduce((ret, error) => {
        switch (error.keyword) {
          case "additionalProperties":
            return ret.concat(
              new AdditionalPropValidationError(error, options)
            );
          case "enum":
            return ret.concat(new EnumValidationError(error, options));
          case "required":
            return ret.concat(new RequiredValidationError(error, options));
          default:
            return ret.concat(new DefaultValidationError(error, options));
        }
      }, [])
    )(getChildren(root).map((child) => createErrorInstances(child, options)));
  }
}
var helpers_default = (ajvErrors, options) => {
  const tree = makeTree(ajvErrors || []);
  filterRedundantErrors(tree);
  return createErrorInstances(tree, options);
};

// src/index.js
var src_default = (schema, data, errors, options = {}) => {
  const { format = "cli", indent = null, json = null } = options;
  const jsonRaw = json || JSON.stringify(data, null, indent);
  const jsonAst = parse(jsonRaw);
  const customErrorToText = (error) => error.print().join("\n");
  const customErrorToStructure = (error) => error.getError();
  const customErrors = helpers_default(errors, {
    data,
    schema,
    jsonAst,
    jsonRaw
  });
  if (format === "cli") {
    return customErrors.map(customErrorToText).join("\n\n");
  } else {
    return customErrors.map(customErrorToStructure);
  }
};
//# sourceMappingURL=index.js.map
