/**
 * Retrieves the actual path to SDK root folder from the system environment
 *
 * @return {string|undefined} The full path to the SDK root folder
 */
export function getSdkRootFromEnv(): string | undefined;
/**
 * Retrieves the actual path to SDK root folder
 *
 * @param {string?} [customRoot]
 * @return {Promise<string>} The full path to the SDK root folder
 * @throws {Error} If either the corresponding env variable is unset or is
 * pointing to an invalid file system entry
 */
export function requireSdkRoot(customRoot?: string | null): Promise<string>;
/**
 * @param {string} zipPath
 * @param {string} dstRoot
 */
export function unzipFile(zipPath: string, dstRoot?: string): Promise<void>;
/**
 * Transforms given options into the list of `adb install.install-multiple` command arguments
 *
 * @param {number} apiLevel - The current API level
 * @param {InstallOptions} [options={}] - The options mapping to transform
 * @returns {string[]} The array of arguments
 */
export function buildInstallArgs(apiLevel: number, options?: InstallOptions): string[];
/**
 * Extracts various package manifest details
 * from the given application file.
 *
 * @this {import('./adb.js').ADB}
 * @param {string} apkPath Full path to the application file.
 * @returns {Promise<import('./tools/types').ApkManifest>}
 */
export function readPackageManifest(this: import("./adb.js").ADB, apkPath: string): Promise<import("./tools/types").ApkManifest>;
export const APKS_EXTENSION: ".apks";
export const APK_EXTENSION: ".apk";
export const APK_INSTALL_TIMEOUT: 60000;
export const DEFAULT_ADB_EXEC_TIMEOUT: 20000;
/**
 * Calculates the absolsute path to the given resource
 *
 * @param {string} relPath Relative path to the resource starting from the current module root
 * @returns {Promise<string>} The full path to the resource
 * @throws {Error} If the absolute resource path cannot be determined
 */
export const getResourcePath: ((relPath: any) => Promise<string>) & _.MemoizedFunction;
/** @type {() => Promise<string>} */
export const getJavaHome: () => Promise<string>;
/** @type {() => Promise<string>} */
export const getJavaForOs: () => Promise<string>;
export type InstallOptions = {
    /**
     * - Set to true in order to allow test
     *    packages installation.
     */
    allowTestPackages?: boolean | undefined;
    /**
     * - Set to true to install the app on sdcard
     *    instead of the device memory.
     */
    useSdcard?: boolean | undefined;
    /**
     * - Set to true in order to grant all the
     *    permissions requested in the application's manifest
     *    automatically after the installation is completed
     *    under Android 6+.
     */
    grantPermissions?: boolean | undefined;
    /**
     * - Set it to false if you don't want
     *    the application to be upgraded/reinstalled
     *    if it is already present on the device.
     */
    replace?: boolean | undefined;
    /**
     * - Install apks partially. It is used for 'install-multiple'.
     *   https://android.stackexchange.com/questions/111064/what-is-a-partial-application-install-via-adb
     */
    partialInstall?: boolean | undefined;
};
import _ from 'lodash';
//# sourceMappingURL=helpers.d.ts.map