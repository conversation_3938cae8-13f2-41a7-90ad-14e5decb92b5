{"version": 3, "file": "proxy-request.js", "sourceRoot": "", "sources": ["../../../lib/jsonwp-proxy/proxy-request.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,uCAA6C;AAC7C,8DAAuC;AAEvC,MAAM,YAAY,GAAG,QAAQ,CAAC;AAC9B,MAAM,YAAY,GAAG,QAAQ,CAAC;AAE9B,MAAa,YAAY;IAKvB,YAAY,aAA+C;QACzD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,GAAG,GAAG,IAAI,qBAAY,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC;QACnC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC;gBACjC,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,YAAY,EAAE;aACpB,CAAC,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC;QACnC,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,OAAO,MAAM,IAAA,eAAK,EAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC1C,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,4BAAiB,CAC5D,gCAAgC,CACjC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA5CD,oCA4CC"}