{"version": 3, "file": "desired-caps.js", "sourceRoot": "", "sources": ["../../../lib/basedriver/desired-caps.js"], "names": [], "mappings": ";;;;;;AAAA,sDAA2B;AAC3B,8DAAqC;AACrC,wDAAyB;AAEZ,QAAA,SAAS;AACpB,sFAAsF,CAAC,CACrF,qBAAU,CACX,CAAC;AAEJ,iBAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,KAAK;IACrD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,wBAAwB,CAAC;AAClC,CAAC,CAAC;AACF,iBAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,KAAK;IACrD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,uBAAuB;IACvB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QACvD,gBAAG,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,wBAAwB,CAAC;AAClC,CAAC,CAAC;AACF,iBAAS,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,SAAS,CAAC,KAAK;IACvD,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,uBAAuB;IACvB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,yBAAyB,CAAC;AACnC,CAAC,CAAC;AACF,iBAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,KAAK;IACrD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,wBAAwB,CAAC;AAClC,CAAC,CAAC;AACF,iBAAS,CAAC,UAAU,CAAC,OAAO,GAAG,SAAS,OAAO,CAAC,KAAK;IACnD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,uBAAuB,CAAC;AACjC,CAAC,CAAC;AACF,iBAAS,CAAC,UAAU,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG;IACvE,+CAA+C;IAC/C,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,OAAO,EAAE,CAAC;QAC5C,gBAAG,CAAC,IAAI,CACN,QAAQ,GAAG,iEAAiE;YAC5E,kEAAkE,CACnE,CAAC;IACJ,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AACF,iBAAS,CAAC,UAAU,CAAC,wBAAwB,GAAG,SAAS,wBAAwB,CAAC,KAAK,EAAE,OAAO;IAC9F,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACrC,OAAO,cAAc,CAAC;IACxB,CAAC;IACD,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE,CAAC;QAC3B,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,GAAG,KAAK,gBAAgB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;AACtD,CAAC,CAAC;AAEF,iBAAS,CAAC,OAAO,GAAG,kBAAC,CAAC;AACtB,iBAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,GAAG;IACxC,OAAO,GAAG,CAAC;AACb,CAAC,CAAC"}