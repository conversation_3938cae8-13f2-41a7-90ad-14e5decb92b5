{"compilerOptions": {"moduleResolution": "node", "module": "ESNext", "target": "es2022", "lib": ["es2022", "dom"], "types": ["node", "@wdio/globals/types", "expect-webdriverio", "@wdio/mocha-framework", "@wdio/appium-service"], "skipLibCheck": true, "noEmit": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true}, "include": ["test", "wdio.conf.ts"]}