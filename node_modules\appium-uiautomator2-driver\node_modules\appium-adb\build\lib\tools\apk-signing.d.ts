/**
 * Execute apksigner utility with given arguments.
 *
 * @this {import('../adb.js').ADB}
 * @param {string[]} args - The list of tool arguments.
 * @return {Promise<string>} - Command stdout
 * @throws {Error} If apksigner binary is not present on the local file system
 *                 or the return code is not equal to zero.
 */
export function executeApksigner(this: import("../adb.js").ADB, args: string[]): Promise<string>;
/**
 * (Re)sign the given apk file on the local file system with the default certificate.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} apk - The full path to the local apk file.
 * @throws {Error} If signing fails.
 */
export function signWithDefaultCert(this: import("../adb.js").ADB, apk: string): Promise<void>;
/**
 * (Re)sign the given apk file on the local file system with a custom certificate.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} apk - The full path to the local apk file.
 * @throws {Error} If signing fails.
 */
export function signWithCustomCert(this: import("../adb.js").ADB, apk: string): Promise<void>;
/**
 * (Re)sign the given apk file on the local file system with either
 * custom or default certificate based on _this.useKeystore_ property value
 * and Zip-aligns it after signing.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to the local .apk(s) file.
 * @throws {Error} If signing fails.
 */
export function sign(this: import("../adb.js").ADB, appPath: string): Promise<void>;
/**
 * Perform zip-aligning to the given local apk file.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} apk - The full path to the local apk file.
 * @returns {Promise<boolean>} True if the apk has been successfully aligned
 * or false if the apk has been already aligned.
 * @throws {Error} If zip-align fails.
 */
export function zipAlignApk(this: import("../adb.js").ADB, apk: string): Promise<boolean>;
/**
 * Check if the app is already signed with the default Appium certificate.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to the local .apk(s) file.
 * @param {string} pkg - The name of application package.
 * @param {import('./types').CertCheckOptions} [opts={}] - Certificate checking options
 * @return {Promise<boolean>} True if given application is already signed.
 */
export function checkApkCert(this: import("../adb.js").ADB, appPath: string, pkg: string, opts?: import("./types").CertCheckOptions): Promise<boolean>;
/**
 * Retrieve the the hash of the given keystore.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<import('./types').KeystoreHash>}
 * @throws {Error} If getting keystore hash fails.
 */
export function getKeystoreHash(this: import("../adb.js").ADB): Promise<import("./types").KeystoreHash>;
/**
 * Get the absolute path to apksigner tool
 *
 * @this {import('../adb').ADB}
 * @returns {Promise<string>} An absolute path to apksigner tool.
 * @throws {Error} If the tool is not present on the local file system.
 */
export function getApksignerForOs(this: import("../adb.js").ADB): Promise<string>;
/**
 * Unsigns the given apk by removing the
 * META-INF folder recursively from the archive.
 * !!! The function overwrites the given apk after successful unsigning !!!
 *
 * @param {string} apkPath The path to the apk
 * @returns {Promise<boolean>} `true` if the apk has been successfully
 * unsigned and overwritten
 * @throws {Error} if there was an error during the unsign operation
 */
export function unsignApk(apkPath: string): Promise<boolean>;
//# sourceMappingURL=apk-signing.d.ts.map