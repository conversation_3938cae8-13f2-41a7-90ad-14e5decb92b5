/**
 * Verify whether the given argument is a
 * valid class name.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} classString - The actual class name to be verified.
 * @return {boolean} The result of Regexp.exec operation
 * or _null_ if no matches are found.
 */
export function isValidClass(this: import("../adb.js").ADB, classString: string): boolean;
/**
 * Fetches the fully qualified name of the launchable activity for the
 * given package. It is expected the package is already installed on
 * the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The target package identifier
 * @param {import('./types').ResolveActivityOptions} opts
 * @return {Promise<string>} Fully qualified name of the launchable activity
 * @throws {Error} If there was an error while resolving the activity name
 */
export function resolveLaunchableActivity(this: import("../adb.js").ADB, pkg: string, opts?: import("./types").ResolveActivityOptions): Promise<string>;
/**
 * Force application to stop on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be stopped.
 * @return {Promise<string>} The output of the corresponding adb command.
 */
export function forceStop(this: import("../adb.js").ADB, pkg: string): Promise<string>;
/**
 * Kill application
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be stopped.
 * @return {Promise<string>} The output of the corresponding adb command.
 */
export function killPackage(this: import("../adb.js").ADB, pkg: string): Promise<string>;
/**
 * Clear the user data of the particular application on the device
 * under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be cleared.
 * @return {Promise<string>} The output of the corresponding adb command.
 */
export function clear(this: import("../adb.js").ADB, pkg: string): Promise<string>;
/**
 * Grant all permissions requested by the particular package.
 * This method is only useful on Android 6.0+ and for applications
 * that support components-based permissions setting.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be processed.
 * @param {string} [apk] - The path to the actual apk file.
 * @throws {Error} If there was an error while granting permissions
 */
export function grantAllPermissions(this: import("../adb.js").ADB, pkg: string, apk?: string): Promise<void>;
/**
 * Grant multiple permissions for the particular package.
 * This call is more performant than `grantPermission` one, since it combines
 * multiple `adb shell` calls into a single command.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be processed.
 * @param {Array<string>} permissions - The list of permissions to be granted.
 * @throws {Error} If there was an error while changing permissions.
 */
export function grantPermissions(this: import("../adb.js").ADB, pkg: string, permissions: Array<string>): Promise<void>;
/**
 * Grant single permission for the particular package.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be processed.
 * @param {string} permission - The full name of the permission to be granted.
 * @throws {Error} If there was an error while changing permissions.
 */
export function grantPermission(this: import("../adb.js").ADB, pkg: string, permission: string): Promise<void>;
/**
 * Revoke single permission from the particular package.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be processed.
 * @param {string} permission - The full name of the permission to be revoked.
 * @throws {Error} If there was an error while changing permissions.
 */
export function revokePermission(this: import("../adb.js").ADB, pkg: string, permission: string): Promise<void>;
/**
 * Retrieve the list of granted permissions for the particular package.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be processed.
 * @param {string?} [cmdOutput=null] - Optional parameter containing command output of
 * _dumpsys package_ command. It may speed up the method execution.
 * @return {Promise<string[]>} The list of granted permissions or an empty list.
 * @throws {Error} If there was an error while changing permissions.
 */
export function getGrantedPermissions(this: import("../adb.js").ADB, pkg: string, cmdOutput?: string | null): Promise<string[]>;
/**
 * Retrieve the list of denied permissions for the particular package.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be processed.
 * @param {string?} [cmdOutput=null] - Optional parameter containing command output of
 * _dumpsys package_ command. It may speed up the method execution.
 * @return {Promise<string[]>} The list of denied permissions or an empty list.
 */
export function getDeniedPermissions(this: import("../adb.js").ADB, pkg: string, cmdOutput?: string | null): Promise<string[]>;
/**
 * Retrieve the list of requested permissions for the particular package.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be processed.
 * @param {string?} [cmdOutput=null] - Optional parameter containing command output of
 *                                    _dumpsys package_ command. It may speed up the method execution.
 * @return {Promise<string[]>} The list of requested permissions or an empty list.
 */
export function getReqPermissions(this: import("../adb.js").ADB, pkg: string, cmdOutput?: string | null): Promise<string[]>;
/**
 * Stop the particular package if it is running and clears its application data.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The package name to be processed.
 */
export function stopAndClear(this: import("../adb.js").ADB, pkg: string): Promise<void>;
/**
 * At some point of time Google has changed the default `ps` behaviour, so it only
 * lists processes that belong to the current shell user rather to all
 * users. It is necessary to execute ps with -A command line argument
 * to mimic the previous behaviour.
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<string>} the output of `ps` command where all processes are included
 */
export function listProcessStatus(this: import("../adb.js").ADB): Promise<string>;
export class listProcessStatus {
    _doesPsSupportAOption: boolean;
}
/**
 * Returns process name for the given process identifier
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} pid - The valid process identifier
 * @throws {Error} If the given PID is either invalid or is not present
 * in the active processes list
 * @returns {Promise<string>} The process name
 */
export function getNameByPid(this: import("../adb.js").ADB, pid: string | number): Promise<string>;
/**
 * Get the list of process ids for the particular process on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} name - The part of process name.
 * @return {Promise<number[]>} The list of matched process IDs or an empty list.
 * @throws {Error} If the passed process name is not a valid one
 */
export function getPIDsByName(this: import("../adb.js").ADB, name: string): Promise<number[]>;
export class getPIDsByName {
    /**
     * Get the list of process ids for the particular process on the device under test.
     *
     * @this {import('../adb.js').ADB}
     * @param {string} name - The part of process name.
     * @return {Promise<number[]>} The list of matched process IDs or an empty list.
     * @throws {Error} If the passed process name is not a valid one
     */
    constructor(this: import("../adb.js").ADB, name: string);
    _isPgrepAvailable: boolean | undefined;
    _canPgrepUseFullCmdLineSearch: boolean | undefined;
    _isPidofAvailable: boolean | undefined;
}
/**
 * Get the list of process ids for the particular process on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} name - The part of process name.
 */
export function killProcessesByName(this: import("../adb.js").ADB, name: string): Promise<void>;
/**
 * Kill the particular process on the device under test.
 * The current user is automatically switched to root if necessary in order
 * to properly kill the process.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} pid - The ID of the process to be killed.
 * @throws {Error} If the process cannot be killed.
 */
export function killProcessByPID(this: import("../adb.js").ADB, pid: string | number): Promise<void>;
/**
 * Broadcast process killing on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} intent - The name of the intent to broadcast to.
 * @param {string} processName - The name of the killed process.
 * @throws {error} If the process was not killed.
 */
export function broadcastProcessEnd(this: import("../adb.js").ADB, intent: string, processName: string): Promise<void>;
/**
 * Broadcast a message to the given intent.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} intent - The name of the intent to broadcast to.
 * @throws {error} If intent name is not a valid class name.
 */
export function broadcast(this: import("../adb.js").ADB, intent: string): Promise<void>;
/**
 * Check whether the process with the particular name is running on the device
 * under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} processName - The name of the process to be checked.
 * @return {Promise<boolean>} True if the given process is running.
 * @throws {Error} If the given process name is not a valid class name.
 */
export function processExists(this: import("../adb.js").ADB, processName: string): Promise<boolean>;
/**
 * Get the package info from the installed application.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The name of the installed package.
 * @return {Promise<import('./types').AppInfo>} The parsed application information.
 */
export function getPackageInfo(this: import("../adb.js").ADB, pkg: string): Promise<import("./types").AppInfo>;
/**
 * Fetches base.apk of the given package to the local file system
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg The package identifier (must be already installed on the device)
 * @param {string} tmpDir The destination folder path
 * @returns {Promise<string>} Full path to the downloaded file
 * @throws {Error} If there was an error while fetching the .apk
 */
export function pullApk(this: import("../adb.js").ADB, pkg: string, tmpDir: string): Promise<string>;
/**
 * Activates the given application or launches it if necessary.
 * The action literally simulates
 * clicking the corresponding application icon on the dashboard.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appId - Application package identifier
 * @throws {Error} If the app cannot be activated
 */
export function activateApp(this: import("../adb.js").ADB, appId: string): Promise<void>;
/**
 * Check whether the particular package is present on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The name of the package to check.
 * @param {import('./types').IsAppInstalledOptions} [opts={}]
 * @return {Promise<boolean>} True if the package is installed.
 */
export function isAppInstalled(this: import("../adb.js").ADB, pkg: string, opts?: import("./types").IsAppInstalledOptions): Promise<boolean>;
/**
 * Start the particular URI on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} uri - The name of URI to start.
 * @param {string?} [pkg=null] - The name of the package to start the URI with.
 * @param {import('./types').StartUriOptions} [opts={}]
 */
export function startUri(this: import("../adb.js").ADB, uri: string, pkg?: string | null, opts?: import("./types").StartUriOptions): Promise<void>;
/**
 * Start the particular package/activity on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {import('./types').StartAppOptions} startAppOptions - Startup options mapping.
 * @return {Promise<string>} The output of the corresponding adb command.
 * @throws {Error} If there is an error while executing the activity
 */
export function startApp(this: import("../adb.js").ADB, startAppOptions: import("./types").StartAppOptions): Promise<string>;
/**
 * Helper method to call `adb dumpsys window windows/displays`
 * @this {import('../adb.js').ADB}
 * @returns {Promise<string>}
 */
export function dumpWindows(this: import("../adb.js").ADB): Promise<string>;
/**
 * Get the name of currently focused package and activity.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<import('./types').PackageActivityInfo>}
 * @throws {Error} If there is an error while parsing the data.
 */
export function getFocusedPackageAndActivity(this: import("../adb.js").ADB): Promise<import("./types").PackageActivityInfo>;
/**
 * Wait for the given activity to be focused/non-focused.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The name of the package to wait for.
 * @param {string} activity - The name of the activity, belonging to that package,
 *                            to wait for.
 * @param {boolean} waitForStop - Whether to wait until the activity is focused (true)
 *                                or is not focused (false).
 * @param {number} [waitMs=20000] - Number of milliseconds to wait before timeout occurs.
 * @throws {error} If timeout happens.
 */
export function waitForActivityOrNot(this: import("../adb.js").ADB, pkg: string, activity: string, waitForStop: boolean, waitMs?: number): Promise<void>;
/**
 * Wait for the given activity to be focused
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The name of the package to wait for.
 * @param {string} act - The name of the activity, belonging to that package,
 *                            to wait for.
 * @param {number} [waitMs=20000] - Number of milliseconds to wait before timeout occurs.
 * @throws {error} If timeout happens.
 */
export function waitForActivity(this: import("../adb.js").ADB, pkg: string, act: string, waitMs?: number): Promise<void>;
/**
 * Wait for the given activity to be non-focused.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The name of the package to wait for.
 * @param {string} act - The name of the activity, belonging to that package,
 *                            to wait for.
 * @param {number} [waitMs=20000] - Number of milliseconds to wait before timeout occurs.
 * @throws {error} If timeout happens.
 */
export function waitForNotActivity(this: import("../adb.js").ADB, pkg: string, act: string, waitMs?: number): Promise<void>;
/**
 * Builds command line representation for the given
 * application startup options
 *
 * @param {StartCmdOptions} startAppOptions - Application options mapping
 * @param {number} apiLevel - The actual OS API level
 * @returns {string[]} The actual command line array
 */
export function buildStartCmd(startAppOptions: StartCmdOptions, apiLevel: number): string[];
/**
 * Parses the name of launchable package activity
 * from dumpsys output.
 *
 * @param {string} dumpsys the actual dumpsys output
 * @returns {string[]} Either the fully qualified
 * activity name as a single list item or an empty list if nothing could be parsed.
 * In Android 6 and older there is no reliable way to determine
 * the category name for the given activity, so this API just
 * returns all activity names belonging to 'android.intent.action.MAIN'
 * with the expectation that the app manifest could be parsed next
 * in order to determine category names for these.
 */
export function parseLaunchableActivityNames(dumpsys: string): string[];
/**
 * Check if the given string is a valid component name
 *
 * @param {string} classString The string to verify
 * @return {RegExpExecArray?} The result of Regexp.exec operation
 * or _null_ if no matches are found
 */
export function matchComponentName(classString: string): RegExpExecArray | null;
/**
 * Retrieves the list of permission names encoded in `dumpsys package` command output.
 *
 * @param {string} dumpsysOutput - The actual command output.
 * @param {string[]} groupNames - The list of group names to list permissions for.
 * @param {boolean?} [grantedState=null] - The expected state of `granted` attribute to filter with.
 *  No filtering is done if the parameter is not set.
 * @returns {string[]} The list of matched permission names or an empty list if no matches were found.
 */
export function extractMatchingPermissions(dumpsysOutput: string, groupNames: string[], grantedState?: boolean | null): string[];
/** @type {import('./types').StringRecord<import('./types').InstallState>} */
export const APP_INSTALL_STATE: import("./types").StringRecord<import("./types").InstallState>;
export type StartCmdOptions = {
    user?: string | number | undefined;
    waitForLaunch?: boolean | undefined;
    pkg?: string | undefined;
    activity?: string | undefined;
    action?: string | undefined;
    category?: string | undefined;
    stopApp?: boolean | undefined;
    flags?: string | undefined;
    optionalIntentArguments?: string | undefined;
};
//# sourceMappingURL=app-commands.d.ts.map