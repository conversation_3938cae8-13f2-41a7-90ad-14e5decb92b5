"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
var find_package_1 = require("./find-package");
var get_package_hash_1 = require("./get-package-hash");
var get_packagelock_hash_1 = require("./get-packagelock-hash");
var get_packagelock_1 = require("./get-packagelock");
function isPackageChanged(options, callback) {
    var _a, _b;
    if (options === void 0) { options = {}; }
    return __awaiter(this, void 0, void 0, function () {
        var _c, hashFilename, _d, cwd, lockfile, noHashFile, packagePath, packagelockPath, packageHashPath, writeHash, packageHashPathExists, recentDigest, previousDigest, isChanged, result, canWriteHash;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    _c = options.hashFilename, hashFilename = _c === void 0 ? '.packagehash' : _c, _d = options.cwd, cwd = _d === void 0 ? process.cwd() : _d, lockfile = options.lockfile, noHashFile = options.noHashFile;
                    packagePath = find_package_1.findPackage({ cwd: cwd });
                    if (!packagePath) {
                        throw new Error('Cannot find package.json. Travelling up from current working directory.');
                    }
                    if (lockfile) {
                        packagelockPath = get_packagelock_1.getPackagelock({ packagePath: packagePath });
                    }
                    packageHashPath = path_1.default.join(cwd, hashFilename);
                    writeHash = function (hash) {
                        return hash && fs_1.default.writeFileSync(packageHashPath, hash, {});
                    };
                    packageHashPathExists = fs_1.default.existsSync(packageHashPath);
                    recentDigest = lockfile
                        ? "" + get_package_hash_1.getPackageHash(packagePath) + ((_a = get_packagelock_hash_1.getPackagelockHash(packagelockPath)) !== null && _a !== void 0 ? _a : '')
                        : get_package_hash_1.getPackageHash(packagePath);
                    previousDigest = packageHashPathExists && fs_1.default.readFileSync(packageHashPath, 'utf-8');
                    isChanged = !packageHashPathExists || previousDigest !== recentDigest;
                    result = {
                        hash: recentDigest,
                        isChanged: isChanged,
                        oldHash: previousDigest || undefined,
                    };
                    if (!callback) return [3 /*break*/, 2];
                    return [4 /*yield*/, callback(result)];
                case 1:
                    canWriteHash = _e.sent();
                    if (lockfile) {
                        // hash may have changed since package-lock.file could have been updated after command
                        result.hash = "" + get_package_hash_1.getPackageHash(packagePath) + ((_b = get_packagelock_hash_1.getPackagelockHash(packagelockPath)) !== null && _b !== void 0 ? _b : '');
                    }
                    if (canWriteHash === undefined) {
                        canWriteHash = process.env.CI !== 'true';
                    }
                    if (canWriteHash && !noHashFile) {
                        writeHash(result.hash);
                    }
                    _e.label = 2;
                case 2: return [2 /*return*/, __assign(__assign({}, result), { writeHash: writeHash.bind(null, result.hash) })];
            }
        });
    });
}
exports.default = isPackageChanged;
