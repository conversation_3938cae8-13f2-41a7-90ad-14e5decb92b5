{"version": 3, "file": "logcat.js", "sourceRoot": "", "sources": ["../../lib/logcat.js"], "names": [], "mappings": ";;;;;;AAAA,6CAA+C;AAC/C,wDAAyB;AACzB,oDAAuB;AACvB,6CAA2C;AAC3C,+CAAgD;AAChD,yCAAqC;AAErC,MAAM,GAAG,GAAG,gBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACvC,MAAM,eAAe,GAAG,KAAK,CAAC;AAC9B,MAAM,2BAA2B,GAAG,KAAK,CAAC;AAC1C,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;AACrG,MAAM,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACjE,MAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,MAAM,WAAW,GAAG,GAAG,CAAC;AACxB,MAAM,cAAc,GAAG,YAAY,CAAC;AACpC,MAAM,aAAa,GAAG,UAAU,CAAC;AACjC,MAAM,kBAAkB,GAAG,YAAY,CAAC;AAExC,SAAS,aAAa,CAAE,MAAM;IAC5B,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,GAAG,CAAC,IAAI,CAAC,qBAAqB,MAAM,uCAAuC,iBAAiB,EAAE,CAAC,CAAC;QAChG,GAAG,CAAC,IAAI,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC;QAC9C,OAAO,cAAc,CAAC;IACxB,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,SAAS,UAAU,CAAC,OAAO,EAAE,SAAS;IACpC,OAAO;QACL,SAAS;QACT,KAAK,EAAE,KAAK;QACZ,OAAO;KACR,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,WAAW,CAAE,IAAI;IACxB,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxC,IAAI,SAAS,GAAG,GAAG,CAAC;IACpB,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,GAAG,CAAC,IAAI,CAAC,0BAA0B,IAAI,mBAAmB,CAAC,CAAC;QAC5D,GAAG,CAAC,IAAI,CAAC,kBAAkB,WAAW,GAAG,CAAC,CAAC;QAC3C,SAAS,GAAG,WAAW,CAAC;IAC1B,CAAC;IACD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,GAAG,CAAC,IAAI,CAAC,+BAA+B,IAAI,sCAAsC,gBAAgB,GAAG,CAAC,CAAC;QACvG,OAAO,GAAG,SAAS,IAAI,gBAAgB,EAAE,CAAC;IAC5C,CAAC;IACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,gBAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5E,GAAG,CAAC,IAAI,CAAC,+BAA+B,IAAI,uCAAuC,oBAAoB,EAAE,CAAC,CAAC;QAC3G,GAAG,CAAC,IAAI,CAAC,0BAA0B,gBAAgB,GAAG,CAAC,CAAC;QACxD,OAAO,GAAG,SAAS,IAAI,gBAAgB,EAAE,CAAC;IAC5C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAE,WAAW;IACrC,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAC5B,WAAW,GAAG,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IACD,OAAO,WAAW;SACf,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SACnE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAClE,CAAC;AAGD,MAAa,MAAO,SAAQ,0BAAY;IACtC,YAAa,IAAI,GAAG,EAAE;QACpB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,IAAI,KAAK,CAAC;QACtD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,eAAe,CAAC;QAC3D,iDAAiD;QACjD,IAAI,CAAC,IAAI,GAAG,IAAI,oBAAQ,CAAC;YACvB,GAAG,EAAE,IAAI,CAAC,aAAa;SACxB,CAAC,CAAC;QACH,sBAAsB;QACtB,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,YAAY,CAAE,IAAI,GAAG,EAAE;QAC3B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,OAAO,MAAM,IAAI,kBAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;YAC7C,MAAM,OAAO,GAAG,UAAU,GAAG,IAAI;gBAC/B,OAAO,GAAG,IAAI,CAAC;gBACf,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;YACpB,CAAC,CAAC;YACF,MAAM,MAAM,GAAG,UAAU,GAAG,IAAI;gBAC9B,OAAO,GAAG,IAAI,CAAC;gBACf,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;YACnB,CAAC,CAAC;YAEF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YACrB,CAAC;YAED,MAAM,EACJ,MAAM,GAAG,cAAc,EACvB,WAAW,GAAG,EAAE,GACjB,GAAG,IAAI,CAAC;YACT,MAAM,GAAG,GAAG;gBACV,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW;gBACvB,QAAQ;gBACR,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC;gBAC3B,GAAG,iBAAiB,CAAC,WAAW,CAAC;aAClC,CAAC;YACF,GAAG,CAAC,KAAK,CAAC,uCAAuC,cAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;YACxF,IAAI,CAAC,IAAI,GAAG,IAAI,yBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;gBACpC,GAAG,CAAC,KAAK,CAAC,+BAA+B,IAAI,YAAY,MAAM,EAAE,CAAC,CAAC;gBACnE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,GAAG,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;oBAC3C,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE;gBACnC,IAAI,CAAC,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC9C,GAAG,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;oBAC5C,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,2CAA2C,IAAI,EAAE,CAAC,CAAC,CAAC;gBAC9E,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBACrC,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE;gBACnC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACzB,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzB,0DAA0D;YAC1D,UAAU,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAE,OAAO,EAAE,MAAM,GAAG,EAAE;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;QACrB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;YACnC,WAAW,GAAG,GAAG,CAAC;YAClB,MAAM;QACR,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YACpE,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,WAAW;QACf,GAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;YAC1B,GAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,iDAAiD;QACjD,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,sBAAsB;QACtB,IAAI,cAAc,GAAG,IAAI,CAAC;QAC1B,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,IAAI,wBAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;YAC5F,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,GAAG,IAAI,CAAC,wBAAwB;mBACnE,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBACtC,cAAc,GAAG,KAAK,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QACD,IAAI,gBAAC,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,wBAAwB,GAAG,cAAc,CAAC;QACjD,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,UAAU;QACR,iDAAiD;QACjD,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,wBAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,GAAG,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAC9C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YACvD,MAAM,IAAA,mBAAI,EAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,GAAG,CAAC,IAAI,CAAC,gCAAgC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;CACF;AAzJD,wBAyJC;AAED,kBAAe,MAAM,CAAC"}