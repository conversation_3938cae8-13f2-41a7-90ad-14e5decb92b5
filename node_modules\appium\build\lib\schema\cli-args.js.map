{"version": 3, "file": "cli-args.js", "sourceRoot": "", "sources": ["../../../lib/schema/cli-args.js"], "names": [], "mappings": ";;;;;AAsOA,oCAGC;AAzOD,uCAA2C;AAC3C,oDAAuB;AACvB,gDAA4C;AAC5C,qCAAiD;AACjD,yDAA8D;AAE9D;;;;GAIG;AAEH;;;GAGG;AACH,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;IAC9B,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;IAChB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,QAAQ;CACjB,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAE3B;;;;;GAKG;AACH,SAAS,WAAW,CAAC,OAAO,EAAE,KAAK;IACjC,MAAM,EAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAC,GAAG,OAAO,CAAC;IACzC,MAAM,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC;IAC1B,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,GAAG,gBAAgB,CAAC;IAC9C,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;QACvB,OAAO,OAAO;YACZ,CAAC,CAAC,KAAK,OAAO,IAAI,gBAAC,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE;YAC/C,CAAC,CAAC,KAAK,OAAO,IAAI,gBAAC,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,gBAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;IACjE,CAAC;IACD,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,gBAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,MAAM,kBAAkB,GAAG,gBAAC,CAAC,IAAI,CAAC,gBAAC,CAAC,SAAS,EAAE,gBAAC,CAAC,OAAO,CAAC,CAAC;AAE1D;;;;;;;;;;GAUG;AACH,SAAS,kBAAkB,CAAC,EAAC,GAAG,EAAE,QAAQ,EAAC,EAAE,MAAM,GAAG,gBAAC,CAAC,QAAQ;IAC9D,4BAA4B;IAC5B,OAAO,CAAC,KAAK,EAAE,EAAE;QACf,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAA,iBAAQ,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC3C,IAAI,gBAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,MAAM,IAAI,4BAAiB,CAAC,MAAM,GAAG,IAAA,0BAAY,EAAC,MAAM,EAAE,KAAK,EAAE,EAAC,QAAQ,EAAC,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,MAAM;IAC7B,MAAM,EAAC,oBAAoB,EAAE,WAAW,GAAG,EAAE,EAAE,gBAAgB,EAAC,GAAG,MAAM,CAAC;IAC1E,IAAI,IAAI,GAAG,oBAAoB,IAAI,WAAW,CAAC;IAC/C,IAAI,gBAAgB,EAAE,CAAC;QACrB,IAAI,GAAG,gBAAgB,IAAI,EAAE,CAAC;IAChC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;GAMG;AACH,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO;IAC3C,IAAI,EAAC,IAAI,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,EAAC,GAAG,SAAS,CAAC;IAEjF,MAAM,EAAC,IAAI,EAAE,GAAG,EAAC,GAAG,OAAO,CAAC;IAE5B,MAAM,OAAO,GAAG;QACd,WAAW,CAAC,OAAO,CAAC;QACpB,IAAG,uBAAwB,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAChG,CAAC;IAEF,iDAAiD;IACjD,IAAI,OAAO,GAAG;QACZ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC;KACjC,CAAC;IAEF;;;;;;;;;;OAUG;IACH,IAAI,eAAe,CAAC;IAEpB,yCAAyC;IACzC,QAAQ,IAAI,EAAE,CAAC;QACb,sEAAsE;QACtE,4HAA4H;QAC5H,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACvB,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC;YAC/B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YACrB,MAAM;QACR,CAAC;QAED,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,eAAe,GAAG,gBAAC,CAAC,IAAI,CAAC,+BAAY,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE;gBAChD,+CAA+C;gBAC/C,IAAI,CAAC,gBAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;oBACxB,MAAM,IAAI,4BAAiB,CAAC,IAAI,gBAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAC,MAAM,EAAE,GAAG,EAAC,CAAC,0BAA0B,CAAC,CAAC;oBAAA,CAAC;gBAC3F,CAAC;gBACD,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YACH,MAAM;QACR,CAAC;QAED,4EAA4E;QAC5E,KAAK,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YACrB,eAAe,GAAG,+BAAY,CAAC;YAC/B,MAAM;QACR,CAAC;QAED,6FAA6F;QAC7F,YAAY;QACZ,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,eAAe,GAAG,kBAAkB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YAC1D,MAAM;QACR,CAAC;QAED,mGAAmG;QACnG,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACvB,eAAe,GAAG,kBAAkB,CAAC,OAAO,EAAE,gBAAC,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM;QACR,CAAC;QAED,sEAAsE;QACtE,qEAAqE;QACrE,YAAY;QACZ,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YACtB,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM;QACR,CAAC;QAED,mEAAmE;QACnE,4CAA4C;QAC5C,KAAK,SAAS,CAAC,IAAI,CAAC;QACpB,gBAAgB;QAChB,OAAO,CAAC,CAAC,CAAC;YACR,MAAM,IAAI,SAAS,CAAC,oBAAoB,GAAG,QAAQ,IAAI,+BAA+B,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAED,2EAA2E;IAC3E,uDAAuD;IACvD,IAAI,IAAI,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;QAC/B,OAAO,CAAC,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,oBAAoB,IAAI,+BAAY,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC/D,IAAI,IAAI,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;YAC7B,MAAM,cAAc,GAAG,sCAAsC,CAAC,CAAC,eAAe,CAAC,CAAC;YAChF,eAAe,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,gBAAC,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,+BAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC;aAAM,CAAC;YACN,eAAe,GAAG,gBAAC,CAAC,IAAI,CAAC,eAAe,IAAI,gBAAC,CAAC,QAAQ,EAAE,+BAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAED,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC;IACjC,CAAC;IAED,4FAA4F;IAC5F,qFAAqF;IACrF,oEAAoE;IACpE,IAAI,UAAU,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QACzC,IAAI,IAAI,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YAC9B,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,SAAS,CACjB,2BAA2B,GAAG,qDAAqD,CACpF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,qFAAqF;IACrF,8BAA8B;IAC9B,MAAM,YAAY,GAAG,wCAAwC,CAAC,CAAC,OAAO,CAAC,CAAC;IACxE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AACjC,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,YAAY;IAC1B,MAAM,SAAS,GAAG,IAAA,sBAAa,GAAE,CAAC,MAAM,CAAC,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACjF,OAAO,IAAI,GAAG,CAAC,gBAAC,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,EAAC,MAAM,EAAE,OAAO,EAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAC9F,CAAC;AAED;;;GAGG;AAEH;;;GAGG;AAEH;;GAEG"}