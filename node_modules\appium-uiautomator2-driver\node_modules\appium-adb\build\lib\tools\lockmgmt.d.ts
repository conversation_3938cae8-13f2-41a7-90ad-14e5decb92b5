/**
 * Check whether the device supports lock settings management with `locksettings`
 * command line tool. This tool has been added to Android toolset since  API 27 Oreo
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if the management is supported. The result is cached per ADB instance
 */
export function isLockManagementSupported(this: import("../adb.js").ADB): Promise<boolean>;
export class isLockManagementSupported {
    _isLockManagementSupported: boolean;
}
/**
 * Check whether the given credential is matches to the currently set one.
 *
 * @this {import('../adb.js').ADB}
 * @param {string?} [credential=null] The credential value. It could be either
 * pin, password or a pattern. A pattern is specified by a non-separated list
 * of numbers that index the cell on the pattern in a 1-based manner in left
 * to right and top to bottom order, i.e. the top-left cell is indexed with 1,
 * whereas the bottom-right cell is indexed with 9. Example: 1234.
 * null/empty value assumes the device has no lock currently set.
 * @return {Promise<boolean>} True if the given credential matches to the device's one
 * @throws {Error} If the verification faces an unexpected error
 */
export function verifyLockCredential(this: import("../adb.js").ADB, credential?: string | null): Promise<boolean>;
/**
 * Clears current lock credentials. Usually it takes several seconds for a device to
 * sync the credential state after this method returns.
 *
 * @this {import('../adb.js').ADB}
 * @param {string?} [credential=null] The credential value. It could be either
 * pin, password or a pattern. A pattern is specified by a non-separated list
 * of numbers that index the cell on the pattern in a 1-based manner in left
 * to right and top to bottom order, i.e. the top-left cell is indexed with 1,
 * whereas the bottom-right cell is indexed with 9. Example: 1234.
 * null/empty value assumes the device has no lock currently set.
 * @throws {Error} If operation faces an unexpected error
 */
export function clearLockCredential(this: import("../adb.js").ADB, credential?: string | null): Promise<void>;
/**
 * Checks whether the device is locked with a credential (either pin or a password
 * or a pattern).
 *
 * @this {import('../adb.js').ADB}
 * @returns {Promise<boolean>} `true` if the device is locked
 * @throws {Error} If operation faces an unexpected error
 */
export function isLockEnabled(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Sets the device lock.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} credentialType One of: password, pin, pattern.
 * @param {string} credential A non-empty credential value to be set.
 * Make sure your new credential matches to the actual system security requirements,
 * e.g. a minimum password length. A pattern is specified by a non-separated list
 * of numbers that index the cell on the pattern in a 1-based manner in left
 * to right and top to bottom order, i.e. the top-left cell is indexed with 1,
 * whereas the bottom-right cell is indexed with 9. Example: 1234.
 * @param {string?} [oldCredential=null] An old credential string.
 * It is only required to be set in case you need to change the current
 * credential rather than to set a new one. Setting it to a wrong value will
 * make this method to fail and throw an exception.
 * @throws {Error} If there was a failure while verifying input arguments or setting
 * the credential
 */
export function setLockCredential(this: import("../adb.js").ADB, credentialType: string, credential: string, oldCredential?: string | null): Promise<void>;
/**
 * Retrieve the screen lock state of the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<boolean>} True if the device is locked.
 */
export function isScreenLocked(this: import("../adb.js").ADB): Promise<boolean>;
/**
 * Dismisses keyguard overlay.
 * @this {import('../adb.js').ADB}
 */
export function dismissKeyguard(this: import("../adb.js").ADB): Promise<void>;
/**
 * Presses the corresponding key combination to make sure the device's screen
 * is not turned off and is locked if the latter is enabled.
 * @this {import('../adb.js').ADB}
 */
export function cycleWakeUp(this: import("../adb.js").ADB): Promise<void>;
/**
 * Send the special keycode to the device under test in order to lock it.
 * @this {import('../adb.js').ADB}
 */
export function lock(this: import("../adb.js").ADB): Promise<void>;
/**
 * Checks mShowingLockscreen or mDreamingLockscreen in dumpsys output to determine
 * if lock screen is showing
 *
 * A note: `adb shell dumpsys trust` performs better while detecting the locked screen state
 * in comparison to `adb dumpsys window` output parsing.
 * But the trust command does not work for `Swipe` unlock pattern.
 *
 * In some Android devices (Probably around Android 10+), `mShowingLockscreen` and `mDreamingLockscreen`
 * do not work to detect lock status. Instead, keyguard preferences helps to detect the lock condition.
 * Some devices such as Android TV do not have keyguard, so we should keep
 * screen condition as this primary method.
 *
 * @param {string} dumpsys - The output of dumpsys window command.
 * @return {boolean} True if lock screen is showing.
 */
export function isShowingLockscreen(dumpsys: string): boolean;
/**
 * Checks screenState has SCREEN_STATE_OFF in dumpsys output to determine
 * possible lock screen.
 *
 * @param {string} dumpsys - The output of dumpsys window command.
 * @return {boolean} True if lock screen is showing.
 */
export function isScreenStateOff(dumpsys: string): boolean;
//# sourceMappingURL=lockmgmt.d.ts.map