{"name": "@appium/docutils", "version": "1.1.1", "description": "Documentation generation utilities for Appium and related projects", "keywords": ["automation", "javascript", "selenium", "webdriver", "ios", "android", "firefoxos", "testing"], "homepage": "https://appium.io", "bugs": {"url": "https://github.com/appium/appium/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/appium.git", "directory": "packages/docutils"}, "license": "Apache-2.0", "author": "https://github.com/appium", "types": "./build/lib/index.d.ts", "directories": {"lib": "lib"}, "bin": {"appium-docs": "./bin/appium-docs.js"}, "files": ["index.js", "index.d.ts", "base-mkdocs.yml", "lib", "build", "tsconfig.json", "!build/tsconfig.tsbuildinfo", "requirements.txt"], "scripts": {"test": "npm run test:unit", "test:e2e": "echo \"No e2e tests for this package\"", "test:smoke": "node ./index.js", "test:unit": "mocha \"./test/unit/**/*.spec.js\"", "start": "node ./build/lib/cli/index.js"}, "dependencies": {"@appium/support": "^6.1.1", "@appium/tsconfig": "^0.3.5", "@sliphua/lilconfig-ts-loader": "3.2.2", "chalk": "4.1.2", "consola": "3.4.2", "diff": "8.0.2", "json5": "2.2.3", "lilconfig": "3.1.3", "lodash": "4.17.21", "pkg-dir": "5.0.0", "read-pkg": "5.2.0", "semver": "7.7.2", "source-map-support": "0.5.21", "teen_process": "2.3.2", "type-fest": "4.41.0", "typescript": "5.8.3", "yaml": "2.8.0", "yargs": "17.7.2", "yargs-parser": "21.1.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "publishConfig": {"access": "public"}, "gitHead": "c8fe4412525f7e1fa237813cf83fe7d98f0125eb"}