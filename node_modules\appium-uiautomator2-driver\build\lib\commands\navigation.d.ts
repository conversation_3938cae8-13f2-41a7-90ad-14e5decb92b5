/**
 * @this {AndroidUiautomator2Driver}
 * @param {string} url
 * @returns {Promise<void>}
 */
export function setUrl(this: import("../driver").AndroidUiautomator2Driver, url: string): Promise<void>;
/**
 * Start URL that take users directly to specific content in the app
 * @this {AndroidUiautomator2Driver}
 * @param {string} url The name of URL to start.
 * @param {string} [pkg] The name of the package to start the URI with.
 * @param {boolean} [waitForLaunch=true] If `false` then adb won't wait for
 * the started activity to return the control.
 * @returns {Promise<void>}
 */
export function mobileDeepLink(this: import("../driver").AndroidUiautomator2Driver, url: string, pkg?: string, waitForLaunch?: boolean): Promise<void>;
/**
 * @this {AndroidUiautomator2Driver}
 * @returns {Promise<void>}
 */
export function back(this: import("../driver").AndroidUiautomator2Driver): Promise<void>;
export type AndroidUiautomator2Driver = import("../driver").AndroidUiautomator2Driver;
//# sourceMappingURL=navigation.d.ts.map