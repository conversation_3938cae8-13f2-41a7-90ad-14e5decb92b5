/**
 * <PERSON>des software keyboard if it is visible.
 * Noop if the keyboard is already hidden.
 *
 * @this {import('../adb.js').ADB}
 * @param {number} [timeoutMs=1000] For how long to wait (in milliseconds)
 * until the keyboard is actually hidden.
 * @returns {Promise<boolean>} `false` if the keyboard was already hidden
 * @throws {Error} If the keyboard cannot be hidden.
 */
export function hideKeyboard(this: import("../adb.js").ADB, timeoutMs?: number): Promise<boolean>;
/**
 * Retrieve the state of the software keyboard on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<import('./types').KeyboardState>} The keyboard state.
 */
export function isSoftKeyboardPresent(this: import("../adb.js").ADB): Promise<import("./types").KeyboardState>;
/**
 * Send the particular keycode to the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} keycode - The actual key code to be sent.
 */
export function keyevent(this: import("../adb.js").ADB, keycode: string | number): Promise<void>;
/**
 * Retrieve the list of available input methods (IMEs) for the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string[]>} The list of IME names or an empty list.
 */
export function availableIMEs(this: import("../adb.js").ADB): Promise<string[]>;
/**
 * Retrieve the list of enabled input methods (IMEs) for the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string[]>} The list of enabled IME names or an empty list.
 */
export function enabledIMEs(this: import("../adb.js").ADB): Promise<string[]>;
/**
 * Enable the particular input method on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} imeId - One of existing IME ids.
 */
export function enableIME(this: import("../adb.js").ADB, imeId: string): Promise<void>;
/**
 * Disable the particular input method on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} imeId - One of existing IME ids.
 */
export function disableIME(this: import("../adb.js").ADB, imeId: string): Promise<void>;
/**
 * Set the particular input method on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} imeId - One of existing IME ids.
 */
export function setIME(this: import("../adb.js").ADB, imeId: string): Promise<void>;
/**
 * Get the default input method on the device under test.
 *
 * @this {import('../adb.js').ADB}
 * @return {Promise<string|null>} The name of the default input method
 */
export function defaultIME(this: import("../adb.js").ADB): Promise<string | null>;
/**
 * Send the particular text or a number to the device under test.
 * The text gets properly escaped before being passed to ADB.
 * Noop if the text is empty.
 *
 * @this {import('../adb.js').ADB}
 * @param {string|number} text - The actual text to be sent.
 * @throws {Error} If it is impossible to escape the given string
 */
export function inputText(this: import("../adb.js").ADB, text: string | number): Promise<void>;
/**
 * Executes the given function with the given input method context
 * and then restores the IME to the original value
 *
 * @this {import('../adb.js').ADB}
 * @param {string} ime - Valid IME identifier
 * @param {Function} fn - Function to execute
 * @returns {Promise<any>} The result of the given function
 */
export function runInImeContext(this: import("../adb.js").ADB, ime: string, fn: Function): Promise<any>;
//# sourceMappingURL=keyboard-commands.d.ts.map