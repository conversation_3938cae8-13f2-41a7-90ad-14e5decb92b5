{"version": 3, "file": "system-calls.d.ts", "sourceRoot": "", "sources": ["../../../lib/tools/system-calls.js"], "names": [], "mappings": "AAoCA;;;;;;GAMG;AACH,4EAHW,MAAM,GACL,OAAO,CAAC,MAAM,CAAC,CAI1B;AAyBD;;;;;;;;;;;;;GAaG;AACH,gFATW,MAAM,GACL,OAAO,CAAC,MAAM,CAAC,CAkD1B;AAeD;;;;;;;;;;;;;;GAcG;AACH,iDAXW,MAAM,GAEL,OAAO,CAAC,MAAM,CAAC,CAoB1B;AAED;;;;;;;;GAQG;AACH,6EALW,MAAM,GACL,OAAO,CAAC,MAAM,CAAC,CAmB1B;AAED;;;;;;;;GAQG;AACH,0EALW,OAAO,SAAS,EAAE,uBAAuB,GACxC,OAAO,CAAC,OAAO,SAAS,EAAE,MAAM,EAAE,CAAC,CAuD9C;AAED;;;;;;;;GAQG;AACH,+EALW,MAAM,GAEL,OAAO,CAAC,OAAO,SAAS,EAAE,MAAM,EAAE,CAAC,CAqC9C;AAED;;;;;;;;;;GAUG;AACH,kEAPW,MAAM,iBAmBhB;AAED;;;;GAIG;AACH,yEAaC;AAED;;;GAGG;AACH,yEAKC;AA4BD;;;;;GAKG;AACH,+DAFW,MAAM,EAAE,iBAMlB;AAUD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAjB4I,SAAS,SAAvI,OAAO,cAAc,EAAE,sBAAsB,GAAG,OAAO,SAAS,EAAE,gBAAgB,GAAG,OAAO,SAAS,EAAE,qBAAsB,sCAEhI,MAAM,GAAC,MAAM,EAAE,SAEf,SAAS,GASR,OAAO,CAAC,SAAS,SAAS,OAAO,SAAS,EAAE,iBAAiB,GAAG,iDAA4C,GAAG,MAAM,CAAC,CA4EjI;AAED;;;;;;;;;;;GAWG;AACH,sBARkD,cAAc,SAAnD,OAAQ,SAAS,EAAE,gBAAiB,sCACtC,MAAM,GAAC,MAAM,EAAE,SAEf,cAAc,GACb,OAAO,CAAC,cAAc,SAAS,OAAO,SAAS,EAAE,iBAAiB,GAAG,iDAA4C,GAAG,MAAM,CAAC,CAuBtI;AAED;;;;;GAKG;AACH,uEAHW,MAAM,EAAE,GACN,OAAO,cAAc,EAAE,UAAU,CAO7C;AAED;;;;;;GAMG;AACH,iEAFY,MAAM,CAIjB;AAED;;;;;;GAMG;AACH,gEAHY,OAAO,CAAC,MAAM,CAAC,CAmB1B;AAED;;;;;;;GAOG;AACH,gFAJW,MAAM,GACL,MAAM,GAAC,KAAK,CASvB;AAED;;;;;;GAMG;AACH,4EAHW,OAAO,SAAS,EAAE,uBAAuB,GACxC,OAAO,CAAC,OAAO,SAAS,EAAE,MAAM,EAAE,CAAC,CAmB9C;AAED;;;;;GAKG;AACH,uEAFW,MAAM,QAIhB;;IARD;;;;;OAKG;IACH,mDAFW,MAAM,EAIhB;IADC,qBAA0B;;AAG5B;;;;;GAKG;AACH,qEAFW,MAAM,QAWhB;;IAfD;;;;;OAKG;IACH,qDAFW,MAAM,EAWhB;IAPC,oBAA2B;;AAS7B;;;;;GAKG;AACH,oEAFW,OAAO,SAAS,EAAE,MAAM,QASlC;AAED;;;;;;;;;GASG;AACH,sEAHW,MAAM,GACL,OAAO,CAAC,OAAO,SAAS,EAAE,MAAM,GAAC,IAAI,CAAC,CA0BjD;AAED;;;;;;;;;;GAUG;AACH,+EAPW,MAAM,cACN,MAAM,GAGL,OAAO,CAAC,OAAO,SAAS,EAAE,MAAM,GAAC,IAAI,CAAC,CAmBjD;AAED;;;;;GAKG;AACH,+EAcC;AAED;;;;;;;;;;;GAWG;AACH,sEAPW,MAAM,OAAC,YAEP,MAAM,GAEL,OAAO,CAAC,OAAO,CAAC,CAsC3B;AAED;;;;;;;;GAQG;AACH,kEALW,MAAM,SACN,OAAO,SAAS,EAAE,gBAAgB,GAChC,OAAO,CAAC,UAAU,CAAC,CA+E/B;AAkCD;;;;;;;GAOG;AACH,gFAJW,MAAM,GACJ,OAAO,CAAC,IAAI,CAAC,CA4DzB;AAED;;;;;;GAMG;AACH,qFAHW,MAAM,iBAqBhB;;IAzBD;;;;;;OAMG;IACH,mEAHW,MAAM,EAqBhB;IAjBC,8BAAkD;;AAmBpD;;;;;;GAMG;AACH,gEAHW,MAAM,iBAuChB;AAED;;;;;;GAMG;AACH,gFAHW,OAAO,GACN,OAAO,CAAC,OAAO,SAAS,EAAE,UAAU,CAAC,CAqDhD;AAED;;;;;GAKG;AACH,qDAFY,OAAO,CAAC,OAAO,SAAS,EAAE,UAAU,CAAC,CAIhD;AAED;;;;;GAKG;AACH,uDAFY,OAAO,CAAC,OAAO,SAAS,EAAE,UAAU,CAAC,CAIhD;AAED;;;;;;;GAOG;AACH,uDAJY,OAAO,CAAC,OAAO,CAAC,CAM3B;AAED;;;;;;;;;;;;;;GAcG;AACH,4EALW,MAAM,GAAC,MAAM,iBA0CvB;AAED;;;;;;;;;GASG;AACH,gFANW,MAAM,GAAC,MAAM,GAIX,OAAO,CAAC,OAAO,CAAC,CAsB5B;AAED;;;;;;;;;;;;;;GAcG;AACH,2EARW,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,EAAE,QAKvB,MAAM,EAAE,iBAoClB;AAID;;;;;;;;GAQG;AACH,0CALY,MAAM,OAAA,WACN,MAAM,OAAA,GACL,KAAK,CAAC,MAAM,CAAC,CAuBzB;AAjuCD,iFAAiE;AAkSjE;;;;;;GAMG;AACH,iFAiBG;AAgBH,+CAA+C;AAC/C,iCADW;IAAC,MAAM,EAAE,QAAQ,CAAC;IAAC,IAAI,EAAE,MAAM,CAAA;CAAC,CAIxC;AAqeH;;;;;;GAMG;AACH;;;;;;;;yBAuBG;AAmZH;;;;;GAKG;AACH,gCAFU,CAAC,OAAO,EAAE,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC,CA4B7C;2BAj0C8B,cAAc;AAwC/C;;;;;;GAMG;AACH,iDAJW,MAAM,GACL,MAAM,CAejB;cAzDa,QAAQ;wBACE,QAAQ"}