"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_WS_PATHNAME_PREFIX = void 0;
exports.addWebSocketHandler = addWebSocketHandler;
exports.removeWebSocketHandler = removeWebSocketHandler;
exports.removeAllWebSocketHandlers = removeAllWebSocketHandlers;
exports.getWebSocketHandlers = getWebSocketHandlers;
const lodash_1 = __importDefault(require("lodash"));
const bluebird_1 = __importDefault(require("bluebird"));
const DEFAULT_WS_PATHNAME_PREFIX = '/ws';
exports.DEFAULT_WS_PATHNAME_PREFIX = DEFAULT_WS_PATHNAME_PREFIX;
/**
 * @this {AppiumServer}
 * @type {AppiumServer['addWebSocketHandler']}
 */
async function addWebSocketHandler(handlerPathname, handlerServer) {
    this.webSocketsMapping[handlerPathname] = handlerServer;
}
/**
 * @this {AppiumServer}
 * @type {AppiumServer['getWebSocketHandlers']}
 */
async function getWebSocketHandlers(keysFilter = null) {
    return lodash_1.default.toPairs(this.webSocketsMapping).reduce((acc, [pathname, wsServer]) => {
        if (!lodash_1.default.isString(keysFilter) || pathname.includes(keysFilter)) {
            acc[pathname] = wsServer;
        }
        return acc;
    }, {});
}
/**
 * @this {AppiumServer}
 * @type {AppiumServer['removeWebSocketHandler']}
 */
async function removeWebSocketHandler(handlerPathname) {
    const wsServer = this.webSocketsMapping?.[handlerPathname];
    if (!wsServer) {
        return false;
    }
    try {
        wsServer.close();
        for (const client of wsServer.clients || []) {
            client.terminate();
        }
        return true;
    }
    catch {
        // ignore
    }
    finally {
        delete this.webSocketsMapping[handlerPathname];
    }
    return false;
}
/**
 *
 * @this {AppiumServer}
 * @type {AppiumServer['removeAllWebSocketHandlers']}
 */
async function removeAllWebSocketHandlers() {
    if (lodash_1.default.isEmpty(this.webSocketsMapping)) {
        return false;
    }
    return lodash_1.default.some(await bluebird_1.default.all(lodash_1.default.keys(this.webSocketsMapping).map((pathname) => this.removeWebSocketHandler(pathname))));
}
/**
 * @typedef {import('@appium/types').AppiumServer} AppiumServer
 */
//# sourceMappingURL=websocket.js.map