{"name": "@appium/schema", "version": "0.8.1", "description": "Appium Configuration Schema", "keywords": ["automation", "javascript", "selenium", "webdriver", "ios", "android", "firefoxos", "testing"], "homepage": "https://appium.io", "bugs": {"url": "https://github.com/appium/appium/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/appium.git", "directory": "packages/schema"}, "license": "Apache-2.0", "author": "https://github.com/appium", "types": "./build/lib/index.d.ts", "files": ["build", "lib", "index.js", "tsconfig.json", "!build/tsconfig.tsbuildinfo"], "scripts": {"build": "node ./scripts/generate-schema-json.js", "clean": "git checkout -- ./lib/appium-config.schema.json || true", "test:smoke": "node ./index.js", "test": "exit 0"}, "dependencies": {"json-schema": "0.4.0", "source-map-support": "0.5.21"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "publishConfig": {"access": "public"}, "gitHead": "715fd03c45d791ef24b28ad915a20ce6430ff946"}