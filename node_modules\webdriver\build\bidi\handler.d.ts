/**
 * !!! PLEASE DO NOT EDIT THIS FILE !!!
 *
 * This source file, even though checked in, is auto-generated based on the
 * current development within the WebDriver Bidi spec. Any changes to this
 * file need to come from the specification. You can generate this file by calling
 *
 * ```
 * $ npm run generate:bidi
 * ```
 *
 * from the project root. You can find the scripts that generates this file in
 * ./scripts/bidi/**
 */
import type * as local from './localTypes.js';
import type * as remote from './remoteTypes.js';
import { BidiCore } from './core.js';
export declare class BidiHandler extends BidiCore {
    /**
     * WebDriver Bidi command to send command method "session.status" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-session-status
     * @param params `remote.EmptyParams` {@link https://w3c.github.io/webdriver-bidi/#command-session-status | command parameter}
     * @returns `Promise<local.SessionStatusResult>`
     **/
    sessionStatus(params: remote.EmptyParams): Promise<local.SessionStatusResult>;
    /**
     * WebDriver Bidi command to send command method "session.new" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-session-new
     * @param params `remote.SessionNewParameters` {@link https://w3c.github.io/webdriver-bidi/#command-session-new | command parameter}
     * @returns `Promise<local.SessionNewResult>`
     **/
    sessionNew(params: remote.SessionNewParameters): Promise<local.SessionNewResult>;
    /**
     * WebDriver Bidi command to send command method "session.end" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-session-end
     * @param params `remote.EmptyParams` {@link https://w3c.github.io/webdriver-bidi/#command-session-end | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    sessionEnd(params: remote.EmptyParams): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "session.subscribe" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-session-subscribe
     * @param params `remote.SessionSubscriptionRequest` {@link https://w3c.github.io/webdriver-bidi/#command-session-subscribe | command parameter}
     * @returns `Promise<local.SessionSubscribeResult>`
     **/
    sessionSubscribe(params: remote.SessionSubscriptionRequest): Promise<local.SessionSubscribeResult>;
    /**
     * WebDriver Bidi command to send command method "session.unsubscribe" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-session-unsubscribe
     * @param params `remote.SessionUnsubscribeParameters` {@link https://w3c.github.io/webdriver-bidi/#command-session-unsubscribe | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    sessionUnsubscribe(params: remote.SessionUnsubscribeParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browser.close" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browser-close
     * @param params `remote.EmptyParams` {@link https://w3c.github.io/webdriver-bidi/#command-browser-close | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    browserClose(params: remote.EmptyParams): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browser.createUserContext" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browser-createUserContext
     * @param params `remote.BrowserCreateUserContextParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browser-createUserContext | command parameter}
     * @returns `Promise<local.BrowserCreateUserContextResult>`
     **/
    browserCreateUserContext(params: remote.BrowserCreateUserContextParameters): Promise<local.BrowserCreateUserContextResult>;
    /**
     * WebDriver Bidi command to send command method "browser.getClientWindows" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browser-getClientWindows
     * @param params `remote.EmptyParams` {@link https://w3c.github.io/webdriver-bidi/#command-browser-getClientWindows | command parameter}
     * @returns `Promise<local.BrowserGetClientWindowsResult>`
     **/
    browserGetClientWindows(params: remote.EmptyParams): Promise<local.BrowserGetClientWindowsResult>;
    /**
     * WebDriver Bidi command to send command method "browser.getUserContexts" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browser-getUserContexts
     * @param params `remote.EmptyParams` {@link https://w3c.github.io/webdriver-bidi/#command-browser-getUserContexts | command parameter}
     * @returns `Promise<local.BrowserGetUserContextsResult>`
     **/
    browserGetUserContexts(params: remote.EmptyParams): Promise<local.BrowserGetUserContextsResult>;
    /**
     * WebDriver Bidi command to send command method "browser.removeUserContext" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browser-removeUserContext
     * @param params `remote.BrowserRemoveUserContextParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browser-removeUserContext | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    browserRemoveUserContext(params: remote.BrowserRemoveUserContextParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browser.setClientWindowState" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browser-setClientWindowState
     * @param params `remote.BrowserSetClientWindowStateParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browser-setClientWindowState | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    browserSetClientWindowState(params: remote.BrowserSetClientWindowStateParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.activate" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-activate
     * @param params `remote.BrowsingContextActivateParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-activate | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    browsingContextActivate(params: remote.BrowsingContextActivateParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.captureScreenshot" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-captureScreenshot
     * @param params `remote.BrowsingContextCaptureScreenshotParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-captureScreenshot | command parameter}
     * @returns `Promise<local.BrowsingContextCaptureScreenshotResult>`
     **/
    browsingContextCaptureScreenshot(params: remote.BrowsingContextCaptureScreenshotParameters): Promise<local.BrowsingContextCaptureScreenshotResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.close" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-close
     * @param params `remote.BrowsingContextCloseParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-close | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    browsingContextClose(params: remote.BrowsingContextCloseParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.create" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-create
     * @param params `remote.BrowsingContextCreateParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-create | command parameter}
     * @returns `Promise<local.BrowsingContextCreateResult>`
     **/
    browsingContextCreate(params: remote.BrowsingContextCreateParameters): Promise<local.BrowsingContextCreateResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.getTree" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-getTree
     * @param params `remote.BrowsingContextGetTreeParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-getTree | command parameter}
     * @returns `Promise<local.BrowsingContextGetTreeResult>`
     **/
    browsingContextGetTree(params: remote.BrowsingContextGetTreeParameters): Promise<local.BrowsingContextGetTreeResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.handleUserPrompt" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-handleUserPrompt
     * @param params `remote.BrowsingContextHandleUserPromptParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-handleUserPrompt | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    browsingContextHandleUserPrompt(params: remote.BrowsingContextHandleUserPromptParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.locateNodes" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-locateNodes
     * @param params `remote.BrowsingContextLocateNodesParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-locateNodes | command parameter}
     * @returns `Promise<local.BrowsingContextLocateNodesResult>`
     **/
    browsingContextLocateNodes(params: remote.BrowsingContextLocateNodesParameters): Promise<local.BrowsingContextLocateNodesResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.navigate" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-navigate
     * @param params `remote.BrowsingContextNavigateParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-navigate | command parameter}
     * @returns `Promise<local.BrowsingContextNavigateResult>`
     **/
    browsingContextNavigate(params: remote.BrowsingContextNavigateParameters): Promise<local.BrowsingContextNavigateResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.print" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-print
     * @param params `remote.BrowsingContextPrintParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-print | command parameter}
     * @returns `Promise<local.BrowsingContextPrintResult>`
     **/
    browsingContextPrint(params: remote.BrowsingContextPrintParameters): Promise<local.BrowsingContextPrintResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.reload" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-reload
     * @param params `remote.BrowsingContextReloadParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-reload | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    browsingContextReload(params: remote.BrowsingContextReloadParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.setViewport" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-setViewport
     * @param params `remote.BrowsingContextSetViewportParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-setViewport | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    browsingContextSetViewport(params: remote.BrowsingContextSetViewportParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "browsingContext.traverseHistory" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-browsingContext-traverseHistory
     * @param params `remote.BrowsingContextTraverseHistoryParameters` {@link https://w3c.github.io/webdriver-bidi/#command-browsingContext-traverseHistory | command parameter}
     * @returns `Promise<local.BrowsingContextTraverseHistoryResult>`
     **/
    browsingContextTraverseHistory(params: remote.BrowsingContextTraverseHistoryParameters): Promise<local.BrowsingContextTraverseHistoryResult>;
    /**
     * WebDriver Bidi command to send command method "emulation.setGeolocationOverride" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-emulation-setGeolocationOverride
     * @param params `remote.EmulationSetGeolocationOverrideParameters` {@link https://w3c.github.io/webdriver-bidi/#command-emulation-setGeolocationOverride | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    emulationSetGeolocationOverride(params: remote.EmulationSetGeolocationOverrideParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "network.addIntercept" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-network-addIntercept
     * @param params `remote.NetworkAddInterceptParameters` {@link https://w3c.github.io/webdriver-bidi/#command-network-addIntercept | command parameter}
     * @returns `Promise<local.NetworkAddInterceptResult>`
     **/
    networkAddIntercept(params: remote.NetworkAddInterceptParameters): Promise<local.NetworkAddInterceptResult>;
    /**
     * WebDriver Bidi command to send command method "network.continueRequest" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-network-continueRequest
     * @param params `remote.NetworkContinueRequestParameters` {@link https://w3c.github.io/webdriver-bidi/#command-network-continueRequest | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    networkContinueRequest(params: remote.NetworkContinueRequestParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "network.continueResponse" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-network-continueResponse
     * @param params `remote.NetworkContinueResponseParameters` {@link https://w3c.github.io/webdriver-bidi/#command-network-continueResponse | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    networkContinueResponse(params: remote.NetworkContinueResponseParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "network.continueWithAuth" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-network-continueWithAuth
     * @param params `remote.NetworkContinueWithAuthParameters` {@link https://w3c.github.io/webdriver-bidi/#command-network-continueWithAuth | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    networkContinueWithAuth(params: remote.NetworkContinueWithAuthParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "network.failRequest" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-network-failRequest
     * @param params `remote.NetworkFailRequestParameters` {@link https://w3c.github.io/webdriver-bidi/#command-network-failRequest | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    networkFailRequest(params: remote.NetworkFailRequestParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "network.provideResponse" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-network-provideResponse
     * @param params `remote.NetworkProvideResponseParameters` {@link https://w3c.github.io/webdriver-bidi/#command-network-provideResponse | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    networkProvideResponse(params: remote.NetworkProvideResponseParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "network.removeIntercept" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-network-removeIntercept
     * @param params `remote.NetworkRemoveInterceptParameters` {@link https://w3c.github.io/webdriver-bidi/#command-network-removeIntercept | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    networkRemoveIntercept(params: remote.NetworkRemoveInterceptParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "network.setCacheBehavior" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-network-setCacheBehavior
     * @param params `remote.NetworkSetCacheBehaviorParameters` {@link https://w3c.github.io/webdriver-bidi/#command-network-setCacheBehavior | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    networkSetCacheBehavior(params: remote.NetworkSetCacheBehaviorParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "script.addPreloadScript" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-script-addPreloadScript
     * @param params `remote.ScriptAddPreloadScriptParameters` {@link https://w3c.github.io/webdriver-bidi/#command-script-addPreloadScript | command parameter}
     * @returns `Promise<local.ScriptAddPreloadScriptResult>`
     **/
    scriptAddPreloadScript(params: remote.ScriptAddPreloadScriptParameters): Promise<local.ScriptAddPreloadScriptResult>;
    /**
     * WebDriver Bidi command to send command method "script.disown" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-script-disown
     * @param params `remote.ScriptDisownParameters` {@link https://w3c.github.io/webdriver-bidi/#command-script-disown | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    scriptDisown(params: remote.ScriptDisownParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "script.callFunction" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-script-callFunction
     * @param params `remote.ScriptCallFunctionParameters` {@link https://w3c.github.io/webdriver-bidi/#command-script-callFunction | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    scriptCallFunction(params: remote.ScriptCallFunctionParameters): Promise<local.ScriptEvaluateResult>;
    /**
     * WebDriver Bidi command to send command method "script.evaluate" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-script-evaluate
     * @param params `remote.ScriptEvaluateParameters` {@link https://w3c.github.io/webdriver-bidi/#command-script-evaluate | command parameter}
     * @returns `Promise<local.ScriptEvaluateResult>`
     **/
    scriptEvaluate(params: remote.ScriptEvaluateParameters): Promise<local.ScriptEvaluateResult>;
    /**
     * WebDriver Bidi command to send command method "script.getRealms" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-script-getRealms
     * @param params `remote.ScriptGetRealmsParameters` {@link https://w3c.github.io/webdriver-bidi/#command-script-getRealms | command parameter}
     * @returns `Promise<local.ScriptGetRealmsResult>`
     **/
    scriptGetRealms(params: remote.ScriptGetRealmsParameters): Promise<local.ScriptGetRealmsResult>;
    /**
     * WebDriver Bidi command to send command method "script.removePreloadScript" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-script-removePreloadScript
     * @param params `remote.ScriptRemovePreloadScriptParameters` {@link https://w3c.github.io/webdriver-bidi/#command-script-removePreloadScript | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    scriptRemovePreloadScript(params: remote.ScriptRemovePreloadScriptParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "storage.getCookies" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-storage-getCookies
     * @param params `remote.StorageGetCookiesParameters` {@link https://w3c.github.io/webdriver-bidi/#command-storage-getCookies | command parameter}
     * @returns `Promise<local.StorageGetCookiesResult>`
     **/
    storageGetCookies(params: remote.StorageGetCookiesParameters): Promise<local.StorageGetCookiesResult>;
    /**
     * WebDriver Bidi command to send command method "storage.setCookie" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-storage-setCookie
     * @param params `remote.StorageSetCookieParameters` {@link https://w3c.github.io/webdriver-bidi/#command-storage-setCookie | command parameter}
     * @returns `Promise<local.StorageSetCookieResult>`
     **/
    storageSetCookie(params: remote.StorageSetCookieParameters): Promise<local.StorageSetCookieResult>;
    /**
     * WebDriver Bidi command to send command method "storage.deleteCookies" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-storage-deleteCookies
     * @param params `remote.StorageDeleteCookiesParameters` {@link https://w3c.github.io/webdriver-bidi/#command-storage-deleteCookies | command parameter}
     * @returns `Promise<local.StorageDeleteCookiesResult>`
     **/
    storageDeleteCookies(params: remote.StorageDeleteCookiesParameters): Promise<local.StorageDeleteCookiesResult>;
    /**
     * WebDriver Bidi command to send command method "input.performActions" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-input-performActions
     * @param params `remote.InputPerformActionsParameters` {@link https://w3c.github.io/webdriver-bidi/#command-input-performActions | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    inputPerformActions(params: remote.InputPerformActionsParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "input.releaseActions" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-input-releaseActions
     * @param params `remote.InputReleaseActionsParameters` {@link https://w3c.github.io/webdriver-bidi/#command-input-releaseActions | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    inputReleaseActions(params: remote.InputReleaseActionsParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "input.setFiles" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-input-setFiles
     * @param params `remote.InputSetFilesParameters` {@link https://w3c.github.io/webdriver-bidi/#command-input-setFiles | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    inputSetFiles(params: remote.InputSetFilesParameters): Promise<local.EmptyResult>;
    /**
     * WebDriver Bidi command to send command method "webExtension.install" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-webExtension-install
     * @param params `remote.WebExtensionInstallParameters` {@link https://w3c.github.io/webdriver-bidi/#command-webExtension-install | command parameter}
     * @returns `Promise<local.WebExtensionInstallResult>`
     **/
    webExtensionInstall(params: remote.WebExtensionInstallParameters): Promise<local.WebExtensionInstallResult>;
    /**
     * WebDriver Bidi command to send command method "webExtension.uninstall" with parameters.
     * @url https://w3c.github.io/webdriver-bidi/#command-webExtension-uninstall
     * @param params `remote.WebExtensionUninstallParameters` {@link https://w3c.github.io/webdriver-bidi/#command-webExtension-uninstall | command parameter}
     * @returns `Promise<local.EmptyResult>`
     **/
    webExtensionUninstall(params: remote.WebExtensionUninstallParameters): Promise<local.EmptyResult>;
}
//# sourceMappingURL=handler.d.ts.map