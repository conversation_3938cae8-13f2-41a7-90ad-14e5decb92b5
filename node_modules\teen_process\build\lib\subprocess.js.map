{"version": 3, "file": "subprocess.js", "sourceRoot": "", "sources": ["../../lib/subprocess.js"], "names": [], "mappings": ";;;;;;AAAA,iDAAsC;AACtC,mCAAsC;AACtC,wDAAyB;AACzB,6CAAoC;AACpC,oDAAuB;AACvB,uCAAyC;AACzC,iDAAgD;AAEhD;;GAEG;AACH,MAAa,UAAW,SAAQ,qBAAY;IA6B1C;;;;OAIG;IACH,YAAa,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI;QAC/B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,GAAG;YAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,4BAA4B;QAC9E,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC,4BAA4B;QAC/F,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,IAAI,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC,4BAA4B;QAE5F,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,+DAA+D;QAC/D,IAAI,CAAC,GAAG,GAAG,IAAA,mBAAK,EAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,SAAS;QACX,yDAAyD;QACzD,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAE,UAAU,EAAE,KAAK;QAC1B,MAAM,MAAM,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,UAAU,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;QAEjH,IAAI,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,CAAC;YACf,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,KAAK,CAAE,aAAa,GAAG,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,MAAM,GAAG,KAAK;QACjE,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,MAAM,oBAAoB,GAAG,4BAA4B,CAAA,CAAC,SAAS,oBAAoB,CAAE,MAAM,EAAE,MAAM;YACrG,OAAO,MAAM,IAAI,MAAM,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,wEAAwE;QACxE,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YAC3B,aAAa,GAAG,oBAAoB,CAAC;QACvC,CAAC;QAED,wEAAwE;QACxE,qEAAqE;QACrE,IAAI,gBAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9B,UAAU,GAAG,aAAa,CAAC;YAC3B,aAAa,GAAG,IAAI,CAAC;QACvB,CAAC;QAED,+EAA+E;QAC/E,IAAI,gBAAC,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,aAAa,EAAE,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACzF,CAAC;YACD,MAAM,GAAG,IAAI,CAAC;YACd,aAAa,GAAG,oBAAoB,CAAC;QACvC,CAAC;aAAM,IAAI,gBAAC,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,SAAS,EAAE,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACzF,CAAC;YACD,MAAM,GAAG,IAAI,CAAC;YACd,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC;QAED,qDAAqD;QACrD,OAAO,MAAM,IAAI,kBAAC,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,6BAA6B;YAC7B,IAAI,CAAC,IAAI,GAAG,IAAA,qBAAK,EAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAElD,gEAAgE;YAChE;;;;;eAKG;YACH,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,EAAE;gBAC/B,MAAM,EAAC,MAAM,EAAE,MAAM,EAAC,GAAG,OAAO,CAAC;gBACjC,sEAAsE;gBACtE,gCAAgC;gBAChC,IAAI,CAAC;oBACH,IAAI,gBAAC,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC;wBACjE,aAAa,GAAG,IAAI,CAAC;wBACrB,OAAO,EAAE,CAAC;oBACZ,CAAC;gBACH,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,MAAM,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC;gBAED,kDAAkD;gBAClD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACtC,CAAC,CAAC;YAEF,qEAAqE;YACrE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,yCAAyC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBAC5E,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBACtC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE1B,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC1B,GAAG,GAAG,MAAM,IAAA,sBAAY,EAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;gBACtE,CAAC;gBACD,MAAM,CAAC,GAAG,CAAC,CAAC;gBAEZ,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;gBACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,CAAC,qBAAqB,CAAC,UAAU,EAAE,wCAAwC,CAAC,KAAK,EAAE,EAAE;gBAC7G,MAAM,EAAE,GAAG,IAAA,+BAAe,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC;gBACpC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACrB,6BAA6B;oBAC7B,yCAAyC;oBACzC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,UAAU,EAAE,CAAC,EAAE,CAAC;wBAC9C,IAAI,CAAC,IAAI,CAAC,SAAS,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC3C,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC;wBACtC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC;YAE9C,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CACpC,YAAY,CAAC;oBACX,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBACnD,wBAAwB;oBACxB,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;iBACxC,CAAC,CACH,CAAC;gBACF,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CACpC,YAAY,CAAC;oBACX,wBAAwB;oBACxB,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACvC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;iBACpD,CAAC,CACH,CAAC;gBACF,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChD,CAAC;YAED,qEAAqE;YACrE,oEAAoE;YACpE,yEAAyE;YACzE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;gBACpC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;gBAEhC,mEAAmE;gBACnE,gDAAgD;gBAChD,0BAA0B;gBAC1B,mEAAmE;gBACnE,+DAA+D;gBAC/D,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;gBAChD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACtC,KAAK,GAAG,KAAK,CAAC;gBAChB,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;gBAE/B,4DAA4D;gBAC5D,eAAe;gBACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC7B,CAAC,CAAC,CAAC;YAEH,oEAAoE;YACpE,iCAAiC;YACjC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,UAAU,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAC/C,CAAC;YAED,oEAAoE;YACpE,2CAA2C;YAC3C,IAAI,gBAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1B,UAAU,CAAC,GAAG,EAAE;oBACd,MAAM,CAAC,IAAI,KAAK,CAAC,oCAAoC,SAAS,KAAK;wBACjE,UAAU,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBAC7B,CAAC,EAAE,SAAS,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACxB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe;QACb,wDAAwD;QACxD,+CAA+C;IACjD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,IAAI,CAAE,MAAM,GAAG,SAAS,EAAE,OAAO,GAAG,KAAK;QAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,yDAAyD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACzF,CAAC;QACD,OAAO,MAAM,IAAI,kBAAC,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,8EAA8E;YAC9E,uBAAuB;YACvB,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,OAAO,aAAa,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YAClF,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI,CAAE,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,2DAA2D,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3F,CAAC;QAED,OAAO,MAAM,IAAI,kBAAC,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC7B,IAAI,IAAI,KAAK,IAAI,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC3D,MAAM,CAAC,IAAI,KAAK,CAAC,+BAA+B,IAAI,WAAW,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBAChF,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,IAAI,CAAC,CAAC;gBAChB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxB,kEAAkE;YAClE,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QACzF,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1C,CAAC;CACF;AAhTD,gCAgTC;AAED,kBAAe,UAAU,CAAC;AAE1B;;;;GAIG;AAEH;;;GAGG"}