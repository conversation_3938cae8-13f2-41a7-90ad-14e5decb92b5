import type { DriverData, ExternalDriver, InitialOpts, RouteMatcher, SingularSessionData, StringRecord } from '@appium/types';
import { AndroidDriver } from 'appium-android-driver';
import { mjpeg } from 'appium/support';
import { type Uiautomator2Constraints } from './constraints';
import type { Uiautomator2Settings, Uiautomator2DeviceDetails, Uiautomator2DriverCaps, Uiautomator2DriverOpts, Uiautomator2SessionCaps, Uiautomator2StartSessionOpts, W3CUiautomator2DriverCaps } from './types';
import { UiAutomator2Server } from './uiautomator2';
import { mobileGetActionHistory, mobileScheduleAction, mobileUnscheduleAction, performActions, releaseActions } from './commands/actions';
import { getAlertText, mobileAcceptAlert, mobileDismissAlert, postAcceptAlert, postDismissAlert } from './commands/alert';
import { mobileInstallMultipleApks } from './commands/app-management';
import { mobileGetBatteryInfo } from './commands/battery';
import { getClipboard, setClipboard } from './commands/clipboard';
import { active, getAttribute, elementEnabled, elementDisplayed, elementSelected, getName, getLocation, getSize, getElementRect, getElementScreenshot, getText, setValueImmediate, doSetElementValue, click, clear, mobileReplaceElementValue } from './commands/element';
import { doFindElementOrEls } from './commands/find';
import { mobileClickGesture, mobileDoubleClickGesture, mobileDragGesture, mobileFlingGesture, mobileLongClickGesture, mobilePinchCloseGesture, mobilePinchOpenGesture, mobileScroll, mobileScrollBackTo, mobileScrollGesture, mobileSwipeGesture } from './commands/gestures';
import { pressKeyCode, longPressKeyCode, mobilePressKey, mobileType, doSendKeys, keyevent } from './commands/keyboard';
import { getPageSource, getOrientation, setOrientation, openNotifications, mobileGetDeviceInfo } from './commands/misc';
import { setUrl, mobileDeepLink, back } from './commands/navigation';
import { mobileScreenshots, mobileViewportScreenshot, getScreenshot, getViewportScreenshot } from './commands/screenshot';
import { getStatusBarHeight, getDevicePixelRatio, getDisplayDensity, getViewPortRect, getWindowRect, getWindowSize, mobileViewPortRect } from './commands/viewport';
declare class AndroidUiautomator2Driver extends AndroidDriver implements ExternalDriver<Uiautomator2Constraints, string, StringRecord> {
    static newMethodMap: {
        readonly '/session/:sessionId/appium/device/get_clipboard': {
            readonly POST: {
                readonly command: "getClipboard";
                readonly payloadParams: {
                    readonly optional: readonly ["contentType"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/timeouts/implicit_wait': {
            readonly POST: {
                readonly command: "implicitWait";
                readonly payloadParams: {
                    readonly required: readonly ["ms"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/ime/available_engines': {
            readonly GET: {
                readonly command: "availableIMEEngines";
            };
        };
        readonly '/session/:sessionId/ime/active_engine': {
            readonly GET: {
                readonly command: "getActiveIMEEngine";
            };
        };
        readonly '/session/:sessionId/ime/activated': {
            readonly GET: {
                readonly command: "isIMEActivated";
            };
        };
        readonly '/session/:sessionId/ime/deactivate': {
            readonly POST: {
                readonly command: "deactivateIMEEngine";
            };
        };
        readonly '/session/:sessionId/ime/activate': {
            readonly POST: {
                readonly command: "activateIMEEngine";
                readonly payloadParams: {
                    readonly required: readonly ["engine"];
                };
            };
        };
        readonly '/session/:sessionId/window/:windowhandle/size': {
            readonly GET: {
                readonly command: "getWindowSize";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/keys': {
            readonly POST: {
                readonly command: "keys";
                readonly payloadParams: {
                    readonly required: readonly ["value"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/element/:elementId/location': {
            readonly GET: {
                readonly command: "getLocation";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/element/:elementId/location_in_view': {
            readonly GET: {
                readonly command: "getLocationInView";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/element/:elementId/size': {
            readonly GET: {
                readonly command: "getSize";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/lock': {
            readonly POST: {
                readonly command: "lock";
                readonly payloadParams: {
                    readonly optional: readonly ["seconds"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/unlock': {
            readonly POST: {
                readonly command: "unlock";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/is_locked': {
            readonly POST: {
                readonly command: "isLocked";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/start_recording_screen': {
            readonly POST: {
                readonly command: "startRecordingScreen";
                readonly payloadParams: {
                    readonly optional: readonly ["options"];
                };
            };
        };
        readonly '/session/:sessionId/appium/stop_recording_screen': {
            readonly POST: {
                readonly command: "stopRecordingScreen";
                readonly payloadParams: {
                    readonly optional: readonly ["options"];
                };
            };
        };
        readonly '/session/:sessionId/appium/performanceData/types': {
            readonly POST: {
                readonly command: "getPerformanceDataTypes";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/getPerformanceData': {
            readonly POST: {
                readonly command: "getPerformanceData";
                readonly payloadParams: {
                    readonly required: readonly ["packageName", "dataType"];
                    readonly optional: readonly ["dataReadTimeout"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/press_keycode': {
            readonly POST: {
                readonly command: "pressKeyCode";
                readonly payloadParams: {
                    readonly required: readonly ["keycode"];
                    readonly optional: readonly ["metastate", "flags"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/long_press_keycode': {
            readonly POST: {
                readonly command: "longPressKeyCode";
                readonly payloadParams: {
                    readonly required: readonly ["keycode"];
                    readonly optional: readonly ["metastate", "flags"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/finger_print': {
            readonly POST: {
                readonly command: "fingerprint";
                readonly payloadParams: {
                    readonly required: readonly ["fingerprintId"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/send_sms': {
            readonly POST: {
                readonly command: "sendSMS";
                readonly payloadParams: {
                    readonly required: readonly ["phoneNumber", "message"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/gsm_call': {
            readonly POST: {
                readonly command: "gsmCall";
                readonly payloadParams: {
                    readonly required: readonly ["phoneNumber", "action"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/gsm_signal': {
            readonly POST: {
                readonly command: "gsmSignal";
                readonly payloadParams: {
                    readonly required: readonly ["signalStrength"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/gsm_voice': {
            readonly POST: {
                readonly command: "gsmVoice";
                readonly payloadParams: {
                    readonly required: readonly ["state"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/power_capacity': {
            readonly POST: {
                readonly command: "powerCapacity";
                readonly payloadParams: {
                    readonly required: readonly ["percent"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/power_ac': {
            readonly POST: {
                readonly command: "powerAC";
                readonly payloadParams: {
                    readonly required: readonly ["state"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/network_speed': {
            readonly POST: {
                readonly command: "networkSpeed";
                readonly payloadParams: {
                    readonly required: readonly ["netspeed"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/keyevent': {
            readonly POST: {
                readonly command: "keyevent";
                readonly payloadParams: {
                    readonly required: readonly ["keycode"];
                    readonly optional: readonly ["metastate"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/current_activity': {
            readonly GET: {
                readonly command: "getCurrentActivity";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/current_package': {
            readonly GET: {
                readonly command: "getCurrentPackage";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/app_state': {
            readonly POST: {
                readonly command: "queryAppState";
                readonly payloadParams: {
                    readonly required: readonly [readonly ["appId"], readonly ["bundleId"]];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/toggle_airplane_mode': {
            readonly POST: {
                readonly command: "toggleFlightMode";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/toggle_data': {
            readonly POST: {
                readonly command: "toggleData";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/toggle_wifi': {
            readonly POST: {
                readonly command: "toggleWiFi";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/toggle_location_services': {
            readonly POST: {
                readonly command: "toggleLocationServices";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/open_notifications': {
            readonly POST: {
                readonly command: "openNotifications";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/start_activity': {
            readonly POST: {
                readonly command: "startActivity";
                readonly payloadParams: {
                    readonly required: readonly ["appPackage", "appActivity"];
                    readonly optional: readonly ["appWaitPackage", "appWaitActivity", "intentAction", "intentCategory", "intentFlags", "optionalIntentArguments", "dontStopAppOnReset"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/system_bars': {
            readonly GET: {
                readonly command: "getSystemBars";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/device/display_density': {
            readonly GET: {
                readonly command: "getDisplayDensity";
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/app/background': {
            readonly POST: {
                readonly command: "background";
                readonly payloadParams: {
                    readonly required: readonly ["seconds"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/app/strings': {
            readonly POST: {
                readonly command: "getStrings";
                readonly payloadParams: {
                    readonly optional: readonly ["language", "stringFile"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/element/:elementId/value': {
            readonly POST: {
                readonly command: "setValueImmediate";
                readonly payloadParams: {
                    readonly required: readonly ["text"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/appium/element/:elementId/replace_value': {
            readonly POST: {
                readonly command: "replaceValue";
                readonly payloadParams: {
                    readonly required: readonly ["text"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/network_connection': {
            readonly GET: {
                readonly command: "getNetworkConnection";
                readonly deprecated: true;
            };
            readonly POST: {
                readonly command: "setNetworkConnection";
                readonly payloadParams: {
                    readonly unwrap: "parameters";
                    readonly required: readonly ["type"];
                };
                readonly deprecated: true;
            };
        };
        readonly '/session/:sessionId/location': {
            readonly GET: {
                readonly command: "getGeoLocation";
                readonly deprecated: true;
            };
            readonly POST: {
                readonly command: "setGeoLocation";
                readonly payloadParams: {
                    readonly required: readonly ["location"];
                };
                readonly deprecated: true;
            };
        };
    };
    static executeMethodMap: {
        readonly 'mobile: dragGesture': {
            readonly command: "mobileDragGesture";
            readonly params: {
                readonly optional: readonly ["elementId", "startX", "startY", "endX", "endY", "speed"];
            };
        };
        readonly 'mobile: flingGesture': {
            readonly command: "mobileFlingGesture";
            readonly params: {
                readonly required: readonly ["direction"];
                readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
            };
        };
        readonly 'mobile: doubleClickGesture': {
            readonly command: "mobileDoubleClickGesture";
            readonly params: {
                readonly optional: readonly ["elementId", "x", "y"];
            };
        };
        readonly 'mobile: clickGesture': {
            readonly command: "mobileClickGesture";
            readonly params: {
                readonly optional: readonly ["elementId", "x", "y"];
            };
        };
        readonly 'mobile: longClickGesture': {
            readonly command: "mobileLongClickGesture";
            readonly params: {
                readonly optional: readonly ["elementId", "x", "y", "duration"];
            };
        };
        readonly 'mobile: pinchCloseGesture': {
            readonly command: "mobilePinchCloseGesture";
            readonly params: {
                readonly required: readonly ["percent"];
                readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
            };
        };
        readonly 'mobile: pinchOpenGesture': {
            readonly command: "mobilePinchOpenGesture";
            readonly params: {
                readonly required: readonly ["percent"];
                readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
            };
        };
        readonly 'mobile: swipeGesture': {
            readonly command: "mobileSwipeGesture";
            readonly params: {
                readonly required: readonly ["direction", "percent"];
                readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
            };
        };
        readonly 'mobile: scrollGesture': {
            readonly command: "mobileScrollGesture";
            readonly params: {
                readonly required: readonly ["direction", "percent"];
                readonly optional: readonly ["elementId", "left", "top", "width", "height", "speed"];
            };
        };
        readonly 'mobile: scrollBackTo': {
            readonly command: "mobileScrollBackTo";
            readonly params: {
                readonly required: readonly ["elementId", "elementToId"];
            };
        };
        readonly 'mobile: scroll': {
            readonly command: "mobileScroll";
            readonly params: {
                readonly required: readonly ["strategy", "selector"];
                readonly optional: readonly ["elementId", "maxSwipes"];
            };
        };
        readonly 'mobile: viewportScreenshot': {
            readonly command: "mobileViewportScreenshot";
        };
        readonly 'mobile: viewportRect': {
            readonly command: "mobileViewPortRect";
        };
        readonly 'mobile: deepLink': {
            readonly command: "mobileDeepLink";
            readonly params: {
                readonly required: readonly ["url"];
                readonly optional: readonly ["package", "waitForLaunch"];
            };
        };
        readonly 'mobile: acceptAlert': {
            readonly command: "mobileAcceptAlert";
            readonly params: {
                readonly optional: readonly ["buttonLabel"];
            };
        };
        readonly 'mobile: dismissAlert': {
            readonly command: "mobileDismissAlert";
            readonly params: {
                readonly optional: readonly ["buttonLabel"];
            };
        };
        readonly 'mobile: batteryInfo': {
            readonly command: "mobileGetBatteryInfo";
        };
        readonly 'mobile: deviceInfo': {
            readonly command: "mobileGetDeviceInfo";
        };
        readonly 'mobile: openNotifications': {
            readonly command: "openNotifications";
        };
        readonly 'mobile: type': {
            readonly command: "mobileType";
            readonly params: {
                readonly required: readonly ["text"];
            };
        };
        readonly 'mobile: replaceElementValue': {
            readonly command: "mobileReplaceElementValue";
            readonly params: {
                readonly required: readonly ["elementId", "text"];
            };
        };
        readonly 'mobile: installMultipleApks': {
            readonly command: "mobileInstallMultipleApks";
            readonly params: {
                readonly required: readonly ["apks"];
                readonly optional: readonly ["options"];
            };
        };
        readonly 'mobile: pressKey': {
            readonly command: "mobilePressKey";
            readonly params: {
                readonly required: readonly ["keycode"];
                readonly optional: readonly ["metastate", "flags", "isLongPress"];
            };
        };
        readonly 'mobile: screenshots': {
            readonly command: "mobileScreenshots";
            readonly params: {
                readonly optional: readonly ["displayId"];
            };
        };
        readonly 'mobile: scheduleAction': {
            readonly command: "mobileScheduleAction";
            readonly params: {
                readonly required: readonly ["name", "steps"];
                readonly optional: readonly ["maxPass", "maxFail", "times", "intervalMs", "maxHistoryItems"];
            };
        };
        readonly 'mobile: getActionHistory': {
            readonly command: "mobileGetActionHistory";
            readonly params: {
                readonly required: readonly ["name"];
            };
        };
        readonly 'mobile: unscheduleAction': {
            readonly command: "mobileUnscheduleAction";
            readonly params: {
                readonly required: readonly ["name"];
            };
        };
        readonly 'mobile: setClipboard': {
            readonly command: "setClipboard";
            readonly params: {
                readonly required: readonly ["content"];
                readonly optional: readonly ["contentType", "label"];
            };
        };
        readonly 'mobile: getClipboard': {
            readonly command: "getClipboard";
        };
        readonly 'mobile: shell': {
            readonly command: "mobileShell";
            readonly params: {
                readonly required: readonly ["command"];
                readonly optional: readonly ["args", "timeout", "includeStderr"];
            };
        };
        readonly 'mobile: execEmuConsoleCommand': {
            readonly command: "mobileExecEmuConsoleCommand";
            readonly params: {
                readonly required: readonly ["command"];
                readonly optional: readonly ["execTimeout", "connTimeout", "initTimeout"];
            };
        };
        readonly 'mobile: startLogsBroadcast': {
            readonly command: "mobileStartLogsBroadcast";
        };
        readonly 'mobile: stopLogsBroadcast': {
            readonly command: "mobileStopLogsBroadcast";
        };
        readonly 'mobile: changePermissions': {
            readonly command: "mobileChangePermissions";
            readonly params: {
                readonly required: readonly ["permissions"];
                readonly optional: readonly ["appPackage", "action", "target"];
            };
        };
        readonly 'mobile: getPermissions': {
            readonly command: "mobileGetPermissions";
            readonly params: {
                readonly optional: readonly ["type", "appPackage"];
            };
        };
        readonly 'mobile: performEditorAction': {
            readonly command: "mobilePerformEditorAction";
            readonly params: {
                readonly required: readonly ["action"];
            };
        };
        readonly 'mobile: getDeviceTime': {
            readonly command: "mobileGetDeviceTime";
            readonly params: {
                readonly optional: readonly ["format"];
            };
        };
        readonly 'mobile: startScreenStreaming': {
            readonly command: "mobileStartScreenStreaming";
            readonly params: {
                readonly optional: readonly ["width", "height", "bitRate", "host", "port", "pathname", "tcpPort", "quality", "considerRotation", "logPipelineDetails"];
            };
        };
        readonly 'mobile: stopScreenStreaming': {
            readonly command: "mobileStopScreenStreaming";
        };
        readonly 'mobile: getNotifications': {
            readonly command: "mobileGetNotifications";
        };
        readonly 'mobile: listSms': {
            readonly command: "mobileListSms";
        };
        readonly 'mobile: pushFile': {
            readonly command: "pushFile";
            readonly params: {
                readonly required: readonly ["remotePath", "payload"];
            };
        };
        readonly 'mobile: pullFolder': {
            readonly command: "pullFolder";
            readonly params: {
                readonly required: readonly ["remotePath"];
            };
        };
        readonly 'mobile: pullFile': {
            readonly command: "pullFile";
            readonly params: {
                readonly required: readonly ["remotePath"];
            };
        };
        readonly 'mobile: deleteFile': {
            readonly command: "mobileDeleteFile";
            readonly params: {
                readonly required: readonly ["remotePath"];
            };
        };
        readonly 'mobile: isAppInstalled': {
            readonly command: "mobileIsAppInstalled";
            readonly params: {
                readonly required: readonly ["appId"];
                readonly optional: readonly ["user"];
            };
        };
        readonly 'mobile: queryAppState': {
            readonly command: "queryAppState";
            readonly params: {
                readonly required: readonly ["appId"];
            };
        };
        readonly 'mobile: activateApp': {
            readonly command: "activateApp";
            readonly params: {
                readonly required: readonly ["appId"];
            };
        };
        readonly 'mobile: removeApp': {
            readonly command: "mobileRemoveApp";
            readonly params: {
                readonly required: readonly ["appId"];
                readonly optional: readonly ["timeout", "keepData", "skipInstallCheck"];
            };
        };
        readonly 'mobile: terminateApp': {
            readonly command: "mobileTerminateApp";
            readonly params: {
                readonly required: readonly ["appId"];
                readonly optional: readonly ["timeout"];
            };
        };
        readonly 'mobile: installApp': {
            readonly command: "mobileInstallApp";
            readonly params: {
                readonly required: readonly ["appPath"];
                readonly optional: readonly ["checkVersion", "timeout", "allowTestPackages", "useSdcard", "grantPermissions", "replace", "noIncremental"];
            };
        };
        readonly 'mobile: clearApp': {
            readonly command: "mobileClearApp";
            readonly params: {
                readonly required: readonly ["appId"];
            };
        };
        readonly 'mobile: backgroundApp': {
            readonly command: "mobileBackgroundApp";
            readonly params: {
                readonly optional: readonly ["seconds"];
            };
        };
        readonly 'mobile: startService': {
            readonly command: "mobileStartService";
            readonly params: {
                readonly optional: readonly ["foreground", "user", "intent", "action", "package", "uri", "mimeType", "identifier", "component", "categories", "extras", "flags"];
            };
        };
        readonly 'mobile: stopService': {
            readonly command: "mobileStopService";
            readonly params: {
                readonly optional: readonly ["user", "intent", "action", "package", "uri", "mimeType", "identifier", "component", "categories", "extras", "flags"];
            };
        };
        readonly 'mobile: startActivity': {
            readonly command: "mobileStartActivity";
            readonly params: {
                readonly optional: readonly ["wait", "stop", "windowingMode", "activityType", "display", "user", "intent", "action", "package", "uri", "mimeType", "identifier", "component", "categories", "extras", "flags"];
            };
        };
        readonly 'mobile: broadcast': {
            readonly command: "mobileBroadcast";
            readonly params: {
                readonly optional: readonly ["receiverPermission", "allowBackgroundActivityStarts", "user", "intent", "action", "package", "uri", "mimeType", "identifier", "component", "categories", "extras", "flags"];
            };
        };
        readonly 'mobile: getContexts': {
            readonly command: "mobileGetContexts";
            readonly params: {
                readonly optional: readonly ["waitForWebviewMs"];
            };
        };
        readonly 'mobile: lock': {
            readonly command: "lock";
            readonly params: {
                readonly optional: readonly ["seconds"];
            };
        };
        readonly 'mobile: unlock': {
            readonly command: "mobileUnlock";
            readonly params: {
                readonly optional: readonly ["key", "type", "strategy", "timeoutMs"];
            };
        };
        readonly 'mobile: isLocked': {
            readonly command: "isLocked";
        };
        readonly 'mobile: refreshGpsCache': {
            readonly command: "mobileRefreshGpsCache";
            readonly params: {
                readonly optional: readonly ["timeoutMs"];
            };
        };
        readonly 'mobile: startMediaProjectionRecording': {
            readonly command: "mobileStartMediaProjectionRecording";
            readonly params: {
                readonly optional: readonly ["resolution", "priority", "maxDurationSec", "filename"];
            };
        };
        readonly 'mobile: isMediaProjectionRecordingRunning': {
            readonly command: "mobileIsMediaProjectionRecordingRunning";
        };
        readonly 'mobile: stopMediaProjectionRecording': {
            readonly command: "mobileStopMediaProjectionRecording";
            readonly params: {
                readonly optional: readonly ["remotePath", "user", "pass", "method", "headers", "fileFieldName", "formFields", "uploadTimeout"];
            };
        };
        readonly 'mobile: getConnectivity': {
            readonly command: "mobileGetConnectivity";
            readonly params: {
                readonly optional: readonly ["services"];
            };
        };
        readonly 'mobile: setConnectivity': {
            readonly command: "mobileSetConnectivity";
            readonly params: {
                readonly optional: readonly ["wifi", "data", "airplaneMode"];
            };
        };
        readonly 'mobile: hideKeyboard': {
            readonly command: "hideKeyboard";
        };
        readonly 'mobile: isKeyboardShown': {
            readonly command: "isKeyboardShown";
        };
        readonly 'mobile: deviceidle': {
            readonly command: "mobileDeviceidle";
            readonly params: {
                readonly required: readonly ["action"];
                readonly optional: readonly ["packages"];
            };
        };
        readonly 'mobile: bluetooth': {
            readonly command: "mobileBluetooth";
            readonly params: {
                readonly required: readonly ["action"];
            };
        };
        readonly 'mobile: nfc': {
            readonly command: "mobileNfc";
            readonly params: {
                readonly required: readonly ["action"];
            };
        };
        readonly 'mobile: setUiMode': {
            readonly command: "mobileSetUiMode";
            readonly params: {
                readonly required: readonly ["mode", "value"];
            };
        };
        readonly 'mobile: getUiMode': {
            readonly command: "mobileGetUiMode";
            readonly params: {
                readonly required: readonly ["mode"];
            };
        };
        readonly 'mobile: injectEmulatorCameraImage': {
            readonly command: "mobileInjectEmulatorCameraImage";
            readonly params: {
                readonly required: readonly ["payload"];
            };
        };
        readonly 'mobile: sendTrimMemory': {
            readonly command: "mobileSendTrimMemory";
            readonly params: {
                readonly required: readonly ["pkg", "level"];
            };
        };
        readonly 'mobile: getPerformanceData': {
            readonly command: "mobileGetPerformanceData";
            readonly params: {
                readonly required: readonly ["packageName", "dataType"];
            };
        };
        readonly 'mobile: getPerformanceDataTypes': {
            readonly command: "getPerformanceDataTypes";
        };
        readonly 'mobile: toggleGps': {
            readonly command: "toggleLocationServices";
        };
        readonly 'mobile: isGpsEnabled': {
            readonly command: "isLocationServicesEnabled";
        };
        readonly 'mobile: getDisplayDensity': {
            readonly command: "getDisplayDensity";
        };
        readonly 'mobile: getSystemBars': {
            readonly command: "getSystemBars";
        };
        readonly 'mobile: statusBar': {
            readonly command: "mobilePerformStatusBarCommand";
            readonly params: {
                readonly required: readonly ["command"];
                readonly optional: readonly ["component"];
            };
        };
        readonly 'mobile: fingerprint': {
            readonly command: "mobileFingerprint";
            readonly params: {
                readonly required: readonly ["fingerprintId"];
            };
        };
        readonly 'mobile: sendSms': {
            readonly command: "mobileSendSms";
            readonly params: {
                readonly required: readonly ["phoneNumber", "message"];
            };
        };
        readonly 'mobile: gsmCall': {
            readonly command: "mobileGsmCall";
            readonly params: {
                readonly required: readonly ["phoneNumber", "action"];
            };
        };
        readonly 'mobile: gsmSignal': {
            readonly command: "mobileGsmSignal";
            readonly params: {
                readonly required: readonly ["strength"];
            };
        };
        readonly 'mobile: gsmVoice': {
            readonly command: "mobileGsmVoice";
            readonly params: {
                readonly required: readonly ["state"];
            };
        };
        readonly 'mobile: powerAc': {
            readonly command: "mobilePowerAc";
            readonly params: {
                readonly required: readonly ["state"];
            };
        };
        readonly 'mobile: powerCapacity': {
            readonly command: "mobilePowerCapacity";
            readonly params: {
                readonly required: readonly ["percent"];
            };
        };
        readonly 'mobile: networkSpeed': {
            readonly command: "mobileNetworkSpeed";
            readonly params: {
                readonly required: readonly ["speed"];
            };
        };
        readonly 'mobile: sensorSet': {
            readonly command: "sensorSet";
            readonly params: {
                readonly required: readonly ["sensorType", "value"];
            };
        };
        readonly 'mobile: getCurrentActivity': {
            readonly command: "getCurrentActivity";
        };
        readonly 'mobile: getCurrentPackage': {
            readonly command: "getCurrentPackage";
        };
        readonly 'mobile: setGeolocation': {
            readonly command: "mobileSetGeolocation";
            readonly params: {
                readonly required: readonly ["latitude", "longitude"];
                readonly optional: readonly ["altitude", "satellites", "speed", "bearing", "accuracy"];
            };
        };
        readonly 'mobile: getGeolocation': {
            readonly command: "mobileGetGeolocation";
        };
        readonly 'mobile: resetGeolocation': {
            readonly command: "mobileResetGeolocation";
        };
        readonly 'mobile: getAppStrings': {
            readonly command: "getStrings";
            readonly params: {
                readonly optional: readonly ["language"];
            };
        };
    };
    uiautomator2: UiAutomator2Server;
    systemPort: number | undefined;
    _originalIme: string | null;
    mjpegStream?: mjpeg.MJpegStream;
    caps: Uiautomator2DriverCaps;
    opts: Uiautomator2DriverOpts;
    desiredCapConstraints: Uiautomator2Constraints;
    constructor(opts?: InitialOpts, shouldValidateCaps?: boolean);
    validateDesiredCaps(caps: any): caps is Uiautomator2DriverCaps;
    createSession(w3cCaps1: W3CUiautomator2DriverCaps, w3cCaps2?: W3CUiautomator2DriverCaps, w3cCaps3?: W3CUiautomator2DriverCaps, driverData?: DriverData[]): Promise<any>;
    getDeviceDetails(): Promise<Uiautomator2DeviceDetails>;
    get driverData(): {};
    getSession(): Promise<SingularSessionData<Uiautomator2Constraints>>;
    allocateSystemPort(): Promise<void>;
    releaseSystemPort(): Promise<void>;
    allocateMjpegServerPort(): Promise<void>;
    releaseMjpegServerPort(): Promise<void>;
    performSessionPreExecSetup(): Promise<StringRecord | undefined>;
    performSessionExecution(capsWithSessionInfo: StringRecord): Promise<void>;
    performSessionPostExecSetup(): Promise<void>;
    startUiAutomator2Session(caps: Uiautomator2StartSessionOpts): Promise<Uiautomator2SessionCaps>;
    initUiAutomator2Server(): Promise<UiAutomator2Server>;
    initAUT(): Promise<void>;
    ensureAppStarts(): Promise<void>;
    deleteSession(): Promise<void>;
    checkAppPresent(): Promise<void>;
    onSettingsUpdate(): Promise<void>;
    proxyActive(sessionId: string): boolean;
    canProxy(sessionId: string): boolean;
    getProxyAvoidList(): RouteMatcher[];
    updateSettings(settings: Uiautomator2Settings): Promise<void>;
    getSettings(): Promise<any>;
    mobileGetActionHistory: typeof mobileGetActionHistory;
    mobileScheduleAction: typeof mobileScheduleAction;
    mobileUnscheduleAction: typeof mobileUnscheduleAction;
    performActions: typeof performActions;
    releaseActions: typeof releaseActions;
    getAlertText: typeof getAlertText;
    mobileAcceptAlert: typeof mobileAcceptAlert;
    mobileDismissAlert: typeof mobileDismissAlert;
    postAcceptAlert: typeof postAcceptAlert;
    postDismissAlert: typeof postDismissAlert;
    mobileInstallMultipleApks: typeof mobileInstallMultipleApks;
    mobileGetBatteryInfo: typeof mobileGetBatteryInfo;
    active: typeof active;
    getAttribute: typeof getAttribute;
    elementEnabled: typeof elementEnabled;
    elementDisplayed: typeof elementDisplayed;
    elementSelected: typeof elementSelected;
    getName: typeof getName;
    getLocation: typeof getLocation;
    getSize: typeof getSize;
    getElementRect: typeof getElementRect;
    getElementScreenshot: typeof getElementScreenshot;
    getText: typeof getText;
    setValueImmediate: typeof setValueImmediate;
    doSetElementValue: typeof doSetElementValue;
    click: typeof click;
    clear: typeof clear;
    mobileReplaceElementValue: typeof mobileReplaceElementValue;
    doFindElementOrEls: typeof doFindElementOrEls;
    mobileClickGesture: typeof mobileClickGesture;
    mobileDoubleClickGesture: typeof mobileDoubleClickGesture;
    mobileDragGesture: typeof mobileDragGesture;
    mobileFlingGesture: typeof mobileFlingGesture;
    mobileLongClickGesture: typeof mobileLongClickGesture;
    mobilePinchCloseGesture: typeof mobilePinchCloseGesture;
    mobilePinchOpenGesture: typeof mobilePinchOpenGesture;
    mobileScroll: typeof mobileScroll;
    mobileScrollBackTo: typeof mobileScrollBackTo;
    mobileScrollGesture: typeof mobileScrollGesture;
    mobileSwipeGesture: typeof mobileSwipeGesture;
    pressKeyCode: typeof pressKeyCode;
    longPressKeyCode: typeof longPressKeyCode;
    mobilePressKey: typeof mobilePressKey;
    mobileType: typeof mobileType;
    doSendKeys: typeof doSendKeys;
    keyevent: typeof keyevent;
    getPageSource: typeof getPageSource;
    getOrientation: typeof getOrientation;
    setOrientation: typeof setOrientation;
    openNotifications: typeof openNotifications;
    suspendChromedriverProxy: any;
    mobileGetDeviceInfo: typeof mobileGetDeviceInfo;
    getClipboard: typeof getClipboard;
    setClipboard: typeof setClipboard;
    setUrl: typeof setUrl;
    mobileDeepLink: typeof mobileDeepLink;
    back: typeof back;
    mobileScreenshots: typeof mobileScreenshots;
    mobileViewportScreenshot: typeof mobileViewportScreenshot;
    getScreenshot: typeof getScreenshot;
    getViewportScreenshot: typeof getViewportScreenshot;
    getStatusBarHeight: typeof getStatusBarHeight;
    getDevicePixelRatio: typeof getDevicePixelRatio;
    getDisplayDensity: typeof getDisplayDensity;
    getViewPortRect: typeof getViewPortRect;
    getWindowRect: typeof getWindowRect;
    getWindowSize: typeof getWindowSize;
    mobileViewPortRect: typeof mobileViewPortRect;
}
export { AndroidUiautomator2Driver };
//# sourceMappingURL=driver.d.ts.map