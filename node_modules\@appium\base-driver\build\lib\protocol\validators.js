"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validators = void 0;
const lodash_1 = __importDefault(require("lodash"));
function isNumber(o) {
    return lodash_1.default.isNumber(o) || !lodash_1.default.isNaN(parseInt(o, 10)) || !lodash_1.default.isNaN(parseFloat(o));
}
function msValidator(ms) {
    if (!lodash_1.default.isNumber(ms) || ms < 0) {
        throw new Error('Wait ms must be a number equal to 0 or greater');
    }
}
/**
 * @deprecated
 */
const validators = {
    setUrl: (url) => {
        // either an `xyz://`, `about:`, or `data:` scheme is allowed
        if (!url || !url.match(/^([a-zA-Z0-9_+.-]+:\/\/)|(about:)|(data:)/)) {
            throw new Error('Url or <PERSON>ri must start with <scheme>://');
        }
    },
    implicitWait: (ms) => {
        msValidator(ms);
    },
    asyncScriptTimeout: (ms) => {
        msValidator(ms);
    },
    clickCurrent: (button) => {
        if (!(isNumber(button) || lodash_1.default.isUndefined(button)) || button < 0 || button > 2) {
            throw new Error('Click button must be 0, 1, or 2');
        }
    },
    setNetworkConnection: (type) => {
        if (!isNumber(type) || [0, 1, 2, 4, 6].indexOf(type) === -1) {
            throw new Error('Network type must be one of 0, 1, 2, 4, 6');
        }
    },
};
exports.validators = validators;
//# sourceMappingURL=validators.js.map