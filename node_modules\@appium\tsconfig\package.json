{"name": "@appium/tsconfig", "version": "0.3.5", "description": "Shared TypeScript Config for Appium", "main": "tsconfig.json", "keywords": ["automation", "javascript", "selenium", "webdriver", "ios", "android", "firefoxos", "testing"], "license": "Apache-2.0", "files": ["tsconfig*.json"], "author": "https://github.com/appium", "bugs": {"url": "https://github.com/appium/appium/issues"}, "repository": {"type": "git", "url": "https://github.com/appium/appium.git", "directory": "packages/tsconfig"}, "homepage": "https://appium.io", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=8"}, "dependencies": {"@tsconfig/node14": "14.1.3"}, "scripts": {"test:smoke": "node tsconfig.json && node tsconfig.plugin.json"}, "publishConfig": {"access": "public"}, "gitHead": "7c05f5c213e284b6be0f1ecf5550a40d84e0899e"}