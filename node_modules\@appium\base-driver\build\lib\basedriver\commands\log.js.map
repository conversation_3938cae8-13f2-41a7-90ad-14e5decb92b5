{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../../../../lib/basedriver/commands/log.ts"], "names": [], "mappings": ";;;;;AACA,oDAAuB;AAEvB,mCAA8B;AAO9B,MAAM,WAAW,GAAiB;IAChC,iBAAiB,EAAE,EAAE;IAErB,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,MAAM,CAAyC,OAAe;QAClE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACzC,MAAM,yBAAyB,GAAG,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;YACrF,MAAM,IAAI,KAAK,CACb,yBAAyB,MAAM,CAAC,OAAO,CAAC,KAAK;gBAC3C,oBAAoB,IAAI,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE,CAClE,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;CACF,CAAC;AAEF,IAAA,aAAK,EAAC,WAAW,CAAC,CAAC"}