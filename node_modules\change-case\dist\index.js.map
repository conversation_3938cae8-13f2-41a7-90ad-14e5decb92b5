{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,iEAAiE;AACjE,MAAM,oBAAoB,GAAG,wBAAwB,CAAC;AACtD,MAAM,oBAAoB,GAAG,8BAA8B,CAAC;AAE5D,sEAAsE;AACtE,MAAM,wBAAwB,GAAG,uBAAuB,CAAC;AAEzD,sEAAsE;AACtE,MAAM,oBAAoB,GAAG,gBAAgB,CAAC;AAE9C,oCAAoC;AACpC,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAErC,0DAA0D;AAC1D,MAAM,gCAAgC,GAAG,EAAE,CAAC;AA4B5C;;GAEG;AACH,MAAM,UAAU,KAAK,CAAC,KAAa;IACjC,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IAE1B,MAAM,GAAG,MAAM;SACZ,OAAO,CAAC,oBAAoB,EAAE,mBAAmB,CAAC;SAClD,OAAO,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;IAEtD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;IAEpD,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAExB,oDAAoD;IACpD,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI;QAAE,KAAK,EAAE,CAAC;IAC9C,IAAI,KAAK,KAAK,GAAG;QAAE,OAAO,EAAE,CAAC;IAC7B,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI;QAAE,GAAG,EAAE,CAAC;IAE9C,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,KAAa;IAChD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,MAAM,KAAK,GAAG,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,KAAK,EAAE;YACT,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC3D,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;SAC/D;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAa,EAAE,OAAiB;IACrD,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClE,OAAO,CACL,MAAM;QACN,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,GAAG,CAAC;QACxE,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,KAAa,EAAE,OAA2B;IAClE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClE,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAG,OAAO,EAAE,wBAAwB;QACjD,CAAC,CAAC,2BAA2B,CAAC,KAAK,EAAE,KAAK,CAAC;QAC3C,CAAC,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC7C,OAAO,CACL,MAAM;QACN,KAAK;aACF,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnB,IAAI,KAAK,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC;YACpC,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC;aACD,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC;QACjC,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,KAAa,EAAE,OAA2B;IACnE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClE,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAG,OAAO,EAAE,wBAAwB;QACjD,CAAC,CAAC,2BAA2B,CAAC,KAAK,EAAE,KAAK,CAAC;QAC3C,CAAC,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC7C,OAAO,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC;AAC/E,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,KAAa,EAAE,OAAiB;IAC9D,OAAO,WAAW,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,KAAa,EAAE,OAAiB;IAC1D,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClE,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,OAAO,CACL,MAAM;QACN,KAAK;aACF,GAAG,CAAC,2BAA2B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aAC9C,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,GAAG,CAAC;QAClC,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa,EAAE,OAAiB;IAC3D,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClE,OAAO,CACL,MAAM;QACN,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,GAAG,CAAC;QACxE,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,KAAa,EAAE,OAAiB;IACtD,OAAO,MAAM,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,KAAa,EAAE,OAAiB;IACxD,OAAO,MAAM,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAa,EAAE,OAAiB;IACvD,OAAO,MAAM,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa,EAAE,OAAiB;IAC3D,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClE,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAG,2BAA2B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC5D,OAAO,CACL,MAAM;QACN,KAAK;aACF,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnB,IAAI,KAAK,KAAK,CAAC;gBAAE,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;YACxC,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC,CAAC;aACD,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,GAAG,CAAC;QAClC,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,KAAa,EAAE,OAAiB;IACxD,OAAO,MAAM,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,KAAa,EAAE,OAAiB;IACxD,OAAO,WAAW,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,YAAY,CAAC,MAAc;IAClC,OAAO,MAAM,KAAK,KAAK;QACrB,CAAC,CAAC,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE;QACxC,CAAC,CAAC,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,YAAY,CAAC,MAAc;IAClC,OAAO,MAAM,KAAK,KAAK;QACrB,CAAC,CAAC,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE;QACxC,CAAC,CAAC,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,2BAA2B,CAClC,KAAgC,EAChC,KAAgC;IAEhC,OAAO,CAAC,IAAY,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACtE,CAAC;AAED,SAAS,0BAA0B,CACjC,KAAgC,EAChC,KAAgC;IAEhC,OAAO,CAAC,IAAY,EAAE,KAAa,EAAE,EAAE;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,MAAM,OAAO,GACX,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzE,OAAO,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,KAAa,EACb,UAAmB,EAAE;IAErB,MAAM,OAAO,GACX,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC5E,MAAM,gBAAgB,GACpB,OAAO,CAAC,gBAAgB,IAAI,gCAAgC,CAAC;IAC/D,MAAM,gBAAgB,GACpB,OAAO,CAAC,gBAAgB,IAAI,gCAAgC,CAAC;IAC/D,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;IAE/B,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,EAAE;QACjC,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACvC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,MAAM;QAC5C,WAAW,EAAE,CAAC;KACf;IAED,OAAO,WAAW,GAAG,WAAW,EAAE;QAChC,MAAM,KAAK,GAAG,WAAW,GAAG,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,MAAM;QAC5C,WAAW,GAAG,KAAK,CAAC;KACrB;IAED,OAAO;QACL,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC;QAC3B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAC9C,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;KACzB,CAAC;AACJ,CAAC", "sourcesContent": ["// Regexps involved with splitting words in various case formats.\nconst SPLIT_LOWER_UPPER_RE = /([\\p{Ll}\\d])(\\p{Lu})/gu;\nconst SPLIT_UPPER_UPPER_RE = /(\\p{Lu})([\\p{Lu}][\\p{Ll}])/gu;\n\n// Used to iterate over the initial split result and separate numbers.\nconst SPLIT_SEPARATE_NUMBER_RE = /(\\d)\\p{Ll}|(\\p{L})\\d/u;\n\n// Regexp involved with stripping non-word characters from the result.\nconst DEFAULT_STRIP_REGEXP = /[^\\p{L}\\d]+/giu;\n\n// The replacement value for splits.\nconst SPLIT_REPLACE_VALUE = \"$1\\0$2\";\n\n// The default characters to keep after transforming case.\nconst DEFAULT_PREFIX_SUFFIX_CHARACTERS = \"\";\n\n/**\n * Supported locale values. Use `false` to ignore locale.\n * Defaults to `undefined`, which uses the host environment.\n */\nexport type Locale = string[] | string | false | undefined;\n\n/**\n * Options used for converting strings to pascal/camel case.\n */\nexport interface PascalCaseOptions extends Options {\n  mergeAmbiguousCharacters?: boolean;\n}\n\n/**\n * Options used for converting strings to any case.\n */\nexport interface Options {\n  locale?: Locale;\n  split?: (value: string) => string[];\n  /** @deprecated Pass `split: splitSeparateNumbers` instead. */\n  separateNumbers?: boolean;\n  delimiter?: string;\n  prefixCharacters?: string;\n  suffixCharacters?: string;\n}\n\n/**\n * Split any cased input strings into an array of words.\n */\nexport function split(value: string) {\n  let result = value.trim();\n\n  result = result\n    .replace(SPLIT_LOWER_UPPER_RE, SPLIT_REPLACE_VALUE)\n    .replace(SPLIT_UPPER_UPPER_RE, SPLIT_REPLACE_VALUE);\n\n  result = result.replace(DEFAULT_STRIP_REGEXP, \"\\0\");\n\n  let start = 0;\n  let end = result.length;\n\n  // Trim the delimiter from around the output string.\n  while (result.charAt(start) === \"\\0\") start++;\n  if (start === end) return [];\n  while (result.charAt(end - 1) === \"\\0\") end--;\n\n  return result.slice(start, end).split(/\\0/g);\n}\n\n/**\n * Split the input string into an array of words, separating numbers.\n */\nexport function splitSeparateNumbers(value: string) {\n  const words = split(value);\n  for (let i = 0; i < words.length; i++) {\n    const word = words[i];\n    const match = SPLIT_SEPARATE_NUMBER_RE.exec(word);\n    if (match) {\n      const offset = match.index + (match[1] ?? match[2]).length;\n      words.splice(i, 1, word.slice(0, offset), word.slice(offset));\n    }\n  }\n  return words;\n}\n\n/**\n * Convert a string to space separated lower case (`foo bar`).\n */\nexport function noCase(input: string, options?: Options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  return (\n    prefix +\n    words.map(lowerFactory(options?.locale)).join(options?.delimiter ?? \" \") +\n    suffix\n  );\n}\n\n/**\n * Convert a string to camel case (`fooBar`).\n */\nexport function camelCase(input: string, options?: PascalCaseOptions) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  const lower = lowerFactory(options?.locale);\n  const upper = upperFactory(options?.locale);\n  const transform = options?.mergeAmbiguousCharacters\n    ? capitalCaseTransformFactory(lower, upper)\n    : pascalCaseTransformFactory(lower, upper);\n  return (\n    prefix +\n    words\n      .map((word, index) => {\n        if (index === 0) return lower(word);\n        return transform(word, index);\n      })\n      .join(options?.delimiter ?? \"\") +\n    suffix\n  );\n}\n\n/**\n * Convert a string to pascal case (`FooBar`).\n */\nexport function pascalCase(input: string, options?: PascalCaseOptions) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  const lower = lowerFactory(options?.locale);\n  const upper = upperFactory(options?.locale);\n  const transform = options?.mergeAmbiguousCharacters\n    ? capitalCaseTransformFactory(lower, upper)\n    : pascalCaseTransformFactory(lower, upper);\n  return prefix + words.map(transform).join(options?.delimiter ?? \"\") + suffix;\n}\n\n/**\n * Convert a string to pascal snake case (`Foo_Bar`).\n */\nexport function pascalSnakeCase(input: string, options?: Options) {\n  return capitalCase(input, { delimiter: \"_\", ...options });\n}\n\n/**\n * Convert a string to capital case (`Foo Bar`).\n */\nexport function capitalCase(input: string, options?: Options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  const lower = lowerFactory(options?.locale);\n  const upper = upperFactory(options?.locale);\n  return (\n    prefix +\n    words\n      .map(capitalCaseTransformFactory(lower, upper))\n      .join(options?.delimiter ?? \" \") +\n    suffix\n  );\n}\n\n/**\n * Convert a string to constant case (`FOO_BAR`).\n */\nexport function constantCase(input: string, options?: Options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  return (\n    prefix +\n    words.map(upperFactory(options?.locale)).join(options?.delimiter ?? \"_\") +\n    suffix\n  );\n}\n\n/**\n * Convert a string to dot case (`foo.bar`).\n */\nexport function dotCase(input: string, options?: Options) {\n  return noCase(input, { delimiter: \".\", ...options });\n}\n\n/**\n * Convert a string to kebab case (`foo-bar`).\n */\nexport function kebabCase(input: string, options?: Options) {\n  return noCase(input, { delimiter: \"-\", ...options });\n}\n\n/**\n * Convert a string to path case (`foo/bar`).\n */\nexport function pathCase(input: string, options?: Options) {\n  return noCase(input, { delimiter: \"/\", ...options });\n}\n\n/**\n * Convert a string to path case (`Foo bar`).\n */\nexport function sentenceCase(input: string, options?: Options) {\n  const [prefix, words, suffix] = splitPrefixSuffix(input, options);\n  const lower = lowerFactory(options?.locale);\n  const upper = upperFactory(options?.locale);\n  const transform = capitalCaseTransformFactory(lower, upper);\n  return (\n    prefix +\n    words\n      .map((word, index) => {\n        if (index === 0) return transform(word);\n        return lower(word);\n      })\n      .join(options?.delimiter ?? \" \") +\n    suffix\n  );\n}\n\n/**\n * Convert a string to snake case (`foo_bar`).\n */\nexport function snakeCase(input: string, options?: Options) {\n  return noCase(input, { delimiter: \"_\", ...options });\n}\n\n/**\n * Convert a string to header case (`Foo-Bar`).\n */\nexport function trainCase(input: string, options?: Options) {\n  return capitalCase(input, { delimiter: \"-\", ...options });\n}\n\nfunction lowerFactory(locale: Locale): (input: string) => string {\n  return locale === false\n    ? (input: string) => input.toLowerCase()\n    : (input: string) => input.toLocaleLowerCase(locale);\n}\n\nfunction upperFactory(locale: Locale): (input: string) => string {\n  return locale === false\n    ? (input: string) => input.toUpperCase()\n    : (input: string) => input.toLocaleUpperCase(locale);\n}\n\nfunction capitalCaseTransformFactory(\n  lower: (input: string) => string,\n  upper: (input: string) => string,\n) {\n  return (word: string) => `${upper(word[0])}${lower(word.slice(1))}`;\n}\n\nfunction pascalCaseTransformFactory(\n  lower: (input: string) => string,\n  upper: (input: string) => string,\n) {\n  return (word: string, index: number) => {\n    const char0 = word[0];\n    const initial =\n      index > 0 && char0 >= \"0\" && char0 <= \"9\" ? \"_\" + char0 : upper(char0);\n    return initial + lower(word.slice(1));\n  };\n}\n\nfunction splitPrefixSuffix(\n  input: string,\n  options: Options = {},\n): [string, string[], string] {\n  const splitFn =\n    options.split ?? (options.separateNumbers ? splitSeparateNumbers : split);\n  const prefixCharacters =\n    options.prefixCharacters ?? DEFAULT_PREFIX_SUFFIX_CHARACTERS;\n  const suffixCharacters =\n    options.suffixCharacters ?? DEFAULT_PREFIX_SUFFIX_CHARACTERS;\n  let prefixIndex = 0;\n  let suffixIndex = input.length;\n\n  while (prefixIndex < input.length) {\n    const char = input.charAt(prefixIndex);\n    if (!prefixCharacters.includes(char)) break;\n    prefixIndex++;\n  }\n\n  while (suffixIndex > prefixIndex) {\n    const index = suffixIndex - 1;\n    const char = input.charAt(index);\n    if (!suffixCharacters.includes(char)) break;\n    suffixIndex = index;\n  }\n\n  return [\n    input.slice(0, prefixIndex),\n    splitFn(input.slice(prefixIndex, suffixIndex)),\n    input.slice(suffixIndex),\n  ];\n}\n"]}