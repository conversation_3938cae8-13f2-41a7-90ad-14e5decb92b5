## [5.14.12](https://github.com/appium/io.appium.settings/compare/v5.14.11...v5.14.12) (2025-06-10)

### Miscellaneous Chores

* **deps-dev:** bump @types/node from 22.15.31 to 24.0.0 ([#228](https://github.com/appium/io.appium.settings/issues/228)) ([2a4f3bd](https://github.com/appium/io.appium.settings/commit/2a4f3bd445a605ddc1ff266004d7fb6ea6218e11))

## [5.14.11](https://github.com/appium/io.appium.settings/compare/v5.14.10...v5.14.11) (2025-05-29)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.10.0 to 8.10.1 ([#227](https://github.com/appium/io.appium.settings/issues/227)) ([c456a1e](https://github.com/appium/io.appium.settings/commit/c456a1e47cd1c2a0ac409ea8d851ede0a6d7f093))

## [5.14.10](https://github.com/appium/io.appium.settings/compare/v5.14.9...v5.14.10) (2025-05-21)

### Miscellaneous Chores

* **deps-dev:** bump conventional-changelog-conventionalcommits ([#226](https://github.com/appium/io.appium.settings/issues/226)) ([870cce3](https://github.com/appium/io.appium.settings/commit/870cce396f7596d2bd878c58a42eec4242111992))

## [5.14.9](https://github.com/appium/io.appium.settings/compare/v5.14.8...v5.14.9) (2025-05-07)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.9.2 to 8.10.0 ([#225](https://github.com/appium/io.appium.settings/issues/225)) ([11c18ae](https://github.com/appium/io.appium.settings/commit/11c18ae56b119507392905b34a57d8ed1c8ecf80))

## [5.14.8](https://github.com/appium/io.appium.settings/compare/v5.14.7...v5.14.8) (2025-04-22)

### Miscellaneous Chores

* Log exception stacktrace ([e670106](https://github.com/appium/io.appium.settings/commit/e67010632cbd20f79ca2810a71c804cec47b00fc))

## [5.14.7](https://github.com/appium/io.appium.settings/compare/v5.14.6...v5.14.7) (2025-04-22)

### Miscellaneous Chores

* Retry setting locale once to make sure hidden API policies were picked up ([#223](https://github.com/appium/io.appium.settings/issues/223)) ([2dd61fa](https://github.com/appium/io.appium.settings/commit/2dd61fa6ff080a143487a8f32569dc2d687d6327))

## [5.14.6](https://github.com/appium/io.appium.settings/compare/v5.14.5...v5.14.6) (2025-04-22)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.9.1 to 8.9.2 ([#222](https://github.com/appium/io.appium.settings/issues/222)) ([e882561](https://github.com/appium/io.appium.settings/commit/e882561ff5b0ceaf8139dab87fc324465a86715c))

## [5.14.5](https://github.com/appium/io.appium.settings/compare/v5.14.4...v5.14.5) (2025-03-26)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.9.0 to 8.9.1 ([#219](https://github.com/appium/io.appium.settings/issues/219)) ([a163898](https://github.com/appium/io.appium.settings/commit/a163898d3e344f52be591e767935d2104f846b37))

## [5.14.4](https://github.com/appium/io.appium.settings/compare/v5.14.3...v5.14.4) (2025-03-10)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.8.2 to 8.9.0 ([#216](https://github.com/appium/io.appium.settings/issues/216)) ([1dfd752](https://github.com/appium/io.appium.settings/commit/1dfd752781ae5154999599d8759f5421b29ecd25))

## [5.14.3](https://github.com/appium/io.appium.settings/compare/v5.14.2...v5.14.3) (2025-03-10)

### Bug Fixes

* accuracy handling ([#218](https://github.com/appium/io.appium.settings/issues/218)) ([d768b5d](https://github.com/appium/io.appium.settings/commit/d768b5d7e7286f2c3e844ac392c91c567fd4c6d2))

## [5.14.2](https://github.com/appium/io.appium.settings/compare/v5.14.1...v5.14.2) (2025-03-01)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.8.1 to 8.8.2 ([#215](https://github.com/appium/io.appium.settings/issues/215)) ([7a56655](https://github.com/appium/io.appium.settings/commit/7a566552019389174cf225cd4655005e5b66e83f))

## [5.14.1](https://github.com/appium/io.appium.settings/compare/v5.14.0...v5.14.1) (2025-02-26)

### Bug Fixes

* crash while enabling/disabling bluetooth on API level >= 31 ([8fb4a11](https://github.com/appium/io.appium.settings/commit/8fb4a117fab9be81fafdf198087a8d5dca9a3d9a))

## [5.14.0](https://github.com/appium/io.appium.settings/compare/v5.13.0...v5.14.0) (2025-02-24)

### Features

* add bearing and accuracy params into setGeoLocation ([#213](https://github.com/appium/io.appium.settings/issues/213)) ([a684a98](https://github.com/appium/io.appium.settings/commit/a684a98b3d919d7af4e9f5d2aeabdd09561ba0e8))

## [5.13.0](https://github.com/appium/io.appium.settings/compare/v5.12.24...v5.13.0) (2025-02-23)

### Features

* Add accuracy to the location builder ([#212](https://github.com/appium/io.appium.settings/issues/212)) ([360fd3b](https://github.com/appium/io.appium.settings/commit/360fd3b315a4aee16af8cc872cdc23e6b67d55f4))

## [5.12.24](https://github.com/appium/io.appium.settings/compare/v5.12.23...v5.12.24) (2025-02-21)

### Bug Fixes

* set location issue on Android 10 ([#211](https://github.com/appium/io.appium.settings/issues/211)) ([73c09d9](https://github.com/appium/io.appium.settings/commit/73c09d90f4fd8fab0f4cb35160daa18ce959174b))

## [5.12.23](https://github.com/appium/io.appium.settings/compare/v5.12.22...v5.12.23) (2025-02-14)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.8.0 to 8.8.1 ([#209](https://github.com/appium/io.appium.settings/issues/209)) ([5edf693](https://github.com/appium/io.appium.settings/commit/5edf6934d308db422450b1126675218b4aa0dbf7))

## [5.12.22](https://github.com/appium/io.appium.settings/compare/v5.12.21...v5.12.22) (2025-01-16)

### Bug Fixes

* Do not register the broad cast receivers for record action ([#206](https://github.com/appium/io.appium.settings/issues/206)) ([1ab3716](https://github.com/appium/io.appium.settings/commit/1ab37169629fe5807c86e5b965b36f8f5b568c0b))

## [5.12.21](https://github.com/appium/io.appium.settings/compare/v5.12.20...v5.12.21) (2025-01-10)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.7.3 to 8.8.0 ([#204](https://github.com/appium/io.appium.settings/issues/204)) ([0753393](https://github.com/appium/io.appium.settings/commit/0753393d9da4b8f3aa64d127b85da3f89c57f5a5))

## [5.12.20](https://github.com/appium/io.appium.settings/compare/v5.12.19...v5.12.20) (2025-01-06)

### Miscellaneous Chores

* **deps-dev:** bump @appium/eslint-config-appium-ts from 0.3.3 to 1.0.1 ([#203](https://github.com/appium/io.appium.settings/issues/203)) ([33a845e](https://github.com/appium/io.appium.settings/commit/33a845e60a4e869dc9fd382a49d050024165acc4))

## [5.12.19](https://github.com/appium/io.appium.settings/compare/v5.12.18...v5.12.19) (2024-12-03)

### Miscellaneous Chores

* **deps-dev:** bump mocha from 10.8.2 to 11.0.1 ([#200](https://github.com/appium/io.appium.settings/issues/200)) ([05959d8](https://github.com/appium/io.appium.settings/commit/05959d8c17d36e8003ba45b7d3eb6f6b40c571be))

## [5.12.18](https://github.com/appium/io.appium.settings/compare/v5.12.17...v5.12.18) (2024-12-03)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.7.2 to 8.7.3 ([#201](https://github.com/appium/io.appium.settings/issues/201)) ([481c4de](https://github.com/appium/io.appium.settings/commit/481c4defd5b4f5ccf3e94a6e5b98f9b3d0a1f422))

## [5.12.17](https://github.com/appium/io.appium.settings/compare/v5.12.16...v5.12.17) (2024-11-02)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.7.1 to 8.7.2 ([#199](https://github.com/appium/io.appium.settings/issues/199)) ([ffd8234](https://github.com/appium/io.appium.settings/commit/ffd823405e15c2916af3a29afb55881e00084d3b))

## [5.12.16](https://github.com/appium/io.appium.settings/compare/v5.12.15...v5.12.16) (2024-10-15)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.7.0 to 8.7.1 ([#198](https://github.com/appium/io.appium.settings/issues/198)) ([2a8b881](https://github.com/appium/io.appium.settings/commit/2a8b88167fb34313670509613fd185b42e6962e5))

## [5.12.15](https://github.com/appium/io.appium.settings/compare/v5.12.14...v5.12.15) (2024-10-05)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.6.1 to 8.7.0 ([#197](https://github.com/appium/io.appium.settings/issues/197)) ([078d45a](https://github.com/appium/io.appium.settings/commit/078d45af7da68110455b2f16cbbb4ed99edb9aad))

## [5.12.14](https://github.com/appium/io.appium.settings/compare/v5.12.13...v5.12.14) (2024-09-18)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.6.0 to 8.6.1 ([#196](https://github.com/appium/io.appium.settings/issues/196)) ([cdfcbc1](https://github.com/appium/io.appium.settings/commit/cdfcbc1a093c811e1478c171cdd0302b9d5643c3))

## [5.12.13](https://github.com/appium/io.appium.settings/compare/v5.12.12...v5.12.13) (2024-08-31)

### Miscellaneous Chores

* **deps:** bump org.apache.commons:commons-lang3 from 3.16.0 to 3.17.0 ([#195](https://github.com/appium/io.appium.settings/issues/195)) ([417a5e7](https://github.com/appium/io.appium.settings/commit/417a5e7de4e29b5cdaad2b19f4ec133b0c9c851e))

## [5.12.12](https://github.com/appium/io.appium.settings/compare/v5.12.11...v5.12.12) (2024-08-31)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.5.2 to 8.6.0 ([#194](https://github.com/appium/io.appium.settings/issues/194)) ([589a0c6](https://github.com/appium/io.appium.settings/commit/589a0c6d2b762cd721c24686eb47632cf0bb009a))

## [5.12.11](https://github.com/appium/io.appium.settings/compare/v5.12.10...v5.12.11) (2024-08-14)

### Bug Fixes

* Stop media projection when stopping recording ([#193](https://github.com/appium/io.appium.settings/issues/193)) ([aeeb032](https://github.com/appium/io.appium.settings/commit/aeeb0324cc3dd641a75f6bdf84056aec7bf7d76f))

## [5.12.10](https://github.com/appium/io.appium.settings/compare/v5.12.9...v5.12.10) (2024-08-09)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.5.1 to 8.5.2 ([#192](https://github.com/appium/io.appium.settings/issues/192)) ([58738d1](https://github.com/appium/io.appium.settings/commit/58738d1b61b712525adb34239cf25bf97e4f240a))

## [5.12.9](https://github.com/appium/io.appium.settings/compare/v5.12.8...v5.12.9) (2024-08-08)

### Miscellaneous Chores

* **deps:** bump org.apache.commons:commons-lang3 from 3.15.0 to 3.16.0 ([#191](https://github.com/appium/io.appium.settings/issues/191)) ([5564add](https://github.com/appium/io.appium.settings/commit/5564adde7d07bf834fd736360d498e50577c5c34))

## [5.12.8](https://github.com/appium/io.appium.settings/compare/v5.12.7...v5.12.8) (2024-07-29)

### Miscellaneous Chores

* **deps-dev:** bump @types/node from 20.14.13 to 22.0.0 ([#190](https://github.com/appium/io.appium.settings/issues/190)) ([f0d8513](https://github.com/appium/io.appium.settings/commit/f0d8513ddba335cc7431090a9370f5335e2bdc50))

## [5.12.7](https://github.com/appium/io.appium.settings/compare/v5.12.6...v5.12.7) (2024-07-18)

### Miscellaneous Chores

* **deps:** bump org.apache.commons:commons-lang3 from 3.14.0 to 3.15.0 ([#189](https://github.com/appium/io.appium.settings/issues/189)) ([e48c667](https://github.com/appium/io.appium.settings/commit/e48c667aa6a9809d28493d2c0650a433819a821b))

## [5.12.6](https://github.com/appium/io.appium.settings/compare/v5.12.5...v5.12.6) (2024-07-12)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.5.0 to 8.5.1 ([#188](https://github.com/appium/io.appium.settings/issues/188)) ([e1fbe0c](https://github.com/appium/io.appium.settings/commit/e1fbe0c30ebec9d19ea78f0852046106a27b6208))

## [5.12.5](https://github.com/appium/io.appium.settings/compare/v5.12.4...v5.12.5) (2024-07-09)

### Miscellaneous Chores

* Remove extra import ([11d4ae3](https://github.com/appium/io.appium.settings/commit/11d4ae3b5f74bf9d74e6ec8a08e157fed85ad86b))

## [5.12.4](https://github.com/appium/io.appium.settings/compare/v5.12.3...v5.12.4) (2024-06-20)

### Miscellaneous Chores

* Bump chai and chai-as-promised ([#187](https://github.com/appium/io.appium.settings/issues/187)) ([627d88b](https://github.com/appium/io.appium.settings/commit/627d88b58d55294078a200e97fe1df7beab76c0c))

## [5.12.3](https://github.com/appium/io.appium.settings/compare/v5.12.2...v5.12.3) (2024-06-14)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.4.2 to 8.5.0 ([#185](https://github.com/appium/io.appium.settings/issues/185)) ([eee1e3d](https://github.com/appium/io.appium.settings/commit/eee1e3d78ec5f494d44de6d11619cb237e7c9b24))

## [5.12.2](https://github.com/appium/io.appium.settings/compare/v5.12.1...v5.12.2) (2024-06-14)

### Bug Fixes

* log argument ([#186](https://github.com/appium/io.appium.settings/issues/186)) ([d634033](https://github.com/appium/io.appium.settings/commit/d6340336ad2dcfdc223229661976a038f062a401))

## [5.12.1](https://github.com/appium/io.appium.settings/compare/v5.12.0...v5.12.1) (2024-06-11)

### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.4.1 to 8.4.2 ([#184](https://github.com/appium/io.appium.settings/issues/184)) ([fdfd940](https://github.com/appium/io.appium.settings/commit/fdfd94088b736ba295300c4df57f4589c9bdb399))

## [5.12.0](https://github.com/appium/io.appium.settings/compare/v5.11.0...v5.12.0) (2024-06-09)

### Features

* bump compileSdk and targetSdkVersion to 32 ([#181](https://github.com/appium/io.appium.settings/issues/181)) ([a69c004](https://github.com/appium/io.appium.settings/commit/a69c0041a5edabcea44ed149eec4ff5ca06f2319))

## [5.11.0](https://github.com/appium/io.appium.settings/compare/v5.10.2...v5.11.0) (2024-06-07)

### Features

* Replace the deprecated npm log with @appium/logger ([#179](https://github.com/appium/io.appium.settings/issues/179)) ([715a83d](https://github.com/appium/io.appium.settings/commit/715a83d9d42cd87ef6c1db3e97bce0e110c522b2))

### Bug Fixes

* Logger import ([edc42d9](https://github.com/appium/io.appium.settings/commit/edc42d9a1a2dc82be7e74b2fbf30ac04f174aa47))

## [5.10.2](https://github.com/appium/io.appium.settings/compare/v5.10.1...v5.10.2) (2024-06-04)

### Miscellaneous Chores

* **deps-dev:** bump semantic-release from 23.1.1 to 24.0.0 and conventional-changelog-conventionalcommits to 8.0.0 ([#178](https://github.com/appium/io.appium.settings/issues/178)) ([0b354d0](https://github.com/appium/io.appium.settings/commit/0b354d014c32ac7b86853c835161ade3b49d9a23))

## [5.10.1](https://github.com/appium/io.appium.settings/compare/v5.10.0...v5.10.1) (2024-05-31)


### Miscellaneous Chores

* **deps:** bump com.google.android.gms:play-services-location ([#177](https://github.com/appium/io.appium.settings/issues/177)) ([f751709](https://github.com/appium/io.appium.settings/commit/f751709baf1663df7302a66a59d2b999254fb858))


### Code Refactoring

* LocationManager provider ([#174](https://github.com/appium/io.appium.settings/issues/174)) ([da7e36a](https://github.com/appium/io.appium.settings/commit/da7e36a1a6f3ad51d606c67203cd17e6f73098a2))

## [5.10.0](https://github.com/appium/io.appium.settings/compare/v5.9.2...v5.10.0) (2024-05-27)


### Features

* use adb settings method to set animation for newer than api level 26 ([#176](https://github.com/appium/io.appium.settings/issues/176)) ([d0e5d4f](https://github.com/appium/io.appium.settings/commit/d0e5d4f3b7441d98402828a65717b2fddc6fa3e9))

## [5.9.2](https://github.com/appium/io.appium.settings/compare/v5.9.1...v5.9.2) (2024-05-18)


### Miscellaneous Chores

* Enable BuildConfig generation ([#173](https://github.com/appium/io.appium.settings/issues/173)) ([af4c160](https://github.com/appium/io.appium.settings/commit/af4c16038463ad97a4cbe80bf89a926b9139b718))

## [5.9.1](https://github.com/appium/io.appium.settings/compare/v5.9.0...v5.9.1) (2024-05-16)


### Miscellaneous Chores

* Update dev dependencies ([89d759d](https://github.com/appium/io.appium.settings/commit/89d759daed18e33abde90a2e56006139074f369d))

## [5.9.0](https://github.com/appium/io.appium.settings/compare/v5.8.2...v5.9.0) (2024-05-16)


### Features

* Add an API to list locales supported by the device ([#171](https://github.com/appium/io.appium.settings/issues/171)) ([6d00491](https://github.com/appium/io.appium.settings/commit/6d0049129ed21e017f4df25448c5f10df7d8774b))

## [5.8.2](https://github.com/appium/io.appium.settings/compare/v5.8.1...v5.8.2) (2024-05-16)


### Miscellaneous Chores

* **deps-dev:** bump sinon from 17.0.2 to 18.0.0 ([#170](https://github.com/appium/io.appium.settings/issues/170)) ([bccbe16](https://github.com/appium/io.appium.settings/commit/bccbe161df1654d4fc9ba9a3d6ce1d03d46d0b51))

## [5.8.1](https://github.com/appium/io.appium.settings/compare/v5.8.0...v5.8.1) (2024-05-08)


### Miscellaneous Chores

* Update Gradle version ([#166](https://github.com/appium/io.appium.settings/issues/166)) ([4a5ab38](https://github.com/appium/io.appium.settings/commit/4a5ab381fc6b757b5419fb8ded353cc893a157c5))

## [5.8.0](https://github.com/appium/io.appium.settings/compare/v5.7.9...v5.8.0) (2024-05-08)


### Features

* Add helpers for dealing with bluetooth ([#165](https://github.com/appium/io.appium.settings/issues/165)) ([d54ec63](https://github.com/appium/io.appium.settings/commit/d54ec634831859c418e9a4c7d0c6dd641936321d))


### Bug Fixes

* Revert "chore(deps-dev): bump conventional-changelog-conventionalcommits ([#164](https://github.com/appium/io.appium.settings/issues/164))" ([#167](https://github.com/appium/io.appium.settings/issues/167)) ([ddebbc4](https://github.com/appium/io.appium.settings/commit/ddebbc4ea7f4bb7a46e74e33f96fac5889ba4d46))


### Miscellaneous Chores

* **deps-dev:** bump conventional-changelog-conventionalcommits ([#164](https://github.com/appium/io.appium.settings/issues/164)) ([b9db694](https://github.com/appium/io.appium.settings/commit/b9db6942d4a90c44c63efb4b74a0f5ffdae86208))

## [5.7.9](https://github.com/appium/io.appium.settings/compare/v5.7.8...v5.7.9) (2024-04-09)


### Miscellaneous Chores

* Remove extra imports ([a71ba56](https://github.com/appium/io.appium.settings/commit/a71ba56a93776e7aa92c8850a0e306d5626b5a71))

## [5.7.8](https://github.com/appium/io.appium.settings/compare/v5.7.7...v5.7.8) (2024-04-09)


### Miscellaneous Chores

* **deps-dev:** bump @typescript-eslint/parser from 6.21.0 to 7.6.0 ([#161](https://github.com/appium/io.appium.settings/issues/161)) ([05c9fe9](https://github.com/appium/io.appium.settings/commit/05c9fe91e6fe9c77337c6ffc4b4a749b4349873b))
* **deps:** bump com.google.android.gms:play-services-location ([#155](https://github.com/appium/io.appium.settings/issues/155)) ([c549af4](https://github.com/appium/io.appium.settings/commit/c549af4224d250abca07a79710951930c3399626))

## [5.7.7](https://github.com/appium/io.appium.settings/compare/v5.7.6...v5.7.7) (2024-03-20)


### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.3.0 to 8.3.1 ([#157](https://github.com/appium/io.appium.settings/issues/157)) ([178ec79](https://github.com/appium/io.appium.settings/commit/178ec79ad88989302e4e09176381628651f05de9))

## [5.7.6](https://github.com/appium/io.appium.settings/compare/v5.7.5...v5.7.6) (2024-03-02)


### Miscellaneous Chores

* update gradle env ([#153](https://github.com/appium/io.appium.settings/issues/153)) ([6835b93](https://github.com/appium/io.appium.settings/commit/6835b9304c77fd239593860796a75a6c0316771c))

## [5.7.5](https://github.com/appium/io.appium.settings/compare/v5.7.4...v5.7.5) (2024-01-25)


### Miscellaneous Chores

* **deps:** bump com.android.tools.build:gradle from 8.2.1 to 8.2.2 ([#145](https://github.com/appium/io.appium.settings/issues/145)) ([96250ed](https://github.com/appium/io.appium.settings/commit/96250edf1b3a5e265fd292c9d3a127408a07ec56))

## [5.7.4](https://github.com/appium/io.appium.settings/compare/v5.7.3...v5.7.4) (2024-01-24)


### Miscellaneous Chores

* **deps:** bump com.google.android.gms:play-services-location ([#144](https://github.com/appium/io.appium.settings/issues/144)) ([ef7f5dc](https://github.com/appium/io.appium.settings/commit/ef7f5dc3f61729af47537253e56d4132c51873a4))

## [5.7.3](https://github.com/appium/io.appium.settings/compare/v5.7.2...v5.7.3) (2024-01-16)


### Miscellaneous Chores

* **deps-dev:** bump semantic-release from 22.0.12 to 23.0.0 ([#143](https://github.com/appium/io.appium.settings/issues/143)) ([a1d155d](https://github.com/appium/io.appium.settings/commit/a1d155db89e892febb0c02811668aa433bacde2c))

## [5.7.2](https://github.com/appium/io.appium.settings/compare/v5.7.1...v5.7.2) (2024-01-13)


### Miscellaneous Chores

* Bump appium-adb ([#142](https://github.com/appium/io.appium.settings/issues/142)) ([adb209e](https://github.com/appium/io.appium.settings/commit/adb209e3d25a4f996fa52d92894f08d1dda35a9b))

## [5.7.1](https://github.com/appium/io.appium.settings/compare/v5.7.0...v5.7.1) (2024-01-11)


### Bug Fixes

* Update index path ([a8ff340](https://github.com/appium/io.appium.settings/commit/a8ff340fce213de13341ee9132044be187c19374))

## [5.7.0](https://github.com/appium/io.appium.settings/compare/v5.6.0...v5.7.0) (2024-01-11)


### Features

* Make the index typed ([#140](https://github.com/appium/io.appium.settings/issues/140)) ([7020af0](https://github.com/appium/io.appium.settings/commit/7020af0c0ec542b2271b5e4685ff9c63a5faa4c2))

## [5.6.0](https://github.com/appium/io.appium.settings/compare/v5.5.0...v5.6.0) (2024-01-10)


### Features

* Add media-projection-based video recorder ([#138](https://github.com/appium/io.appium.settings/issues/138)) ([ba357e8](https://github.com/appium/io.appium.settings/commit/ba357e8541e2f86e9db902ace4850fd04fb3ec57))

## [5.5.0](https://github.com/appium/io.appium.settings/compare/v5.4.2...v5.5.0) (2024-01-10)


### Features

* Add a helper to adjust notifications permissions ([#139](https://github.com/appium/io.appium.settings/issues/139)) ([e0b88e6](https://github.com/appium/io.appium.settings/commit/e0b88e6f4c3291c5932e9773aa2594280efe80ba))
* Update method to set device locale ([#136](https://github.com/appium/io.appium.settings/issues/136)) ([329c29f](https://github.com/appium/io.appium.settings/commit/329c29f31fcb1774a569820b06bf26b9462df6cd))

## [5.4.2](https://github.com/appium/io.appium.settings/compare/v5.4.1...v5.4.2) (2024-01-10)


### Miscellaneous Chores

* Add more constants to export ([#137](https://github.com/appium/io.appium.settings/issues/137)) ([1a27223](https://github.com/appium/io.appium.settings/commit/1a27223718db9f7c57fc8cf30def2f4d10321efd))

## [5.4.1](https://github.com/appium/io.appium.settings/compare/v5.4.0...v5.4.1) (2024-01-10)


### Bug Fixes

* Update process existence check ([#133](https://github.com/appium/io.appium.settings/issues/133)) ([ea1f8e0](https://github.com/appium/io.appium.settings/commit/ea1f8e0f1dca11adbf4c54052945569a90625bf6))

## [5.4.0](https://github.com/appium/io.appium.settings/compare/v5.3.1...v5.4.0) (2024-01-09)


### Features

* Add javascript wrappers over Settings App APIs ([#131](https://github.com/appium/io.appium.settings/issues/131)) ([bd56d5e](https://github.com/appium/io.appium.settings/commit/bd56d5ed958f243bb32c22197cf9526bfc6428f7))


### Bug Fixes

* Add missing packages ([#132](https://github.com/appium/io.appium.settings/issues/132)) ([7d3015a](https://github.com/appium/io.appium.settings/commit/7d3015a45c44e9cf7edd0ce2d918561571879ade))


### Miscellaneous Chores

* **deps:** bump org.apache.commons:commons-lang3 from 3.13.0 to 3.14.0 ([#130](https://github.com/appium/io.appium.settings/issues/130)) ([ae96723](https://github.com/appium/io.appium.settings/commit/ae96723d82f5f2436294bb74db0d4184fafe5c85))

## [5.3.1](https://github.com/appium/io.appium.settings/compare/v5.3.0...v5.3.1) (2024-01-06)


### Miscellaneous Chores

* Bump Gradle version ([#129](https://github.com/appium/io.appium.settings/issues/129)) ([865c90f](https://github.com/appium/io.appium.settings/commit/865c90f65c0b5dfb2ff5dc89f0cc2c4b1bc9915d))

## [5.3.0](https://github.com/appium/io.appium.settings/compare/v5.2.0...v5.3.0) (2024-01-06)


### Features

* use play services location 21 ([#128](https://github.com/appium/io.appium.settings/issues/128)) ([acc0d55](https://github.com/appium/io.appium.settings/commit/acc0d552882a6c4f2f22359dd24e90b8aac3700c))

## [5.2.0](https://github.com/appium/io.appium.settings/compare/v5.1.3...v5.2.0) (2023-10-29)


### Features

* Add EmptyIME ([#126](https://github.com/appium/io.appium.settings/issues/126)) ([01d475d](https://github.com/appium/io.appium.settings/commit/01d475d5db16cf0a48412c24fae5edf0852f7be8))

## [5.1.3](https://github.com/appium/io.appium.settings/compare/v5.1.2...v5.1.3) (2023-10-28)


### Miscellaneous Chores

* **deps-dev:** bump semantic-release from 21.1.2 to 22.0.5 ([#125](https://github.com/appium/io.appium.settings/issues/125)) ([e788a3e](https://github.com/appium/io.appium.settings/commit/e788a3eb8610eae389dc26f4b9834a46b00f5b6a))

## [5.1.2](https://github.com/appium/io.appium.settings/compare/v5.1.1...v5.1.2) (2023-08-28)


### Miscellaneous Chores

* **deps-dev:** bump conventional-changelog-conventionalcommits ([#121](https://github.com/appium/io.appium.settings/issues/121)) ([db3f343](https://github.com/appium/io.appium.settings/commit/db3f343a842522eea4a3dec0b976d0505e58fb75))

## [5.1.1](https://github.com/appium/io.appium.settings/compare/v5.1.0...v5.1.1) (2023-08-25)


### Miscellaneous Chores

* **deps-dev:** bump semantic-release from 20.1.3 to 21.1.0 ([#120](https://github.com/appium/io.appium.settings/issues/120)) ([07ae699](https://github.com/appium/io.appium.settings/commit/07ae699d89d2970ac9b33c70c000839a0b43e556))

## [5.1.0](https://github.com/appium/io.appium.settings/compare/v5.0.5...v5.1.0) (2023-07-27)


### Features

* add option to ignore locale verification ([#118](https://github.com/appium/io.appium.settings/issues/118)) ([7b751a6](https://github.com/appium/io.appium.settings/commit/7b751a60f76baacdb5d50cab0ecc5df7b758eca9))

## [5.0.5](https://github.com/appium/io.appium.settings/compare/v5.0.4...v5.0.5) (2023-06-23)


### Bug Fixes

* Prevent Appium IME from sending extraneous onKeyUp events ([#115](https://github.com/appium/io.appium.settings/issues/115)) ([71b8428](https://github.com/appium/io.appium.settings/commit/71b8428903775cea3ca4f5e2c8dd9c4e987937d6))

## [5.0.4](https://github.com/appium/io.appium.settings/compare/v5.0.3...v5.0.4) (2023-06-07)


### Miscellaneous Chores

* **deps-dev:** bump conventional-changelog-conventionalcommits ([#113](https://github.com/appium/io.appium.settings/issues/113)) ([1362a5b](https://github.com/appium/io.appium.settings/commit/1362a5b24a3a416dfa616b13e1f5278e7521e83b))

## [5.0.3](https://github.com/appium/io.appium.settings/compare/v5.0.2...v5.0.3) (2023-01-17)


### Miscellaneous Chores

* **deps-dev:** bump semantic-release from 19.0.5 to 20.0.2 ([#104](https://github.com/appium/io.appium.settings/issues/104)) ([516e785](https://github.com/appium/io.appium.settings/commit/516e785099ba95128379898829b48f4500f5eb01))

## [5.0.2](https://github.com/appium/io.appium.settings/compare/v5.0.1...v5.0.2) (2023-01-12)


### Miscellaneous Chores

* Move the namespace definition from the manifest to build.gradle ([#105](https://github.com/appium/io.appium.settings/issues/105)) ([9a1699d](https://github.com/appium/io.appium.settings/commit/9a1699d09dafcbefd7130ca45fc451f19f092e5e))

## [5.0.1](https://github.com/appium/io.appium.settings/compare/v5.0.0...v5.0.1) (2023-01-09)


### Reverts

* Revert "chore(deps-dev): bump semantic-release from 19.0.5 to 20.0.2 (#102)" (#103) ([5e73672](https://github.com/appium/io.appium.settings/commit/5e73672bf3964be5dc2a36813e073676925c94d4)), closes [#102](https://github.com/appium/io.appium.settings/issues/102) [#103](https://github.com/appium/io.appium.settings/issues/103)


### Miscellaneous Chores

* **deps-dev:** bump semantic-release from 19.0.5 to 20.0.2 ([#102](https://github.com/appium/io.appium.settings/issues/102)) ([36e598e](https://github.com/appium/io.appium.settings/commit/36e598e105ba05650acb367300eba721f1b41f94))

## [5.0.0](https://github.com/appium/io.appium.settings/compare/v4.2.4...v5.0.0) (2022-12-23)


### ⚠ BREAKING CHANGES

* Removed the obsolete Unlocker activity. This hack in this activity was only properly working for Android below API 21
* Bumped the minimum required Android version to 21

### Code Refactoring

* Remove the obsolete Unlocker activity ([#101](https://github.com/appium/io.appium.settings/issues/101)) ([a4d4db9](https://github.com/appium/io.appium.settings/commit/a4d4db9b92cd760588cee87860d2bd3deda0748c))

## [4.2.4](https://github.com/appium/io.appium.settings/compare/v4.2.3...v4.2.4) (2022-12-01)


### Miscellaneous Chores

* update releaserc ([#100](https://github.com/appium/io.appium.settings/issues/100)) ([4ce65e3](https://github.com/appium/io.appium.settings/commit/4ce65e3b8aacd88ad9be208e562be4d03e9160ec))

## [4.2.3](https://github.com/appium/io.appium.settings/compare/v4.2.2...v4.2.3) (2022-11-05)
