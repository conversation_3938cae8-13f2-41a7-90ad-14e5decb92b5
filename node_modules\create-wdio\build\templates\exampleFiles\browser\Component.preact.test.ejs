<%
const harnessImport = answers.installTestingLibrary
    ? `import { h } from 'preact'\nimport { render, screen, fireEvent } from '@testing-library/preact'`
    : `import { h, render } from 'preact'\nimport htm from 'https://esm.sh/htm'`
const renderCommand = answers.installTestingLibrary
    ? `render(<ExampleComponent />)`
    : "render(html`<${ExampleComponent} />`, container)"
%>
import { expect, $ } from '@wdio/globals'
<%- harnessImport %>
<% if (answers.installTestingLibrary) { %>
import * as matchers from '@testing-library/jest-dom/matchers'
expect.extend(matchers)
<% } else { %>
// Initialize htm with Preact
const html = htm.bind(h);
<% } %>
import ExampleComponent from './Component'

describe('Preact Component Tests', () => {
    <% if (answers.installTestingLibrary) { %>
    it('should test component with Testing Library', async () => {
        render(<ExampleComponent />)
        const component = screen.getByText(/count is 0/i)
        expect(component).toBeInTheDocument()

        await fireEvent.click(component)
        await fireEvent.click(component)

        expect(screen.getByText(/count is 2/i)).toBeInTheDocument()
    })
    <% } else { %>
    let container<%- answers.isUsingTypeScript ? `: Element` : '' %>

    beforeEach(() => {
        container = document.createElement('div')
        document.body.appendChild(container)
    })

    afterEach(() => {
        container?.remove()
    })
    <% } %>

    it('should test component with WebdriverIO', async () => {
        <%- renderCommand %>

        const component = await $('button*=count is')
        await expect(component).toBePresent()
        await expect(component).toHaveText('count is 0')

        await component.click()
        await component.click()

        await expect(component).toHaveText('count is 2')<%-
        answers.includeVisualTesting ? `
        await expect(component).toMatchElementSnapshot('counterButton')` : '' %>
    })
})
