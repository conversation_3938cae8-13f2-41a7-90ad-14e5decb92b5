"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateDriverLogPrefix = exports.BIDI_COMMANDS = exports.DEFAULT_WS_PATHNAME_PREFIX = exports.promoteAppiumOptionsForObject = exports.promoteAppiumOptions = exports.validateCaps = exports.isStandardCap = exports.processCapabilities = exports.STANDARD_CAPS = exports.PREFIXED_APPIUM_OPTS_CAP = exports.statusCodes = exports.getSummaryByCode = exports.WebDriverProxy = exports.JWProxy = exports.normalizeBasePath = exports.server = exports.STATIC_DIR = exports.errorFromCode = exports.W3C_ELEMENT_KEY = exports.PROTOCOLS = exports.DEFAULT_BASE_PATH = exports.MAX_LOG_BODY_LENGTH = exports.BaseDriver = exports.DeviceSettings = exports.DriverCore = exports.ExtensionCore = void 0;
const bluebird_1 = __importDefault(require("bluebird"));
try {
    bluebird_1.default.config({
        cancellation: true,
    });
}
catch {
    // sometimes during testing this somehow gets required twice and results in an error about
    // cancellation not being able to be enabled after promise has been configured
}
// BaseDriver exports
var extension_core_1 = require("./basedriver/extension-core");
Object.defineProperty(exports, "ExtensionCore", { enumerable: true, get: function () { return extension_core_1.ExtensionCore; } });
const driver_1 = require("./basedriver/driver");
Object.defineProperty(exports, "BaseDriver", { enumerable: true, get: function () { return driver_1.BaseDriver; } });
var core_1 = require("./basedriver/core");
Object.defineProperty(exports, "DriverCore", { enumerable: true, get: function () { return core_1.DriverCore; } });
var device_settings_1 = require("./basedriver/device-settings");
Object.defineProperty(exports, "DeviceSettings", { enumerable: true, get: function () { return device_settings_1.DeviceSettings; } });
exports.default = driver_1.BaseDriver;
var constants_1 = require("./constants");
Object.defineProperty(exports, "MAX_LOG_BODY_LENGTH", { enumerable: true, get: function () { return constants_1.MAX_LOG_BODY_LENGTH; } });
Object.defineProperty(exports, "DEFAULT_BASE_PATH", { enumerable: true, get: function () { return constants_1.DEFAULT_BASE_PATH; } });
Object.defineProperty(exports, "PROTOCOLS", { enumerable: true, get: function () { return constants_1.PROTOCOLS; } });
Object.defineProperty(exports, "W3C_ELEMENT_KEY", { enumerable: true, get: function () { return constants_1.W3C_ELEMENT_KEY; } });
// MJSONWP exports
__exportStar(require("./protocol"), exports);
var protocol_1 = require("./protocol");
Object.defineProperty(exports, "errorFromCode", { enumerable: true, get: function () { return protocol_1.errorFromMJSONWPStatusCode; } });
// Express exports
var static_1 = require("./express/static");
Object.defineProperty(exports, "STATIC_DIR", { enumerable: true, get: function () { return static_1.STATIC_DIR; } });
var server_1 = require("./express/server");
Object.defineProperty(exports, "server", { enumerable: true, get: function () { return server_1.server; } });
Object.defineProperty(exports, "normalizeBasePath", { enumerable: true, get: function () { return server_1.normalizeBasePath; } });
// jsonwp-proxy exports
/** @deprecated The JWProxy export is deprecated. Please use WebDriverProxy instead */
var proxy_1 = require("./jsonwp-proxy/proxy");
Object.defineProperty(exports, "JWProxy", { enumerable: true, get: function () { return proxy_1.JWProxy; } });
var proxy_2 = require("./jsonwp-proxy/proxy");
Object.defineProperty(exports, "WebDriverProxy", { enumerable: true, get: function () { return proxy_2.JWProxy; } });
// jsonwp-status exports
var status_1 = require("./jsonwp-status/status");
Object.defineProperty(exports, "getSummaryByCode", { enumerable: true, get: function () { return status_1.getSummaryByCode; } });
Object.defineProperty(exports, "statusCodes", { enumerable: true, get: function () { return status_1.codes; } });
// W3C capabilities parser
var capabilities_1 = require("./basedriver/capabilities");
Object.defineProperty(exports, "PREFIXED_APPIUM_OPTS_CAP", { enumerable: true, get: function () { return capabilities_1.PREFIXED_APPIUM_OPTS_CAP; } });
Object.defineProperty(exports, "STANDARD_CAPS", { enumerable: true, get: function () { return capabilities_1.STANDARD_CAPS; } });
Object.defineProperty(exports, "processCapabilities", { enumerable: true, get: function () { return capabilities_1.processCapabilities; } });
Object.defineProperty(exports, "isStandardCap", { enumerable: true, get: function () { return capabilities_1.isStandardCap; } });
Object.defineProperty(exports, "validateCaps", { enumerable: true, get: function () { return capabilities_1.validateCaps; } });
Object.defineProperty(exports, "promoteAppiumOptions", { enumerable: true, get: function () { return capabilities_1.promoteAppiumOptions; } });
Object.defineProperty(exports, "promoteAppiumOptionsForObject", { enumerable: true, get: function () { return capabilities_1.promoteAppiumOptionsForObject; } });
// Web socket helpers
var websocket_1 = require("./express/websocket");
Object.defineProperty(exports, "DEFAULT_WS_PATHNAME_PREFIX", { enumerable: true, get: function () { return websocket_1.DEFAULT_WS_PATHNAME_PREFIX; } });
// BiDi exports
var bidi_commands_1 = require("./protocol/bidi-commands");
Object.defineProperty(exports, "BIDI_COMMANDS", { enumerable: true, get: function () { return bidi_commands_1.BIDI_COMMANDS; } });
var helpers_1 = require("./basedriver/helpers");
Object.defineProperty(exports, "generateDriverLogPrefix", { enumerable: true, get: function () { return helpers_1.generateDriverLogPrefix; } });
/**
 * @typedef {import('./express/server').ServerOpts} ServerOpts
 */
//# sourceMappingURL=index.js.map