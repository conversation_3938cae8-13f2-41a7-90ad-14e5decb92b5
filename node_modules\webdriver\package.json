{"name": "webdriver", "version": "9.17.0", "description": "A Node.js bindings implementation for the W3C WebDriver and Mobile JSONWire Protocol", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/webdriverio/webdriverio/tree/main/packages/webdriver", "license": "MIT", "type": "module", "main": "./build/index.cjs", "module": "./build/index.js", "exports": {".": {"types": "./build/index.d.ts", "browserSource": "./src/browser.js", "browser": "./build/index.js", "importSource": "./src/node.ts", "import": "./build/node.js", "requireSource": "./src/index.cts", "require": "./build/index.cjs"}}, "types": "./build/index.d.ts", "typeScriptVersion": "3.8.3", "engines": {"node": ">=18.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/webdriverio/webdriverio.git", "directory": "packages/webdriver"}, "keywords": ["webdriver"], "bugs": {"url": "https://github.com/webdriverio/webdriverio/issues"}, "dependencies": {"@types/node": "^20.1.0", "@types/ws": "^8.5.3", "@wdio/config": "9.17.0", "@wdio/logger": "9.16.2", "@wdio/protocols": "9.16.2", "@wdio/types": "9.16.2", "@wdio/utils": "9.17.0", "deepmerge-ts": "^7.0.3", "https-proxy-agent": "^7.0.6", "undici": "^6.20.1", "ws": "^8.8.0"}, "gitHead": "796df04f0f5c744d5ef4bafd3759995fa4829d6f"}