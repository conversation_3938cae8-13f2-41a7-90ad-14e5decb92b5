"use strict";
/**
 * Constants used across various modules in this package
 * @module
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MESSAGE_PYTHON_MISSING = exports.PIP_ENV_VARS = exports.DEFAULT_SITE_DIR = exports.LogLevelMap = exports.DEFAULT_SERVE_HOST = exports.DEFAULT_SERVE_PORT = exports.DEFAULT_DEPLOY_REMOTE = exports.DEFAULT_DEPLOY_BRANCH = exports.DEFAULT_DEPLOY_ALIAS_TYPE = exports.REQUIREMENTS_TXT_PATH = exports.DOCUTILS_PKG = exports.PKG_ROOT_DIR = exports.DEFAULT_LOG_LEVEL = exports.NAME_THEME = exports.NAME_ERR_ENOENT = exports.NAME_TYPESCRIPT = exports.NAME_NPM = exports.NAME_PIP = exports.NAME_MIKE = exports.NAME_MKDOCS = exports.NAME_SCHEMA = exports.NAME_REQUIREMENTS_TXT = exports.NAME_PACKAGE_JSON = exports.NAME_PYTHON = exports.NAME_TSCONFIG_JSON = exports.NAME_MKDOCS_YML = exports.NAME_BIN = void 0;
const { LogLevels } = require('consola');
const node_fs_1 = require("node:fs");
const support_1 = require("@appium/support");
const node_path_1 = __importDefault(require("node:path"));
/**
 * CLI executable name
 */
exports.NAME_BIN = 'appium-docs';
/**
 * Expected name of the `mkdocs.yml` config file
 */
exports.NAME_MKDOCS_YML = 'mkdocs.yml';
/**
 * Default name of the `tsconfig.json` config file
 */
exports.NAME_TSCONFIG_JSON = 'tsconfig.json';
/**
 * `python` executable
 */
exports.NAME_PYTHON = 'python';
/**
 * It's `package.json`!
 */
exports.NAME_PACKAGE_JSON = 'package.json';
/**
 * Name of the `requirements.txt` file for `pip`
 */
exports.NAME_REQUIREMENTS_TXT = 'requirements.txt';
/**
 * Name of the `$schema` property which can be present in JSON files; it may need to be removed to
 * avoid warnings/errors by 3p libs
 */
exports.NAME_SCHEMA = '$schema';
/**
 * Name of the `mkdocs` executable
 */
exports.NAME_MKDOCS = 'mkdocs';
/**
 * Name of the `mike` executable
 */
exports.NAME_MIKE = 'mike';
/**
 * Name of the `pip` module.
 *
 * @remarks We don't execute the `pip` executable; but rather use `python -m pip` since that seems
 * to work better ... on my computer.
 */
exports.NAME_PIP = 'pip';
/**
 * Name of the `npm` executable, which we use to check for
 */
exports.NAME_NPM = 'npm';
/**
 * The name of the `typescript` package (not the `tsc` executable)
 */
exports.NAME_TYPESCRIPT = 'typescript';
/**
 * Code for a "file not found" error
 */
exports.NAME_ERR_ENOENT = 'ENOENT';
/**
 * Name of the default theme
 */
exports.NAME_THEME = 'material';
/**
 * Default log level
 */
exports.DEFAULT_LOG_LEVEL = 'info';
/**
 * Blocking I/O
 */
exports.PKG_ROOT_DIR = support_1.fs.findRoot(__dirname);
/**
 * Blocking I/O
 */
exports.DOCUTILS_PKG = JSON.parse((0, node_fs_1.readFileSync)(node_path_1.default.join(exports.PKG_ROOT_DIR, exports.NAME_PACKAGE_JSON), 'utf8'));
/**
 * Path to the `requirements.txt` file (in this package)
 */
exports.REQUIREMENTS_TXT_PATH = node_path_1.default.join(exports.PKG_ROOT_DIR, exports.NAME_REQUIREMENTS_TXT);
/**
 * The default alias creation strategy to pass to `mike` when deploying
 * (`symlink`, `redirect` or `copy`)
 */
exports.DEFAULT_DEPLOY_ALIAS_TYPE = 'symlink';
/**
 * The default branch to deploy to
 */
exports.DEFAULT_DEPLOY_BRANCH = 'gh-pages';
/**
 * The default remote to push the deployed branch to
 */
exports.DEFAULT_DEPLOY_REMOTE = 'origin';
/**
 * The default port for serving docs
 */
exports.DEFAULT_SERVE_PORT = 8000;
/**
 * The default host for serving docs
 */
exports.DEFAULT_SERVE_HOST = 'localhost';
/**
 * Mapping of `@appium/docutils`' log levels to `consola` log levels
 */
exports.LogLevelMap = {
    silent: LogLevels.silent,
    error: LogLevels.error,
    warn: LogLevels.warn,
    info: LogLevels.info,
    debug: LogLevels.debug,
};
/**
 * If the user does not specify a site directory _and_ the `mkdocs.yml` doesn't either, use this dir.
 */
exports.DEFAULT_SITE_DIR = 'site';
/**
 * pip 23.0 implements PEP 668, which may prevent overriding Python system packages
 * unless the --break-system-packages flag is passed.
 * To ensure backwards compatibility, its environment variable version is used
 */
exports.PIP_ENV_VARS = { PIP_BREAK_SYSTEM_PACKAGES: '1' };
/**
 * Error message emitted when the path to Python was not found
 */
exports.MESSAGE_PYTHON_MISSING = 'Could not find Python in PATH. Is it installed?';
//# sourceMappingURL=constants.js.map