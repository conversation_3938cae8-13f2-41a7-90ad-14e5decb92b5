{"version": 3, "file": "grid-register.js", "sourceRoot": "", "sources": ["../../lib/grid-register.js"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,6CAAmC;AACnC,sDAA8B;AAC9B,oDAAuB;AAEvB,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,EAAE;IACxB,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC;IAC9C,OAAO,GAAG,QAAQ,MAAM,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;AAC7D,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,KAAK,UAAU,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;IACpD,IAAI,cAAc,CAAC;IACnB,IAAI,gBAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACrB,cAAc,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC;YACH,IAAI,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,gBAAM,CAAC,KAAK,CACV,0CAA0C,cAAc,2BAA2B,GAAG,CAAC,OAAO,EAAE,CACjG,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,gBAAM,CAAC,kBAAkB,CAC7B,2CAA2C,cAAc,KAAK,GAAG,CAAC,OAAO,EAAE,CAC5E,CAAC;QACJ,CAAC;IACH,CAAC;IAED,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1C,CAAC;AAED,KAAK,UAAU,cAAc,CAAC,WAAW,EAAE,YAAY;IACrD,IAAI,CAAC;QACH,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;QACxD,CAAC;QACD,gBAAM,CAAC,KAAK,CACV,sDAAsD,GAAG,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAC5F,CAAC;IACJ,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,gBAAM,CAAC,KAAK,CAAC,0DAA0D,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IACxF,CAAC;AACH,CAAC;AAED,SAAS,WAAW,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;IACrD,mEAAmE;IACnE,IAAI,CAAC,gBAAC,CAAC,GAAG,CAAC,YAAY,EAAE,eAAe,CAAC,EAAE,CAAC;QAC1C,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,QAAQ,IAAI,mDAAmD,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC;YAC1F,IAAI,gBAAC,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,QAAQ,KAAK,cAAc,EAAE,CAAC;gBACjE,aAAa,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACjD,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QACD,mDAAmD,CAAC,CAAC,YAAY,CAAC,CAAC,aAAa,GAAG,aAAa,CAAC;IACnG,CAAC;IAED,6EAA6E;IAC7E,2DAA2D;IAC3D,kDAAkD;IAClD,0EAA0E;IAC1E,yCAAyC;IACzC,IACE,CAAC,YAAY,CAAC,aAAa,CAAC,GAAG;QAC/B,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI;QAChC,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,EAChC,CAAC;QACD,YAAY,CAAC,aAAa,CAAC,GAAG,GAAG,UAAU,IAAI,IAAI,IAAI,GAAG,QAAQ,EAAE,CAAC;QACrE,YAAY,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC;QACvC,YAAY,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC;IACzC,CAAC;IACD,2DAA2D;IAC3D,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;QACnC,YAAY,CAAC,aAAa,CAAC,EAAE,GAAG,UAAU,YAAY,CAAC,aAAa,CAAC,IAAI,IAAI,YAAY,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACjH,CAAC;IAED,mBAAmB;IACnB,MAAM,UAAU,GAAG;QACjB,GAAG,EAAE,GAAG,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,gBAAgB;QAC1D,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,YAAY;KACnB,CAAC;IAEF,IAAI,YAAY,CAAC,aAAa,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;QACjD,gBAAM,CAAC,KAAK,CAAC,yBAAyB,YAAY,CAAC,aAAa,CAAC,QAAQ,WAAW,CAAC,CAAC;QACtF,OAAO;IACT,CAAC;IAED,MAAM,qBAAqB,GAAG,YAAY,CAAC,aAAa,CAAC,aAAa,CAAC;IACvE,IAAI,KAAK,CAAC,qBAAqB,CAAC,IAAI,qBAAqB,IAAI,CAAC,EAAE,CAAC;QAC/D,gBAAM,CAAC,IAAI,CACT,kDAAkD;YAChD,mDAAmD,CACtD,CAAC;QACF,OAAO;IACT,CAAC;IACD,wBAAwB;IACxB,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,gBAAM,CAAC,KAAK,CACV,8CAA8C;QAC5C,8BAA8B,qBAAqB,MAAM,CAC5D,CAAC;IACF,WAAW,CAAC,KAAK,UAAU,aAAa;QACtC,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,GAAG,KAAK,CAAC;YACd,MAAM,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,CAAC,CAAC,MAAM,mBAAmB,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;YACtD,kDAAkD;YAClD,MAAM,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACjD,CAAC;IACH,CAAC,EAAE,qBAAqB,CAAC,CAAC;AAC5B,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,YAAY;IAC7C,qCAAqC;IACrC,MAAM,EAAE,GAAG,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC;IACzC,IAAI,CAAC;QACH,MAAM,EAAC,IAAI,EAAE,MAAM,EAAC,GAAG,MAAM,IAAA,eAAK,EAAC;YACjC,GAAG,EAAE,GAAG,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,sBAAsB,EAAE,EAAE;YACpE,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QACH,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,wCAAwC;YACxC,gBAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,gBAAM,CAAC,KAAK,CAAC,+BAA+B,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC;AAED,kBAAe,YAAY,CAAC"}