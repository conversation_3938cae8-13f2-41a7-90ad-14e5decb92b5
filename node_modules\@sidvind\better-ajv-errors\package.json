{"name": "@sidvind/better-ajv-errors", "version": "3.0.1", "description": "JSON Schema validation for Human", "repository": "https://github.com/ext/better-ajv-errors.git", "homepage": "https://github.com/ext/better-ajv-errors", "bugs": "https://github.com/ext/better-ajv-errors/issues", "sideEffects": false, "main": "./lib/cjs/index.js", "exports": {".": {"types": "./typings.d.ts", "require": "./lib/cjs/index.js", "import": "./lib/esm/index.mjs"}}, "module": "./lib/esm/index.mjs", "keywords": ["json-schema", "ajv", "ajv-errors"], "author": "Rifat Nabi <<EMAIL>>", "maintainers": ["Rifat Nabi <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "Tong Li"], "license": "Apache-2.0", "types": "./typings.d.ts", "engines": {"node": ">= 16.14"}, "files": ["lib", "typings.d.ts", "!**/__tests__", "!lib/test-helpers.js"], "dependencies": {"kleur": "^4.1.0"}, "peerDependencies": {"ajv": "^6.12.3 || ^7.0.0 || ^8.0.0"}}