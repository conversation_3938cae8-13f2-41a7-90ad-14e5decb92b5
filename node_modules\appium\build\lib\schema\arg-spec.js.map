{"version": 3, "file": "arg-spec.js", "sourceRoot": "", "sources": ["../../../lib/schema/arg-spec.js"], "names": [], "mappings": ";;;;;;AAAA,oDAAuB;AAEvB;;;GAGG;AACU,QAAA,uBAAuB,GAAG,aAAa,CAAC;AAErD;;;;GAIG;AACU,QAAA,gBAAgB,GAAG,QAAQ,CAAC;AAEzC;;GAEG;AACH,MAAM,gBAAgB,GAAG,kDAAkD,CAAC;AAE5E;;GAEG;AACH,MAAM,UAAU,GAAG,YAAY,CAAC;AAEhC;;;;;;GAMG;AACH,MAAa,OAAO;IAwDlB;;;;;;;;;OASG;IACH,YAAY,IAAI,EAAE,EAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAC,GAAG,EAAE;QAC3D,0EAA0E;QAC1E,QAAQ;QACR,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAElD,MAAM,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAExD,2EAA2E;QAC3E,wEAAwE;QACxE,MAAM,OAAO,GAAG,gBAAC,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;QAE1C,MAAM,WAAW,GAAG,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAEzF,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO;QACvC,MAAM,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1D,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,UAAU,EAAE,wBAAgB,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnF,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO;QACrC,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;YACvB,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC;QACnE,CAAC;QACD,OAAO,+BAAuB,CAAC;IACjC,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO;QACjC,MAAM,UAAU,GAAG,gBAAC,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;YACvB,OAAO,CAAC,OAAO,EAAE,gBAAC,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,mBAAmB,CAAC,OAAO;QAChC,OAAO,gBAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,6BAA6B,CAAC,QAAQ;QAC3C,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACjD,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACpB,MAAM,EAAC,OAAO,EAAE,iBAAiB,EAAC;YAChC,oEAAoE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxF,OAAO,EAAC,OAAO,EAAE,iBAAiB,EAAC,CAAC;QACtC,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI;QACtB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,0BAA0B;IAC1B,QAAQ;QACN,IAAI,GAAG,GAAG,aAAa,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC;QACjD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjC,GAAG,IAAI,UAAU,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC;QACnD,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAtLD,0BAsLC;AAED;;;;;;;;GAQG;AAEH;;GAEG"}