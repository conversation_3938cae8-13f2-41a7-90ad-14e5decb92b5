{"version": 3, "file": "driver-config.js", "sourceRoot": "", "sources": ["../../../lib/extension/driver-config.js"], "names": [], "mappings": ";;;;;;AAAA,oDAAuB;AACvB,4CAAyC;AACzC,uDAA4B;AAC5B,yDAAmD;AAEnD;;GAEG;AACH,MAAa,YAAa,SAAQ,kCAAe;IAmB/C;;;;OAIG;IACH,YAAY,QAAQ;QAClB,KAAK,CAAC,uBAAW,EAAE,QAAQ,CAAC,CAAC;QAE7B,IAAI,CAAC,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,MAAM,CAAC,QAAQ;QACpB,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,6BAA6B,QAAQ,CAAC,UAAU,6EAA6E,CAC9H,CAAC;QACJ,CAAC;QACD,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAChD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,WAAW,CAAC,QAAQ;QACzB,OAAO,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,OAAO,MAAM,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,uBAAW,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;OAGG;IACH,iBAAiB,CAAC,OAAO;QACvB,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,MAAM,EAAC,aAAa,EAAE,cAAc,EAAC,GAAG,OAAO,CAAC;QAEhD,IAAI,CAAC,gBAAC,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9B,QAAQ,CAAC,IAAI,CAAC;gBACZ,GAAG,EAAE,oDAAoD;gBACzD,GAAG,EAAE,aAAa;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,gBAAC,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC7B,QAAQ,CAAC,IAAI,CAAC;oBACZ,GAAG,EAAE,2BAA2B;oBAChC,GAAG,EAAE,aAAa;iBACnB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;oBAClC,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;wBACvB,QAAQ,CAAC,IAAI,CAAC;4BACZ,GAAG,EAAE,qCAAqC;4BAC1C,GAAG,EAAE,KAAK;yBACX,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC;gBACZ,GAAG,EAAE,qCAAqC;gBAC1C,GAAG,EAAE,cAAc;aACpB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;YAClD,QAAQ,CAAC,IAAI,CAAC;gBACZ,GAAG,EAAE,4DAA4D;gBACjE,GAAG,EAAE,cAAc;aACpB,CAAC,CAAC;QACL,CAAC;QAED,mGAAmG;QACnG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAE9C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,UAAU,EAAE,EAAC,OAAO,EAAE,cAAc,EAAC;QACjD,OAAO,GAAG,UAAU,IAAI,OAAO,qBAAqB,cAAc,IAAI,CAAC;IACzE,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,kBAAkB,CAAC,EAAC,cAAc,EAAE,YAAY,EAAC;QACrD,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,gBAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,gBAAG,CAAC,IAAI,CACN,wDAAwD;YACtD,IAAI,cAAc,uBAAuB,YAAY,GAAG,CAC3D,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,EAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAC,GAAG,IAAI,CAAC,mBAAmB,CAC/D,cAAc,EACd,YAAY,CACb,CAAC;YACF,gBAAG,CAAC,IAAI,CAAC,QAAQ,UAAU,0CAA0C,CAAC,CAAC;YACvE,gBAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAClE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CACb,WAAW,UAAU,uCAAuC,SAAS,sCAAsC,CAC5G,CAAC;YACJ,CAAC;YACD,OAAO,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAC,CAAC;QACvC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,GAAG,GACP,6CAA6C;gBAC7C,IAAI,cAAc,uBAAuB,YAAY,KAAK;gBAC1D,kDAAkD;gBAClD,6DAA6D;gBAC7D,uBAAuB,GAAG,CAAC,OAAO,GAAG,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,mBAAmB,CAAC,mBAAmB,EAAE,iBAAiB;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACzC,KAAK,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,gBAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,MAAM,EAAC,cAAc,EAAE,aAAa,EAAC,GAAG,UAAU,CAAC;YACnD,MAAM,YAAY,GAAG,cAAc,CAAC,WAAW,EAAE,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACxF,MAAM,YAAY,GAAG,gBAAC,CAAC,QAAQ,CAC7B,aAAa,CAAC,GAAG,CAAC,gBAAC,CAAC,OAAO,CAAC,EAC5B,iBAAiB,CAAC,WAAW,EAAE,CAChC,CAAC;YAEF,IAAI,YAAY,IAAI,YAAY,EAAE,CAAC;gBACjC,OAAO,EAAC,UAAU,EAAE,GAAG,UAAU,EAAC,CAAC;YACrC,CAAC;YAED,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CACb,WAAW,UAAU,4BAA4B;oBAC/C,IAAI,cAAc,+BAA+B;oBACjD,6BAA6B,iBAAiB,eAAe;oBAC7D,qBAAqB;oBACrB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAChC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;IAC3E,CAAC;;AA1MH,oCA2MC;AApMC;;;;;;;;;GASG;AACI,uBAAU,GAAG,IAAI,OAAO,EAAE,CAAC;AA4LpC;;;GAGG;AAEH;;;GAGG;AAEH;;;;;GAKG;AAEH;;;GAGG;AAEH;;;GAGG;AAEH;;;;;;GAMG"}