import type { App<PERSON>Logger, BidiModuleMap, BiDiResultData, StringRecord } from '@appium/types';
export declare class ExtensionCore {
    bidiEventSubs: Record<string, string[]>;
    bidiCommands: BidiModuleMap;
    _logPrefix?: string;
    protected _log: AppiumLogger;
    readonly eventEmitter: NodeJS.EventEmitter;
    constructor(logPrefix?: string);
    get log(): AppiumLogger;
    updateLogPrefix(logPrefix: string): void;
    updateBidiCommands(cmds: BidiModuleMap): void;
    doesBidiCommandExist(moduleName: string, methodName: string): boolean;
    ensureBidiCommandExists(moduleName: string, methodName: string): void;
    executeBidiCommand(bidiCmd: string, bidiParams: StringRecord, next?: () => Promise<any>, driver?: ExtensionCore): Promise<BiDiResultData>;
}
//# sourceMappingURL=extension-core.d.ts.map