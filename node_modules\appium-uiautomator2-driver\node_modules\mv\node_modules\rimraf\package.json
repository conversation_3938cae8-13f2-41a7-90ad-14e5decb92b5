{"name": "<PERSON><PERSON><PERSON>", "version": "2.4.5", "main": "rimraf.js", "description": "A deep deletion module for node (like `rm -rf`)", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "repository": "git://github.com/isaacs/rimraf.git", "scripts": {"test": "tap test/*.js"}, "bin": "./bin.js", "dependencies": {"glob": "^6.0.1"}, "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "devDependencies": {"mkdirp": "^0.5.1", "tap": "^2.3.4"}}