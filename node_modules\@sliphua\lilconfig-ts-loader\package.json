{"name": "@sliphua/lilconfig-ts-loader", "description": "A TypeScript loader for lilconfig", "version": "3.2.2", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "keywords": ["lilconfig", "lilconfig-loader", "typescript"], "author": {"name": "Endemol Shine Group Technology", "url": "https://github.com/EndemolShineGroup"}, "files": ["dist"], "homepage": "https://github.com/PaperStrike/lilconfig-typescript-loader", "repository": {"type": "git", "url": "https://github.com/PaperStrike/lilconfig-typescript-loader.git"}, "engines": {"node": ">=10.0.0"}, "scripts": {"pretest": "rimraf coverage/", "test": "jest --no-cache --coverage", "prebuild": "rimraf dist/", "build": "tsc", "build:docs": "rimraf docs/api && typedoc --out docs/api --target es6 --theme minimal --mode file src", "semantic-release": "semantic-release", "commit": "git-cz", "lint": "tslint -p tsconfig.json -t codeFrame 'src/**/*.ts' -e 'src/**/*.spec.ts'"}, "peerDependencies": {"lilconfig": ">=2"}, "dependencies": {"lodash.get": "^4", "make-error": "^1", "ts-node": "^9", "tslib": "^2"}, "devDependencies": {"@commitlint/cli": "^11", "@commitlint/config-conventional": "^11", "@endemolshinegroup/cz-github": "^2", "@endemolshinegroup/prettier-config": "^1", "@endemolshinegroup/tslint-config": "^1", "@semantic-release/changelog": "^5", "@semantic-release/git": "^9", "@types/jest": "^26", "@types/lodash.get": "^4", "@types/node": "10.*", "commitizen": "^4", "husky": "^4", "jest": "^26", "lilconfig": "^2", "lint-staged": "^10", "prettier": "^2", "rimraf": "^3", "semantic-release": "^17", "ts-jest": "^26", "tslint": "^5", "tslint-config-prettier": "^1", "tslint-eslint-rules": "^5", "typedoc": "^0.19.1", "typescript": "^3"}}