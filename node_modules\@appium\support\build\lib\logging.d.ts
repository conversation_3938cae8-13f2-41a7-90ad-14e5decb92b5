/**
 *
 * @param {AppiumLoggerPrefix?} [prefix=null]
 * @returns {AppiumLogger}
 */
export function getLogger(prefix?: AppiumLoggerPrefix | null): AppiumLogger;
/**
 * Marks arbitrary log message as sensitive.
 * This message will then be replaced with the default replacer
 * while being logged by any `info`, `debug`, etc. methods if the
 * asyncStorage has `isSensitive` flag enabled in its async context.
 * The latter is enabled by the corresponding HTTP middleware
 * in response to the `X-Appium-Is-Sensitive` request header
 * being set to 'true'.
 *
 * @template {any} T
 * @param {T} logMessage
 * @returns {{[k: string]: T}}
 */
export function markSensitive<T extends unknown>(logMessage: T): {
    [k: string]: T;
};
/** @type {import('@appium/types').AppiumLoggerLevel[]} */
export const LEVELS: import("@appium/types").AppiumLoggerLevel[];
export const log: import("@appium/types").AppiumLogger;
export default log;
export type AppiumLoggerPrefix = import("@appium/types").AppiumLoggerPrefix;
export type AppiumLogger = import("@appium/types").AppiumLogger;
export type AppiumLoggerLevel = import("@appium/types").AppiumLoggerLevel;
//# sourceMappingURL=logging.d.ts.map