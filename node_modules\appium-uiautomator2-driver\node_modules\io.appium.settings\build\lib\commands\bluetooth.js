"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setBluetoothState = setBluetoothState;
exports.unpairAllBluetoothDevices = unpairAllBluetoothDevices;
const constants_js_1 = require("../constants.js");
/**
 * Change the state of bluetooth on the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @param {boolean} on - True to enable and false to disable it.
 */
async function setBluetoothState(on) {
    if (await this.adb.getApiLevel() < 30) {
        await this.checkBroadcast([
            '-a', constants_js_1.BLUETOOTH_SETTING_ACTION,
            '-n', constants_js_1.BLUETOOTH_SETTING_RECEIVER,
            '--es', 'setstatus', on ? 'enable' : 'disable'
        ], `${on ? 'enable' : 'disable'} bluetooth`);
    }
    else {
        await this.adb.setBluetoothOn(on);
    }
}
/**
 * Unpairs all previosly paired bluetooth devices if any exist
 *
 * @this {import('../client').SettingsApp}
 */
async function unpairAllBluetoothDevices() {
    await this.checkBroadcast([
        '-a', constants_js_1.BLUETOOTH_UNPAIR_ACTION,
        '-n', constants_js_1.BLUETOOTH_UNPAIR_RECEIVER,
    ], 'unpair all bluetooth devices');
}
//# sourceMappingURL=bluetooth.js.map