{"name": "@wdio/local-runner", "version": "9.17.0", "description": "A WebdriverIO runner to run tests locally", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/webdriverio/webdriverio/tree/main/packages/wdio-local-runner", "license": "MIT", "engines": {"node": ">=18.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/webdriverio/webdriverio.git", "directory": "packages/wdio-local-runner"}, "keywords": ["webdriver", "webdriverio", "wdio", "wdio-runner"], "bugs": {"url": "https://github.com/webdriverio/webdriverio/issues"}, "type": "module", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.js"}, "./run": {"source": "./src/run.ts", "import": "./build/run.js"}}, "typeScriptVersion": "3.8.3", "dependencies": {"@types/node": "^20.1.0", "@wdio/logger": "9.16.2", "@wdio/repl": "9.16.2", "@wdio/runner": "9.17.0", "@wdio/types": "9.16.2", "exit-hook": "^4.0.0", "expect-webdriverio": "^5.3.4", "split2": "^4.1.0", "stream-buffers": "^3.0.2"}, "devDependencies": {"@types/stream-buffers": "^3.0.4"}, "publishConfig": {"access": "public"}, "gitHead": "796df04f0f5c744d5ef4bafd3759995fa4829d6f"}