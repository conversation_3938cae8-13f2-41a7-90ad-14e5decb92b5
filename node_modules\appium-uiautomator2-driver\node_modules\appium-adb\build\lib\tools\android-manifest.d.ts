/**
 * Extract package and main activity name from application manifest.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to application .apk(s) package
 * @return {Promise<import('./types').APKInfo>} The parsed application info.
 * @throws {error} If there was an error while getting the data from the given
 *                 application package.
 */
export function packageAndLaunchActivityFromManifest(this: import("../adb.js").ADB, appPath: string): Promise<import("./types").APKInfo>;
/**
 * Extract target SDK version from application manifest.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to .apk(s) package.
 * @return {Promise<number>} The version of the target SDK.
 * @throws {error} If there was an error while getting the data from the given
 *                 application package.
 */
export function targetSdkVersionFromManifest(this: import("../adb.js").ADB, appPath: string): Promise<number>;
/**
 * Extract target SDK version from package information.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} pkg - The class name of the package installed on the device under test.
 * @param {string?} [cmdOutput=null] - Optional parameter containing the output of
 * _dumpsys package_ command. It may speed up the method execution.
 * @return {Promise<number>} The version of the target SDK.
 */
export function targetSdkVersionUsingPKG(this: import("../adb.js").ADB, pkg: string, cmdOutput?: string | null): Promise<number>;
/**
 * Create binary representation of package manifest (usually AndroidManifest.xml).
 * `${manifest}.apk` file will be created as the result of this method
 * containing the compiled manifest.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} manifest - Full path to the initial manifest template
 * @param {string} manifestPackage - The name of the manifest package
 * @param {string} targetPackage - The name of the destination package
 */
export function compileManifest(this: import("../adb.js").ADB, manifest: string, manifestPackage: string, targetPackage: string): Promise<void>;
/**
 * Replace/insert the specially precompiled manifest file into the
 * particular package.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} manifest - Full path to the precompiled manifest
 *                            created by `compileManifest` method call
 *                            without .apk extension
 * @param {string} srcApk - Full path to the existing valid application package, where
 *                          this manifest has to be insetred to. This package
 *                          will NOT be modified.
 * @param {string} dstApk - Full path to the resulting package.
 *                          The file will be overridden if it already exists.
 */
export function insertManifest(this: import("../adb.js").ADB, manifest: string, srcApk: string, dstApk: string): Promise<void>;
/**
 * Check whether package manifest contains Internet permissions.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} appPath - The full path to .apk(s) package.
 * @return {Promise<boolean>} True if the manifest requires Internet access permission.
 */
export function hasInternetPermissionFromManifest(this: import("../adb.js").ADB, appPath: string): Promise<boolean>;
/**
 * Retrieve the path to the recent installed Android platform.
 *
 * @param {string} sdkRoot
 * @return {Promise<import('./types').PlatformInfo>} The resulting path to the newest installed platform.
 */
export function getAndroidPlatformAndPath(sdkRoot: string): Promise<import("./types").PlatformInfo>;
//# sourceMappingURL=android-manifest.d.ts.map