# WebdriverIO Mobile Testing Setup

This project is configured for mobile app testing using WebdriverIO with Appium for Android applications.

## Prerequisites

1. **Android SDK** - Make sure Android SDK is installed and `ANDROID_HOME` environment variable is set
2. **Java JDK** - Required for Android development
3. **Android Emulator or Physical Device** - For running tests

## Project Structure

```
├── test/
│   ├── specs/           # Test files
│   └── pageobjects/     # Page Object Model files
├── logs/                # Appium logs
├── wdio.conf.ts         # WebdriverIO configuration
├── org-wikipedia.apk    # Sample Android app
└── package.json         # Dependencies and scripts
```

## Installation

Dependencies are already installed. If you need to reinstall:

```bash
npm install
```

## Configuration

The main configuration is in `wdio.conf.ts`. Key settings:

- **App Path**: `./org-wikipedia.apk` (Wikipedia Android app)
- **Platform**: Android
- **Automation**: UiAutomator2
- **Framework**: Mocha
- **Reporter**: Spec

## Running Tests

### 1. Start Appium Server

In one terminal:
```bash
npm run appium
```

### 2. Start Android Emulator

Make sure you have an Android emulator running or a physical device connected.

### 3. Run Tests

In another terminal:
```bash
npm test
```

Or specifically for Android:
```bash
npm run test:android
```

## Writing Tests

### Page Objects

Page objects are located in `test/pageobjects/`. Example:

```typescript
import { $ } from '@wdio/globals'

class WikipediaPage {
    get searchInput() {
        return $('~Search Wikipedia')
    }
    
    async search(query: string) {
        await this.searchInput.click()
        await this.searchInput.setValue(query)
    }
}

export default new WikipediaPage()
```

### Test Files

Test files are located in `test/specs/`. Example:

```typescript
import { expect } from '@wdio/globals'
import WikipediaPage from '../pageobjects/wikipedia.page.js'

describe('Wikipedia App Tests', () => {
    it('should search for a topic', async () => {
        await WikipediaPage.search('Selenium')
        // Add assertions here
    })
})
```

## Troubleshooting

### Common Issues

1. **Appium Server Not Running**: Make sure to start Appium server before running tests
2. **Device Not Connected**: Verify emulator is running or device is connected via `adb devices`
3. **App Not Found**: Ensure the APK file path is correct in `wdio.conf.ts`

### Useful Commands

- Check connected devices: `adb devices`
- List installed Appium drivers: `npx appium driver list`
- Start Appium with logs: `npx appium server --log-level debug`

## Next Steps

1. Customize the `wdio.conf.ts` for your specific app
2. Update page objects to match your app's elements
3. Write comprehensive test cases
4. Set up CI/CD integration if needed

## Resources

- [WebdriverIO Documentation](https://webdriver.io/)
- [Appium Documentation](https://appium.io/)
- [Android Testing Guide](https://developer.android.com/training/testing)
