"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mobileInstallMultipleApks = mobileInstallMultipleApks;
const lodash_1 = __importDefault(require("lodash"));
const bluebird_1 = __importDefault(require("bluebird"));
const driver_1 = require("appium/driver");
const extensions_1 = require("../extensions");
/**
 * Install multiple APKs with `install-multiple` option.
 * @this {AndroidUiautomator2Driver}
 * @param {string[]} apks The list of APKs to install. Each APK should be a path to a apk
 * or downloadable URL as HTTP/HTTPS.
 * @param {import('./types').InstallOptions} [options] Installation options.
 * @throws {Error} if an error occured while installing the given APKs.
 * @returns {Promise<void>}
 */
async function mobileInstallMultipleApks(apks, options) {
    if (!lodash_1.default.isArray(apks) || lodash_1.default.isEmpty(apks)) {
        throw new driver_1.errors.InvalidArgumentError('No apks are given to install');
    }
    const configuredApks = await bluebird_1.default.all(apks.map((app) => this.helpers.configureApp(app, [extensions_1.APK_EXTENSION])));
    await this.adb.installMultipleApks(configuredApks, options);
}
/**
 * @typedef {import('../driver').AndroidUiautomator2Driver} AndroidUiautomator2Driver
 */
/**
 * @template [T=any]
 * @typedef {import('@appium/types').StringRecord<T>} StringRecord
 */
//# sourceMappingURL=app-management.js.map