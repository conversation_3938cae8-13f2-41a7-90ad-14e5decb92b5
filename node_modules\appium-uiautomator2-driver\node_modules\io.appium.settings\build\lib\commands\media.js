"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.scanMedia = scanMedia;
const logger_js_1 = require("../logger.js");
const constants_js_1 = require("../constants.js");
/**
 * Performs recursive media scan at the given destination.
 * All successfully scanned items are being added to the device's
 * media library.
 *
 * @this {import('../client').SettingsApp}
 * @param {string} destination File/folder path on the remote device.
 * @throws {Error} If there was an unexpected error by scanning.
 */
async function scanMedia(destination) {
    this.log.debug(logger_js_1.LOG_PREFIX, `Scanning '${destination}' for media files`);
    await this.checkBroadcast([
        '-n', constants_js_1.MEDIA_SCAN_RECEIVER,
        '-a', constants_js_1.MEDIA_SCAN_ACTION,
        '--es', 'path', destination
    ], 'scan media');
}
//# sourceMappingURL=media.js.map