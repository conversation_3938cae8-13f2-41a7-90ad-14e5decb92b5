/**
 * Builds a universal .apk from the given .aab package. See
 * https://developer.android.com/studio/command-line/bundletool#generate_apks
 * for more details.
 *
 * @this {import('../adb.js').ADB}
 * @param {string} aabPath Full path to the source .aab package
 * @param {import('./types').ApkCreationOptions} [opts={}]
 * @returns The path to the resulting universal .apk. The .apk is stored in the internal cache
 * by default.
 * @throws {Error} If there was an error while creating the universal .apk
 */
export function extractUniversalApk(this: import("../adb.js").ADB, aabPath: string, opts?: import("./types").ApkCreationOptions): Promise<string>;
//# sourceMappingURL=aab-utils.d.ts.map