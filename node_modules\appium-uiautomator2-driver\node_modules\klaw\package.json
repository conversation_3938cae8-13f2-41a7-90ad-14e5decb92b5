{"name": "klaw", "version": "4.1.0", "description": "File system walker with Readable stream interface.", "main": "./src/index.js", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape tests/**/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/jprichardson/node-klaw.git"}, "keywords": ["walk", "walker", "fs", "readable", "streams"], "engines": {"node": ">=14.14.0"}, "author": "<PERSON>", "license": "MIT", "files": ["src/"], "bugs": {"url": "https://github.com/jprichardson/node-klaw/issues"}, "homepage": "https://github.com/jprichardson/node-klaw#readme", "devDependencies": {"standard": "^16.0.3", "tap-spec": "^5.0.0", "tape": "^5.3.1"}}