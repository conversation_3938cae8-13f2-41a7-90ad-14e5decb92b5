/** Class which stores the last bit of data streamed into it */
export class MJpegStream extends Writable {
    /**
     * Create an MJpegStream
     * @param {string} mJpegUrl - URL of MJPEG-over-HTTP stream
     * @param {function} [errorHandler=noop] - additional function that will be
     * called in the case of any errors.
     * @param {object} [options={}] - Options to pass to the Writable constructor
     */
    constructor(mJpegUrl: string, errorHandler?: Function, options?: object);
    /**
     * @type {number}
     */
    updateCount: number;
    errorHandler: Function;
    url: string;
    /**
     * Get the base64-encoded version of the JPEG
     *
     * @returns {?string} base64-encoded JPEG image data
     * or `null` if no image can be parsed
     */
    get lastChunkBase64(): string | null;
    /**
     * Get the PNG version of the JPEG buffer
     *
     * @returns {Promise<Buffer?>} PNG image data or `null` if no PNG
     * image can be parsed
     */
    lastChunkPNG(): Promise<Buffer | null>;
    /**
     * Get the base64-encoded version of the PNG
     *
     * @returns {Promise<string?>} base64-encoded PNG image data
     * or `null` if no image can be parsed
     */
    lastChunkPNGBase64(): Promise<string | null>;
    /**
     * Reset internal state
     */
    clear(): void;
    registerStartSuccess: ((thenableOrResult?: any) => void) | null | undefined;
    registerStartFailure: ((error?: any) => void) | null | undefined;
    responseStream: any;
    consumer: any;
    lastChunk: Buffer<ArrayBufferLike> | null | undefined;
    /**
     * Start reading the MJpeg stream and storing the last image
     */
    start(serverTimeout?: number): Promise<void>;
    /**
     * Stop reading the MJpeg stream. Ensure we disconnect all the pipes and stop
     * the HTTP request itself. Then reset the state.
     */
    stop(): void;
    /**
     * Override the Writable write() method in order to save the last image and
     * log the number of images we have received
     * @override
     * @param {Buffer} data - binary data streamed from the MJpeg consumer
     */
    override write(data: Buffer): boolean;
}
import { Writable } from 'stream';
//# sourceMappingURL=mjpeg.d.ts.map