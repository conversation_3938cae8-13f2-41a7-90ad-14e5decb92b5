import { type Theme } from '@inquirer/core';
import type { PartialDeep } from '@inquirer/type';
declare const _default: <Required extends boolean>(config: {
    message: string;
    default?: number | undefined;
    min?: number | undefined;
    max?: number | undefined;
    step?: number | "any" | undefined;
    required?: Required | undefined;
    validate?: ((value: Required extends true ? number : number | undefined) => boolean | string | Promise<string | boolean>) | undefined;
    theme?: PartialDeep<Theme> | undefined;
}, context?: import("@inquirer/type").Context) => Promise<Required extends true ? number : number | undefined> & {
    cancel: () => void;
};
export default _default;
