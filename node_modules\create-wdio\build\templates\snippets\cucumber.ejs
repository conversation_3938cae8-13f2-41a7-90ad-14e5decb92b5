<%  if (answers.framework === 'cucumber') { %>
    // If you are using Cucumber you need to specify the location of your step definitions.
    cucumberOpts: {
        // <string[]> (file/dir) require files before executing features
        require: ['<%- answers.stepDefinitions %>'],
        // <boolean> show full backtrace for errors
        backtrace: false,
        // <string[]> ("extension:module") require files with the given EXTENSION after requiring MODULE (repeatable)
        requireModule: [],
        // <boolean> invoke formatters without executing steps
        dryRun: false,
        // <boolean> abort the run on first failure
        failFast: false,
        // <string[]> Only execute the scenarios with name matching the expression (repeatable).
        name: [],
        // <boolean> hide step definition snippets for pending steps
        snippets: true,
        // <boolean> hide source uris
        source: true,
        // <boolean> fail if there are any undefined or pending steps
        strict: false,
        // <string> (expression) only execute the features or scenarios with tags matching the expression
        tagExpression: '',
        // <number> timeout for step definitions
        timeout: 60000,
        // <boolean> Enable this config to treat undefined definitions as warnings.
        ignoreUndefinedDefinitions: false
    },
<% } else if (answers.serenityAdapter === 'cucumber') { %>
    // Cucumber configuration, see
    //  https://serenity-js.org/api/cucumber-adapter/interface/CucumberConfig/
    cucumberOpts: {
        // <string[]> (file/dir) require files before executing features
        import: [
            <% if (answers.stepDefinitions) {
                %>'<%- answers.stepDefinitions %>',
            <% } %>'./features/support/*.<%- answers.isUsingTypeScript ? 'ts' : 'js' %>',
        ],
        // <string[]> (type[:path]) specify native Cucumber.js output format, if needed. Optionally supply PATH to redirect formatter output (repeatable)
        format: [ ],
        // <string> (name) specify the profile to use
        profile: '',
        // <boolean> fail if there are any undefined or pending steps
        strict: false,
        // <string[] | string> (expression) only execute the features or scenarios with tags matching the expression
        tags: [],
        // <number> timeout for step definitions
        timeout: 60000,
    },
<% } %>
