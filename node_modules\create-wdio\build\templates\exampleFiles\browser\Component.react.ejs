import { useState } from 'react'

import './Component.css'

export default function ExampleComponent () {
    const [count, setCount] = useState(0)

    return (
        <div id="root">
            <div>
                <a href="https://webdriver.io/docs/component-testing" target="_blank">
                    <img src="https://webdriver.io/assets/images/robot-3677788dd63849c56aa5cb3f332b12d5.svg" className="logo" alt="WebdriverIO logo" />
                </a>
            </div>
            <h1>WebdriverIO Component Testing</h1>
            <div className="card">
                <button onClick={() => setCount((count) => count + 1)}>
                    count is {count}
                </button>
                <p>
                    Edit <code>src/Component.test.<%- answers.isUsingTypeScript ? `tsx` : 'jsx' %></code> and save to test HMR
                </p>
            </div>
            <p className="read-the-docs">
                Click on the WebdriverIO logo to learn more
            </p>
        </div>
    )
}
