{"version": 3, "file": "asyncbox.d.ts", "sourceRoot": "", "sources": ["../../lib/asyncbox.js"], "names": [], "mappings": ";;;;;;;yBAwPgC,GAAG,EAAE,KAAK,IAAI;;;;;;;;;;;;;;;;eAehC,MAAM;cACN,MAAM;cACN,MAAM;;;;;0CAMT,QAAQ,KACN,IAAI;AA3QjB;;;;GAIG;AACH,0BAHW,MAAM,GACJ,QAAQ,IAAI,CAAC,CAIzB;AAsCD;;;;;;;GAOG;AACH,sCALW,MAAM,gBACI,GAAG,EAAE,0BACX,GAAG,uBAmBjB;AAiCD;;;;;;;GAOG;AACH,2CAJW,GAAG,YACG,GAAG,4BAAgB,IAAI,cAKvC;AAED;;;;GAIG;AACH,iEAHmC,GAAG,EAAE,KAAK,GAAG,6BACX,GAAG,EAAE,KAAG,IAAI,EAahD;AA3DD;;;;;;;;;GASG;AACH,8CANW,MAAM,WACN,MAAM,gBACI,GAAG,EAAE,0BACX,GAAG,uBAoBjB;AAkCD;;;GAGG;AACH,uCAHqB,GAAG,EAAE,KAAK,GAAG,GAAC,QAAQ,GAAG,CAAC,WAChC,GAAG,UAIjB;AAtCD,oCAAuB;AAwCvB;;;;;GAKG;AACH,+BAJW,GAAG,EAAE,kBACG,GAAG,KAAK,GAAG,GAAC,QAAQ,GAAG,CAAC,4BAC9B,QAAQ,GAAG,EAAE,CAAC,CAY1B;AAED;;;;;;GAMG;AACH,kCALW,GAAG,EAAE,kBACG,GAAG,KAAK,GAAG,GAAC,QAAQ,GAAG,CAAC,kBAChC,OAAO,GACL,QAAQ,GAAG,EAAE,CAAC,CAmB1B;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,6HA8BC;AA9ND;;;;;;;;;;;;GAYG;AACH,8BAJW,MAAM,4EAEJ,QAAQ,IAAI,CAAC,CAuBzB;cAhDa,UAAU"}