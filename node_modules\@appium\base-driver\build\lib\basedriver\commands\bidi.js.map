{"version": 3, "file": "bidi.js", "sourceRoot": "", "sources": ["../../../../lib/basedriver/commands/bidi.ts"], "names": [], "mappings": ";;;;;AAEA,mCAA8B;AAC9B,oDAAuB;AAOvB,MAAM,YAAY,GAAkB;IAClC,KAAK,CAAC,aAAa,CAEjB,MAAgB,EAChB,WAAqB,CAAC,EAAE,CAAC;QAEzB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;QACvC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAEnB,MAAgB,EAChB,WAAqB,CAAC,EAAE,CAAC;QAEzB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7F,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACtC,IAAI,CAAC,gBAAC,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC;YAC5B,uBAAuB;YACvB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,gBAAC,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC;YAC9B,uBAAuB;YACvB,MAAM,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,8BAA8B,CAAC;QAC1E,CAAC;QACD,OAAO,MAAsB,CAAC;IAChC,CAAC;CACF,CAAC;AAEF,IAAA,aAAK,EAAC,YAAY,CAAC,CAAC"}