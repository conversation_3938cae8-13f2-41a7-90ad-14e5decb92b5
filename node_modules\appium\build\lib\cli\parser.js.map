{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../../lib/cli/parser.js"], "names": [], "mappings": ";;;;;;AA2WQ,8BAAS;AA3WjB,6CAAmC;AACnC,uCAAwC;AACxC,oDAAuB;AACvB,gDAAwB;AACxB,4CAWsB;AACtB,sCAAiE;AACjE,sCAAkC;AAClC,iCAAuD;AACvD,mDAQyB;AAEZ,QAAA,UAAU,GAAG,WAAW,CAAC;AAEtC;;;GAGG;AACH,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CACnC,IAAI,GAAG,CAAC,CAAC,4BAAgB,EAAE,uBAAW,EAAE,uBAAW,EAAE,6BAAiB,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAC5G,CAAC;AAEF,MAAM,OAAO,GAAG,YAAE,CAAC,mBAAmB,CAAC,gBAAO,CAAC,CAAC,OAAO,CAAC;AAExD;;;;;;GAMG;AACH,MAAM,SAAS;IACb;;OAEG;IACH,YAAY,KAAK,GAAG,KAAK;QACvB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACzE,MAAM,MAAM,GAAG,IAAI,yBAAc,CAAC;YAChC,QAAQ,EAAE,IAAI;YACd,WAAW,EACT,sFAAsF;gBACtF,yCAAyC;YAC3C,IAAI;SACL,CAAC,CAAC;QAEH,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAE7B;;;WAGG;QACH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB;;;WAGG;QACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB;;;WAGG;QACH,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,EAAE;YACrC,MAAM,EAAE,SAAS;YACjB,OAAO;SACR,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,CAAC,EAAC,IAAI,EAAE,YAAY,EAAC,CAAC,CAAC;QAE/D,yEAAyE;QACzE,yEAAyE;QACzE,iDAAiD;QACjD,MAAM,UAAU,GAAG,SAAS,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAE5D,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;QAE1B,4CAA4C;QAC5C,SAAS,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;QAEpD,0BAA0B;QAC1B,SAAS,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAExC,4CAA4C;QAC5C;;WAEG;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;IACnC,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,6BAAiB,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC;YACxC,wEAAwE;YACxE,wEAAwE;YACxE,IAAI,SAAS,EAAE,aAAa,KAAK,IAAI,EAAE,CAAC;gBACtC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;YACnC,CAAC;iBAAM,IAAI,SAAS,EAAE,aAAa,KAAK,IAAI,EAAE,CAAC;gBAC7C,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;YACnC,CAAC;YACD,IACE,WAAW,EAAE,MAAM;gBACnB,CAAC,SAAS,CAAC,aAAa,KAAK,KAAK,IAAI,SAAS,CAAC,aAAa,KAAK,KAAK,CAAC,EACxE,CAAC;gBACD,OAAO,SAAS,CAAC,oBAAoB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAChE,CAAC;iBAAM,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,mCAAmC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC9E,CAAC;YACD,OAAO,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM,GAAG,CAAC;YACZ,CAAC;YACD,kFAAkF;YAElF,0BAA0B;YAC1B,CAAC;gBACC,sCAAsC;gBACtC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,mDAAmD;gBACpE,sCAAsC;gBACtC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,WAAW,GAAG,EAAE;QAChD,MAAM,MAAM,GAAG,gBAAC,CAAC,MAAM,CACrB,IAAI,EACJ,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;YACvB,IAAI,CAAC,gBAAC,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,IAAA,mBAAU,EAAC,GAAG,CAAC,EAAE,CAAC;gBAC7C,MAAM,EAAC,IAAI,EAAC,GAAG,mDAAmD,CAAC,CAAC,IAAA,mBAAU,EAAC,GAAG,CAAC,CAAC,CAAC;gBACrF,gBAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,mDAAmD;gBACnD,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACxB,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,EAAE,CACH,CAAC;QACF,MAAM,CAAC,kBAAU,CAAC,GAAG,WAAW,CAAC;QACjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,UAAU,CAAC,MAAM;QACtB,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;YAC1B,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,CAAC;YACD,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,CAAC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,kBAAkB,CAAC,SAAS;QACjC,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,EAAE;YAClD,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,wBAAwB;YAC9B,WAAW,EAAE,8DAA8D;SAC5E,CAAC,CAAC;QAEH,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAEnC,MAAM,UAAU,GAAG,IAAA,oBAAa,GAAE,CAAC;QACnC,KAAK,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,UAAU,EAAE,CAAC;YAC9C,uDAAuD;YACvD,YAAY,CAAC,YAAY,CAAC,GAAG,YAAY,EAAE,EAAC,GAAG,IAAI,EAAC,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,6BAA6B,CAAC,UAAU;QAC7C,KAAK,MAAM,IAAI,IAAI,uCAAuC,CAAC,CAAC,CAAC,uBAAW,EAAE,uBAAW,CAAC,CAAC,EAAE,CAAC;YACxF,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE;gBAC5C,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,iBAAiB,IAAI,GAAG;gBAC9B,WAAW,EAAE,iBAAiB,IAAI,6BAA6B;aAChE,CAAC,CAAC;YAEH,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAEhC,MAAM,aAAa,GAAG,SAAS,CAAC,cAAc,CAAC;gBAC7C,IAAI,EAAE,GAAG,IAAI,SAAS;aACvB,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,IAAA,uBAAgB,GAAE,CAAC;YACzC;;eAEG;YACH,MAAM,WAAW,GAAG;gBAClB;oBACE,OAAO,EAAE,+BAAmB;oBAC5B,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI;oBAC9B,IAAI,EAAE,gCAAgC,IAAI,GAAG;oBAC7C,OAAO,EAAE,CAAC,IAAI,CAAC;iBAChB;gBACD;oBACE,OAAO,EAAE,kCAAsB;oBAC/B,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO;oBACjC,IAAI,EAAE,aAAa,IAAI,EAAE;iBAC1B;gBACD;oBACE,OAAO,EAAE,oCAAwB;oBACjC,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,SAAS;oBACnC,IAAI,EAAE,eAAe,IAAI,EAAE;iBAC5B;gBACD;oBACE,OAAO,EAAE,iCAAqB;oBAC9B,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM;oBAChC,IAAI,EAAE,gCAAgC,IAAI,yBAAyB;iBACpE;gBACD;oBACE,OAAO,EAAE,8BAAkB;oBAC3B,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,GAAG;oBAC7B,IAAI,EAAE,8CAA8C,IAAI,EAAE;iBAC3D;gBACD;oBACE,OAAO,EAAE,iCAAqB;oBAC9B,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM;oBAChC,IAAI,EAAE,kDAAkD,IAAI,EAAE;iBAC/D;aACF,CAAC;YAEF,KAAK,MAAM,EAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAC,IAAI,WAAW,EAAE,CAAC;gBACzD,MAAM,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC,OAAO,EAAE,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,EAAC,CAAC,CAAC;gBAEjF,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBAE7B,KAAK,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;oBACxC,qEAAqE;oBACrE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC9B,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,EAAC,GAAG,IAAI,EAAC,CAAC,CAAC;oBACnE,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAC,GAAG,IAAI,EAAC,CAAC,CAAC;oBAClD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,iBAAiB,CAAC,SAAS;QAChC,MAAM,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC,OAAO,EAAE;YAChD,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,uDAAuD;YAC7D,WAAW,EACT,yFAAyF;gBACzF,IAAI,IAAA,qCAAqB,GAAE,8DAA8D;gBACzF,mFAAmF;gBACnF,+DAA+D;SAClE,CAAC,CAAC;QAGH,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAClC,MAAM,aAAa,GAAG,WAAW,CAAC,cAAc,CAAC;YAC/C,IAAI,EAAE,cAAc;SACrB,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG;YAClB;gBACE,OAAO,EAAE,iCAAiB;gBAC1B,IAAI,EACF,gCAAgC;oBAChC,aAAa,gBAAC,CAAC,IAAI,CAAC,IAAA,gCAAgB,EAAC,iCAAiB,CAAC,EAAE,GAAG,CAAC,cAAc,+BAAe,GAAG;aAChG;YACD;gBACE,OAAO,EAAE,kCAAkB;gBAC3B,IAAI,EACF,kCAAkC;oBAClC,aAAa,gBAAC,CAAC,IAAI,CAAC,IAAA,gCAAgB,EAAC,kCAAkB,CAAC,EAAE,GAAG,CAAC,cAAc,+BAAe,GAAG;aACjG;YACD;gBACE,OAAO,EAAE,kCAAkB;gBAC3B,IAAI,EACF,sCAAsC;oBACtC,aAAa,gBAAC,CAAC,IAAI,CAAC,IAAA,gCAAgB,EAAC,kCAAkB,CAAC,EAAE,GAAG,CAAC,cAAc,+BAAe,GAAG;aACjG;YACD;gBACE,OAAO,EAAE,gCAAgB;gBACzB,IAAI,EAAE,0CAA0C;aACjD;SACF,CAAC;QAEF,KAAK,MAAM,EAAC,OAAO,EAAE,IAAI,EAAC,IAAI,WAAW,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC,OAAO,EAAE,EAAC,IAAI,EAAC,CAAC,CAAC;YACzD,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;CACF;AAekB,8BAAS;AAb5B;;;;;;GAMG;AACH,SAAS,SAAS,CAAC,KAAK;IACtB,IAAA,uBAAc,GAAE,CAAC;IAEjB,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAID;;;GAGG"}