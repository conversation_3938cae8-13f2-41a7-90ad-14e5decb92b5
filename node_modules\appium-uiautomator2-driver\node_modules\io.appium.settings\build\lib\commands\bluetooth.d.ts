/**
 * Change the state of bluetooth on the device under test.
 *
 * @this {import('../client').SettingsApp}
 * @param {boolean} on - True to enable and false to disable it.
 */
export function setBluetoothState(this: import("../client").SettingsApp, on: boolean): Promise<void>;
/**
 * Unpairs all previosly paired bluetooth devices if any exist
 *
 * @this {import('../client').SettingsApp}
 */
export function unpairAllBluetoothDevices(this: import("../client").SettingsApp): Promise<void>;
//# sourceMappingURL=bluetooth.d.ts.map